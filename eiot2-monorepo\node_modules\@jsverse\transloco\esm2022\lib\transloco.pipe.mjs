import { ChangeDetectorRef, Inject, Optional, Pipe, } from '@angular/core';
import { switchMap, forkJoin } from 'rxjs';
import { TranslocoService } from './transloco.service';
import { TRANSLOCO_SCOPE } from './transloco-scope';
import { TRANSLOCO_LANG } from './transloco-lang';
import { listenOrNotOperator, resolveInlineLoader, shouldListenToLangChanges, } from './shared';
import { LangResolver } from './lang-resolver';
import { ScopeResolver } from './scope-resolver';
import * as i0 from "@angular/core";
import * as i1 from "./transloco.service";
export class TranslocoPipe {
    service;
    providerScope;
    providerLang;
    cdr;
    subscription = null;
    lastValue = '';
    lastKey;
    path;
    langResolver = new LangResolver();
    scopeResolver;
    constructor(service, providerScope, providerLang, cdr) {
        this.service = service;
        this.providerScope = providerScope;
        this.providerLang = providerLang;
        this.cdr = cdr;
        this.scopeResolver = new ScopeResolver(this.service);
    }
    // null is for handling strict mode + async pipe types https://github.com/jsverse/transloco/issues/311
    // null is for handling strict mode + optional chaining types https://github.com/jsverse/transloco/issues/488
    transform(key, params, inlineLang) {
        if (!key) {
            return key;
        }
        const keyName = params ? `${key}${JSON.stringify(params)}` : key;
        if (keyName === this.lastKey) {
            return this.lastValue;
        }
        this.lastKey = keyName;
        this.subscription?.unsubscribe();
        const listenToLangChange = shouldListenToLangChanges(this.service, this.providerLang || inlineLang);
        this.subscription = this.service.langChanges$
            .pipe(switchMap((activeLang) => {
            const lang = this.langResolver.resolve({
                inline: inlineLang,
                provider: this.providerLang,
                active: activeLang,
            });
            return Array.isArray(this.providerScope)
                ? forkJoin(this.providerScope.map((providerScope) => this.resolveScope(lang, providerScope)))
                : this.resolveScope(lang, this.providerScope);
        }), listenOrNotOperator(listenToLangChange))
            .subscribe(() => this.updateValue(key, params));
        return this.lastValue;
    }
    ngOnDestroy() {
        this.subscription?.unsubscribe();
        // Caretaker note: it's important to clean up references to subscriptions since they save the `next`
        // callback within its `destination` property, preventing classes from being GC'd.
        this.subscription = null;
    }
    updateValue(key, params) {
        const lang = this.langResolver.resolveLangBasedOnScope(this.path);
        this.lastValue = this.service.translate(key, params, lang);
        this.cdr.markForCheck();
    }
    resolveScope(lang, providerScope) {
        const resolvedScope = this.scopeResolver.resolve({
            inline: undefined,
            provider: providerScope,
        });
        this.path = this.langResolver.resolveLangPath(lang, resolvedScope);
        const inlineLoader = resolveInlineLoader(providerScope, resolvedScope);
        return this.service._loadDependencies(this.path, inlineLoader);
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "18.2.9", ngImport: i0, type: TranslocoPipe, deps: [{ token: i1.TranslocoService }, { token: TRANSLOCO_SCOPE, optional: true }, { token: TRANSLOCO_LANG, optional: true }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Pipe });
    static ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: "14.0.0", version: "18.2.9", ngImport: i0, type: TranslocoPipe, isStandalone: true, name: "transloco", pure: false });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "18.2.9", ngImport: i0, type: TranslocoPipe, decorators: [{
            type: Pipe,
            args: [{
                    name: 'transloco',
                    pure: false,
                    standalone: true,
                }]
        }], ctorParameters: () => [{ type: i1.TranslocoService }, { type: undefined, decorators: [{
                    type: Optional
                }, {
                    type: Inject,
                    args: [TRANSLOCO_SCOPE]
                }] }, { type: undefined, decorators: [{
                    type: Optional
                }, {
                    type: Inject,
                    args: [TRANSLOCO_LANG]
                }] }, { type: i0.ChangeDetectorRef }] });
//# sourceMappingURL=data:application/json;base64,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