import { EmbeddedViewRef, <PERSON><PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy, OnInit, SimpleChanges } from '@angular/core';
import { Content } from './template-handler';
import { HashMap } from './types';
import * as i0 from "@angular/core";
type TranslateFn = (key: string, params?: HashMap) => any;
interface ViewContext {
    $implicit: TranslateFn;
    currentLang: string;
}
export declare class TranslocoDirective implements OnInit, OnDestroy, OnChanges {
    private destroyRef;
    private service;
    private tpl;
    private providerLang;
    private providerScope;
    private providedLoadingTpl;
    private cdr;
    private host;
    private vcr;
    private renderer;
    view: EmbeddedViewRef<ViewContext> | undefined;
    private memo;
    key: string | undefined;
    params: HashMap;
    inlineScope: string | undefined;
    /** @deprecated use prefix instead, will be removed in Transloco v8 */
    inlineRead: string | undefined;
    prefix: string | undefined;
    inlineLang: string | undefined;
    inlineTpl: Content | undefined;
    private currentLang;
    private loaderTpl<PERSON><PERSON><PERSON>;
    private initialized;
    private path;
    private langResolver;
    private scopeResolver;
    private readonly strategy;
    static ngTemplateContextGuard(dir: TranslocoDirective, ctx: unknown): ctx is ViewContext;
    ngOnInit(): void;
    ngOnChanges(changes: SimpleChanges): void;
    private attributeStrategy;
    private structuralStrategy;
    protected getTranslateFn(lang: string, prefix: string | undefined): TranslateFn;
    private resolveLoadingContent;
    ngOnDestroy(): void;
    private detachLoader;
    private resolveScope;
    static ɵfac: i0.ɵɵFactoryDeclaration<TranslocoDirective, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<TranslocoDirective, "[transloco]", never, { "key": { "alias": "transloco"; "required": false; }; "params": { "alias": "translocoParams"; "required": false; }; "inlineScope": { "alias": "translocoScope"; "required": false; }; "inlineRead": { "alias": "translocoRead"; "required": false; }; "prefix": { "alias": "translocoPrefix"; "required": false; }; "inlineLang": { "alias": "translocoLang"; "required": false; }; "inlineTpl": { "alias": "translocoLoadingTpl"; "required": false; }; }, {}, never, never, true, never>;
}
export {};
