{"name": "chroma-js", "description": "JavaScript library for color conversions", "version": "3.1.2", "packageManager": "pnpm@9.6.0", "author": "<PERSON>", "type": "module", "homepage": "https://github.com/gka/chroma.js", "keywords": ["color"], "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>", "web": "https://vis4.net"}], "bugs": "https://github.com/gka/chroma.js/issues", "repository": {"type": "git", "url": "git://github.com/gka/chroma.js.git"}, "exports": {".": {"import": "./index.js", "require": "./dist/chroma.cjs"}, "./light": {"import": "./index-light.js", "require": "./dist/chroma-light.cjs"}, "./src/colors/*.js": {"import": "./src/colors/*.js"}, "./src/generator/*.js": {"import": "./src/generator/*.js"}, "./src/interpolator/*.js": {"import": "./src/interpolator/*.js"}, "./src/io/*.js": {"import": "./src/io/*.js"}, "./src/ops/*.js": {"import": "./src/ops/*.js"}, "./src/utils/*.js": {"import": "./src/utils/*.js"}}, "main": "./index.js", "scripts": {"prepublishOnly": "npm test -- --run && node .bin/update-version.cjs && npm run build", "build": "rollup -c && cross-env DEV=1 rollup -c ", "docs": "cd docs && make", "docs-preview": "cd docs && make && make preview", "test": "vitest", "test:update": "vitest -u", "lint": "prettier --check index.js index.umd.js index.umd.light.js src *.config.js test && eslint index.js index.umd.js index.umd.light.js src", "format": "prettier --write index.js index.umd.js index.umd.light.js src *.config.js test", "prepare": "husky"}, "devDependencies": {"@babel/eslint-parser": "^7.25.1", "@eslint/eslintrc": "^3.1.0", "@eslint/js": "^9.9.0", "@rollup/plugin-buble": "^1.0.3", "@rollup/plugin-terser": "^0.4.4", "cross-env": "^7.0.3", "eslint": "^9.9.0", "globals": "^15.9.0", "http-server": "^14.1.1", "husky": "^9.1.4", "markdown-to-html": "0.0.13", "minimatch": "^10.0.1", "prettier": "^3.3.3", "rollup": "^4.20.0", "rollup-plugin-license": "^3.5.2", "vitest": "^2.0.5"}, "license": "(BSD-3-Clause AND Apache-2.0)", "spm": {"main": "chroma.js", "ignore": ["src", "doc", "test"]}}