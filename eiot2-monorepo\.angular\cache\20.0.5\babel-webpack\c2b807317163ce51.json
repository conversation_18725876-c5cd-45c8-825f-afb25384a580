{"ast": null, "code": "import { provideHttpClient } from '@angular/common/http';\nimport { inject, isDevMode, provideAppInitializer } from '@angular/core';\nimport { LuxonDateAdapter } from '@angular/material-luxon-adapter';\nimport { DateAdapter, MAT_DATE_FORMATS } from '@angular/material/core';\nimport { provideAnimations } from '@angular/platform-browser/animations';\nimport { provideRouter, withInMemoryScrolling } from '@angular/router';\nimport { provideFuse } from '@fuse';\nimport { TranslocoService, provideTransloco } from '@jsverse/transloco';\nimport { appRoutes } from './app.routes';\nimport { provideAuth } from './core/auth/auth.provider';\nimport { provideIcons } from './core/icons/icons.provider';\nimport { MockApiService } from './mock-api';\nimport { firstValueFrom } from 'rxjs';\nimport { TranslocoHttpLoader } from './core/transloco/transloco.http-loader';\nexport const appConfig = {\n  providers: [provideAnimations(), provideHttpClient(), provideRouter(appRoutes, withInMemoryScrolling({\n    scrollPositionRestoration: 'enabled'\n  })),\n  // Material Date Adapter\n  {\n    provide: DateAdapter,\n    useClass: LuxonDateAdapter\n  }, {\n    provide: MAT_DATE_FORMATS,\n    useValue: {\n      parse: {\n        dateInput: 'D'\n      },\n      display: {\n        dateInput: 'DDD',\n        monthYearLabel: 'LLL yyyy',\n        dateA11yLabel: 'DD',\n        monthYearA11yLabel: 'LLLL yyyy'\n      }\n    }\n  },\n  // Transloco Config\n  provideTransloco({\n    config: {\n      availableLangs: [{\n        id: 'en',\n        label: 'English'\n      }, {\n        id: 'tr',\n        label: 'Turkish'\n      }],\n      defaultLang: 'en',\n      fallbackLang: 'en',\n      reRenderOnLangChange: true,\n      prodMode: !isDevMode()\n    },\n    loader: TranslocoHttpLoader\n  }), provideAppInitializer(() => {\n    const translocoService = inject(TranslocoService);\n    const defaultLang = translocoService.getDefaultLang();\n    translocoService.setActiveLang(defaultLang);\n    return firstValueFrom(translocoService.load(defaultLang));\n  }),\n  // Fuse\n  provideAuth(), provideIcons(), provideFuse({\n    mockApi: {\n      delay: 0,\n      service: MockApiService\n    },\n    fuse: {\n      layout: 'classy',\n      scheme: 'light',\n      screens: {\n        sm: '600px',\n        md: '960px',\n        lg: '1280px',\n        xl: '1440px'\n      },\n      theme: 'theme-default',\n      themes: [{\n        id: 'theme-default',\n        name: 'Default'\n      }, {\n        id: 'theme-brand',\n        name: 'Brand'\n      }, {\n        id: 'theme-teal',\n        name: 'Teal'\n      }, {\n        id: 'theme-rose',\n        name: 'Rose'\n      }, {\n        id: 'theme-purple',\n        name: 'Purple'\n      }, {\n        id: 'theme-amber',\n        name: 'Amber'\n      }]\n    }\n  })]\n};", "map": {"version": 3, "names": ["provideHttpClient", "inject", "isDevMode", "provideAppInitializer", "LuxonDateAdapter", "DateAdapter", "MAT_DATE_FORMATS", "provideAnimations", "provideRouter", "withInMemoryScrolling", "provideFuse", "TranslocoService", "provideTransloco", "appRoutes", "provideAuth", "provideIcons", "MockApiService", "firstValueFrom", "TranslocoHttpLoader", "appConfig", "providers", "scrollPositionRestoration", "provide", "useClass", "useValue", "parse", "dateInput", "display", "month<PERSON><PERSON><PERSON><PERSON><PERSON>", "dateA11yLabel", "monthYearA11yLabel", "config", "availableLangs", "id", "label", "defaultLang", "fallback<PERSON><PERSON>", "reRenderOnLangChange", "prodMode", "loader", "translocoService", "getDefaultLang", "setActiveLang", "load", "mockApi", "delay", "service", "fuse", "layout", "scheme", "screens", "sm", "md", "lg", "xl", "theme", "themes", "name"], "sources": ["D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\app\\app.config.ts"], "sourcesContent": ["import { provideHttpClient } from '@angular/common/http';\nimport {\n    ApplicationConfig,\n    inject,\n    isDevMode,\n    provideAppInitializer,\n} from '@angular/core';\nimport { LuxonDateAdapter } from '@angular/material-luxon-adapter';\nimport { DateAdapter, MAT_DATE_FORMATS } from '@angular/material/core';\nimport { provideAnimations } from '@angular/platform-browser/animations';\nimport { provideRouter, withInMemoryScrolling } from '@angular/router';\nimport { provideFuse } from '@fuse';\nimport { TranslocoService, provideTransloco } from '@jsverse/transloco';\nimport { appRoutes } from './app.routes';\nimport { provideAuth } from './core/auth/auth.provider';\nimport { provideIcons } from './core/icons/icons.provider';\nimport { MockApiService } from './mock-api';\nimport { firstValueFrom } from 'rxjs';\nimport { TranslocoHttpLoader } from './core/transloco/transloco.http-loader';\n\nexport const appConfig: ApplicationConfig = {\n    providers: [\n        provideAnimations(),\n        provideHttpClient(),\n        provideRouter(\n            appRoutes,\n            withInMemoryScrolling({ scrollPositionRestoration: 'enabled' })\n        ),\n\n        // Material Date Adapter\n        {\n            provide: DateAdapter,\n            useClass: LuxonDateAdapter,\n        },\n        {\n            provide: MAT_DATE_FORMATS,\n            useValue: {\n                parse: {\n                    dateInput: 'D',\n                },\n                display: {\n                    dateInput: 'DDD',\n                    monthYearLabel: 'LLL yyyy',\n                    dateA11yLabel: 'DD',\n                    monthYearA11yLabel: 'LLLL yyyy',\n                },\n            },\n        },\n\n        // Transloco Config\n        provideTransloco({\n            config: {\n                availableLangs: [\n                    {\n                        id: 'en',\n                        label: 'English',\n                    },\n                    {\n                        id: 'tr',\n                        label: 'Turkish',\n                    },\n                ],\n                defaultLang: 'en',\n                fallbackLang: 'en',\n                reRenderOnLangChange: true,\n                prodMode: !isDevMode(),\n            },\n            loader: TranslocoHttpLoader,\n        }),\n        provideAppInitializer(() => {\n            const translocoService = inject(TranslocoService);\n            const defaultLang = translocoService.getDefaultLang();\n            translocoService.setActiveLang(defaultLang);\n\n            return firstValueFrom(translocoService.load(defaultLang));\n        }),\n\n        // Fuse\n        provideAuth(),\n        provideIcons(),\n        provideFuse({\n            mockApi: {\n                delay: 0,\n                service: MockApiService,\n            },\n            fuse: {\n                layout: 'classy',\n                scheme: 'light',\n                screens: {\n                    sm: '600px',\n                    md: '960px',\n                    lg: '1280px',\n                    xl: '1440px',\n                },\n                theme: 'theme-default',\n                themes: [\n                    {\n                        id: 'theme-default',\n                        name: 'Default',\n                    },\n                    {\n                        id: 'theme-brand',\n                        name: 'Brand',\n                    },\n                    {\n                        id: 'theme-teal',\n                        name: 'Teal',\n                    },\n                    {\n                        id: 'theme-rose',\n                        name: 'Rose',\n                    },\n                    {\n                        id: 'theme-purple',\n                        name: 'Purple',\n                    },\n                    {\n                        id: 'theme-amber',\n                        name: 'Amber',\n                    },\n                ],\n            },\n        }),\n    ],\n};\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,sBAAsB;AACxD,SAEIC,MAAM,EACNC,SAAS,EACTC,qBAAqB,QAClB,eAAe;AACtB,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,WAAW,EAAEC,gBAAgB,QAAQ,wBAAwB;AACtE,SAASC,iBAAiB,QAAQ,sCAAsC;AACxE,SAASC,aAAa,EAAEC,qBAAqB,QAAQ,iBAAiB;AACtE,SAASC,WAAW,QAAQ,OAAO;AACnC,SAASC,gBAAgB,EAAEC,gBAAgB,QAAQ,oBAAoB;AACvE,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,cAAc,QAAQ,YAAY;AAC3C,SAASC,cAAc,QAAQ,MAAM;AACrC,SAASC,mBAAmB,QAAQ,wCAAwC;AAE5E,OAAO,MAAMC,SAAS,GAAsB;EACxCC,SAAS,EAAE,CACPb,iBAAiB,EAAE,EACnBP,iBAAiB,EAAE,EACnBQ,aAAa,CACTK,SAAS,EACTJ,qBAAqB,CAAC;IAAEY,yBAAyB,EAAE;EAAS,CAAE,CAAC,CAClE;EAED;EACA;IACIC,OAAO,EAAEjB,WAAW;IACpBkB,QAAQ,EAAEnB;GACb,EACD;IACIkB,OAAO,EAAEhB,gBAAgB;IACzBkB,QAAQ,EAAE;MACNC,KAAK,EAAE;QACHC,SAAS,EAAE;OACd;MACDC,OAAO,EAAE;QACLD,SAAS,EAAE,KAAK;QAChBE,cAAc,EAAE,UAAU;QAC1BC,aAAa,EAAE,IAAI;QACnBC,kBAAkB,EAAE;;;GAG/B;EAED;EACAlB,gBAAgB,CAAC;IACbmB,MAAM,EAAE;MACJC,cAAc,EAAE,CACZ;QACIC,EAAE,EAAE,IAAI;QACRC,KAAK,EAAE;OACV,EACD;QACID,EAAE,EAAE,IAAI;QACRC,KAAK,EAAE;OACV,CACJ;MACDC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,IAAI;MAClBC,oBAAoB,EAAE,IAAI;MAC1BC,QAAQ,EAAE,CAACpC,SAAS;KACvB;IACDqC,MAAM,EAAErB;GACX,CAAC,EACFf,qBAAqB,CAAC,MAAK;IACvB,MAAMqC,gBAAgB,GAAGvC,MAAM,CAACU,gBAAgB,CAAC;IACjD,MAAMwB,WAAW,GAAGK,gBAAgB,CAACC,cAAc,EAAE;IACrDD,gBAAgB,CAACE,aAAa,CAACP,WAAW,CAAC;IAE3C,OAAOlB,cAAc,CAACuB,gBAAgB,CAACG,IAAI,CAACR,WAAW,CAAC,CAAC;EAC7D,CAAC,CAAC;EAEF;EACArB,WAAW,EAAE,EACbC,YAAY,EAAE,EACdL,WAAW,CAAC;IACRkC,OAAO,EAAE;MACLC,KAAK,EAAE,CAAC;MACRC,OAAO,EAAE9B;KACZ;IACD+B,IAAI,EAAE;MACFC,MAAM,EAAE,QAAQ;MAChBC,MAAM,EAAE,OAAO;MACfC,OAAO,EAAE;QACLC,EAAE,EAAE,OAAO;QACXC,EAAE,EAAE,OAAO;QACXC,EAAE,EAAE,QAAQ;QACZC,EAAE,EAAE;OACP;MACDC,KAAK,EAAE,eAAe;MACtBC,MAAM,EAAE,CACJ;QACIvB,EAAE,EAAE,eAAe;QACnBwB,IAAI,EAAE;OACT,EACD;QACIxB,EAAE,EAAE,aAAa;QACjBwB,IAAI,EAAE;OACT,EACD;QACIxB,EAAE,EAAE,YAAY;QAChBwB,IAAI,EAAE;OACT,EACD;QACIxB,EAAE,EAAE,YAAY;QAChBwB,IAAI,EAAE;OACT,EACD;QACIxB,EAAE,EAAE,cAAc;QAClBwB,IAAI,EAAE;OACT,EACD;QACIxB,EAAE,EAAE,aAAa;QACjBwB,IAAI,EAAE;OACT;;GAGZ,CAAC;CAET", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}