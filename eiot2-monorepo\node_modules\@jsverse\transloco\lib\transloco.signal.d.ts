import { Injector, Signal } from '@angular/core';
import { HashMap, Translation, TranslocoScope } from './types';
type ScopeType = string | TranslocoScope | TranslocoScope[];
type SignalKey = Signal<string> | Signal<string[]> | Signal<string>[];
type TranslateSignalKey = string | string[] | SignalKey;
type TranslateSignalParams = HashMap | HashMap<Signal<string>> | Signal<HashMap>;
type TranslateSignalRef<T> = T extends unknown[] | Signal<string[]> ? Signal<string[]> : Signal<string>;
type TranslateObjectSignalRef<T> = T extends unknown[] | Signal<string[]> ? Signal<Translation[]> : Signal<Translation>;
/**
 * Gets the translated value of a key as Signal
 *
 * @example
 * text = translateSignal('hello');
 * textList = translateSignal(['green', 'blue']);
 * textVar = translateSignal('hello', { variable: 'world' });
 * textSpanish = translateSignal('hello', { variable: 'world' }, 'es');
 * textTodosScope = translateSignal('hello', { variable: 'world' }, { scope: 'todos' });
 *
 * @example
 * dynamicKey = signal('hello');
 * dynamicParam = signal({ variable: 'world' });
 * text = translateSignal(this.dynamicKey, this.dynamicParam);
 *
 */
export declare function translateSignal<T extends TranslateSignalKey>(key: T, params?: TranslateSignalParams, lang?: ScopeType, injector?: Injector): TranslateSignalRef<T>;
/**
 * Gets the translated object of a key as Signal
 *
 * @example
 * object = translateObjectSignal('nested.object');
 * title = object().title;
 *
 * @example
 * dynamicKey = signal('nested.object');
 * dynamicParam = signal({ variable: 'world' });
 * object = translateObjectSignal(this.dynamicKey, this.dynamicParam);
 */
export declare function translateObjectSignal<T extends TranslateSignalKey>(key: T, params?: TranslateSignalParams, lang?: ScopeType, injector?: Injector): TranslateObjectSignalRef<T>;
export {};
