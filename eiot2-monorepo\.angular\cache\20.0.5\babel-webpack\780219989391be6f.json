{"ast": null, "code": "import { provideHttpClient } from '@angular/common/http';\nimport { inject, isDevMode, provideAppInitializer } from '@angular/core';\nimport { LuxonDateAdapter } from '@angular/material-luxon-adapter';\nimport { DateAdapter, MAT_DATE_FORMATS } from '@angular/material/core';\nimport { provideAnimations } from '@angular/platform-browser/animations';\nimport { provideRouter, withInMemoryScrolling } from '@angular/router';\nimport { TranslocoService, provideTransloco } from '@jsverse/transloco';\nimport { appRoutes } from 'app/app.routes';\n// import { provideAuth } from 'app/core/auth/auth.provider';\n// import { provideIcons } from 'app/core/icons/icons.provider';\n// import { MockApiService } from 'app/mock-api';\nimport { firstValueFrom } from 'rxjs';\nimport { TranslocoHttpLoader } from './core/transloco/transloco.http-loader';\nexport const appConfig = {\n  providers: [provideAnimations(), provideHttpClient(), provideRouter(appRoutes, withInMemoryScrolling({\n    scrollPositionRestoration: 'enabled'\n  })),\n  // Material Date Adapter\n  {\n    provide: DateAdapter,\n    useClass: LuxonDateAdapter\n  }, {\n    provide: MAT_DATE_FORMATS,\n    useValue: {\n      parse: {\n        dateInput: 'D'\n      },\n      display: {\n        dateInput: 'DDD',\n        monthYearLabel: 'LLL yyyy',\n        dateA11yLabel: 'DD',\n        monthYearA11yLabel: 'LLLL yyyy'\n      }\n    }\n  },\n  // Transloco Config\n  provideTransloco({\n    config: {\n      availableLangs: [{\n        id: 'en',\n        label: 'English'\n      }, {\n        id: 'tr',\n        label: 'Turkish'\n      }],\n      defaultLang: 'en',\n      fallbackLang: 'en',\n      reRenderOnLangChange: true,\n      prodMode: !isDevMode()\n    },\n    loader: TranslocoHttpLoader\n  }), provideAppInitializer(() => {\n    const translocoService = inject(TranslocoService);\n    const defaultLang = translocoService.getDefaultLang();\n    translocoService.setActiveLang(defaultLang);\n    return firstValueFrom(translocoService.load(defaultLang));\n  })\n  // Fuse (temporarily disabled until licensed files are copied)\n  // provideAuth(),\n  // provideIcons(),\n  // provideFuse({\n  //     mockApi: {\n  //         delay: 0,\n  //         service: MockApiService,\n  //     },\n  //     fuse: {\n  //         layout: 'classy',\n  //         scheme: 'light',\n  //         screens: {\n  //             sm: '600px',\n  //             md: '960px',\n  //             lg: '1280px',\n  //             xl: '1440px',\n  //         },\n  //         theme: 'theme-default',\n  //         themes: [\n  //             {\n  //                 id: 'theme-default',\n  //                 name: 'Default',\n  //             },\n  //             {\n  //                 id: 'theme-brand',\n  //                 name: 'Brand',\n  //             },\n  //             {\n  //                 id: 'theme-teal',\n  //                 name: 'Teal',\n  //             },\n  //             {\n  //                 id: 'theme-rose',\n  //                 name: 'Rose',\n  //             },\n  //             {\n  //                 id: 'theme-purple',\n  //                 name: 'Purple',\n  //             },\n  //             {\n  //                 id: 'theme-amber',\n  //                 name: 'Amber',\n  //             },\n  //         ],\n  //     },\n  // }),\n  ]\n};", "map": {"version": 3, "names": ["provideHttpClient", "inject", "isDevMode", "provideAppInitializer", "LuxonDateAdapter", "DateAdapter", "MAT_DATE_FORMATS", "provideAnimations", "provideRouter", "withInMemoryScrolling", "TranslocoService", "provideTransloco", "appRoutes", "firstValueFrom", "TranslocoHttpLoader", "appConfig", "providers", "scrollPositionRestoration", "provide", "useClass", "useValue", "parse", "dateInput", "display", "month<PERSON><PERSON><PERSON><PERSON><PERSON>", "dateA11yLabel", "monthYearA11yLabel", "config", "availableLangs", "id", "label", "defaultLang", "fallback<PERSON><PERSON>", "reRenderOnLangChange", "prodMode", "loader", "translocoService", "getDefaultLang", "setActiveLang", "load"], "sources": ["D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\app\\app.config.ts"], "sourcesContent": ["import { provideHttpClient } from '@angular/common/http';\nimport {\n    ApplicationConfig,\n    inject,\n    isDevMode,\n    provideAppInitializer,\n} from '@angular/core';\nimport { LuxonDateAdapter } from '@angular/material-luxon-adapter';\nimport { DateAdapter, MAT_DATE_FORMATS } from '@angular/material/core';\nimport { provideAnimations } from '@angular/platform-browser/animations';\nimport { provideRouter, withInMemoryScrolling } from '@angular/router';\nimport { provideFuse } from '@fuse';\nimport { TranslocoService, provideTransloco } from '@jsverse/transloco';\nimport { appRoutes } from 'app/app.routes';\n// import { provideAuth } from 'app/core/auth/auth.provider';\n// import { provideIcons } from 'app/core/icons/icons.provider';\n// import { MockApiService } from 'app/mock-api';\nimport { firstValueFrom } from 'rxjs';\nimport { TranslocoHttpLoader } from './core/transloco/transloco.http-loader';\n\nexport const appConfig: ApplicationConfig = {\n    providers: [\n        provideAnimations(),\n        provideHttpClient(),\n        provideRouter(\n            appRoutes,\n            withInMemoryScrolling({ scrollPositionRestoration: 'enabled' })\n        ),\n\n        // Material Date Adapter\n        {\n            provide: DateAdapter,\n            useClass: LuxonDateAdapter,\n        },\n        {\n            provide: MAT_DATE_FORMATS,\n            useValue: {\n                parse: {\n                    dateInput: 'D',\n                },\n                display: {\n                    dateInput: 'DDD',\n                    monthYearLabel: 'LLL yyyy',\n                    dateA11yLabel: 'DD',\n                    monthYearA11yLabel: 'LLLL yyyy',\n                },\n            },\n        },\n\n        // Transloco Config\n        provideTransloco({\n            config: {\n                availableLangs: [\n                    {\n                        id: 'en',\n                        label: 'English',\n                    },\n                    {\n                        id: 'tr',\n                        label: 'Turkish',\n                    },\n                ],\n                defaultLang: 'en',\n                fallbackLang: 'en',\n                reRenderOnLangChange: true,\n                prodMode: !isDevMode(),\n            },\n            loader: TranslocoHttpLoader,\n        }),\n        provideAppInitializer(() => {\n            const translocoService = inject(TranslocoService);\n            const defaultLang = translocoService.getDefaultLang();\n            translocoService.setActiveLang(defaultLang);\n\n            return firstValueFrom(translocoService.load(defaultLang));\n        }),\n\n        // Fuse (temporarily disabled until licensed files are copied)\n        // provideAuth(),\n        // provideIcons(),\n        // provideFuse({\n        //     mockApi: {\n        //         delay: 0,\n        //         service: MockApiService,\n        //     },\n        //     fuse: {\n        //         layout: 'classy',\n        //         scheme: 'light',\n        //         screens: {\n        //             sm: '600px',\n        //             md: '960px',\n        //             lg: '1280px',\n        //             xl: '1440px',\n        //         },\n        //         theme: 'theme-default',\n        //         themes: [\n        //             {\n        //                 id: 'theme-default',\n        //                 name: 'Default',\n        //             },\n        //             {\n        //                 id: 'theme-brand',\n        //                 name: 'Brand',\n        //             },\n        //             {\n        //                 id: 'theme-teal',\n        //                 name: 'Teal',\n        //             },\n        //             {\n        //                 id: 'theme-rose',\n        //                 name: 'Rose',\n        //             },\n        //             {\n        //                 id: 'theme-purple',\n        //                 name: 'Purple',\n        //             },\n        //             {\n        //                 id: 'theme-amber',\n        //                 name: 'Amber',\n        //             },\n        //         ],\n        //     },\n        // }),\n    ],\n};\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,sBAAsB;AACxD,SAEIC,MAAM,EACNC,SAAS,EACTC,qBAAqB,QAClB,eAAe;AACtB,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,WAAW,EAAEC,gBAAgB,QAAQ,wBAAwB;AACtE,SAASC,iBAAiB,QAAQ,sCAAsC;AACxE,SAASC,aAAa,EAAEC,qBAAqB,QAAQ,iBAAiB;AAEtE,SAASC,gBAAgB,EAAEC,gBAAgB,QAAQ,oBAAoB;AACvE,SAASC,SAAS,QAAQ,gBAAgB;AAC1C;AACA;AACA;AACA,SAASC,cAAc,QAAQ,MAAM;AACrC,SAASC,mBAAmB,QAAQ,wCAAwC;AAE5E,OAAO,MAAMC,SAAS,GAAsB;EACxCC,SAAS,EAAE,CACPT,iBAAiB,EAAE,EACnBP,iBAAiB,EAAE,EACnBQ,aAAa,CACTI,SAAS,EACTH,qBAAqB,CAAC;IAAEQ,yBAAyB,EAAE;EAAS,CAAE,CAAC,CAClE;EAED;EACA;IACIC,OAAO,EAAEb,WAAW;IACpBc,QAAQ,EAAEf;GACb,EACD;IACIc,OAAO,EAAEZ,gBAAgB;IACzBc,QAAQ,EAAE;MACNC,KAAK,EAAE;QACHC,SAAS,EAAE;OACd;MACDC,OAAO,EAAE;QACLD,SAAS,EAAE,KAAK;QAChBE,cAAc,EAAE,UAAU;QAC1BC,aAAa,EAAE,IAAI;QACnBC,kBAAkB,EAAE;;;GAG/B;EAED;EACAf,gBAAgB,CAAC;IACbgB,MAAM,EAAE;MACJC,cAAc,EAAE,CACZ;QACIC,EAAE,EAAE,IAAI;QACRC,KAAK,EAAE;OACV,EACD;QACID,EAAE,EAAE,IAAI;QACRC,KAAK,EAAE;OACV,CACJ;MACDC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,IAAI;MAClBC,oBAAoB,EAAE,IAAI;MAC1BC,QAAQ,EAAE,CAAChC,SAAS;KACvB;IACDiC,MAAM,EAAErB;GACX,CAAC,EACFX,qBAAqB,CAAC,MAAK;IACvB,MAAMiC,gBAAgB,GAAGnC,MAAM,CAACS,gBAAgB,CAAC;IACjD,MAAMqB,WAAW,GAAGK,gBAAgB,CAACC,cAAc,EAAE;IACrDD,gBAAgB,CAACE,aAAa,CAACP,WAAW,CAAC;IAE3C,OAAOlB,cAAc,CAACuB,gBAAgB,CAACG,IAAI,CAACR,WAAW,CAAC,CAAC;EAC7D,CAAC;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAAA;CAEP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}