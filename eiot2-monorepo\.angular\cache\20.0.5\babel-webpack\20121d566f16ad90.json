{"ast": null, "code": "import { animate, AnimationBuilder, style } from '@angular/animations';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { ElementRef, EventEmitter, inject, Renderer2 } from '@angular/core';\nimport { FuseDrawerService } from '@fuse/components/drawer/drawer.service';\nimport { FuseUtilsService } from '@fuse/services/utils/utils.service';\nimport * as i0 from \"@angular/core\";\nconst _c0 = [\"*\"];\nexport class FuseDrawerComponent {\n  constructor() {\n    /* eslint-enable @typescript-eslint/naming-convention */\n    this._animationBuilder = inject(AnimationBuilder);\n    this._elementRef = inject(ElementRef);\n    this._renderer2 = inject(Renderer2);\n    this._fuseDrawerService = inject(FuseDrawerService);\n    this._fuseUtilsService = inject(FuseUtilsService);\n    this.fixed = false;\n    this.mode = 'side';\n    this.name = this._fuseUtilsService.randomId();\n    this.opened = false;\n    this.position = 'left';\n    this.transparentOverlay = false;\n    this.fixedChanged = new EventEmitter();\n    this.modeChanged = new EventEmitter();\n    this.openedChanged = new EventEmitter();\n    this.positionChanged = new EventEmitter();\n    this._animationsEnabled = false;\n    this._handleOverlayClick = () => this.close();\n    this._hovered = false;\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Accessors\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * Host binding for component classes\n   */\n  get classList() {\n    /* eslint-disable @typescript-eslint/naming-convention */\n    return {\n      'fuse-drawer-animations-enabled': this._animationsEnabled,\n      'fuse-drawer-fixed': this.fixed,\n      'fuse-drawer-hover': this._hovered,\n      [`fuse-drawer-mode-${this.mode}`]: true,\n      'fuse-drawer-opened': this.opened,\n      [`fuse-drawer-position-${this.position}`]: true\n    };\n    /* eslint-enable @typescript-eslint/naming-convention */\n  }\n  /**\n   * Host binding for component inline styles\n   */\n  get styleList() {\n    return {\n      visibility: this.opened ? 'visible' : 'hidden'\n    };\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Decorated methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * On mouseenter\n   *\n   * @private\n   */\n  _onMouseenter() {\n    // Enable the animations\n    this._enableAnimations();\n    // Set the hovered\n    this._hovered = true;\n  }\n  /**\n   * On mouseleave\n   *\n   * @private\n   */\n  _onMouseleave() {\n    // Enable the animations\n    this._enableAnimations();\n    // Set the hovered\n    this._hovered = false;\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Lifecycle hooks\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * On changes\n   *\n   * @param changes\n   */\n  ngOnChanges(changes) {\n    // Fixed\n    if ('fixed' in changes) {\n      // Coerce the value to a boolean\n      this.fixed = coerceBooleanProperty(changes.fixed.currentValue);\n      // Execute the observable\n      this.fixedChanged.next(this.fixed);\n    }\n    // Mode\n    if ('mode' in changes) {\n      // Get the previous and current values\n      const previousMode = changes.mode.previousValue;\n      const currentMode = changes.mode.currentValue;\n      // Disable the animations\n      this._disableAnimations();\n      // If the mode changes: 'over -> side'\n      if (previousMode === 'over' && currentMode === 'side') {\n        // Hide the overlay\n        this._hideOverlay();\n      }\n      // If the mode changes: 'side -> over'\n      if (previousMode === 'side' && currentMode === 'over') {\n        // If the drawer is opened\n        if (this.opened) {\n          // Show the overlay\n          this._showOverlay();\n        }\n      }\n      // Execute the observable\n      this.modeChanged.next(currentMode);\n      // Enable the animations after a delay\n      // The delay must be bigger than the current transition-duration\n      // to make sure nothing will be animated while the mode is changing\n      setTimeout(() => {\n        this._enableAnimations();\n      }, 500);\n    }\n    // Opened\n    if ('opened' in changes) {\n      // Coerce the value to a boolean\n      const open = coerceBooleanProperty(changes.opened.currentValue);\n      // Open/close the drawer\n      this._toggleOpened(open);\n    }\n    // Position\n    if ('position' in changes) {\n      // Execute the observable\n      this.positionChanged.next(this.position);\n    }\n    // Transparent overlay\n    if ('transparentOverlay' in changes) {\n      // Coerce the value to a boolean\n      this.transparentOverlay = coerceBooleanProperty(changes.transparentOverlay.currentValue);\n    }\n  }\n  /**\n   * On init\n   */\n  ngOnInit() {\n    // Register the drawer\n    this._fuseDrawerService.registerComponent(this.name, this);\n  }\n  /**\n   * On destroy\n   */\n  ngOnDestroy() {\n    // Finish the animation\n    if (this._player) {\n      this._player.finish();\n    }\n    // Deregister the drawer from the registry\n    this._fuseDrawerService.deregisterComponent(this.name);\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Public methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * Open the drawer\n   */\n  open() {\n    // Return if the drawer has already opened\n    if (this.opened) {\n      return;\n    }\n    // Open the drawer\n    this._toggleOpened(true);\n  }\n  /**\n   * Close the drawer\n   */\n  close() {\n    // Return if the drawer has already closed\n    if (!this.opened) {\n      return;\n    }\n    // Close the drawer\n    this._toggleOpened(false);\n  }\n  /**\n   * Toggle the drawer\n   */\n  toggle() {\n    if (this.opened) {\n      this.close();\n    } else {\n      this.open();\n    }\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Private methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * Enable the animations\n   *\n   * @private\n   */\n  _enableAnimations() {\n    // Return if the animations are already enabled\n    if (this._animationsEnabled) {\n      return;\n    }\n    // Enable the animations\n    this._animationsEnabled = true;\n  }\n  /**\n   * Disable the animations\n   *\n   * @private\n   */\n  _disableAnimations() {\n    // Return if the animations are already disabled\n    if (!this._animationsEnabled) {\n      return;\n    }\n    // Disable the animations\n    this._animationsEnabled = false;\n  }\n  /**\n   * Show the backdrop\n   *\n   * @private\n   */\n  _showOverlay() {\n    // Create the backdrop element\n    this._overlay = this._renderer2.createElement('div');\n    // Add a class to the backdrop element\n    this._overlay.classList.add('fuse-drawer-overlay');\n    // Add a class depending on the fixed option\n    if (this.fixed) {\n      this._overlay.classList.add('fuse-drawer-overlay-fixed');\n    }\n    // Add a class depending on the transparentOverlay option\n    if (this.transparentOverlay) {\n      this._overlay.classList.add('fuse-drawer-overlay-transparent');\n    }\n    // Append the backdrop to the parent of the drawer\n    this._renderer2.appendChild(this._elementRef.nativeElement.parentElement, this._overlay);\n    // Create enter animation and attach it to the player\n    this._player = this._animationBuilder.build([style({\n      opacity: 0\n    }), animate('300ms cubic-bezier(0.25, 0.8, 0.25, 1)', style({\n      opacity: 1\n    }))]).create(this._overlay);\n    // Play the animation\n    this._player.play();\n    // Add an event listener to the overlay\n    this._overlay.addEventListener('click', this._handleOverlayClick);\n  }\n  /**\n   * Hide the backdrop\n   *\n   * @private\n   */\n  _hideOverlay() {\n    if (!this._overlay) {\n      return;\n    }\n    // Create the leave animation and attach it to the player\n    this._player = this._animationBuilder.build([animate('300ms cubic-bezier(0.25, 0.8, 0.25, 1)', style({\n      opacity: 0\n    }))]).create(this._overlay);\n    // Play the animation\n    this._player.play();\n    // Once the animation is done...\n    this._player.onDone(() => {\n      // If the overlay still exists...\n      if (this._overlay) {\n        // Remove the event listener\n        this._overlay.removeEventListener('click', this._handleOverlayClick);\n        // Remove the overlay\n        this._overlay.parentNode.removeChild(this._overlay);\n        this._overlay = null;\n      }\n    });\n  }\n  /**\n   * Open/close the drawer\n   *\n   * @param open\n   * @private\n   */\n  _toggleOpened(open) {\n    // Set the opened\n    this.opened = open;\n    // Enable the animations\n    this._enableAnimations();\n    // If the mode is 'over'\n    if (this.mode === 'over') {\n      // If the drawer opens, show the overlay\n      if (open) {\n        this._showOverlay();\n      }\n      // Otherwise, close the overlay\n      else {\n        this._hideOverlay();\n      }\n    }\n    // Execute the observable\n    this.openedChanged.next(open);\n  }\n  static #_ = this.ɵfac = function FuseDrawerComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FuseDrawerComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: FuseDrawerComponent,\n    selectors: [[\"fuse-drawer\"]],\n    hostVars: 4,\n    hostBindings: function FuseDrawerComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"mouseenter\", function FuseDrawerComponent_mouseenter_HostBindingHandler() {\n          return ctx._onMouseenter();\n        })(\"mouseleave\", function FuseDrawerComponent_mouseleave_HostBindingHandler() {\n          return ctx._onMouseleave();\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵstyleMap(ctx.styleList);\n        i0.ɵɵclassMap(ctx.classList);\n      }\n    },\n    inputs: {\n      fixed: \"fixed\",\n      mode: \"mode\",\n      name: \"name\",\n      opened: \"opened\",\n      position: \"position\",\n      transparentOverlay: \"transparentOverlay\"\n    },\n    outputs: {\n      fixedChanged: \"fixedChanged\",\n      modeChanged: \"modeChanged\",\n      openedChanged: \"openedChanged\",\n      positionChanged: \"positionChanged\"\n    },\n    exportAs: [\"fuseDrawer\"],\n    features: [i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c0,\n    decls: 2,\n    vars: 0,\n    consts: [[1, \"fuse-drawer-content\"]],\n    template: function FuseDrawerComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelementEnd();\n      }\n    },\n    styles: [\"var resource;\\n/******/ (() => { // webpackBootstrap\\n/******/ \\tvar __webpack_modules__ = ({\\n\\n/***/ 950:\\n/*!**************************************************************************************!*\\\\\\n  !*** ./apps/eiot-admin/src/@fuse/components/drawer/drawer.component.scss?ngResource ***!\\n  \\\\**************************************************************************************/\\n/***/ (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/postcss-loader/dist/cjs.js):\\\\nError: Cannot find module 'chroma-js'\\\\nRequire stack:\\\\n- D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\apps\\\\\\\\eiot-admin\\\\\\\\src\\\\\\\\@fuse\\\\\\\\tailwind\\\\\\\\utils\\\\\\\\generate-palette.js\\\\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\\\\n    at Function.resolve (node:internal/modules/helpers:193:19)\\\\n    at _resolve (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:246378)\\\\n    at jiti (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:249092)\\\\n    at D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\apps\\\\\\\\eiot-admin\\\\\\\\src\\\\\\\\@fuse\\\\\\\\tailwind\\\\\\\\utils\\\\\\\\generate-palette.js:1:91\\\\n    at evalModule (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:251913)\\\\n    at jiti (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:249841)\\\\n    at D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\apps\\\\\\\\eiot-admin\\\\\\\\tailwind.config.js:4:25\\\\n    at evalModule (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:251913)\\\\n    at jiti (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:249841)\\\\n    at D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\tailwindcss\\\\\\\\lib\\\\\\\\lib\\\\\\\\load-config.js:48:30\\\\n    at loadConfig (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\tailwindcss\\\\\\\\lib\\\\\\\\lib\\\\\\\\load-config.js:50:6)\\\\n    at getTailwindConfig (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\tailwindcss\\\\\\\\lib\\\\\\\\lib\\\\\\\\setupTrackingContext.js:71:116)\\\\n    at D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\tailwindcss\\\\\\\\lib\\\\\\\\lib\\\\\\\\setupTrackingContext.js:100:92\\\\n    at D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\tailwindcss\\\\\\\\lib\\\\\\\\processTailwindFeatures.js:48:11\\\\n    at plugins (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\tailwindcss\\\\\\\\lib\\\\\\\\plugin.js:38:69)\\\\n    at LazyResult.runOnRoot (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\@angular-devkit\\\\\\\\build-angular\\\\\\\\node_modules\\\\\\\\postcss\\\\\\\\lib\\\\\\\\lazy-result.js:361:16)\\\\n    at LazyResult.runAsync (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\@angular-devkit\\\\\\\\build-angular\\\\\\\\node_modules\\\\\\\\postcss\\\\\\\\lib\\\\\\\\lazy-result.js:290:26)\\\\n    at LazyResult.async (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\@angular-devkit\\\\\\\\build-angular\\\\\\\\node_modules\\\\\\\\postcss\\\\\\\\lib\\\\\\\\lazy-result.js:192:30)\\\\n    at LazyResult.then (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\@angular-devkit\\\\\\\\build-angular\\\\\\\\node_modules\\\\\\\\postcss\\\\\\\\lib\\\\\\\\lazy-result.js:436:17)\\\");\\n\\n/***/ })\\n\\n/******/ \\t});\\n/************************************************************************/\\n/******/ \\t\\n/******/ \\t// startup\\n/******/ \\t// Load entry module and return exports\\n/******/ \\t// This entry module doesn't tell about it's top-level declarations so it can't be inlined\\n/******/ \\tvar __webpack_exports__ = {};\\n/******/ \\t__webpack_modules__[950]();\\n/******/ \\tresource = __webpack_exports__;\\n/******/ \\t\\n/******/ })()\\n;\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["animate", "AnimationBuilder", "style", "coerceBooleanProperty", "ElementRef", "EventEmitter", "inject", "Renderer2", "FuseDrawerService", "FuseUtilsService", "FuseDrawerComponent", "constructor", "_animationBuilder", "_elementRef", "_renderer2", "_fuseDrawerService", "_fuseUtilsService", "fixed", "mode", "name", "randomId", "opened", "position", "transparentOverlay", "fixedChanged", "modeChanged", "openedChanged", "positionChanged", "_animationsEnabled", "_handleOverlayClick", "close", "_hovered", "classList", "styleList", "visibility", "_onMouseenter", "_enableAnimations", "_onMouseleave", "ngOnChanges", "changes", "currentValue", "next", "previousMode", "previousValue", "currentMode", "_disableAnimations", "_hideOverlay", "_showOverlay", "setTimeout", "open", "_toggleOpened", "ngOnInit", "registerComponent", "ngOnDestroy", "_player", "finish", "deregisterComponent", "toggle", "_overlay", "createElement", "add", "append<PERSON><PERSON><PERSON>", "nativeElement", "parentElement", "build", "opacity", "create", "play", "addEventListener", "onDone", "removeEventListener", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "_", "_2", "selectors", "hostVars", "hostBindings", "FuseDrawerComponent_HostBindings", "rf", "ctx", "i0", "ɵɵlistener", "FuseDrawerComponent_mouseenter_HostBindingHandler", "FuseDrawerComponent_mouseleave_HostBindingHandler", "ɵɵstyleMap", "ɵɵclassMap", "ɵɵelementStart", "ɵɵprojection", "ɵɵelementEnd"], "sources": ["D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\@fuse\\components\\drawer\\drawer.component.ts", "D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\@fuse\\components\\drawer\\drawer.component.html"], "sourcesContent": ["import {\n    animate,\n    AnimationBuilder,\n    AnimationPlayer,\n    style,\n} from '@angular/animations';\nimport { BooleanInput, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport {\n    Component,\n    ElementRef,\n    EventEmitter,\n    HostBinding,\n    HostListener,\n    inject,\n    Input,\n    OnChanges,\n    OnDestroy,\n    OnInit,\n    Output,\n    Renderer2,\n    SimpleChanges,\n    ViewEncapsulation,\n} from '@angular/core';\nimport { FuseDrawerService } from '@fuse/components/drawer/drawer.service';\nimport {\n    FuseDrawerMode,\n    FuseDrawerPosition,\n} from '@fuse/components/drawer/drawer.types';\nimport { FuseUtilsService } from '@fuse/services/utils/utils.service';\n\n@Component({\n    selector: 'fuse-drawer',\n    templateUrl: './drawer.component.html',\n    styleUrls: ['./drawer.component.scss'],\n    encapsulation: ViewEncapsulation.None,\n    exportAs: 'fuseDrawer',\n    standalone: true,\n})\nexport class FuseDrawerComponent implements OnChanges, OnInit, OnD<PERSON>roy {\n    /* eslint-disable @typescript-eslint/naming-convention */\n    static ngAcceptInputType_fixed: BooleanInput;\n    static ngAcceptInputType_opened: BooleanInput;\n    static ngAcceptInputType_transparentOverlay: BooleanInput;\n    /* eslint-enable @typescript-eslint/naming-convention */\n\n    private _animationBuilder = inject(AnimationBuilder);\n    private _elementRef = inject(ElementRef);\n    private _renderer2 = inject(Renderer2);\n    private _fuseDrawerService = inject(FuseDrawerService);\n    private _fuseUtilsService = inject(FuseUtilsService);\n\n    @Input() fixed: boolean = false;\n    @Input() mode: FuseDrawerMode = 'side';\n    @Input() name: string = this._fuseUtilsService.randomId();\n    @Input() opened: boolean = false;\n    @Input() position: FuseDrawerPosition = 'left';\n    @Input() transparentOverlay: boolean = false;\n    @Output() readonly fixedChanged: EventEmitter<boolean> =\n        new EventEmitter<boolean>();\n    @Output() readonly modeChanged: EventEmitter<FuseDrawerMode> =\n        new EventEmitter<FuseDrawerMode>();\n    @Output() readonly openedChanged: EventEmitter<boolean> =\n        new EventEmitter<boolean>();\n    @Output() readonly positionChanged: EventEmitter<FuseDrawerPosition> =\n        new EventEmitter<FuseDrawerPosition>();\n\n    private _animationsEnabled: boolean = false;\n    private readonly _handleOverlayClick = (): void => this.close();\n    private _hovered: boolean = false;\n    private _overlay: HTMLElement;\n    private _player: AnimationPlayer;\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Accessors\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * Host binding for component classes\n     */\n    @HostBinding('class') get classList(): any {\n        /* eslint-disable @typescript-eslint/naming-convention */\n        return {\n            'fuse-drawer-animations-enabled': this._animationsEnabled,\n            'fuse-drawer-fixed': this.fixed,\n            'fuse-drawer-hover': this._hovered,\n            [`fuse-drawer-mode-${this.mode}`]: true,\n            'fuse-drawer-opened': this.opened,\n            [`fuse-drawer-position-${this.position}`]: true,\n        };\n        /* eslint-enable @typescript-eslint/naming-convention */\n    }\n\n    /**\n     * Host binding for component inline styles\n     */\n    @HostBinding('style') get styleList(): any {\n        return {\n            visibility: this.opened ? 'visible' : 'hidden',\n        };\n    }\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Decorated methods\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * On mouseenter\n     *\n     * @private\n     */\n    @HostListener('mouseenter')\n    _onMouseenter(): void {\n        // Enable the animations\n        this._enableAnimations();\n\n        // Set the hovered\n        this._hovered = true;\n    }\n\n    /**\n     * On mouseleave\n     *\n     * @private\n     */\n    @HostListener('mouseleave')\n    _onMouseleave(): void {\n        // Enable the animations\n        this._enableAnimations();\n\n        // Set the hovered\n        this._hovered = false;\n    }\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Lifecycle hooks\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * On changes\n     *\n     * @param changes\n     */\n    ngOnChanges(changes: SimpleChanges): void {\n        // Fixed\n        if ('fixed' in changes) {\n            // Coerce the value to a boolean\n            this.fixed = coerceBooleanProperty(changes.fixed.currentValue);\n\n            // Execute the observable\n            this.fixedChanged.next(this.fixed);\n        }\n\n        // Mode\n        if ('mode' in changes) {\n            // Get the previous and current values\n            const previousMode = changes.mode.previousValue;\n            const currentMode = changes.mode.currentValue;\n\n            // Disable the animations\n            this._disableAnimations();\n\n            // If the mode changes: 'over -> side'\n            if (previousMode === 'over' && currentMode === 'side') {\n                // Hide the overlay\n                this._hideOverlay();\n            }\n\n            // If the mode changes: 'side -> over'\n            if (previousMode === 'side' && currentMode === 'over') {\n                // If the drawer is opened\n                if (this.opened) {\n                    // Show the overlay\n                    this._showOverlay();\n                }\n            }\n\n            // Execute the observable\n            this.modeChanged.next(currentMode);\n\n            // Enable the animations after a delay\n            // The delay must be bigger than the current transition-duration\n            // to make sure nothing will be animated while the mode is changing\n            setTimeout(() => {\n                this._enableAnimations();\n            }, 500);\n        }\n\n        // Opened\n        if ('opened' in changes) {\n            // Coerce the value to a boolean\n            const open = coerceBooleanProperty(changes.opened.currentValue);\n\n            // Open/close the drawer\n            this._toggleOpened(open);\n        }\n\n        // Position\n        if ('position' in changes) {\n            // Execute the observable\n            this.positionChanged.next(this.position);\n        }\n\n        // Transparent overlay\n        if ('transparentOverlay' in changes) {\n            // Coerce the value to a boolean\n            this.transparentOverlay = coerceBooleanProperty(\n                changes.transparentOverlay.currentValue\n            );\n        }\n    }\n\n    /**\n     * On init\n     */\n    ngOnInit(): void {\n        // Register the drawer\n        this._fuseDrawerService.registerComponent(this.name, this);\n    }\n\n    /**\n     * On destroy\n     */\n    ngOnDestroy(): void {\n        // Finish the animation\n        if (this._player) {\n            this._player.finish();\n        }\n\n        // Deregister the drawer from the registry\n        this._fuseDrawerService.deregisterComponent(this.name);\n    }\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Public methods\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * Open the drawer\n     */\n    open(): void {\n        // Return if the drawer has already opened\n        if (this.opened) {\n            return;\n        }\n\n        // Open the drawer\n        this._toggleOpened(true);\n    }\n\n    /**\n     * Close the drawer\n     */\n    close(): void {\n        // Return if the drawer has already closed\n        if (!this.opened) {\n            return;\n        }\n\n        // Close the drawer\n        this._toggleOpened(false);\n    }\n\n    /**\n     * Toggle the drawer\n     */\n    toggle(): void {\n        if (this.opened) {\n            this.close();\n        } else {\n            this.open();\n        }\n    }\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Private methods\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * Enable the animations\n     *\n     * @private\n     */\n    private _enableAnimations(): void {\n        // Return if the animations are already enabled\n        if (this._animationsEnabled) {\n            return;\n        }\n\n        // Enable the animations\n        this._animationsEnabled = true;\n    }\n\n    /**\n     * Disable the animations\n     *\n     * @private\n     */\n    private _disableAnimations(): void {\n        // Return if the animations are already disabled\n        if (!this._animationsEnabled) {\n            return;\n        }\n\n        // Disable the animations\n        this._animationsEnabled = false;\n    }\n\n    /**\n     * Show the backdrop\n     *\n     * @private\n     */\n    private _showOverlay(): void {\n        // Create the backdrop element\n        this._overlay = this._renderer2.createElement('div');\n\n        // Add a class to the backdrop element\n        this._overlay.classList.add('fuse-drawer-overlay');\n\n        // Add a class depending on the fixed option\n        if (this.fixed) {\n            this._overlay.classList.add('fuse-drawer-overlay-fixed');\n        }\n\n        // Add a class depending on the transparentOverlay option\n        if (this.transparentOverlay) {\n            this._overlay.classList.add('fuse-drawer-overlay-transparent');\n        }\n\n        // Append the backdrop to the parent of the drawer\n        this._renderer2.appendChild(\n            this._elementRef.nativeElement.parentElement,\n            this._overlay\n        );\n\n        // Create enter animation and attach it to the player\n        this._player = this._animationBuilder\n            .build([\n                style({ opacity: 0 }),\n                animate(\n                    '300ms cubic-bezier(0.25, 0.8, 0.25, 1)',\n                    style({ opacity: 1 })\n                ),\n            ])\n            .create(this._overlay);\n\n        // Play the animation\n        this._player.play();\n\n        // Add an event listener to the overlay\n        this._overlay.addEventListener('click', this._handleOverlayClick);\n    }\n\n    /**\n     * Hide the backdrop\n     *\n     * @private\n     */\n    private _hideOverlay(): void {\n        if (!this._overlay) {\n            return;\n        }\n\n        // Create the leave animation and attach it to the player\n        this._player = this._animationBuilder\n            .build([\n                animate(\n                    '300ms cubic-bezier(0.25, 0.8, 0.25, 1)',\n                    style({ opacity: 0 })\n                ),\n            ])\n            .create(this._overlay);\n\n        // Play the animation\n        this._player.play();\n\n        // Once the animation is done...\n        this._player.onDone(() => {\n            // If the overlay still exists...\n            if (this._overlay) {\n                // Remove the event listener\n                this._overlay.removeEventListener(\n                    'click',\n                    this._handleOverlayClick\n                );\n\n                // Remove the overlay\n                this._overlay.parentNode.removeChild(this._overlay);\n                this._overlay = null;\n            }\n        });\n    }\n\n    /**\n     * Open/close the drawer\n     *\n     * @param open\n     * @private\n     */\n    private _toggleOpened(open: boolean): void {\n        // Set the opened\n        this.opened = open;\n\n        // Enable the animations\n        this._enableAnimations();\n\n        // If the mode is 'over'\n        if (this.mode === 'over') {\n            // If the drawer opens, show the overlay\n            if (open) {\n                this._showOverlay();\n            }\n            // Otherwise, close the overlay\n            else {\n                this._hideOverlay();\n            }\n        }\n\n        // Execute the observable\n        this.openedChanged.next(open);\n    }\n}\n", "<div class=\"fuse-drawer-content\">\n    <ng-content></ng-content>\n</div>\n"], "mappings": "AAAA,SACIA,OAAO,EACPC,gBAAgB,EAEhBC,KAAK,QACF,qBAAqB;AAC5B,SAAuBC,qBAAqB,QAAQ,uBAAuB;AAC3E,SAEIC,UAAU,EACVC,YAAY,EAGZC,MAAM,EAMNC,SAAS,QAGN,eAAe;AACtB,SAASC,iBAAiB,QAAQ,wCAAwC;AAK1E,SAASC,gBAAgB,QAAQ,oCAAoC;;;AAUrE,OAAM,MAAOC,mBAAmB;EARhCC,YAAA;IAaI;IAEQ,KAAAC,iBAAiB,GAAGN,MAAM,CAACL,gBAAgB,CAAC;IAC5C,KAAAY,WAAW,GAAGP,MAAM,CAACF,UAAU,CAAC;IAChC,KAAAU,UAAU,GAAGR,MAAM,CAACC,SAAS,CAAC;IAC9B,KAAAQ,kBAAkB,GAAGT,MAAM,CAACE,iBAAiB,CAAC;IAC9C,KAAAQ,iBAAiB,GAAGV,MAAM,CAACG,gBAAgB,CAAC;IAE3C,KAAAQ,KAAK,GAAY,KAAK;IACtB,KAAAC,IAAI,GAAmB,MAAM;IAC7B,KAAAC,IAAI,GAAW,IAAI,CAACH,iBAAiB,CAACI,QAAQ,EAAE;IAChD,KAAAC,MAAM,GAAY,KAAK;IACvB,KAAAC,QAAQ,GAAuB,MAAM;IACrC,KAAAC,kBAAkB,GAAY,KAAK;IACzB,KAAAC,YAAY,GAC3B,IAAInB,YAAY,EAAW;IACZ,KAAAoB,WAAW,GAC1B,IAAIpB,YAAY,EAAkB;IACnB,KAAAqB,aAAa,GAC5B,IAAIrB,YAAY,EAAW;IACZ,KAAAsB,eAAe,GAC9B,IAAItB,YAAY,EAAsB;IAElC,KAAAuB,kBAAkB,GAAY,KAAK;IAC1B,KAAAC,mBAAmB,GAAG,MAAY,IAAI,CAACC,KAAK,EAAE;IACvD,KAAAC,QAAQ,GAAY,KAAK;;EAIjC;EACA;EACA;EAEA;;;EAGA,IAA0BC,SAASA,CAAA;IAC/B;IACA,OAAO;MACH,gCAAgC,EAAE,IAAI,CAACJ,kBAAkB;MACzD,mBAAmB,EAAE,IAAI,CAACX,KAAK;MAC/B,mBAAmB,EAAE,IAAI,CAACc,QAAQ;MAClC,CAAC,oBAAoB,IAAI,CAACb,IAAI,EAAE,GAAG,IAAI;MACvC,oBAAoB,EAAE,IAAI,CAACG,MAAM;MACjC,CAAC,wBAAwB,IAAI,CAACC,QAAQ,EAAE,GAAG;KAC9C;IACD;EACJ;EAEA;;;EAGA,IAA0BW,SAASA,CAAA;IAC/B,OAAO;MACHC,UAAU,EAAE,IAAI,CAACb,MAAM,GAAG,SAAS,GAAG;KACzC;EACL;EAEA;EACA;EACA;EAEA;;;;;EAMAc,aAAaA,CAAA;IACT;IACA,IAAI,CAACC,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACL,QAAQ,GAAG,IAAI;EACxB;EAEA;;;;;EAMAM,aAAaA,CAAA;IACT;IACA,IAAI,CAACD,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACL,QAAQ,GAAG,KAAK;EACzB;EAEA;EACA;EACA;EAEA;;;;;EAKAO,WAAWA,CAACC,OAAsB;IAC9B;IACA,IAAI,OAAO,IAAIA,OAAO,EAAE;MACpB;MACA,IAAI,CAACtB,KAAK,GAAGd,qBAAqB,CAACoC,OAAO,CAACtB,KAAK,CAACuB,YAAY,CAAC;MAE9D;MACA,IAAI,CAAChB,YAAY,CAACiB,IAAI,CAAC,IAAI,CAACxB,KAAK,CAAC;IACtC;IAEA;IACA,IAAI,MAAM,IAAIsB,OAAO,EAAE;MACnB;MACA,MAAMG,YAAY,GAAGH,OAAO,CAACrB,IAAI,CAACyB,aAAa;MAC/C,MAAMC,WAAW,GAAGL,OAAO,CAACrB,IAAI,CAACsB,YAAY;MAE7C;MACA,IAAI,CAACK,kBAAkB,EAAE;MAEzB;MACA,IAAIH,YAAY,KAAK,MAAM,IAAIE,WAAW,KAAK,MAAM,EAAE;QACnD;QACA,IAAI,CAACE,YAAY,EAAE;MACvB;MAEA;MACA,IAAIJ,YAAY,KAAK,MAAM,IAAIE,WAAW,KAAK,MAAM,EAAE;QACnD;QACA,IAAI,IAAI,CAACvB,MAAM,EAAE;UACb;UACA,IAAI,CAAC0B,YAAY,EAAE;QACvB;MACJ;MAEA;MACA,IAAI,CAACtB,WAAW,CAACgB,IAAI,CAACG,WAAW,CAAC;MAElC;MACA;MACA;MACAI,UAAU,CAAC,MAAK;QACZ,IAAI,CAACZ,iBAAiB,EAAE;MAC5B,CAAC,EAAE,GAAG,CAAC;IACX;IAEA;IACA,IAAI,QAAQ,IAAIG,OAAO,EAAE;MACrB;MACA,MAAMU,IAAI,GAAG9C,qBAAqB,CAACoC,OAAO,CAAClB,MAAM,CAACmB,YAAY,CAAC;MAE/D;MACA,IAAI,CAACU,aAAa,CAACD,IAAI,CAAC;IAC5B;IAEA;IACA,IAAI,UAAU,IAAIV,OAAO,EAAE;MACvB;MACA,IAAI,CAACZ,eAAe,CAACc,IAAI,CAAC,IAAI,CAACnB,QAAQ,CAAC;IAC5C;IAEA;IACA,IAAI,oBAAoB,IAAIiB,OAAO,EAAE;MACjC;MACA,IAAI,CAAChB,kBAAkB,GAAGpB,qBAAqB,CAC3CoC,OAAO,CAAChB,kBAAkB,CAACiB,YAAY,CAC1C;IACL;EACJ;EAEA;;;EAGAW,QAAQA,CAAA;IACJ;IACA,IAAI,CAACpC,kBAAkB,CAACqC,iBAAiB,CAAC,IAAI,CAACjC,IAAI,EAAE,IAAI,CAAC;EAC9D;EAEA;;;EAGAkC,WAAWA,CAAA;IACP;IACA,IAAI,IAAI,CAACC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,MAAM,EAAE;IACzB;IAEA;IACA,IAAI,CAACxC,kBAAkB,CAACyC,mBAAmB,CAAC,IAAI,CAACrC,IAAI,CAAC;EAC1D;EAEA;EACA;EACA;EAEA;;;EAGA8B,IAAIA,CAAA;IACA;IACA,IAAI,IAAI,CAAC5B,MAAM,EAAE;MACb;IACJ;IAEA;IACA,IAAI,CAAC6B,aAAa,CAAC,IAAI,CAAC;EAC5B;EAEA;;;EAGApB,KAAKA,CAAA;IACD;IACA,IAAI,CAAC,IAAI,CAACT,MAAM,EAAE;MACd;IACJ;IAEA;IACA,IAAI,CAAC6B,aAAa,CAAC,KAAK,CAAC;EAC7B;EAEA;;;EAGAO,MAAMA,CAAA;IACF,IAAI,IAAI,CAACpC,MAAM,EAAE;MACb,IAAI,CAACS,KAAK,EAAE;IAChB,CAAC,MAAM;MACH,IAAI,CAACmB,IAAI,EAAE;IACf;EACJ;EAEA;EACA;EACA;EAEA;;;;;EAKQb,iBAAiBA,CAAA;IACrB;IACA,IAAI,IAAI,CAACR,kBAAkB,EAAE;MACzB;IACJ;IAEA;IACA,IAAI,CAACA,kBAAkB,GAAG,IAAI;EAClC;EAEA;;;;;EAKQiB,kBAAkBA,CAAA;IACtB;IACA,IAAI,CAAC,IAAI,CAACjB,kBAAkB,EAAE;MAC1B;IACJ;IAEA;IACA,IAAI,CAACA,kBAAkB,GAAG,KAAK;EACnC;EAEA;;;;;EAKQmB,YAAYA,CAAA;IAChB;IACA,IAAI,CAACW,QAAQ,GAAG,IAAI,CAAC5C,UAAU,CAAC6C,aAAa,CAAC,KAAK,CAAC;IAEpD;IACA,IAAI,CAACD,QAAQ,CAAC1B,SAAS,CAAC4B,GAAG,CAAC,qBAAqB,CAAC;IAElD;IACA,IAAI,IAAI,CAAC3C,KAAK,EAAE;MACZ,IAAI,CAACyC,QAAQ,CAAC1B,SAAS,CAAC4B,GAAG,CAAC,2BAA2B,CAAC;IAC5D;IAEA;IACA,IAAI,IAAI,CAACrC,kBAAkB,EAAE;MACzB,IAAI,CAACmC,QAAQ,CAAC1B,SAAS,CAAC4B,GAAG,CAAC,iCAAiC,CAAC;IAClE;IAEA;IACA,IAAI,CAAC9C,UAAU,CAAC+C,WAAW,CACvB,IAAI,CAAChD,WAAW,CAACiD,aAAa,CAACC,aAAa,EAC5C,IAAI,CAACL,QAAQ,CAChB;IAED;IACA,IAAI,CAACJ,OAAO,GAAG,IAAI,CAAC1C,iBAAiB,CAChCoD,KAAK,CAAC,CACH9D,KAAK,CAAC;MAAE+D,OAAO,EAAE;IAAC,CAAE,CAAC,EACrBjE,OAAO,CACH,wCAAwC,EACxCE,KAAK,CAAC;MAAE+D,OAAO,EAAE;IAAC,CAAE,CAAC,CACxB,CACJ,CAAC,CACDC,MAAM,CAAC,IAAI,CAACR,QAAQ,CAAC;IAE1B;IACA,IAAI,CAACJ,OAAO,CAACa,IAAI,EAAE;IAEnB;IACA,IAAI,CAACT,QAAQ,CAACU,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACvC,mBAAmB,CAAC;EACrE;EAEA;;;;;EAKQiB,YAAYA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACY,QAAQ,EAAE;MAChB;IACJ;IAEA;IACA,IAAI,CAACJ,OAAO,GAAG,IAAI,CAAC1C,iBAAiB,CAChCoD,KAAK,CAAC,CACHhE,OAAO,CACH,wCAAwC,EACxCE,KAAK,CAAC;MAAE+D,OAAO,EAAE;IAAC,CAAE,CAAC,CACxB,CACJ,CAAC,CACDC,MAAM,CAAC,IAAI,CAACR,QAAQ,CAAC;IAE1B;IACA,IAAI,CAACJ,OAAO,CAACa,IAAI,EAAE;IAEnB;IACA,IAAI,CAACb,OAAO,CAACe,MAAM,CAAC,MAAK;MACrB;MACA,IAAI,IAAI,CAACX,QAAQ,EAAE;QACf;QACA,IAAI,CAACA,QAAQ,CAACY,mBAAmB,CAC7B,OAAO,EACP,IAAI,CAACzC,mBAAmB,CAC3B;QAED;QACA,IAAI,CAAC6B,QAAQ,CAACa,UAAU,CAACC,WAAW,CAAC,IAAI,CAACd,QAAQ,CAAC;QACnD,IAAI,CAACA,QAAQ,GAAG,IAAI;MACxB;IACJ,CAAC,CAAC;EACN;EAEA;;;;;;EAMQR,aAAaA,CAACD,IAAa;IAC/B;IACA,IAAI,CAAC5B,MAAM,GAAG4B,IAAI;IAElB;IACA,IAAI,CAACb,iBAAiB,EAAE;IAExB;IACA,IAAI,IAAI,CAAClB,IAAI,KAAK,MAAM,EAAE;MACtB;MACA,IAAI+B,IAAI,EAAE;QACN,IAAI,CAACF,YAAY,EAAE;MACvB;MACA;MAAA,KACK;QACD,IAAI,CAACD,YAAY,EAAE;MACvB;IACJ;IAEA;IACA,IAAI,CAACpB,aAAa,CAACe,IAAI,CAACQ,IAAI,CAAC;EACjC;EAAC,QAAAwB,CAAA,G;qCA9XQ/D,mBAAmB;EAAA;EAAA,QAAAgE,EAAA,G;UAAnBhE,mBAAmB;IAAAiE,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAnBE,EAAA,CAAAC,UAAA,wBAAAC,kDAAA;UAAA,OAAAH,GAAA,CAAA7C,aAAA,EAAe;QAAA,EAAI,wBAAAiD,kDAAA;UAAA,OAAnBJ,GAAA,CAAA3C,aAAA,EAAe;QAAA,EAAI;;;QAAnB4C,EAAA,CAAAI,UAAA,CAAAL,GAAA,CAAA/C,SAAA,CAAmB;QAAnBgD,EAAA,CAAAK,UAAA,CAAAN,GAAA,CAAAhD,SAAA,CAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;QCtChCiD,EAAA,CAAAM,cAAA,aAAiC;QAC7BN,EAAA,CAAAO,YAAA,GAAyB;QAC7BP,EAAA,CAAAQ,YAAA,EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}