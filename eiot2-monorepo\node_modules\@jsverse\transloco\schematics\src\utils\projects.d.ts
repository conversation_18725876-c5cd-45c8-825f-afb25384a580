import { WorkspaceSchema } from '@schematics/angular/utility/workspace-models';
import { Tree } from '@angular-devkit/schematics';
export declare function getWorkspacePath(host: Tree): string;
export declare function getWorkspace(host: Tree): WorkspaceSchema;
export declare function setWorkspace(host: Tree, workspace: any): void;
export declare function getProject(host: Tree, project?: string): import("@schematics/angular/utility/workspace-models").WorkspaceProject<import("@schematics/angular/utility/workspace-models").ProjectType>;
export declare function setEnvironments(host: Tree, sourceRoot: string, transformer: (env: string) => string): void;
export declare function getProjectPath(host: Tree, project: any, options: any): any;
