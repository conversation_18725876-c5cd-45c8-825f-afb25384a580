{"ast": null, "code": "import { DOCUMENT } from '@angular/common';\nimport { NavigationEnd } from '@angular/router';\nimport { FUSE_VERSION } from '@fuse/version';\nimport { Subject, combineLatest, filter, map, takeUntil } from 'rxjs';\nimport { SettingsComponent } from './common/settings/settings.component';\nimport { EmptyLayoutComponent } from './layouts/empty/empty.component';\nimport { CenteredLayoutComponent } from './layouts/horizontal/centered/centered.component';\nimport { EnterpriseLayoutComponent } from './layouts/horizontal/enterprise/enterprise.component';\nimport { MaterialLayoutComponent } from './layouts/horizontal/material/material.component';\nimport { ModernLayoutComponent } from './layouts/horizontal/modern/modern.component';\nimport { ClassicLayoutComponent } from './layouts/vertical/classic/classic.component';\nimport { ClassyLayoutComponent } from './layouts/vertical/classy/classy.component';\nimport { CompactLayoutComponent } from './layouts/vertical/compact/compact.component';\nimport { DenseLayoutComponent } from './layouts/vertical/dense/dense.component';\nimport { FuturisticLayoutComponent } from './layouts/vertical/futuristic/futuristic.component';\nimport { ThinLayoutComponent } from './layouts/vertical/thin/thin.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@fuse/services/config\";\nimport * as i3 from \"@fuse/services/media-watcher\";\nimport * as i4 from \"@fuse/services/platform\";\nfunction LayoutComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"empty-layout\");\n  }\n}\nfunction LayoutComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"centered-layout\");\n  }\n}\nfunction LayoutComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"enterprise-layout\");\n  }\n}\nfunction LayoutComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"material-layout\");\n  }\n}\nfunction LayoutComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"modern-layout\");\n  }\n}\nfunction LayoutComponent_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"classic-layout\");\n  }\n}\nfunction LayoutComponent_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"classy-layout\");\n  }\n}\nfunction LayoutComponent_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"compact-layout\");\n  }\n}\nfunction LayoutComponent_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"dense-layout\");\n  }\n}\nfunction LayoutComponent_Conditional_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"futuristic-layout\");\n  }\n}\nfunction LayoutComponent_Conditional_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"thin-layout\");\n  }\n}\nexport class LayoutComponent {\n  /**\n   * Constructor\n   */\n  constructor(_activatedRoute, _document, _renderer2, _router, _fuseConfigService, _fuseMediaWatcherService, _fusePlatformService) {\n    this._activatedRoute = _activatedRoute;\n    this._document = _document;\n    this._renderer2 = _renderer2;\n    this._router = _router;\n    this._fuseConfigService = _fuseConfigService;\n    this._fuseMediaWatcherService = _fuseMediaWatcherService;\n    this._fusePlatformService = _fusePlatformService;\n    this._unsubscribeAll = new Subject();\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Lifecycle hooks\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * On init\n   */\n  ngOnInit() {\n    // Set the theme and scheme based on the configuration\n    combineLatest([this._fuseConfigService.config$, this._fuseMediaWatcherService.onMediaQueryChange$(['(prefers-color-scheme: dark)', '(prefers-color-scheme: light)'])]).pipe(takeUntil(this._unsubscribeAll), map(([config, mql]) => {\n      const options = {\n        scheme: config.scheme,\n        theme: config.theme\n      };\n      // If the scheme is set to 'auto'...\n      if (config.scheme === 'auto') {\n        // Decide the scheme using the media query\n        options.scheme = mql.breakpoints['(prefers-color-scheme: dark)'] ? 'dark' : 'light';\n      }\n      return options;\n    })).subscribe(options => {\n      // Store the options\n      this.scheme = options.scheme;\n      this.theme = options.theme;\n      // Update the scheme and theme\n      this._updateScheme();\n      this._updateTheme();\n    });\n    // Subscribe to config changes\n    this._fuseConfigService.config$.pipe(takeUntil(this._unsubscribeAll)).subscribe(config => {\n      // Store the config\n      this.config = config;\n      // Update the layout\n      this._updateLayout();\n    });\n    // Subscribe to NavigationEnd event\n    this._router.events.pipe(filter(event => event instanceof NavigationEnd), takeUntil(this._unsubscribeAll)).subscribe(() => {\n      // Update the layout\n      this._updateLayout();\n    });\n    // Set the app version\n    this._renderer2.setAttribute(this._document.querySelector('[ng-version]'), 'fuse-version', FUSE_VERSION);\n    // Set the OS name\n    this._renderer2.addClass(this._document.body, this._fusePlatformService.osName);\n  }\n  /**\n   * On destroy\n   */\n  ngOnDestroy() {\n    // Unsubscribe from all subscriptions\n    this._unsubscribeAll.next(null);\n    this._unsubscribeAll.complete();\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Private methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * Update the selected layout\n   */\n  _updateLayout() {\n    // Get the current activated route\n    let route = this._activatedRoute;\n    while (route.firstChild) {\n      route = route.firstChild;\n    }\n    // 1. Set the layout from the config\n    this.layout = this.config.layout;\n    // 2. Get the query parameter from the current route and\n    // set the layout and save the layout to the config\n    const layoutFromQueryParam = route.snapshot.queryParamMap.get('layout');\n    if (layoutFromQueryParam) {\n      this.layout = layoutFromQueryParam;\n      if (this.config) {\n        this.config.layout = layoutFromQueryParam;\n      }\n    }\n    // 3. Iterate through the paths and change the layout as we find\n    // a config for it.\n    //\n    // The reason we do this is that there might be empty grouping\n    // paths or componentless routes along the path. Because of that,\n    // we cannot just assume that the layout configuration will be\n    // in the last path's config or in the first path's config.\n    //\n    // So, we get all the paths that matched starting from root all\n    // the way to the current activated route, walk through them one\n    // by one and change the layout as we find the layout config. This\n    // way, layout configuration can live anywhere within the path and\n    // we won't miss it.\n    //\n    // Also, this will allow overriding the layout in any time so we\n    // can have different layouts for different routes.\n    const paths = route.pathFromRoot;\n    paths.forEach(path => {\n      // Check if there is a 'layout' data\n      if (path.routeConfig && path.routeConfig.data && path.routeConfig.data.layout) {\n        // Set the layout\n        this.layout = path.routeConfig.data.layout;\n      }\n    });\n  }\n  /**\n   * Update the selected scheme\n   *\n   * @private\n   */\n  _updateScheme() {\n    // Remove class names for all schemes\n    this._document.body.classList.remove('light', 'dark');\n    // Add class name for the currently selected scheme\n    this._document.body.classList.add(this.scheme);\n  }\n  /**\n   * Update the selected theme\n   *\n   * @private\n   */\n  _updateTheme() {\n    // Find the class name for the previously selected theme and remove it\n    this._document.body.classList.forEach(className => {\n      if (className.startsWith('theme-')) {\n        this._document.body.classList.remove(className, className.split('-')[1]);\n      }\n    });\n    // Add class name for the currently selected theme\n    this._document.body.classList.add(this.theme);\n  }\n  static #_ = this.ɵfac = function LayoutComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || LayoutComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.FuseConfigService), i0.ɵɵdirectiveInject(i3.FuseMediaWatcherService), i0.ɵɵdirectiveInject(i4.FusePlatformService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LayoutComponent,\n    selectors: [[\"layout\"]],\n    decls: 12,\n    vars: 11,\n    template: function LayoutComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵconditionalCreate(0, LayoutComponent_Conditional_0_Template, 1, 0, \"empty-layout\");\n        i0.ɵɵconditionalCreate(1, LayoutComponent_Conditional_1_Template, 1, 0, \"centered-layout\");\n        i0.ɵɵconditionalCreate(2, LayoutComponent_Conditional_2_Template, 1, 0, \"enterprise-layout\");\n        i0.ɵɵconditionalCreate(3, LayoutComponent_Conditional_3_Template, 1, 0, \"material-layout\");\n        i0.ɵɵconditionalCreate(4, LayoutComponent_Conditional_4_Template, 1, 0, \"modern-layout\");\n        i0.ɵɵconditionalCreate(5, LayoutComponent_Conditional_5_Template, 1, 0, \"classic-layout\");\n        i0.ɵɵconditionalCreate(6, LayoutComponent_Conditional_6_Template, 1, 0, \"classy-layout\");\n        i0.ɵɵconditionalCreate(7, LayoutComponent_Conditional_7_Template, 1, 0, \"compact-layout\");\n        i0.ɵɵconditionalCreate(8, LayoutComponent_Conditional_8_Template, 1, 0, \"dense-layout\");\n        i0.ɵɵconditionalCreate(9, LayoutComponent_Conditional_9_Template, 1, 0, \"futuristic-layout\");\n        i0.ɵɵconditionalCreate(10, LayoutComponent_Conditional_10_Template, 1, 0, \"thin-layout\");\n        i0.ɵɵelement(11, \"settings\");\n      }\n      if (rf & 2) {\n        i0.ɵɵconditional(ctx.layout === \"empty\" ? 0 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.layout === \"centered\" ? 1 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.layout === \"enterprise\" ? 2 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.layout === \"material\" ? 3 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.layout === \"modern\" ? 4 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.layout === \"classic\" ? 5 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.layout === \"classy\" ? 6 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.layout === \"compact\" ? 7 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.layout === \"dense\" ? 8 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.layout === \"futuristic\" ? 9 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.layout === \"thin\" ? 10 : -1);\n      }\n    },\n    dependencies: [EmptyLayoutComponent, CenteredLayoutComponent, EnterpriseLayoutComponent, MaterialLayoutComponent, ModernLayoutComponent, ClassicLayoutComponent, ClassyLayoutComponent, CompactLayoutComponent, DenseLayoutComponent, FuturisticLayoutComponent, ThinLayoutComponent, SettingsComponent],\n    styles: [\"var resource;\\n/******/ (() => { // webpackBootstrap\\n/******/ \\tvar __webpack_modules__ = ({\\n\\n/***/ 253:\\n/*!*************************************************************************!*\\\\\\n  !*** ./apps/eiot-admin/src/app/layout/layout.component.scss?ngResource ***!\\n  \\\\*************************************************************************/\\n/***/ (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/postcss-loader/dist/cjs.js):\\\\nError: Cannot find module 'chroma-js'\\\\nRequire stack:\\\\n- D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\apps\\\\\\\\eiot-admin\\\\\\\\src\\\\\\\\@fuse\\\\\\\\tailwind\\\\\\\\utils\\\\\\\\generate-palette.js\\\\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\\\\n    at Function.resolve (node:internal/modules/helpers:193:19)\\\\n    at _resolve (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:246378)\\\\n    at jiti (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:249092)\\\\n    at D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\apps\\\\\\\\eiot-admin\\\\\\\\src\\\\\\\\@fuse\\\\\\\\tailwind\\\\\\\\utils\\\\\\\\generate-palette.js:1:91\\\\n    at evalModule (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:251913)\\\\n    at jiti (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:249841)\\\\n    at D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\apps\\\\\\\\eiot-admin\\\\\\\\tailwind.config.js:4:25\\\\n    at evalModule (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:251913)\\\\n    at jiti (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:249841)\\\");\\n\\n/***/ })\\n\\n/******/ \\t});\\n/************************************************************************/\\n/******/ \\t\\n/******/ \\t// startup\\n/******/ \\t// Load entry module and return exports\\n/******/ \\t// This entry module doesn't tell about it's top-level declarations so it can't be inlined\\n/******/ \\tvar __webpack_exports__ = {};\\n/******/ \\t__webpack_modules__[253]();\\n/******/ \\tresource = __webpack_exports__;\\n/******/ \\t\\n/******/ })()\\n;\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["DOCUMENT", "NavigationEnd", "FUSE_VERSION", "Subject", "combineLatest", "filter", "map", "takeUntil", "SettingsComponent", "EmptyLayoutComponent", "CenteredLayoutComponent", "EnterpriseLayoutComponent", "MaterialLayoutComponent", "ModernLayoutComponent", "ClassicLayoutComponent", "ClassyLayoutComponent", "CompactLayoutComponent", "DenseLayoutComponent", "FuturisticLayoutComponent", "ThinLayoutComponent", "i0", "ɵɵelement", "LayoutComponent", "constructor", "_activatedRoute", "_document", "_renderer2", "_router", "_fuseConfigService", "_fuseMediaWatcherService", "_fusePlatformService", "_unsubscribeAll", "ngOnInit", "config$", "onMediaQueryChange$", "pipe", "config", "mql", "options", "scheme", "theme", "breakpoints", "subscribe", "_updateScheme", "_updateTheme", "_updateLayout", "events", "event", "setAttribute", "querySelector", "addClass", "body", "osName", "ngOnDestroy", "next", "complete", "route", "<PERSON><PERSON><PERSON><PERSON>", "layout", "layoutFromQueryParam", "snapshot", "queryParamMap", "get", "paths", "pathFromRoot", "for<PERSON>ach", "path", "routeConfig", "data", "classList", "remove", "add", "className", "startsWith", "split", "_", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Renderer2", "Router", "i2", "FuseConfigService", "i3", "FuseMediaWatcherService", "i4", "FusePlatformService", "_2", "selectors", "decls", "vars", "template", "LayoutComponent_Template", "rf", "ctx", "ɵɵconditionalCreate", "LayoutComponent_Conditional_0_Template", "LayoutComponent_Conditional_1_Template", "LayoutComponent_Conditional_2_Template", "LayoutComponent_Conditional_3_Template", "LayoutComponent_Conditional_4_Template", "LayoutComponent_Conditional_5_Template", "LayoutComponent_Conditional_6_Template", "LayoutComponent_Conditional_7_Template", "LayoutComponent_Conditional_8_Template", "LayoutComponent_Conditional_9_Template", "LayoutComponent_Conditional_10_Template", "ɵɵconditional", "ɵɵadvance", "styles", "encapsulation"], "sources": ["D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\app\\layout\\layout.component.ts", "D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\app\\layout\\layout.component.html"], "sourcesContent": ["import { DOCUMENT } from '@angular/common';\nimport {\n    Component,\n    Inject,\n    <PERSON><PERSON><PERSON>roy,\n    OnInit,\n    Renderer2,\n    ViewEncapsulation,\n} from '@angular/core';\nimport { ActivatedRoute, NavigationEnd, Router } from '@angular/router';\nimport { FuseConfig, FuseConfigService } from '@fuse/services/config';\nimport { FuseMediaWatcherService } from '@fuse/services/media-watcher';\nimport { FusePlatformService } from '@fuse/services/platform';\nimport { FUSE_VERSION } from '@fuse/version';\nimport { Subject, combineLatest, filter, map, takeUntil } from 'rxjs';\nimport { SettingsComponent } from './common/settings/settings.component';\nimport { EmptyLayoutComponent } from './layouts/empty/empty.component';\nimport { CenteredLayoutComponent } from './layouts/horizontal/centered/centered.component';\nimport { EnterpriseLayoutComponent } from './layouts/horizontal/enterprise/enterprise.component';\nimport { MaterialLayoutComponent } from './layouts/horizontal/material/material.component';\nimport { ModernLayoutComponent } from './layouts/horizontal/modern/modern.component';\nimport { ClassicLayoutComponent } from './layouts/vertical/classic/classic.component';\nimport { ClassyLayoutComponent } from './layouts/vertical/classy/classy.component';\nimport { CompactLayoutComponent } from './layouts/vertical/compact/compact.component';\nimport { DenseLayoutComponent } from './layouts/vertical/dense/dense.component';\nimport { FuturisticLayoutComponent } from './layouts/vertical/futuristic/futuristic.component';\nimport { ThinLayoutComponent } from './layouts/vertical/thin/thin.component';\n\n@Component({\n    selector: 'layout',\n    templateUrl: './layout.component.html',\n    styleUrls: ['./layout.component.scss'],\n    encapsulation: ViewEncapsulation.None,\n    imports: [\n        EmptyLayoutComponent,\n        CenteredLayoutComponent,\n        EnterpriseLayoutComponent,\n        MaterialLayoutComponent,\n        ModernLayoutComponent,\n        ClassicLayoutComponent,\n        ClassyLayoutComponent,\n        CompactLayoutComponent,\n        DenseLayoutComponent,\n        FuturisticLayoutComponent,\n        ThinLayoutComponent,\n        SettingsComponent,\n    ],\n})\nexport class LayoutComponent implements OnInit, OnDestroy {\n    config: FuseConfig;\n    layout: string;\n    scheme: 'dark' | 'light';\n    theme: string;\n    private _unsubscribeAll: Subject<any> = new Subject<any>();\n\n    /**\n     * Constructor\n     */\n    constructor(\n        private _activatedRoute: ActivatedRoute,\n        @Inject(DOCUMENT) private _document: any,\n        private _renderer2: Renderer2,\n        private _router: Router,\n        private _fuseConfigService: FuseConfigService,\n        private _fuseMediaWatcherService: FuseMediaWatcherService,\n        private _fusePlatformService: FusePlatformService\n    ) {}\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Lifecycle hooks\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * On init\n     */\n    ngOnInit(): void {\n        // Set the theme and scheme based on the configuration\n        combineLatest([\n            this._fuseConfigService.config$,\n            this._fuseMediaWatcherService.onMediaQueryChange$([\n                '(prefers-color-scheme: dark)',\n                '(prefers-color-scheme: light)',\n            ]),\n        ])\n            .pipe(\n                takeUntil(this._unsubscribeAll),\n                map(([config, mql]) => {\n                    const options = {\n                        scheme: config.scheme,\n                        theme: config.theme,\n                    };\n\n                    // If the scheme is set to 'auto'...\n                    if (config.scheme === 'auto') {\n                        // Decide the scheme using the media query\n                        options.scheme = mql.breakpoints[\n                            '(prefers-color-scheme: dark)'\n                        ]\n                            ? 'dark'\n                            : 'light';\n                    }\n\n                    return options;\n                })\n            )\n            .subscribe((options) => {\n                // Store the options\n                this.scheme = options.scheme;\n                this.theme = options.theme;\n\n                // Update the scheme and theme\n                this._updateScheme();\n                this._updateTheme();\n            });\n\n        // Subscribe to config changes\n        this._fuseConfigService.config$\n            .pipe(takeUntil(this._unsubscribeAll))\n            .subscribe((config: FuseConfig) => {\n                // Store the config\n                this.config = config;\n\n                // Update the layout\n                this._updateLayout();\n            });\n\n        // Subscribe to NavigationEnd event\n        this._router.events\n            .pipe(\n                filter((event) => event instanceof NavigationEnd),\n                takeUntil(this._unsubscribeAll)\n            )\n            .subscribe(() => {\n                // Update the layout\n                this._updateLayout();\n            });\n\n        // Set the app version\n        this._renderer2.setAttribute(\n            this._document.querySelector('[ng-version]'),\n            'fuse-version',\n            FUSE_VERSION\n        );\n\n        // Set the OS name\n        this._renderer2.addClass(\n            this._document.body,\n            this._fusePlatformService.osName\n        );\n    }\n\n    /**\n     * On destroy\n     */\n    ngOnDestroy(): void {\n        // Unsubscribe from all subscriptions\n        this._unsubscribeAll.next(null);\n        this._unsubscribeAll.complete();\n    }\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Private methods\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * Update the selected layout\n     */\n    private _updateLayout(): void {\n        // Get the current activated route\n        let route = this._activatedRoute;\n        while (route.firstChild) {\n            route = route.firstChild;\n        }\n\n        // 1. Set the layout from the config\n        this.layout = this.config.layout;\n\n        // 2. Get the query parameter from the current route and\n        // set the layout and save the layout to the config\n        const layoutFromQueryParam = route.snapshot.queryParamMap.get('layout');\n        if (layoutFromQueryParam) {\n            this.layout = layoutFromQueryParam;\n            if (this.config) {\n                this.config.layout = layoutFromQueryParam;\n            }\n        }\n\n        // 3. Iterate through the paths and change the layout as we find\n        // a config for it.\n        //\n        // The reason we do this is that there might be empty grouping\n        // paths or componentless routes along the path. Because of that,\n        // we cannot just assume that the layout configuration will be\n        // in the last path's config or in the first path's config.\n        //\n        // So, we get all the paths that matched starting from root all\n        // the way to the current activated route, walk through them one\n        // by one and change the layout as we find the layout config. This\n        // way, layout configuration can live anywhere within the path and\n        // we won't miss it.\n        //\n        // Also, this will allow overriding the layout in any time so we\n        // can have different layouts for different routes.\n        const paths = route.pathFromRoot;\n        paths.forEach((path) => {\n            // Check if there is a 'layout' data\n            if (\n                path.routeConfig &&\n                path.routeConfig.data &&\n                path.routeConfig.data.layout\n            ) {\n                // Set the layout\n                this.layout = path.routeConfig.data.layout;\n            }\n        });\n    }\n\n    /**\n     * Update the selected scheme\n     *\n     * @private\n     */\n    private _updateScheme(): void {\n        // Remove class names for all schemes\n        this._document.body.classList.remove('light', 'dark');\n\n        // Add class name for the currently selected scheme\n        this._document.body.classList.add(this.scheme);\n    }\n\n    /**\n     * Update the selected theme\n     *\n     * @private\n     */\n    private _updateTheme(): void {\n        // Find the class name for the previously selected theme and remove it\n        this._document.body.classList.forEach((className: string) => {\n            if (className.startsWith('theme-')) {\n                this._document.body.classList.remove(\n                    className,\n                    className.split('-')[1]\n                );\n            }\n        });\n\n        // Add class name for the currently selected theme\n        this._document.body.classList.add(this.theme);\n    }\n}\n", "<!-- ----------------------------------------------------------------------------------------------------- -->\n<!-- Empty layout -->\n<!-- ----------------------------------------------------------------------------------------------------- -->\n@if (layout === 'empty') {\n    <empty-layout></empty-layout>\n}\n\n<!-- ----------------------------------------------------------------------------------------------------- -->\n<!-- Layouts with horizontal navigation -->\n<!-- ----------------------------------------------------------------------------------------------------- -->\n\n<!-- Centered -->\n@if (layout === 'centered') {\n    <centered-layout></centered-layout>\n}\n\n<!-- Enterprise -->\n@if (layout === 'enterprise') {\n    <enterprise-layout></enterprise-layout>\n}\n\n<!-- Material -->\n@if (layout === 'material') {\n    <material-layout></material-layout>\n}\n\n<!-- Modern -->\n@if (layout === 'modern') {\n    <modern-layout></modern-layout>\n}\n\n<!-- ----------------------------------------------------------------------------------------------------- -->\n<!-- Layouts with vertical navigation -->\n<!-- ----------------------------------------------------------------------------------------------------- -->\n\n<!-- Classic -->\n@if (layout === 'classic') {\n    <classic-layout></classic-layout>\n}\n\n<!-- Classy -->\n@if (layout === 'classy') {\n    <classy-layout></classy-layout>\n}\n\n<!-- Compact -->\n@if (layout === 'compact') {\n    <compact-layout></compact-layout>\n}\n\n<!-- Dense -->\n@if (layout === 'dense') {\n    <dense-layout></dense-layout>\n}\n\n<!-- Futuristic -->\n@if (layout === 'futuristic') {\n    <futuristic-layout></futuristic-layout>\n}\n\n<!-- Thin -->\n@if (layout === 'thin') {\n    <thin-layout></thin-layout>\n}\n\n<!-- ----------------------------------------------------------------------------------------------------- -->\n<!-- Settings drawer - Remove this to remove the drawer and its button -->\n<!-- ----------------------------------------------------------------------------------------------------- -->\n<settings></settings>\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAS1C,SAAyBC,aAAa,QAAgB,iBAAiB;AAIvE,SAASC,YAAY,QAAQ,eAAe;AAC5C,SAASC,OAAO,EAAEC,aAAa,EAAEC,MAAM,EAAEC,GAAG,EAAEC,SAAS,QAAQ,MAAM;AACrE,SAASC,iBAAiB,QAAQ,sCAAsC;AACxE,SAASC,oBAAoB,QAAQ,iCAAiC;AACtE,SAASC,uBAAuB,QAAQ,kDAAkD;AAC1F,SAASC,yBAAyB,QAAQ,sDAAsD;AAChG,SAASC,uBAAuB,QAAQ,kDAAkD;AAC1F,SAASC,qBAAqB,QAAQ,8CAA8C;AACpF,SAASC,sBAAsB,QAAQ,8CAA8C;AACrF,SAASC,qBAAqB,QAAQ,4CAA4C;AAClF,SAASC,sBAAsB,QAAQ,8CAA8C;AACrF,SAASC,oBAAoB,QAAQ,0CAA0C;AAC/E,SAASC,yBAAyB,QAAQ,oDAAoD;AAC9F,SAASC,mBAAmB,QAAQ,wCAAwC;;;;;;;;ICtBxEC,EAAA,CAAAC,SAAA,mBAA6B;;;;;IAS7BD,EAAA,CAAAC,SAAA,sBAAmC;;;;;IAKnCD,EAAA,CAAAC,SAAA,wBAAuC;;;;;IAKvCD,EAAA,CAAAC,SAAA,sBAAmC;;;;;IAKnCD,EAAA,CAAAC,SAAA,oBAA+B;;;;;IAS/BD,EAAA,CAAAC,SAAA,qBAAiC;;;;;IAKjCD,EAAA,CAAAC,SAAA,oBAA+B;;;;;IAK/BD,EAAA,CAAAC,SAAA,qBAAiC;;;;;IAKjCD,EAAA,CAAAC,SAAA,mBAA6B;;;;;IAK7BD,EAAA,CAAAC,SAAA,wBAAuC;;;;;IAKvCD,EAAA,CAAAC,SAAA,kBAA2B;;;ADd/B,OAAM,MAAOC,eAAe;EAOxB;;;EAGAC,YACYC,eAA+B,EACbC,SAAc,EAChCC,UAAqB,EACrBC,OAAe,EACfC,kBAAqC,EACrCC,wBAAiD,EACjDC,oBAAyC;IANzC,KAAAN,eAAe,GAAfA,eAAe;IACG,KAAAC,SAAS,GAATA,SAAS;IAC3B,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,oBAAoB,GAApBA,oBAAoB;IAZxB,KAAAC,eAAe,GAAiB,IAAI5B,OAAO,EAAO;EAavD;EAEH;EACA;EACA;EAEA;;;EAGA6B,QAAQA,CAAA;IACJ;IACA5B,aAAa,CAAC,CACV,IAAI,CAACwB,kBAAkB,CAACK,OAAO,EAC/B,IAAI,CAACJ,wBAAwB,CAACK,mBAAmB,CAAC,CAC9C,8BAA8B,EAC9B,+BAA+B,CAClC,CAAC,CACL,CAAC,CACGC,IAAI,CACD5B,SAAS,CAAC,IAAI,CAACwB,eAAe,CAAC,EAC/BzB,GAAG,CAAC,CAAC,CAAC8B,MAAM,EAAEC,GAAG,CAAC,KAAI;MAClB,MAAMC,OAAO,GAAG;QACZC,MAAM,EAAEH,MAAM,CAACG,MAAM;QACrBC,KAAK,EAAEJ,MAAM,CAACI;OACjB;MAED;MACA,IAAIJ,MAAM,CAACG,MAAM,KAAK,MAAM,EAAE;QAC1B;QACAD,OAAO,CAACC,MAAM,GAAGF,GAAG,CAACI,WAAW,CAC5B,8BAA8B,CACjC,GACK,MAAM,GACN,OAAO;MACjB;MAEA,OAAOH,OAAO;IAClB,CAAC,CAAC,CACL,CACAI,SAAS,CAAEJ,OAAO,IAAI;MACnB;MACA,IAAI,CAACC,MAAM,GAAGD,OAAO,CAACC,MAAM;MAC5B,IAAI,CAACC,KAAK,GAAGF,OAAO,CAACE,KAAK;MAE1B;MACA,IAAI,CAACG,aAAa,EAAE;MACpB,IAAI,CAACC,YAAY,EAAE;IACvB,CAAC,CAAC;IAEN;IACA,IAAI,CAAChB,kBAAkB,CAACK,OAAO,CAC1BE,IAAI,CAAC5B,SAAS,CAAC,IAAI,CAACwB,eAAe,CAAC,CAAC,CACrCW,SAAS,CAAEN,MAAkB,IAAI;MAC9B;MACA,IAAI,CAACA,MAAM,GAAGA,MAAM;MAEpB;MACA,IAAI,CAACS,aAAa,EAAE;IACxB,CAAC,CAAC;IAEN;IACA,IAAI,CAAClB,OAAO,CAACmB,MAAM,CACdX,IAAI,CACD9B,MAAM,CAAE0C,KAAK,IAAKA,KAAK,YAAY9C,aAAa,CAAC,EACjDM,SAAS,CAAC,IAAI,CAACwB,eAAe,CAAC,CAClC,CACAW,SAAS,CAAC,MAAK;MACZ;MACA,IAAI,CAACG,aAAa,EAAE;IACxB,CAAC,CAAC;IAEN;IACA,IAAI,CAACnB,UAAU,CAACsB,YAAY,CACxB,IAAI,CAACvB,SAAS,CAACwB,aAAa,CAAC,cAAc,CAAC,EAC5C,cAAc,EACd/C,YAAY,CACf;IAED;IACA,IAAI,CAACwB,UAAU,CAACwB,QAAQ,CACpB,IAAI,CAACzB,SAAS,CAAC0B,IAAI,EACnB,IAAI,CAACrB,oBAAoB,CAACsB,MAAM,CACnC;EACL;EAEA;;;EAGAC,WAAWA,CAAA;IACP;IACA,IAAI,CAACtB,eAAe,CAACuB,IAAI,CAAC,IAAI,CAAC;IAC/B,IAAI,CAACvB,eAAe,CAACwB,QAAQ,EAAE;EACnC;EAEA;EACA;EACA;EAEA;;;EAGQV,aAAaA,CAAA;IACjB;IACA,IAAIW,KAAK,GAAG,IAAI,CAAChC,eAAe;IAChC,OAAOgC,KAAK,CAACC,UAAU,EAAE;MACrBD,KAAK,GAAGA,KAAK,CAACC,UAAU;IAC5B;IAEA;IACA,IAAI,CAACC,MAAM,GAAG,IAAI,CAACtB,MAAM,CAACsB,MAAM;IAEhC;IACA;IACA,MAAMC,oBAAoB,GAAGH,KAAK,CAACI,QAAQ,CAACC,aAAa,CAACC,GAAG,CAAC,QAAQ,CAAC;IACvE,IAAIH,oBAAoB,EAAE;MACtB,IAAI,CAACD,MAAM,GAAGC,oBAAoB;MAClC,IAAI,IAAI,CAACvB,MAAM,EAAE;QACb,IAAI,CAACA,MAAM,CAACsB,MAAM,GAAGC,oBAAoB;MAC7C;IACJ;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMI,KAAK,GAAGP,KAAK,CAACQ,YAAY;IAChCD,KAAK,CAACE,OAAO,CAAEC,IAAI,IAAI;MACnB;MACA,IACIA,IAAI,CAACC,WAAW,IAChBD,IAAI,CAACC,WAAW,CAACC,IAAI,IACrBF,IAAI,CAACC,WAAW,CAACC,IAAI,CAACV,MAAM,EAC9B;QACE;QACA,IAAI,CAACA,MAAM,GAAGQ,IAAI,CAACC,WAAW,CAACC,IAAI,CAACV,MAAM;MAC9C;IACJ,CAAC,CAAC;EACN;EAEA;;;;;EAKQf,aAAaA,CAAA;IACjB;IACA,IAAI,CAAClB,SAAS,CAAC0B,IAAI,CAACkB,SAAS,CAACC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC;IAErD;IACA,IAAI,CAAC7C,SAAS,CAAC0B,IAAI,CAACkB,SAAS,CAACE,GAAG,CAAC,IAAI,CAAChC,MAAM,CAAC;EAClD;EAEA;;;;;EAKQK,YAAYA,CAAA;IAChB;IACA,IAAI,CAACnB,SAAS,CAAC0B,IAAI,CAACkB,SAAS,CAACJ,OAAO,CAAEO,SAAiB,IAAI;MACxD,IAAIA,SAAS,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;QAChC,IAAI,CAAChD,SAAS,CAAC0B,IAAI,CAACkB,SAAS,CAACC,MAAM,CAChCE,SAAS,EACTA,SAAS,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAC1B;MACL;IACJ,CAAC,CAAC;IAEF;IACA,IAAI,CAACjD,SAAS,CAAC0B,IAAI,CAACkB,SAAS,CAACE,GAAG,CAAC,IAAI,CAAC/B,KAAK,CAAC;EACjD;EAAC,QAAAmC,CAAA,G;qCAxMQrD,eAAe,EAAAF,EAAA,CAAAwD,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA1D,EAAA,CAAAwD,iBAAA,CAYZ5E,QAAQ,GAAAoB,EAAA,CAAAwD,iBAAA,CAAAxD,EAAA,CAAA2D,SAAA,GAAA3D,EAAA,CAAAwD,iBAAA,CAAAC,EAAA,CAAAG,MAAA,GAAA5D,EAAA,CAAAwD,iBAAA,CAAAK,EAAA,CAAAC,iBAAA,GAAA9D,EAAA,CAAAwD,iBAAA,CAAAO,EAAA,CAAAC,uBAAA,GAAAhE,EAAA,CAAAwD,iBAAA,CAAAS,EAAA,CAAAC,mBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAZXjE,eAAe;IAAAkE,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC7C5BzE,EAAA,CAAA2E,mBAAA,IAAAC,sCAAA,uBAA0B;QAS1B5E,EAAA,CAAA2E,mBAAA,IAAAE,sCAAA,0BAA6B;QAK7B7E,EAAA,CAAA2E,mBAAA,IAAAG,sCAAA,4BAA+B;QAK/B9E,EAAA,CAAA2E,mBAAA,IAAAI,sCAAA,0BAA6B;QAK7B/E,EAAA,CAAA2E,mBAAA,IAAAK,sCAAA,wBAA2B;QAS3BhF,EAAA,CAAA2E,mBAAA,IAAAM,sCAAA,yBAA4B;QAK5BjF,EAAA,CAAA2E,mBAAA,IAAAO,sCAAA,wBAA2B;QAK3BlF,EAAA,CAAA2E,mBAAA,IAAAQ,sCAAA,yBAA4B;QAK5BnF,EAAA,CAAA2E,mBAAA,IAAAS,sCAAA,uBAA0B;QAK1BpF,EAAA,CAAA2E,mBAAA,IAAAU,sCAAA,4BAA+B;QAK/BrF,EAAA,CAAA2E,mBAAA,KAAAW,uCAAA,sBAAyB;QAOzBtF,EAAA,CAAAC,SAAA,gBAAqB;;;QAjErBD,EAAA,CAAAuF,aAAA,CAAAb,GAAA,CAAApC,MAAA,sBAEC;QAODtC,EAAA,CAAAwF,SAAA,EAEC;QAFDxF,EAAA,CAAAuF,aAAA,CAAAb,GAAA,CAAApC,MAAA,yBAEC;QAGDtC,EAAA,CAAAwF,SAAA,EAEC;QAFDxF,EAAA,CAAAuF,aAAA,CAAAb,GAAA,CAAApC,MAAA,2BAEC;QAGDtC,EAAA,CAAAwF,SAAA,EAEC;QAFDxF,EAAA,CAAAuF,aAAA,CAAAb,GAAA,CAAApC,MAAA,yBAEC;QAGDtC,EAAA,CAAAwF,SAAA,EAEC;QAFDxF,EAAA,CAAAuF,aAAA,CAAAb,GAAA,CAAApC,MAAA,uBAEC;QAODtC,EAAA,CAAAwF,SAAA,EAEC;QAFDxF,EAAA,CAAAuF,aAAA,CAAAb,GAAA,CAAApC,MAAA,wBAEC;QAGDtC,EAAA,CAAAwF,SAAA,EAEC;QAFDxF,EAAA,CAAAuF,aAAA,CAAAb,GAAA,CAAApC,MAAA,uBAEC;QAGDtC,EAAA,CAAAwF,SAAA,EAEC;QAFDxF,EAAA,CAAAuF,aAAA,CAAAb,GAAA,CAAApC,MAAA,wBAEC;QAGDtC,EAAA,CAAAwF,SAAA,EAEC;QAFDxF,EAAA,CAAAuF,aAAA,CAAAb,GAAA,CAAApC,MAAA,sBAEC;QAGDtC,EAAA,CAAAwF,SAAA,EAEC;QAFDxF,EAAA,CAAAuF,aAAA,CAAAb,GAAA,CAAApC,MAAA,2BAEC;QAGDtC,EAAA,CAAAwF,SAAA,EAEC;QAFDxF,EAAA,CAAAuF,aAAA,CAAAb,GAAA,CAAApC,MAAA,sBAEC;;;mBD7BOjD,oBAAoB,EACpBC,uBAAuB,EACvBC,yBAAyB,EACzBC,uBAAuB,EACvBC,qBAAqB,EACrBC,sBAAsB,EACtBC,qBAAqB,EACrBC,sBAAsB,EACtBC,oBAAoB,EACpBC,yBAAyB,EACzBC,mBAAmB,EACnBX,iBAAiB;IAAAqG,MAAA;IAAAC,aAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}