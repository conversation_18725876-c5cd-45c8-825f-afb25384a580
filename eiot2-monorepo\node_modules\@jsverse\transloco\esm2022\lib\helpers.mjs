export function getValue(obj, path) {
    if (!obj) {
        return obj;
    }
    /* For cases where the key is like: 'general.something.thing' */
    if (Object.prototype.hasOwnProperty.call(obj, path)) {
        return obj[path];
    }
    return path.split('.').reduce((p, c) => p?.[c], obj);
}
export function setValue(obj, prop, val) {
    obj = { ...obj };
    const split = prop.split('.');
    const lastIndex = split.length - 1;
    split.reduce((acc, part, index) => {
        if (index === lastIndex) {
            acc[part] = val;
        }
        else {
            acc[part] = Array.isArray(acc[part])
                ? acc[part].slice()
                : { ...acc[part] };
        }
        return acc && acc[part];
    }, obj);
    return obj;
}
export function size(collection) {
    if (!collection) {
        return 0;
    }
    if (Array.isArray(collection)) {
        return collection.length;
    }
    if (isObject(collection)) {
        return Object.keys(collection).length;
    }
    return collection ? collection.length : 0;
}
export function isEmpty(collection) {
    return size(collection) === 0;
}
export function isFunction(val) {
    return typeof val === 'function';
}
export function isString(val) {
    return typeof val === 'string';
}
export function isNumber(val) {
    return typeof val === 'number';
}
export function isObject(item) {
    return !!item && typeof item === 'object' && !Array.isArray(item);
}
export function coerceArray(value) {
    return Array.isArray(value) ? value : [value];
}
/*
 * @example
 *
 * given: path-to-happiness => pathToHappiness
 * given: path_to_happiness => pathToHappiness
 * given: path-to_happiness => pathToHappiness
 *
 */
export function toCamelCase(str) {
    return str
        .replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => index == 0 ? word.toLowerCase() : word.toUpperCase())
        .replace(/\s+|_|-|\//g, '');
}
export function isBrowser() {
    return typeof window !== 'undefined';
}
export function isNil(value) {
    return value === null || value === undefined;
}
export function isDefined(value) {
    return isNil(value) === false;
}
export function toNumber(value) {
    if (isNumber(value))
        return value;
    if (isString(value) && !isNaN(Number(value) - parseFloat(value))) {
        return Number(value);
    }
    return null;
}
export function isScopeObject(item) {
    return item && typeof item.scope === 'string';
}
export function hasInlineLoader(item) {
    return item && isObject(item.loader);
}
export function flatten(obj) {
    const result = {};
    function recurse(curr, prop) {
        if (curr === null) {
            result[prop] = null;
        }
        else if (isObject(curr)) {
            for (const [key, value] of Object.entries(curr)) {
                recurse(value, prop ? `${prop}.${key}` : key);
            }
        }
        else {
            result[prop] = curr;
        }
    }
    recurse(obj, '');
    return result;
}
export function unflatten(obj) {
    const result = {};
    for (const [key, value] of Object.entries(obj)) {
        const keys = key.split('.');
        let current = result;
        keys.forEach((key, i) => {
            if (i === keys.length - 1) {
                current[key] = value;
            }
            else {
                current[key] ??= {};
                current = current[key];
            }
        });
    }
    return result;
}
//# sourceMappingURL=data:application/json;base64,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