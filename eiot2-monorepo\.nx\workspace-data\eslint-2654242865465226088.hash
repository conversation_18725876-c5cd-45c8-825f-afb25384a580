{"11814065769852335950": {"apps/eiot2-monorepo": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot2-monorepo"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot2-monorepo/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "12071211824336212581": {"apps/eiot2-monorepo-e2e": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot2-monorepo-e2e"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot2-monorepo-e2e/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "9550295048632623042": {}, "14761259759850068908": {}, "5991637935511622054": {}, "14141415211137077896": {"shell": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "shell"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/shell/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "13398792765959067317": {"shell-e2e": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "shell-e2e"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/shell-e2e/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "5873302074000778689": {}, "14026026858449123610": {"eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "13898919656340416593": {"eiot-admin-e2e": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "eiot-admin-e2e"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/eiot-admin-e2e/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "7056318402657526856": {}, "15881568385703393271": {}, "4564903328511551618": {}, "17512773965627605868": {}, "12889000473726619198": {}, "3704798473301407148": {}, "6029214782751074734": {}, "16471444295288468545": {}, "17639075790998475482": {}, "15706065007992452143": {}, "12904913801695577781": {}, "124049871386943531": {}, "11718688040655893576": {}, "16904112542686585114": {}, "9642257612809885956": {"apps/shell": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/shell"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/shell/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "12407421236211679398": {"apps/shell": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/shell"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/shell/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "17441045703615586323": {"apps/shell": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/shell"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/shell/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "5986009512336762838": {"apps/shell": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/shell"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/shell/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "7923059297185538445": {}, "13711406568082964716": {"apps/shell": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/shell"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/shell/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "8053883466048409981": {}, "1983063957183487032": {"apps/shell": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/shell"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/shell/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "10487956994578989352": {}, "17863808413381700847": {"apps/shell": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/shell"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/shell/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "9973719292156312982": {}, "2590800070855584613": {"apps/shell": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/shell"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/shell/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "15805542801831905466": {}, "7476916221512347767": {"apps": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "11773521587186348456": {"apps-e2e": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps-e2e"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps-e2e/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "18131578167005186724": {}, "4104251414551671318": {}, "311773532443116764": {}, "123001191505021631": {"apps/eiot-stub": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-stub"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-stub/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "7619875288770427580": {}, "18279689280731455237": {"apps/eiot-stub": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-stub"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-stub/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "17941529155350264954": {}, "10202231156654995988": {"apps/eiot-stub": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-stub"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-stub/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "10862254005781694137": {}, "5888992566782200232": {"apps/eiot-stub": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-stub"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-stub/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "7890690227294631877": {}, "7239050073420075952": {"apps/eiot-stub": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-stub"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-stub/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "9919545945772755893": {}, "5436976733229881680": {"apps/eiot-stub": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-stub"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-stub/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "7833363363788582929": {}, "15641476099081089276": {"apps/eiot-stub": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-stub"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-stub/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "1957524409488456615": {}, "6412273237974990620": {"apps/eiot-stub": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-stub"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-stub/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "14329348980426429041": {}, "2474084505445943046": {"apps/eiot-stub": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-stub"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-stub/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "6787529053394231245": {}, "15440492149971686720": {"apps/eiot-stub": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-stub"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-stub/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "5002781728581431228": {}, "1966400559612273606": {"apps/eiot-stub": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-stub"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-stub/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "4703290083180612009": {}, "15932976682950754941": {"apps/eiot-stub": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-stub"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-stub/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "7782033465604821995": {}, "7683470138687911627": {"apps/eiot-stub": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-stub"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-stub/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "13042394729004281906": {}, "15641019058185434261": {}, "1725816588450758031": {"libs/shared/ui": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "libs/shared/ui"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/libs/shared/ui/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "17522162472765540768": {}, "1897691079476166338": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "1593299282341429843": {"apps/eiot-stub": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-stub"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-stub/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "17747195904639808062": {"apps/shell": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/shell"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/shell/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "5239999695810502549": {}, "12195590417782973407": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "16992843555952062089": {}, "16458263331121129956": {"apps/shell": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/shell"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/shell/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "4802838125455799354": {}, "11546955367315767267": {"apps/eiot-stub": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-stub"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-stub/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "13897782878766232012": {}, "17656574913704588394": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "11014052254892554448": {}, "12716458102566135468": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "7477200282004349081": {}, "7281030827253186129": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "11185202225961815433": {}, "11010134147581850466": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "1955988941956421265": {}, "17286078371251158615": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "8929904554839546852": {}, "2222585596304161997": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "308759219698915470": {}, "9158518250766091067": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "9596054707857208406": {}, "15398152123404913947": {}, "14548986690637448408": {}, "17243336149194831441": {}, "1171640792629930994": {}, "9884092530923137354": {}, "6380096552334122686": {}, "8974227894356442271": {}, "17040664010302105995": {}, "17540416825925199230": {"apps/shell": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/shell"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/shell/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "3524851587916456592": {}, "15570505761697282153": {"apps/shell": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/shell"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/shell/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "15608972809809666734": {}, "6288226752471785062": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "16812226675364019192": {}, "3025401101663757442": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "18071329627314297371": {}, "2235639234290230164": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "13400786260340494600": {}, "12946412112543381681": {"apps/shell": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/shell"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/shell/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "9785407328604834571": {}, "14099577913409656712": {"apps/shell": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/shell"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/shell/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "17354344195603801659": {}, "4108618102231240080": {"apps/shell": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/shell"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/shell/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "2927975565444347447": {}, "16880270198483434788": {"apps/shell": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/shell"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/shell/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "3447679135568506303": {}, "8491510769684602421": {"apps/shell": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/shell"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/shell/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "14948057937915544834": {}, "1666739938711971588": {"apps/shell": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/shell"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/shell/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "4171372376216135666": {}, "15721214992362224051": {"apps/shell": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/shell"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/shell/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "4781566330260412255": {}, "8300708673062671962": {"apps/shell": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/shell"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/shell/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "9915339670043530947": {}, "12815294563380232145": {"apps/shell": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/shell"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/shell/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "8781667116025381298": {}, "1527166990009670060": {"apps/shell": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/shell"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/shell/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "7725146132828859393": {}, "648986641762311888": {"libs/shared/ui": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "libs/shared/ui"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/libs/shared/ui/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "15119931895842173479": {}, "3344527262680926040": {"libs/shared/ui": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "libs/shared/ui"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/libs/shared/ui/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "14709163691709838945": {}, "9452154297908147321": {"libs/shared/ui": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "libs/shared/ui"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/libs/shared/ui/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "14642801507459637854": {}, "8617305622585962095": {"libs/shared/ui": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "libs/shared/ui"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/libs/shared/ui/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "7915212808656261876": {}, "6249827441531965857": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "8170205193101067578": {}, "13225881489061792378": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "16070846120712861060": {}, "7007643982119835391": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "509035756789557861": {}, "16834684137445455678": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "8418775765882217005": {}, "4061877247067243770": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "7937406319822366213": {}, "12883886958296725820": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "267958624715545013": {}, "117632639936972196": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "9659283431165915329": {}, "14245274239513363734": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "1152967777152900619": {}, "14341118958401688134": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "11609819182418932153": {}, "16183261995551914144": {}, "11276628436792700602": {}, "3064554507449930910": {}, "6254859001229037617": {}, "9101961023949521331": {"libs/fuse": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "libs/fuse"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "18153955164091344571": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "15400947063423069756": {}, "9277785570247322179": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "4247422242565452825": {}, "5555026152506843976": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "9064579750693004067": {}, "3350761313042919910": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "5090866514759789882": {}, "14534237312717780882": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "17516302588874810459": {}, "3069801848828528125": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "11503145180850140388": {}, "11524370291783755388": {"libs/shared/ui": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "libs/shared/ui"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/libs/shared/ui/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "10405316839751665119": {}, "10434828552994824875": {"libs/shared/ui": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "libs/shared/ui"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/libs/shared/ui/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "2519761616031478945": {}, "125362159382008961": {"libs/shared/ui": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "libs/shared/ui"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/libs/shared/ui/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "1966106171533813621": {}, "12396033827618211848": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "963153940956299165": {}, "9675693972825713146": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "1812781573860525181": {}, "7984767800322040912": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "7611112831578225908": {}, "14656255818193006002": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "15676453917324917267": {}, "9293771816873426217": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "11003248729839793153": {}, "6138711197035537631": {}, "16557094695942903683": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "13948291141565666954": {}, "15641756440019238445": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "12612648295675696269": {}, "17005249522116728250": {}, "4546110845098230742": {}, "3630836748400738538": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "15289956859314346981": {}, "2983817414745563909": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "654829181302915649": {}, "3863400062208477153": {"apps/eiot-admin": {"targets": {"lint": {"command": "eslint .", "cache": true, "options": {"cwd": "apps/eiot-admin"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/apps/eiot-admin/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "9774085675722780096": {}, "4231026132478224627": {}, "13335887331525305159": {}}