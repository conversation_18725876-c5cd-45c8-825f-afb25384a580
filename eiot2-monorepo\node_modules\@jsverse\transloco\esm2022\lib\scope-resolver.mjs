import { isScopeObject, toCamelCase } from './helpers';
export class ScopeResolver {
    service;
    constructor(service) {
        this.service = service;
    }
    // inline => provider
    resolve(params) {
        const { inline, provider } = params;
        if (inline) {
            return inline;
        }
        if (provider) {
            if (isScopeObject(provider)) {
                const { scope, alias = this.service.config.scopes.keepCasing
                    ? scope
                    : toCamelCase(scope), } = provider;
                this.service._setScopeAlias(scope, alias);
                return scope;
            }
            return provider;
        }
        return undefined;
    }
}
//# sourceMappingURL=data:application/json;base64,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