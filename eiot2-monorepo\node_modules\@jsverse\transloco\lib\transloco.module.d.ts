import * as i0 from "@angular/core";
import * as i1 from "./transloco.directive";
import * as i2 from "./transloco.pipe";
export declare class TranslocoModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<TranslocoModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<TranslocoModule, never, [typeof i1.TranslocoDirective, typeof i2.TranslocoPipe], [typeof i1.TranslocoDirective, typeof i2.TranslocoPipe]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<TranslocoModule>;
}
