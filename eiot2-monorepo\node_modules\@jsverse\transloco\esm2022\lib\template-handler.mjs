import { TemplateRef } from '@angular/core';
import { isString } from './helpers';
import { TranslocoLoaderComponent } from './loader-component.component';
export class TemplateHandler {
    view;
    vcr;
    constructor(view, vcr) {
        this.view = view;
        this.vcr = vcr;
    }
    attachView() {
        if (this.view instanceof TemplateRef) {
            this.vcr.createEmbeddedView(this.view);
        }
        else if (isString(this.view)) {
            const componentRef = this.vcr.createComponent(TranslocoLoaderComponent);
            componentRef.instance.html = this.view;
            componentRef.hostView.detectChanges();
        }
        else {
            this.vcr.createComponent(this.view);
        }
    }
    detachView() {
        this.vcr.clear();
    }
}
//# sourceMappingURL=data:application/json;base64,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