{"ast": null, "code": "import { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { RouterOutlet } from '@angular/router';\nimport { FuseFullscreenComponent } from '@fuse/components/fullscreen';\nimport { FuseLoadingBarComponent } from '@fuse/components/loading-bar';\nimport { FuseHorizontalNavigationComponent, FuseVerticalNavigationComponent } from '@fuse/components/navigation';\nimport { LanguagesComponent } from 'app/layout/common/languages/languages.component';\nimport { MessagesComponent } from 'app/layout/common/messages/messages.component';\nimport { NotificationsComponent } from 'app/layout/common/notifications/notifications.component';\nimport { SearchComponent } from 'app/layout/common/search/search.component';\nimport { ShortcutsComponent } from 'app/layout/common/shortcuts/shortcuts.component';\nimport { UserComponent } from 'app/layout/common/user/user.component';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"app/core/navigation/navigation.service\";\nimport * as i3 from \"@fuse/services/media-watcher\";\nimport * as i4 from \"@fuse/components/navigation\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/icon\";\nfunction MaterialLayoutComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"fuse-vertical-navigation\", 0);\n    i0.ɵɵelementContainerStart(1, 16);\n    i0.ɵɵelementStart(2, \"div\", 17);\n    i0.ɵɵelement(3, \"img\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"mode\", \"over\")(\"name\", \"mainNavigation\")(\"navigation\", ctx_r0.navigation.default)(\"opened\", false);\n  }\n}\nfunction MaterialLayoutComponent_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelement(1, \"img\", 19)(2, \"img\", 20);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MaterialLayoutComponent_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function MaterialLayoutComponent_Conditional_7_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.toggleNavigation(\"mainNavigation\"));\n    });\n    i0.ɵɵelement(1, \"mat-icon\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"svgIcon\", \"heroicons_outline:bars-3\");\n  }\n}\nfunction MaterialLayoutComponent_Conditional_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵelement(1, \"fuse-horizontal-navigation\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", \"mainNavigation\")(\"navigation\", ctx_r0.navigation.horizontal);\n  }\n}\nfunction MaterialLayoutComponent_Conditional_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"router-outlet\");\n  }\n}\nexport class MaterialLayoutComponent {\n  /**\n   * Constructor\n   */\n  constructor(_activatedRoute, _router, _navigationService, _fuseMediaWatcherService, _fuseNavigationService) {\n    this._activatedRoute = _activatedRoute;\n    this._router = _router;\n    this._navigationService = _navigationService;\n    this._fuseMediaWatcherService = _fuseMediaWatcherService;\n    this._fuseNavigationService = _fuseNavigationService;\n    this._unsubscribeAll = new Subject();\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Accessors\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * Getter for current year\n   */\n  get currentYear() {\n    return new Date().getFullYear();\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Lifecycle hooks\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * On init\n   */\n  ngOnInit() {\n    // Subscribe to navigation data\n    this._navigationService.navigation$.pipe(takeUntil(this._unsubscribeAll)).subscribe(navigation => {\n      this.navigation = navigation;\n    });\n    // Subscribe to media changes\n    this._fuseMediaWatcherService.onMediaChange$.pipe(takeUntil(this._unsubscribeAll)).subscribe(({\n      matchingAliases\n    }) => {\n      // Check if the screen is small\n      this.isScreenSmall = !matchingAliases.includes('md');\n    });\n  }\n  /**\n   * On destroy\n   */\n  ngOnDestroy() {\n    // Unsubscribe from all subscriptions\n    this._unsubscribeAll.next(null);\n    this._unsubscribeAll.complete();\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Public methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * Toggle navigation\n   *\n   * @param name\n   */\n  toggleNavigation(name) {\n    // Get the navigation\n    const navigation = this._fuseNavigationService.getComponent(name);\n    if (navigation) {\n      // Toggle the opened status\n      navigation.toggle();\n    }\n  }\n  static #_ = this.ɵfac = function MaterialLayoutComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MaterialLayoutComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.NavigationService), i0.ɵɵdirectiveInject(i3.FuseMediaWatcherService), i0.ɵɵdirectiveInject(i4.FuseNavigationService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: MaterialLayoutComponent,\n    selectors: [[\"material-layout\"]],\n    decls: 24,\n    vars: 7,\n    consts: [[1, \"dark\", \"bg-gray-900\", \"print:hidden\", 3, \"mode\", \"name\", \"navigation\", \"opened\"], [1, \"flex\", \"w-full\", \"min-w-0\", \"flex-auto\", \"flex-col\", \"items-center\", \"bg-gray-200\", \"dark:bg-card\"], [1, \"relative\", \"z-49\", \"flex\", \"w-full\", \"justify-center\", \"overflow-hidden\", \"bg-primary-700\", \"print:hidden\"], [1, \"bg-card\", \"w-full\", \"max-w-360\", \"overflow-hidden\", \"border-b\", \"sm:m-8\", \"sm:mb-0\", \"sm:rounded-t-xl\", \"sm:py-3\", \"sm:shadow-2xl\", \"md:mx-8\", \"md:mt-12\", \"md:pb-3\", \"md:pt-4\"], [1, \"relative\", \"flex\", \"h-16\", \"flex-0\", \"flex-auto\", \"items-center\", \"px-4\", \"md:px-6\"], [1, \"mx-2\", \"flex\", \"items-center\"], [\"mat-icon-button\", \"\"], [1, \"ml-auto\", \"flex\", \"items-center\", \"space-x-1\", \"pl-2\", \"sm:space-x-2\"], [1, \"hidden\", \"md:block\"], [3, \"appearance\"], [1, \"relative\", \"flex\", \"h-16\", \"flex-0\", \"flex-auto\", \"items-center\", \"px-4\"], [1, \"flex\", \"w-full\", \"flex-auto\", \"justify-center\", \"sm:px-8\"], [1, \"bg-default\", \"flex\", \"w-full\", \"flex-auto\", \"flex-col\", \"sm:max-w-360\", \"sm:overflow-hidden\", \"sm:shadow-xl\"], [1, \"relative\", \"z-49\", \"flex\", \"w-full\", \"justify-center\", \"print:hidden\"], [1, \"bg-card\", \"flex\", \"h-14\", \"w-full\", \"max-w-360\", \"items-center\", \"border-t\", \"px-6\", \"dark:bg-default\", \"sm:h-20\", \"sm:shadow-xl\", \"md:px-8\"], [1, \"text-secondary\", \"font-medium\"], [\"fuseVerticalNavigationContentHeader\", \"\"], [1, \"flex\", \"h-20\", \"items-center\", \"px-8\", \"pt-6\"], [\"src\", \"images/logo/logo-text-on-dark.svg\", \"alt\", \"Logo image\", 1, \"w-24\"], [\"src\", \"images/logo/logo-text.svg\", \"alt\", \"Logo image\", 1, \"w-24\", \"dark:hidden\"], [\"src\", \"images/logo/logo-text-on-dark.svg\", \"alt\", \"Logo image\", 1, \"hidden\", \"w-24\", \"dark:flex\"], [\"mat-icon-button\", \"\", 3, \"click\"], [3, \"svgIcon\"], [3, \"name\", \"navigation\"]],\n    template: function MaterialLayoutComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"fuse-loading-bar\");\n        i0.ɵɵconditionalCreate(1, MaterialLayoutComponent_Conditional_1_Template, 4, 4, \"fuse-vertical-navigation\", 0);\n        i0.ɵɵelementStart(2, \"div\", 1)(3, \"div\", 2)(4, \"div\", 3)(5, \"div\", 4);\n        i0.ɵɵconditionalCreate(6, MaterialLayoutComponent_Conditional_6_Template, 3, 0, \"div\", 5);\n        i0.ɵɵconditionalCreate(7, MaterialLayoutComponent_Conditional_7_Template, 2, 1, \"button\", 6);\n        i0.ɵɵelementStart(8, \"div\", 7);\n        i0.ɵɵelement(9, \"languages\")(10, \"fuse-fullscreen\", 8)(11, \"search\", 9)(12, \"shortcuts\")(13, \"messages\")(14, \"notifications\")(15, \"user\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵconditionalCreate(16, MaterialLayoutComponent_Conditional_16_Template, 2, 2, \"div\", 10);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(17, \"div\", 11)(18, \"div\", 12);\n        i0.ɵɵconditionalCreate(19, MaterialLayoutComponent_Conditional_19_Template, 1, 0, \"router-outlet\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(20, \"div\", 13)(21, \"div\", 14)(22, \"span\", 15);\n        i0.ɵɵtext(23);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.isScreenSmall ? 1 : -1);\n        i0.ɵɵadvance(5);\n        i0.ɵɵconditional(!ctx.isScreenSmall ? 6 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.isScreenSmall ? 7 : -1);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"appearance\", \"bar\");\n        i0.ɵɵadvance(5);\n        i0.ɵɵconditional(!ctx.isScreenSmall ? 16 : -1);\n        i0.ɵɵadvance(3);\n        i0.ɵɵconditional(true ? 19 : -1);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\"Fuse \\u00A9 \", ctx.currentYear);\n      }\n    },\n    dependencies: [FuseLoadingBarComponent, FuseVerticalNavigationComponent, MatButtonModule, i5.MatIconButton, MatIconModule, i6.MatIcon, LanguagesComponent, FuseFullscreenComponent, SearchComponent, ShortcutsComponent, MessagesComponent, NotificationsComponent, UserComponent, FuseHorizontalNavigationComponent, RouterOutlet],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["MatButtonModule", "MatIconModule", "RouterOutlet", "FuseFullscreenComponent", "FuseLoadingBarComponent", "FuseHorizontalNavigationComponent", "FuseVerticalNavigationComponent", "LanguagesComponent", "MessagesComponent", "NotificationsComponent", "SearchComponent", "ShortcutsComponent", "UserComponent", "Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵelementContainerStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵproperty", "ctx_r0", "navigation", "default", "ɵɵlistener", "MaterialLayoutComponent_Conditional_7_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "toggleNavigation", "ɵɵadvance", "horizontal", "MaterialLayoutComponent", "constructor", "_activatedRoute", "_router", "_navigationService", "_fuseMediaWatcherService", "_fuseNavigationService", "_unsubscribeAll", "currentYear", "Date", "getFullYear", "ngOnInit", "navigation$", "pipe", "subscribe", "onMediaChange$", "matchingAliases", "isScreenSmall", "includes", "ngOnDestroy", "next", "complete", "name", "getComponent", "toggle", "_", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "NavigationService", "i3", "FuseMediaWatcherService", "i4", "FuseNavigationService", "_2", "selectors", "decls", "vars", "consts", "template", "MaterialLayoutComponent_Template", "rf", "ctx", "ɵɵconditionalCreate", "MaterialLayoutComponent_Conditional_1_Template", "MaterialLayoutComponent_Conditional_6_Template", "MaterialLayoutComponent_Conditional_7_Template", "MaterialLayoutComponent_Conditional_16_Template", "MaterialLayoutComponent_Conditional_19_Template", "ɵɵtext", "ɵɵconditional", "ɵɵtextInterpolate1", "i5", "MatIconButton", "i6", "MatIcon", "encapsulation"], "sources": ["D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\app\\layout\\layouts\\horizontal\\material\\material.component.ts", "D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\app\\layout\\layouts\\horizontal\\material\\material.component.html"], "sourcesContent": ["import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>nit, ViewEncapsulation } from '@angular/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { ActivatedRoute, Router, RouterOutlet } from '@angular/router';\nimport { FuseFullscreenComponent } from '@fuse/components/fullscreen';\nimport { FuseLoadingBarComponent } from '@fuse/components/loading-bar';\nimport {\n    FuseHorizontalNavigationComponent,\n    FuseNavigationService,\n    FuseVerticalNavigationComponent,\n} from '@fuse/components/navigation';\nimport { FuseMediaWatcherService } from '@fuse/services/media-watcher';\nimport { NavigationService } from 'app/core/navigation/navigation.service';\nimport { Navigation } from 'app/core/navigation/navigation.types';\nimport { LanguagesComponent } from 'app/layout/common/languages/languages.component';\nimport { MessagesComponent } from 'app/layout/common/messages/messages.component';\nimport { NotificationsComponent } from 'app/layout/common/notifications/notifications.component';\nimport { SearchComponent } from 'app/layout/common/search/search.component';\nimport { ShortcutsComponent } from 'app/layout/common/shortcuts/shortcuts.component';\nimport { UserComponent } from 'app/layout/common/user/user.component';\nimport { Subject, takeUntil } from 'rxjs';\n\n@Component({\n    selector: 'material-layout',\n    templateUrl: './material.component.html',\n    encapsulation: ViewEncapsulation.None,\n    imports: [\n        FuseLoadingBarComponent,\n        FuseVerticalNavigationComponent,\n        MatButtonModule,\n        MatIconModule,\n        LanguagesComponent,\n        FuseFullscreenComponent,\n        SearchComponent,\n        ShortcutsComponent,\n        MessagesComponent,\n        NotificationsComponent,\n        UserComponent,\n        FuseHorizontalNavigationComponent,\n        RouterOutlet,\n    ],\n})\nexport class MaterialLayoutComponent implements OnInit, OnDestroy {\n    isScreenSmall: boolean;\n    navigation: Navigation;\n    private _unsubscribeAll: Subject<any> = new Subject<any>();\n\n    /**\n     * Constructor\n     */\n    constructor(\n        private _activatedRoute: ActivatedRoute,\n        private _router: Router,\n        private _navigationService: NavigationService,\n        private _fuseMediaWatcherService: FuseMediaWatcherService,\n        private _fuseNavigationService: FuseNavigationService\n    ) {}\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Accessors\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * Getter for current year\n     */\n    get currentYear(): number {\n        return new Date().getFullYear();\n    }\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Lifecycle hooks\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * On init\n     */\n    ngOnInit(): void {\n        // Subscribe to navigation data\n        this._navigationService.navigation$\n            .pipe(takeUntil(this._unsubscribeAll))\n            .subscribe((navigation: Navigation) => {\n                this.navigation = navigation;\n            });\n\n        // Subscribe to media changes\n        this._fuseMediaWatcherService.onMediaChange$\n            .pipe(takeUntil(this._unsubscribeAll))\n            .subscribe(({ matchingAliases }) => {\n                // Check if the screen is small\n                this.isScreenSmall = !matchingAliases.includes('md');\n            });\n    }\n\n    /**\n     * On destroy\n     */\n    ngOnDestroy(): void {\n        // Unsubscribe from all subscriptions\n        this._unsubscribeAll.next(null);\n        this._unsubscribeAll.complete();\n    }\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Public methods\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * Toggle navigation\n     *\n     * @param name\n     */\n    toggleNavigation(name: string): void {\n        // Get the navigation\n        const navigation =\n            this._fuseNavigationService.getComponent<FuseVerticalNavigationComponent>(\n                name\n            );\n\n        if (navigation) {\n            // Toggle the opened status\n            navigation.toggle();\n        }\n    }\n}\n", "<!-- Loading bar -->\n<fuse-loading-bar></fuse-loading-bar>\n\n<!-- Navigation -->\n@if (isScreenSmall) {\n    <fuse-vertical-navigation\n        class=\"dark bg-gray-900 print:hidden\"\n        [mode]=\"'over'\"\n        [name]=\"'mainNavigation'\"\n        [navigation]=\"navigation.default\"\n        [opened]=\"false\"\n    >\n        <!-- Navigation header hook -->\n        <ng-container fuseVerticalNavigationContentHeader>\n            <!-- Logo -->\n            <div class=\"flex h-20 items-center px-8 pt-6\">\n                <img\n                    class=\"w-24\"\n                    src=\"images/logo/logo-text-on-dark.svg\"\n                    alt=\"Logo image\"\n                />\n            </div>\n        </ng-container>\n    </fuse-vertical-navigation>\n}\n\n<!-- Wrapper -->\n<div\n    class=\"flex w-full min-w-0 flex-auto flex-col items-center bg-gray-200 dark:bg-card\"\n>\n    <!-- Header -->\n    <div\n        class=\"relative z-49 flex w-full justify-center overflow-hidden bg-primary-700 print:hidden\"\n    >\n        <div\n            class=\"bg-card w-full max-w-360 overflow-hidden border-b sm:m-8 sm:mb-0 sm:rounded-t-xl sm:py-3 sm:shadow-2xl md:mx-8 md:mt-12 md:pb-3 md:pt-4\"\n        >\n            <!-- Top bar -->\n            <div\n                class=\"relative flex h-16 flex-0 flex-auto items-center px-4 md:px-6\"\n            >\n                <!-- Logo -->\n                @if (!isScreenSmall) {\n                    <div class=\"mx-2 flex items-center\">\n                        <!-- Light version -->\n                        <img\n                            class=\"w-24 dark:hidden\"\n                            src=\"images/logo/logo-text.svg\"\n                            alt=\"Logo image\"\n                        />\n                        <!-- Dark version -->\n                        <img\n                            class=\"hidden w-24 dark:flex\"\n                            src=\"images/logo/logo-text-on-dark.svg\"\n                            alt=\"Logo image\"\n                        />\n                    </div>\n                }\n                <!-- Navigation toggle button -->\n                @if (isScreenSmall) {\n                    <button\n                        mat-icon-button\n                        (click)=\"toggleNavigation('mainNavigation')\"\n                    >\n                        <mat-icon\n                            [svgIcon]=\"'heroicons_outline:bars-3'\"\n                        ></mat-icon>\n                    </button>\n                }\n                <!-- Components -->\n                <div\n                    class=\"ml-auto flex items-center space-x-1 pl-2 sm:space-x-2\"\n                >\n                    <languages></languages>\n                    <fuse-fullscreen class=\"hidden md:block\"></fuse-fullscreen>\n                    <search [appearance]=\"'bar'\"></search>\n                    <shortcuts></shortcuts>\n                    <messages></messages>\n                    <notifications></notifications>\n                    <user></user>\n                </div>\n            </div>\n            <!-- Bottom bar -->\n            @if (!isScreenSmall) {\n                <div\n                    class=\"relative flex h-16 flex-0 flex-auto items-center px-4\"\n                >\n                    <fuse-horizontal-navigation\n                        [name]=\"'mainNavigation'\"\n                        [navigation]=\"navigation.horizontal\"\n                    ></fuse-horizontal-navigation>\n                </div>\n            }\n        </div>\n    </div>\n\n    <!-- Content -->\n    <div class=\"flex w-full flex-auto justify-center sm:px-8\">\n        <div\n            class=\"bg-default flex w-full flex-auto flex-col sm:max-w-360 sm:overflow-hidden sm:shadow-xl\"\n        >\n            <!-- *ngIf=\"true\" hack is required here for router-outlet to work correctly.\n                 Otherwise, layout changes won't be registered and the view won't be updated! -->\n            @if (true) {\n                <router-outlet></router-outlet>\n            }\n        </div>\n    </div>\n\n    <!-- Footer -->\n    <div class=\"relative z-49 flex w-full justify-center print:hidden\">\n        <div\n            class=\"bg-card flex h-14 w-full max-w-360 items-center border-t px-6 dark:bg-default sm:h-20 sm:shadow-xl md:px-8\"\n        >\n            <span class=\"text-secondary font-medium\"\n                >Fuse &copy; {{ currentYear }}</span\n            >\n        </div>\n    </div>\n</div>\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAAiCC,YAAY,QAAQ,iBAAiB;AACtE,SAASC,uBAAuB,QAAQ,6BAA6B;AACrE,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SACIC,iCAAiC,EAEjCC,+BAA+B,QAC5B,6BAA6B;AAIpC,SAASC,kBAAkB,QAAQ,iDAAiD;AACpF,SAASC,iBAAiB,QAAQ,+CAA+C;AACjF,SAASC,sBAAsB,QAAQ,yDAAyD;AAChG,SAASC,eAAe,QAAQ,2CAA2C;AAC3E,SAASC,kBAAkB,QAAQ,iDAAiD;AACpF,SAASC,aAAa,QAAQ,uCAAuC;AACrE,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;ICfrCC,EAAA,CAAAC,cAAA,kCAMC;IAEGD,EAAA,CAAAE,uBAAA,OAAkD;IAE9CF,EAAA,CAAAC,cAAA,cAA8C;IAC1CD,EAAA,CAAAG,SAAA,cAIE;IACNH,EAAA,CAAAI,YAAA,EAAM;;IAEdJ,EAAA,CAAAI,YAAA,EAA2B;;;;IAbvBJ,EAHA,CAAAK,UAAA,gBAAe,0BACU,eAAAC,MAAA,CAAAC,UAAA,CAAAC,OAAA,CACQ,iBACjB;;;;;IAiCJR,EAAA,CAAAC,cAAA,aAAoC;IAQhCD,EANA,CAAAG,SAAA,cAIE,cAMA;IACNH,EAAA,CAAAI,YAAA,EAAM;;;;;;IAINJ,EAAA,CAAAC,cAAA,iBAGC;IADGD,EAAA,CAAAS,UAAA,mBAAAC,uEAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAN,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASR,MAAA,CAAAS,gBAAA,CAAiB,gBAAgB,CAAC;IAAA,EAAC;IAE5Cf,EAAA,CAAAG,SAAA,mBAEY;IAChBH,EAAA,CAAAI,YAAA,EAAS;;;IAFDJ,EAAA,CAAAgB,SAAA,EAAsC;IAAtChB,EAAA,CAAAK,UAAA,uCAAsC;;;;;IAmBlDL,EAAA,CAAAC,cAAA,cAEC;IACGD,EAAA,CAAAG,SAAA,qCAG8B;IAClCH,EAAA,CAAAI,YAAA,EAAM;;;;IAHEJ,EAAA,CAAAgB,SAAA,EAAyB;IACzBhB,EADA,CAAAK,UAAA,0BAAyB,eAAAC,MAAA,CAAAC,UAAA,CAAAU,UAAA,CACW;;;;;IAe5CjB,EAAA,CAAAG,SAAA,oBAA+B;;;AD9D/C,OAAM,MAAOe,uBAAuB;EAKhC;;;EAGAC,YACYC,eAA+B,EAC/BC,OAAe,EACfC,kBAAqC,EACrCC,wBAAiD,EACjDC,sBAA6C;IAJ7C,KAAAJ,eAAe,GAAfA,eAAe;IACf,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,sBAAsB,GAAtBA,sBAAsB;IAV1B,KAAAC,eAAe,GAAiB,IAAI3B,OAAO,EAAO;EAWvD;EAEH;EACA;EACA;EAEA;;;EAGA,IAAI4B,WAAWA,CAAA;IACX,OAAO,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;EACnC;EAEA;EACA;EACA;EAEA;;;EAGAC,QAAQA,CAAA;IACJ;IACA,IAAI,CAACP,kBAAkB,CAACQ,WAAW,CAC9BC,IAAI,CAAChC,SAAS,CAAC,IAAI,CAAC0B,eAAe,CAAC,CAAC,CACrCO,SAAS,CAAEzB,UAAsB,IAAI;MAClC,IAAI,CAACA,UAAU,GAAGA,UAAU;IAChC,CAAC,CAAC;IAEN;IACA,IAAI,CAACgB,wBAAwB,CAACU,cAAc,CACvCF,IAAI,CAAChC,SAAS,CAAC,IAAI,CAAC0B,eAAe,CAAC,CAAC,CACrCO,SAAS,CAAC,CAAC;MAAEE;IAAe,CAAE,KAAI;MAC/B;MACA,IAAI,CAACC,aAAa,GAAG,CAACD,eAAe,CAACE,QAAQ,CAAC,IAAI,CAAC;IACxD,CAAC,CAAC;EACV;EAEA;;;EAGAC,WAAWA,CAAA;IACP;IACA,IAAI,CAACZ,eAAe,CAACa,IAAI,CAAC,IAAI,CAAC;IAC/B,IAAI,CAACb,eAAe,CAACc,QAAQ,EAAE;EACnC;EAEA;EACA;EACA;EAEA;;;;;EAKAxB,gBAAgBA,CAACyB,IAAY;IACzB;IACA,MAAMjC,UAAU,GACZ,IAAI,CAACiB,sBAAsB,CAACiB,YAAY,CACpCD,IAAI,CACP;IAEL,IAAIjC,UAAU,EAAE;MACZ;MACAA,UAAU,CAACmC,MAAM,EAAE;IACvB;EACJ;EAAC,QAAAC,CAAA,G;qCAhFQzB,uBAAuB,EAAAlB,EAAA,CAAA4C,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA9C,EAAA,CAAA4C,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA/C,EAAA,CAAA4C,iBAAA,CAAAI,EAAA,CAAAC,iBAAA,GAAAjD,EAAA,CAAA4C,iBAAA,CAAAM,EAAA,CAAAC,uBAAA,GAAAnD,EAAA,CAAA4C,iBAAA,CAAAQ,EAAA,CAAAC,qBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAvBpC,uBAAuB;IAAAqC,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCzCpC7D,EAAA,CAAAG,SAAA,uBAAqC;QAGrCH,EAAA,CAAA+D,mBAAA,IAAAC,8CAAA,sCAAqB;QAkCThE,EAXZ,CAAAC,cAAA,aAEC,aAII,aAGI,aAII;QAEGD,EAAA,CAAA+D,mBAAA,IAAAE,8CAAA,iBAAsB;QAiBtBjE,EAAA,CAAA+D,mBAAA,IAAAG,8CAAA,oBAAqB;QAWrBlE,EAAA,CAAAC,cAAA,aAEC;QAOGD,EANA,CAAAG,SAAA,gBAAuB,0BACoC,iBACrB,iBACf,gBACF,qBACU,YAClB;QAErBH,EADI,CAAAI,YAAA,EAAM,EACJ;QAENJ,EAAA,CAAA+D,mBAAA,KAAAI,+CAAA,kBAAsB;QAW9BnE,EADI,CAAAI,YAAA,EAAM,EACJ;QAIFJ,EADJ,CAAAC,cAAA,eAA0D,eAGrD;QAGGD,EAAA,CAAA+D,mBAAA,KAAAK,+CAAA,wBAAY;QAIpBpE,EADI,CAAAI,YAAA,EAAM,EACJ;QAOEJ,EAJR,CAAAC,cAAA,eAAmE,eAG9D,gBAEQ;QAAAD,EAAA,CAAAqE,MAAA,IAA6B;QAI9CrE,EAJ8C,CAAAI,YAAA,EACjC,EACC,EACJ,EACJ;;;QAnHNJ,EAAA,CAAAgB,SAAA,EAoBC;QApBDhB,EAAA,CAAAsE,aAAA,CAAAR,GAAA,CAAA3B,aAAA,UAoBC;QAkBenC,EAAA,CAAAgB,SAAA,GAeC;QAfDhB,EAAA,CAAAsE,aAAA,EAAAR,GAAA,CAAA3B,aAAA,UAeC;QAEDnC,EAAA,CAAAgB,SAAA,EASC;QATDhB,EAAA,CAAAsE,aAAA,CAAAR,GAAA,CAAA3B,aAAA,UASC;QAOWnC,EAAA,CAAAgB,SAAA,GAAoB;QAApBhB,EAAA,CAAAK,UAAA,qBAAoB;QAQpCL,EAAA,CAAAgB,SAAA,GASC;QATDhB,EAAA,CAAAsE,aAAA,EAAAR,GAAA,CAAA3B,aAAA,WASC;QAWDnC,EAAA,CAAAgB,SAAA,GAEC;QAFDhB,EAAA,CAAAsE,aAAA,gBAEC;QAUItE,EAAA,CAAAgB,SAAA,GAA6B;QAA7BhB,EAAA,CAAAuE,kBAAA,iBAAAT,GAAA,CAAApC,WAAA,CAA6B;;;mBDxFtCrC,uBAAuB,EACvBE,+BAA+B,EAC/BN,eAAe,EAAAuF,EAAA,CAAAC,aAAA,EACfvF,aAAa,EAAAwF,EAAA,CAAAC,OAAA,EACbnF,kBAAkB,EAClBJ,uBAAuB,EACvBO,eAAe,EACfC,kBAAkB,EAClBH,iBAAiB,EACjBC,sBAAsB,EACtBG,aAAa,EACbP,iCAAiC,EACjCH,YAAY;IAAAyF,aAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}