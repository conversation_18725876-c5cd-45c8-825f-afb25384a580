{"ast": null, "code": "import { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { RouterOutlet } from '@angular/router';\nimport { FuseFullscreenComponent } from '@fuse/components/fullscreen';\nimport { FuseLoadingBarComponent } from '@fuse/components/loading-bar';\nimport { FuseHorizontalNavigationComponent, FuseVerticalNavigationComponent } from '@fuse/components/navigation';\nimport { LanguagesComponent } from 'app/layout/common/languages/languages.component';\nimport { MessagesComponent } from 'app/layout/common/messages/messages.component';\nimport { NotificationsComponent } from 'app/layout/common/notifications/notifications.component';\nimport { QuickChatComponent } from 'app/layout/common/quick-chat/quick-chat.component';\nimport { SearchComponent } from 'app/layout/common/search/search.component';\nimport { ShortcutsComponent } from 'app/layout/common/shortcuts/shortcuts.component';\nimport { UserComponent } from 'app/layout/common/user/user.component';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"app/core/navigation/navigation.service\";\nimport * as i3 from \"@fuse/services/media-watcher\";\nimport * as i4 from \"@fuse/components/navigation\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/icon\";\nfunction ModernLayoutComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"fuse-vertical-navigation\", 1);\n    i0.ɵɵelementContainerStart(1, 13);\n    i0.ɵɵelementStart(2, \"div\", 14);\n    i0.ɵɵelement(3, \"img\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"mode\", \"over\")(\"name\", \"mainNavigation\")(\"navigation\", ctx_r1.navigation.default)(\"opened\", false);\n  }\n}\nfunction ModernLayoutComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 17);\n    i0.ɵɵelement(2, \"img\", 18)(3, \"img\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"img\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"fuse-horizontal-navigation\", 21);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"name\", \"mainNavigation\")(\"navigation\", ctx_r1.navigation.horizontal);\n  }\n}\nfunction ModernLayoutComponent_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function ModernLayoutComponent_Conditional_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleNavigation(\"mainNavigation\"));\n    });\n    i0.ɵɵelement(1, \"mat-icon\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"svgIcon\", \"heroicons_outline:bars-3\");\n  }\n}\nfunction ModernLayoutComponent_Conditional_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"router-outlet\");\n  }\n}\nexport class ModernLayoutComponent {\n  /**\n   * Constructor\n   */\n  constructor(_activatedRoute, _router, _navigationService, _fuseMediaWatcherService, _fuseNavigationService) {\n    this._activatedRoute = _activatedRoute;\n    this._router = _router;\n    this._navigationService = _navigationService;\n    this._fuseMediaWatcherService = _fuseMediaWatcherService;\n    this._fuseNavigationService = _fuseNavigationService;\n    this._unsubscribeAll = new Subject();\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Accessors\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * Getter for current year\n   */\n  get currentYear() {\n    return new Date().getFullYear();\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Lifecycle hooks\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * On init\n   */\n  ngOnInit() {\n    // Subscribe to navigation data\n    this._navigationService.navigation$.pipe(takeUntil(this._unsubscribeAll)).subscribe(navigation => {\n      this.navigation = navigation;\n    });\n    // Subscribe to media changes\n    this._fuseMediaWatcherService.onMediaChange$.pipe(takeUntil(this._unsubscribeAll)).subscribe(({\n      matchingAliases\n    }) => {\n      // Check if the screen is small\n      this.isScreenSmall = !matchingAliases.includes('md');\n    });\n  }\n  /**\n   * On destroy\n   */\n  ngOnDestroy() {\n    // Unsubscribe from all subscriptions\n    this._unsubscribeAll.next(null);\n    this._unsubscribeAll.complete();\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Public methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * Toggle navigation\n   *\n   * @param name\n   */\n  toggleNavigation(name) {\n    // Get the navigation\n    const navigation = this._fuseNavigationService.getComponent(name);\n    if (navigation) {\n      // Toggle the opened status\n      navigation.toggle();\n    }\n  }\n  static #_ = this.ɵfac = function ModernLayoutComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ModernLayoutComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.NavigationService), i0.ɵɵdirectiveInject(i3.FuseMediaWatcherService), i0.ɵɵdirectiveInject(i4.FuseNavigationService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ModernLayoutComponent,\n    selectors: [[\"modern-layout\"]],\n    decls: 23,\n    vars: 7,\n    consts: [[\"quickChat\", \"quickChat\"], [1, \"dark\", \"bg-gray-900\", \"print:hidden\", 3, \"mode\", \"name\", \"navigation\", \"opened\"], [1, \"flex\", \"w-full\", \"min-w-0\", \"flex-auto\", \"flex-col\"], [1, \"bg-card\", \"relative\", \"z-49\", \"flex\", \"h-16\", \"w-full\", \"flex-0\", \"items-center\", \"px-4\", \"shadow\", \"dark:border-b\", \"dark:bg-transparent\", \"dark:shadow-none\", \"sm:h-20\", \"md:px-6\", \"print:hidden\"], [\"mat-icon-button\", \"\"], [1, \"ml-auto\", \"flex\", \"items-center\", \"space-x-0.5\", \"pl-2\", \"sm:space-x-2\"], [1, \"hidden\", \"md:block\"], [3, \"appearance\"], [\"mat-icon-button\", \"\", 1, \"lg:hidden\", 3, \"click\"], [3, \"svgIcon\"], [1, \"flex\", \"w-full\", \"flex-auto\", \"flex-col\"], [1, \"bg-card\", \"relative\", \"z-49\", \"flex\", \"h-14\", \"w-full\", \"flex-0\", \"items-center\", \"border-t\", \"px-4\", \"dark:bg-transparent\", \"sm:h-20\", \"md:px-6\", \"print:hidden\"], [1, \"text-secondary\", \"font-medium\"], [\"fuseVerticalNavigationContentHeader\", \"\"], [1, \"flex\", \"h-20\", \"items-center\", \"px-8\", \"pt-6\"], [\"src\", \"images/logo/logo-text-on-dark.svg\", \"alt\", \"Logo image\", 1, \"w-24\"], [1, \"mx-2\", \"flex\", \"items-center\", \"lg:mr-8\"], [1, \"hidden\", \"lg:flex\"], [\"src\", \"images/logo/logo-text.svg\", 1, \"w-24\", \"dark:hidden\"], [\"src\", \"images/logo/logo-text-on-dark.svg\", 1, \"hidden\", \"w-24\", \"dark:flex\"], [\"src\", \"images/logo/logo.svg\", 1, \"flex\", \"w-8\", \"lg:hidden\"], [1, \"mr-2\", 3, \"name\", \"navigation\"], [\"mat-icon-button\", \"\", 3, \"click\"]],\n    template: function ModernLayoutComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelement(0, \"fuse-loading-bar\");\n        i0.ɵɵconditionalCreate(1, ModernLayoutComponent_Conditional_1_Template, 4, 4, \"fuse-vertical-navigation\", 1);\n        i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3);\n        i0.ɵɵconditionalCreate(4, ModernLayoutComponent_Conditional_4_Template, 6, 2);\n        i0.ɵɵconditionalCreate(5, ModernLayoutComponent_Conditional_5_Template, 2, 1, \"button\", 4);\n        i0.ɵɵelementStart(6, \"div\", 5);\n        i0.ɵɵelement(7, \"languages\")(8, \"fuse-fullscreen\", 6)(9, \"search\", 7)(10, \"shortcuts\")(11, \"messages\")(12, \"notifications\");\n        i0.ɵɵelementStart(13, \"button\", 8);\n        i0.ɵɵlistener(\"click\", function ModernLayoutComponent_Template_button_click_13_listener() {\n          i0.ɵɵrestoreView(_r1);\n          const quickChat_r4 = i0.ɵɵreference(22);\n          return i0.ɵɵresetView(quickChat_r4.toggle());\n        });\n        i0.ɵɵelement(14, \"mat-icon\", 9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(15, \"user\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(16, \"div\", 10);\n        i0.ɵɵconditionalCreate(17, ModernLayoutComponent_Conditional_17_Template, 1, 0, \"router-outlet\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(18, \"div\", 11)(19, \"span\", 12);\n        i0.ɵɵtext(20);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelement(21, \"quick-chat\", null, 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.isScreenSmall ? 1 : -1);\n        i0.ɵɵadvance(3);\n        i0.ɵɵconditional(!ctx.isScreenSmall ? 4 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.isScreenSmall ? 5 : -1);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"appearance\", \"bar\");\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"svgIcon\", \"heroicons_outline:chat-bubble-left-right\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵconditional(true ? 17 : -1);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\"Fuse \\u00A9 \", ctx.currentYear);\n      }\n    },\n    dependencies: [FuseLoadingBarComponent, FuseVerticalNavigationComponent, FuseHorizontalNavigationComponent, MatButtonModule, i5.MatIconButton, MatIconModule, i6.MatIcon, LanguagesComponent, FuseFullscreenComponent, SearchComponent, ShortcutsComponent, MessagesComponent, NotificationsComponent, UserComponent, RouterOutlet, QuickChatComponent],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["MatButtonModule", "MatIconModule", "RouterOutlet", "FuseFullscreenComponent", "FuseLoadingBarComponent", "FuseHorizontalNavigationComponent", "FuseVerticalNavigationComponent", "LanguagesComponent", "MessagesComponent", "NotificationsComponent", "QuickChatComponent", "SearchComponent", "ShortcutsComponent", "UserComponent", "Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵelementContainerStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵproperty", "ctx_r1", "navigation", "default", "ɵɵadvance", "horizontal", "ɵɵlistener", "ModernLayoutComponent_Conditional_5_Template_button_click_0_listener", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "ɵɵresetView", "toggleNavigation", "ModernLayoutComponent", "constructor", "_activatedRoute", "_router", "_navigationService", "_fuseMediaWatcherService", "_fuseNavigationService", "_unsubscribeAll", "currentYear", "Date", "getFullYear", "ngOnInit", "navigation$", "pipe", "subscribe", "onMediaChange$", "matchingAliases", "isScreenSmall", "includes", "ngOnDestroy", "next", "complete", "name", "getComponent", "toggle", "_", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "NavigationService", "i3", "FuseMediaWatcherService", "i4", "FuseNavigationService", "_2", "selectors", "decls", "vars", "consts", "template", "ModernLayoutComponent_Template", "rf", "ctx", "ɵɵconditionalCreate", "ModernLayoutComponent_Conditional_1_Template", "ModernLayoutComponent_Conditional_4_Template", "ModernLayoutComponent_Conditional_5_Template", "ModernLayoutComponent_Template_button_click_13_listener", "_r1", "quickChat_r4", "ɵɵreference", "ModernLayoutComponent_Conditional_17_Template", "ɵɵtext", "ɵɵconditional", "ɵɵtextInterpolate1", "i5", "MatIconButton", "i6", "MatIcon", "encapsulation"], "sources": ["D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\app\\layout\\layouts\\horizontal\\modern\\modern.component.ts", "D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\app\\layout\\layouts\\horizontal\\modern\\modern.component.html"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>nit, ViewEncapsulation } from '@angular/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { ActivatedRoute, Router, RouterOutlet } from '@angular/router';\nimport { FuseFullscreenComponent } from '@fuse/components/fullscreen';\nimport { FuseLoadingBarComponent } from '@fuse/components/loading-bar';\nimport {\n    FuseHorizontalNavigationComponent,\n    FuseNavigationService,\n    FuseVerticalNavigationComponent,\n} from '@fuse/components/navigation';\nimport { FuseMediaWatcherService } from '@fuse/services/media-watcher';\nimport { NavigationService } from 'app/core/navigation/navigation.service';\nimport { Navigation } from 'app/core/navigation/navigation.types';\nimport { LanguagesComponent } from 'app/layout/common/languages/languages.component';\nimport { MessagesComponent } from 'app/layout/common/messages/messages.component';\nimport { NotificationsComponent } from 'app/layout/common/notifications/notifications.component';\nimport { QuickChatComponent } from 'app/layout/common/quick-chat/quick-chat.component';\nimport { SearchComponent } from 'app/layout/common/search/search.component';\nimport { ShortcutsComponent } from 'app/layout/common/shortcuts/shortcuts.component';\nimport { UserComponent } from 'app/layout/common/user/user.component';\nimport { Subject, takeUntil } from 'rxjs';\n\n@Component({\n    selector: 'modern-layout',\n    templateUrl: './modern.component.html',\n    encapsulation: ViewEncapsulation.None,\n    imports: [\n        FuseLoadingBarComponent,\n        FuseVerticalNavigationComponent,\n        FuseHorizontalNavigationComponent,\n        MatButtonModule,\n        MatIconModule,\n        LanguagesComponent,\n        FuseFullscreenComponent,\n        SearchComponent,\n        ShortcutsComponent,\n        MessagesComponent,\n        NotificationsComponent,\n        UserComponent,\n        RouterOutlet,\n        QuickChatComponent,\n    ],\n})\nexport class ModernLayoutComponent implements OnInit, OnDestroy {\n    isScreenSmall: boolean;\n    navigation: Navigation;\n    private _unsubscribeAll: Subject<any> = new Subject<any>();\n\n    /**\n     * Constructor\n     */\n    constructor(\n        private _activatedRoute: ActivatedRoute,\n        private _router: Router,\n        private _navigationService: NavigationService,\n        private _fuseMediaWatcherService: FuseMediaWatcherService,\n        private _fuseNavigationService: FuseNavigationService\n    ) {}\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Accessors\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * Getter for current year\n     */\n    get currentYear(): number {\n        return new Date().getFullYear();\n    }\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Lifecycle hooks\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * On init\n     */\n    ngOnInit(): void {\n        // Subscribe to navigation data\n        this._navigationService.navigation$\n            .pipe(takeUntil(this._unsubscribeAll))\n            .subscribe((navigation: Navigation) => {\n                this.navigation = navigation;\n            });\n\n        // Subscribe to media changes\n        this._fuseMediaWatcherService.onMediaChange$\n            .pipe(takeUntil(this._unsubscribeAll))\n            .subscribe(({ matchingAliases }) => {\n                // Check if the screen is small\n                this.isScreenSmall = !matchingAliases.includes('md');\n            });\n    }\n\n    /**\n     * On destroy\n     */\n    ngOnDestroy(): void {\n        // Unsubscribe from all subscriptions\n        this._unsubscribeAll.next(null);\n        this._unsubscribeAll.complete();\n    }\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Public methods\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * Toggle navigation\n     *\n     * @param name\n     */\n    toggleNavigation(name: string): void {\n        // Get the navigation\n        const navigation =\n            this._fuseNavigationService.getComponent<FuseVerticalNavigationComponent>(\n                name\n            );\n\n        if (navigation) {\n            // Toggle the opened status\n            navigation.toggle();\n        }\n    }\n}\n", "<!-- Loading bar -->\n<fuse-loading-bar></fuse-loading-bar>\n\n<!-- Navigation -->\n@if (isScreenSmall) {\n    <fuse-vertical-navigation\n        class=\"dark bg-gray-900 print:hidden\"\n        [mode]=\"'over'\"\n        [name]=\"'mainNavigation'\"\n        [navigation]=\"navigation.default\"\n        [opened]=\"false\"\n    >\n        <!-- Navigation header hook -->\n        <ng-container fuseVerticalNavigationContentHeader>\n            <!-- Logo -->\n            <div class=\"flex h-20 items-center px-8 pt-6\">\n                <img\n                    class=\"w-24\"\n                    src=\"images/logo/logo-text-on-dark.svg\"\n                    alt=\"Logo image\"\n                />\n            </div>\n        </ng-container>\n    </fuse-vertical-navigation>\n}\n\n<!-- Wrapper -->\n<div class=\"flex w-full min-w-0 flex-auto flex-col\">\n    <!-- Header -->\n    <div\n        class=\"bg-card relative z-49 flex h-16 w-full flex-0 items-center px-4 shadow dark:border-b dark:bg-transparent dark:shadow-none sm:h-20 md:px-6 print:hidden\"\n    >\n        @if (!isScreenSmall) {\n            <!-- Logo -->\n            <div class=\"mx-2 flex items-center lg:mr-8\">\n                <div class=\"hidden lg:flex\">\n                    <img\n                        class=\"w-24 dark:hidden\"\n                        src=\"images/logo/logo-text.svg\"\n                    />\n                    <img\n                        class=\"hidden w-24 dark:flex\"\n                        src=\"images/logo/logo-text-on-dark.svg\"\n                    />\n                </div>\n                <img class=\"flex w-8 lg:hidden\" src=\"images/logo/logo.svg\" />\n            </div>\n            <!-- Horizontal navigation -->\n            <fuse-horizontal-navigation\n                class=\"mr-2\"\n                [name]=\"'mainNavigation'\"\n                [navigation]=\"navigation.horizontal\"\n            ></fuse-horizontal-navigation>\n        }\n        <!-- Navigation toggle button -->\n        @if (isScreenSmall) {\n            <button\n                mat-icon-button\n                (click)=\"toggleNavigation('mainNavigation')\"\n            >\n                <mat-icon [svgIcon]=\"'heroicons_outline:bars-3'\"></mat-icon>\n            </button>\n        }\n        <!-- Components -->\n        <div class=\"ml-auto flex items-center space-x-0.5 pl-2 sm:space-x-2\">\n            <languages></languages>\n            <fuse-fullscreen class=\"hidden md:block\"></fuse-fullscreen>\n            <search [appearance]=\"'bar'\"></search>\n            <shortcuts></shortcuts>\n            <messages></messages>\n            <notifications></notifications>\n            <button\n                class=\"lg:hidden\"\n                mat-icon-button\n                (click)=\"quickChat.toggle()\"\n            >\n                <mat-icon\n                    [svgIcon]=\"'heroicons_outline:chat-bubble-left-right'\"\n                ></mat-icon>\n            </button>\n            <user></user>\n        </div>\n    </div>\n\n    <!-- Content -->\n    <div class=\"flex w-full flex-auto flex-col\">\n        <!-- *ngIf=\"true\" hack is required here for router-outlet to work correctly.\n             Otherwise, layout changes won't be registered and the view won't be updated! -->\n        @if (true) {\n            <router-outlet></router-outlet>\n        }\n    </div>\n\n    <!-- Footer -->\n    <div\n        class=\"bg-card relative z-49 flex h-14 w-full flex-0 items-center border-t px-4 dark:bg-transparent sm:h-20 md:px-6 print:hidden\"\n    >\n        <span class=\"text-secondary font-medium\"\n            >Fuse &copy; {{ currentYear }}</span\n        >\n    </div>\n</div>\n\n<!-- Quick chat -->\n<quick-chat #quickChat=\"quickChat\"></quick-chat>\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAAiCC,YAAY,QAAQ,iBAAiB;AACtE,SAASC,uBAAuB,QAAQ,6BAA6B;AACrE,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SACIC,iCAAiC,EAEjCC,+BAA+B,QAC5B,6BAA6B;AAIpC,SAASC,kBAAkB,QAAQ,iDAAiD;AACpF,SAASC,iBAAiB,QAAQ,+CAA+C;AACjF,SAASC,sBAAsB,QAAQ,yDAAyD;AAChG,SAASC,kBAAkB,QAAQ,mDAAmD;AACtF,SAASC,eAAe,QAAQ,2CAA2C;AAC3E,SAASC,kBAAkB,QAAQ,iDAAiD;AACpF,SAASC,aAAa,QAAQ,uCAAuC;AACrE,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;IChBrCC,EAAA,CAAAC,cAAA,kCAMC;IAEGD,EAAA,CAAAE,uBAAA,OAAkD;IAE9CF,EAAA,CAAAC,cAAA,cAA8C;IAC1CD,EAAA,CAAAG,SAAA,cAIE;IACNH,EAAA,CAAAI,YAAA,EAAM;;IAEdJ,EAAA,CAAAI,YAAA,EAA2B;;;;IAbvBJ,EAHA,CAAAK,UAAA,gBAAe,0BACU,eAAAC,MAAA,CAAAC,UAAA,CAAAC,OAAA,CACQ,iBACjB;;;;;IAyBRR,EADJ,CAAAC,cAAA,cAA4C,cACZ;IAKxBD,EAJA,CAAAG,SAAA,cAGE,cAIA;IACNH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAG,SAAA,cAA6D;IACjEH,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAG,SAAA,qCAI8B;;;;IAF1BH,EAAA,CAAAS,SAAA,GAAyB;IACzBT,EADA,CAAAK,UAAA,0BAAyB,eAAAC,MAAA,CAAAC,UAAA,CAAAG,UAAA,CACW;;;;;;IAKxCV,EAAA,CAAAC,cAAA,iBAGC;IADGD,EAAA,CAAAW,UAAA,mBAAAC,qEAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAgB,WAAA,CAASV,MAAA,CAAAW,gBAAA,CAAiB,gBAAgB,CAAC;IAAA,EAAC;IAE5CjB,EAAA,CAAAG,SAAA,kBAA4D;IAChEH,EAAA,CAAAI,YAAA,EAAS;;;IADKJ,EAAA,CAAAS,SAAA,EAAsC;IAAtCT,EAAA,CAAAK,UAAA,uCAAsC;;;;;IA6BpDL,EAAA,CAAAG,SAAA,oBAA+B;;;AD7C3C,OAAM,MAAOe,qBAAqB;EAK9B;;;EAGAC,YACYC,eAA+B,EAC/BC,OAAe,EACfC,kBAAqC,EACrCC,wBAAiD,EACjDC,sBAA6C;IAJ7C,KAAAJ,eAAe,GAAfA,eAAe;IACf,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,sBAAsB,GAAtBA,sBAAsB;IAV1B,KAAAC,eAAe,GAAiB,IAAI3B,OAAO,EAAO;EAWvD;EAEH;EACA;EACA;EAEA;;;EAGA,IAAI4B,WAAWA,CAAA;IACX,OAAO,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;EACnC;EAEA;EACA;EACA;EAEA;;;EAGAC,QAAQA,CAAA;IACJ;IACA,IAAI,CAACP,kBAAkB,CAACQ,WAAW,CAC9BC,IAAI,CAAChC,SAAS,CAAC,IAAI,CAAC0B,eAAe,CAAC,CAAC,CACrCO,SAAS,CAAEzB,UAAsB,IAAI;MAClC,IAAI,CAACA,UAAU,GAAGA,UAAU;IAChC,CAAC,CAAC;IAEN;IACA,IAAI,CAACgB,wBAAwB,CAACU,cAAc,CACvCF,IAAI,CAAChC,SAAS,CAAC,IAAI,CAAC0B,eAAe,CAAC,CAAC,CACrCO,SAAS,CAAC,CAAC;MAAEE;IAAe,CAAE,KAAI;MAC/B;MACA,IAAI,CAACC,aAAa,GAAG,CAACD,eAAe,CAACE,QAAQ,CAAC,IAAI,CAAC;IACxD,CAAC,CAAC;EACV;EAEA;;;EAGAC,WAAWA,CAAA;IACP;IACA,IAAI,CAACZ,eAAe,CAACa,IAAI,CAAC,IAAI,CAAC;IAC/B,IAAI,CAACb,eAAe,CAACc,QAAQ,EAAE;EACnC;EAEA;EACA;EACA;EAEA;;;;;EAKAtB,gBAAgBA,CAACuB,IAAY;IACzB;IACA,MAAMjC,UAAU,GACZ,IAAI,CAACiB,sBAAsB,CAACiB,YAAY,CACpCD,IAAI,CACP;IAEL,IAAIjC,UAAU,EAAE;MACZ;MACAA,UAAU,CAACmC,MAAM,EAAE;IACvB;EACJ;EAAC,QAAAC,CAAA,G;qCAhFQzB,qBAAqB,EAAAlB,EAAA,CAAA4C,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA9C,EAAA,CAAA4C,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA/C,EAAA,CAAA4C,iBAAA,CAAAI,EAAA,CAAAC,iBAAA,GAAAjD,EAAA,CAAA4C,iBAAA,CAAAM,EAAA,CAAAC,uBAAA,GAAAnD,EAAA,CAAA4C,iBAAA,CAAAQ,EAAA,CAAAC,qBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAArBpC,qBAAqB;IAAAqC,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QC3ClC7D,EAAA,CAAAG,SAAA,uBAAqC;QAGrCH,EAAA,CAAA+D,mBAAA,IAAAC,4CAAA,sCAAqB;QAyBjBhE,EAFJ,CAAAC,cAAA,aAAoD,aAI/C;QACGD,EAAA,CAAA+D,mBAAA,IAAAE,4CAAA,OAAsB;QAuBtBjE,EAAA,CAAA+D,mBAAA,IAAAG,4CAAA,oBAAqB;QASrBlE,EAAA,CAAAC,cAAA,aAAqE;QAMjED,EALA,CAAAG,SAAA,gBAAuB,yBACoC,gBACrB,iBACf,gBACF,qBACU;QAC/BH,EAAA,CAAAC,cAAA,iBAIC;QADGD,EAAA,CAAAW,UAAA,mBAAAwD,wDAAA;UAAAnE,EAAA,CAAAa,aAAA,CAAAuD,GAAA;UAAA,MAAAC,YAAA,GAAArE,EAAA,CAAAsE,WAAA;UAAA,OAAAtE,EAAA,CAAAgB,WAAA,CAASqD,YAAA,CAAA3B,MAAA,EAAkB;QAAA,EAAC;QAE5B1C,EAAA,CAAAG,SAAA,mBAEY;QAChBH,EAAA,CAAAI,YAAA,EAAS;QACTJ,EAAA,CAAAG,SAAA,YAAa;QAErBH,EADI,CAAAI,YAAA,EAAM,EACJ;QAGNJ,EAAA,CAAAC,cAAA,eAA4C;QAGxCD,EAAA,CAAA+D,mBAAA,KAAAQ,6CAAA,wBAAY;QAGhBvE,EAAA,CAAAI,YAAA,EAAM;QAMFJ,EAHJ,CAAAC,cAAA,eAEC,gBAEQ;QAAAD,EAAA,CAAAwE,MAAA,IAA6B;QAG1CxE,EAH0C,CAAAI,YAAA,EACjC,EACC,EACJ;QAGNJ,EAAA,CAAAG,SAAA,2BAAgD;;;QApGhDH,EAAA,CAAAS,SAAA,EAoBC;QApBDT,EAAA,CAAAyE,aAAA,CAAAX,GAAA,CAAA3B,aAAA,UAoBC;QAQOnC,EAAA,CAAAS,SAAA,GAqBC;QArBDT,EAAA,CAAAyE,aAAA,EAAAX,GAAA,CAAA3B,aAAA,UAqBC;QAEDnC,EAAA,CAAAS,SAAA,EAOC;QAPDT,EAAA,CAAAyE,aAAA,CAAAX,GAAA,CAAA3B,aAAA,UAOC;QAKWnC,EAAA,CAAAS,SAAA,GAAoB;QAApBT,EAAA,CAAAK,UAAA,qBAAoB;QAUpBL,EAAA,CAAAS,SAAA,GAAsD;QAAtDT,EAAA,CAAAK,UAAA,uDAAsD;QAWlEL,EAAA,CAAAS,SAAA,GAEC;QAFDT,EAAA,CAAAyE,aAAA,gBAEC;QAQIzE,EAAA,CAAAS,SAAA,GAA6B;QAA7BT,EAAA,CAAA0E,kBAAA,iBAAAZ,GAAA,CAAApC,WAAA,CAA6B;;;mBDtElCtC,uBAAuB,EACvBE,+BAA+B,EAC/BD,iCAAiC,EACjCL,eAAe,EAAA2F,EAAA,CAAAC,aAAA,EACf3F,aAAa,EAAA4F,EAAA,CAAAC,OAAA,EACbvF,kBAAkB,EAClBJ,uBAAuB,EACvBQ,eAAe,EACfC,kBAAkB,EAClBJ,iBAAiB,EACjBC,sBAAsB,EACtBI,aAAa,EACbX,YAAY,EACZQ,kBAAkB;IAAAqF,aAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}