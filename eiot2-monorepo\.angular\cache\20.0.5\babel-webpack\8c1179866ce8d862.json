{"ast": null, "code": "import { TextFieldModule } from '@angular/cdk/text-field';\nimport { DOCUMENT, DatePipe, NgClass, NgTemplateOutlet } from '@angular/common';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { FuseScrollbarDirective } from '@fuse/directives/scrollbar';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"app/layout/common/quick-chat/quick-chat.service\";\nimport * as i2 from \"@angular/cdk/overlay\";\nimport * as i3 from \"@angular/material/icon\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/form-field\";\nimport * as i6 from \"@angular/material/input\";\nimport * as i7 from \"@angular/cdk/text-field\";\nconst _c0 = [\"messageInput\"];\nconst _c1 = (a0, a1) => ({\n  \"-translate-x-full shadow sm:-translate-x-96 lg:-translate-x-80\": a0,\n  \"translate-x-0\": a1\n});\nconst _c2 = () => ({\n  wheelPropagation: false\n});\nconst _c3 = (a0, a1) => ({\n  \"dark:hover:bg-hover hover:bg-gray-100\": a0,\n  \"bg-primary-50 dark:bg-hover\": a1\n});\nconst _c4 = (a0, a1, a2, a3) => ({\n  \"items-end\": a0,\n  \"items-start\": a1,\n  \"mt-0.5\": a2,\n  \"mt-3\": a3\n});\nconst _c5 = (a0, a1) => ({\n  \"bg-blue-500 text-blue-50\": a0,\n  \"bg-gray-500 text-gray-50\": a1\n});\nconst _c6 = (a0, a1) => ({\n  \"-right-1 -mr-px mb-px text-blue-500\": a0,\n  \"-left-1 -ml-px mb-px -scale-x-1 text-gray-500\": a1\n});\nconst _c7 = (a0, a1) => ({\n  \"mr-3\": a0,\n  \"ml-3\": a1\n});\nfunction QuickChatComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 13);\n    i0.ɵɵelement(2, \"mat-icon\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 15);\n    i0.ɵɵtext(4, \" Team Chat \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 16);\n    i0.ɵɵelement(6, \"mat-icon\", 17);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"svgIcon\", \"heroicons_outline:chat-bubble-left-right\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"svgIcon\", \"heroicons_outline:x-mark\");\n  }\n}\nfunction QuickChatComponent_Conditional_4_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 19);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.chat.contact.avatar, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction QuickChatComponent_Conditional_4_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.chat.contact.name.charAt(0), \" \");\n  }\n}\nfunction QuickChatComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 18);\n    i0.ɵɵconditionalCreate(2, QuickChatComponent_Conditional_4_Conditional_2_Template, 1, 1, \"img\", 19);\n    i0.ɵɵconditionalCreate(3, QuickChatComponent_Conditional_4_Conditional_3_Template, 2, 1, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 21);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 16);\n    i0.ɵɵelement(7, \"mat-icon\", 17);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(ctx_r1.chat.contact.avatar ? 2 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(!ctx_r1.chat.contact.avatar ? 3 : -1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.chat.contact.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"svgIcon\", \"heroicons_outline:x-mark\");\n  }\n}\nfunction QuickChatComponent_For_9_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 25);\n  }\n  if (rf & 2) {\n    const chat_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"ring-primary-50\", ctx_r1.selectedChat && ctx_r1.selectedChat.id === chat_r4.id);\n  }\n}\nfunction QuickChatComponent_For_9_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 19);\n  }\n  if (rf & 2) {\n    const chat_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"src\", chat_r4.contact.avatar, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction QuickChatComponent_For_9_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const chat_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", chat_r4.contact.name.charAt(0), \" \");\n  }\n}\nfunction QuickChatComponent_For_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵlistener(\"click\", function QuickChatComponent_For_9_Template_div_click_0_listener() {\n      const chat_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.selectChat(chat_r4.id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 23);\n    i0.ɵɵconditionalCreate(2, QuickChatComponent_For_9_Conditional_2_Template, 1, 2, \"div\", 24);\n    i0.ɵɵconditionalCreate(3, QuickChatComponent_For_9_Conditional_3_Template, 1, 1, \"img\", 19);\n    i0.ɵɵconditionalCreate(4, QuickChatComponent_For_9_Conditional_4_Template, 2, 1, \"div\", 20);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const chat_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(4, _c3, !ctx_r1.selectedChat || ctx_r1.selectedChat.id !== chat_r4.id, ctx_r1.selectedChat && ctx_r1.selectedChat.id === chat_r4.id));\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(chat_r4.unreadCount > 0 ? 2 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(chat_r4.contact.avatar ? 3 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(!chat_r4.contact.avatar ? 4 : -1);\n  }\n}\nfunction QuickChatComponent_Conditional_11_For_3_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵelement(1, \"div\", 39);\n    i0.ɵɵelementStart(2, \"div\", 40);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"div\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(4, 1, message_r5.createdAt, \"longDate\"), \" \");\n  }\n}\nfunction QuickChatComponent_Conditional_11_For_3_Conditional_5_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction QuickChatComponent_Conditional_11_For_3_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtemplate(1, QuickChatComponent_Conditional_11_For_3_Conditional_5_ng_container_1_Template, 1, 0, \"ng-container\", 41);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵnextContext(2);\n    const speechBubbleExtension_r6 = i0.ɵɵreference(14);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c6, message_r5.isMine, !message_r5.isMine));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", speechBubbleExtension_r6);\n  }\n}\nfunction QuickChatComponent_Conditional_11_For_3_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(5, _c7, message_r5.isMine, !message_r5.isMine));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(2, 2, message_r5.createdAt, \"HH:mm\"), \" \");\n  }\n}\nfunction QuickChatComponent_Conditional_11_For_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵconditionalCreate(0, QuickChatComponent_Conditional_11_For_3_Conditional_0_Template, 6, 4, \"div\", 33);\n    i0.ɵɵpipe(1, \"date\");\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementStart(3, \"div\", 34)(4, \"div\", 35);\n    i0.ɵɵconditionalCreate(5, QuickChatComponent_Conditional_11_For_3_Conditional_5_Template, 2, 5, \"div\", 36);\n    i0.ɵɵelement(6, \"div\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵconditionalCreate(7, QuickChatComponent_Conditional_11_For_3_Conditional_7_Template, 3, 8, \"div\", 38);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const message_r5 = ctx.$implicit;\n    const ɵ$index_68_r7 = ctx.$index;\n    const ɵ$count_68_r8 = ctx.$count;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵconditional(ɵ$index_68_r7 === 0 || i0.ɵɵpipeBind2(1, 6, ctx_r1.chat.messages[ɵ$index_68_r7 - 1].createdAt, \"d\") !== i0.ɵɵpipeBind2(2, 9, message_r5.createdAt, \"d\") ? 0 : -1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(12, _c4, message_r5.isMine, !message_r5.isMine, ɵ$index_68_r7 > 0 && ctx_r1.chat.messages[ɵ$index_68_r7 - 1].isMine === message_r5.isMine, ɵ$index_68_r7 > 0 && ctx_r1.chat.messages[ɵ$index_68_r7 - 1].isMine !== message_r5.isMine));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(17, _c5, message_r5.isMine, !message_r5.isMine));\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ɵ$index_68_r7 === ɵ$count_68_r8 - 1 || ctx_r1.chat.messages[ɵ$index_68_r7 + 1].isMine !== message_r5.isMine ? 5 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", message_r5.value, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ɵ$index_68_r7 === 0 || ɵ$index_68_r7 === ɵ$count_68_r8 - 1 || ctx_r1.chat.messages[ɵ$index_68_r7 + 1].isMine !== message_r5.isMine || ctx_r1.chat.messages[ɵ$index_68_r7 + 1].createdAt !== message_r5.createdAt ? 7 : -1);\n  }\n}\nfunction QuickChatComponent_Conditional_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27);\n    i0.ɵɵrepeaterCreate(2, QuickChatComponent_Conditional_11_For_3_Template, 8, 20, null, null, i0.ɵɵcomponentInstance().trackByFn, true);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 28)(5, \"mat-form-field\", 29);\n    i0.ɵɵelement(6, \"textarea\", 30, 1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 31)(9, \"button\", 32);\n    i0.ɵɵelement(10, \"mat-icon\", 17);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵrepeater(ctx_r1.chat.messages);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"subscriptSizing\", \"dynamic\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"svgIcon\", \"heroicons_outline:paper-airplane\");\n  }\n}\nfunction QuickChatComponent_Conditional_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵelement(1, \"mat-icon\", 42);\n    i0.ɵɵelementStart(2, \"div\", 43);\n    i0.ɵɵtext(3, \" Select a conversation \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"svgIcon\", \"heroicons_outline:chat-bubble-bottom-center-text\");\n  }\n}\nfunction QuickChatComponent_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 44)(1, \"g\", 45);\n    i0.ɵɵelement(2, \"path\", 46);\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class QuickChatComponent {\n  /**\n   * Constructor\n   */\n  constructor(_document, _elementRef, _renderer2, _ngZone, _quickChatService, _scrollStrategyOptions) {\n    this._document = _document;\n    this._elementRef = _elementRef;\n    this._renderer2 = _renderer2;\n    this._ngZone = _ngZone;\n    this._quickChatService = _quickChatService;\n    this._scrollStrategyOptions = _scrollStrategyOptions;\n    this.opened = false;\n    this._scrollStrategy = this._scrollStrategyOptions.block();\n    this._unsubscribeAll = new Subject();\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Decorated methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * Host binding for component classes\n   */\n  get classList() {\n    return {\n      'quick-chat-opened': this.opened\n    };\n  }\n  /**\n   * Resize on 'input' and 'ngModelChange' events\n   *\n   * @private\n   */\n  _resizeMessageInput() {\n    // This doesn't need to trigger Angular's change detection by itself\n    this._ngZone.runOutsideAngular(() => {\n      setTimeout(() => {\n        // Set the height to 'auto' so we can correctly read the scrollHeight\n        this.messageInput.nativeElement.style.height = 'auto';\n        // Get the scrollHeight and subtract the vertical padding\n        this.messageInput.nativeElement.style.height = `${this.messageInput.nativeElement.scrollHeight}px`;\n      });\n    });\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Lifecycle hooks\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * On init\n   */\n  ngOnInit() {\n    // Chat\n    this._quickChatService.chat$.pipe(takeUntil(this._unsubscribeAll)).subscribe(chat => {\n      this.chat = chat;\n    });\n    // Chats\n    this._quickChatService.chats$.pipe(takeUntil(this._unsubscribeAll)).subscribe(chats => {\n      this.chats = chats;\n    });\n    // Selected chat\n    this._quickChatService.chat$.pipe(takeUntil(this._unsubscribeAll)).subscribe(chat => {\n      this.selectedChat = chat;\n    });\n  }\n  /**\n   * After view init\n   */\n  ngAfterViewInit() {\n    // Fix for Firefox.\n    //\n    // Because 'position: sticky' doesn't work correctly inside a 'position: fixed' parent,\n    // adding the '.cdk-global-scrollblock' to the html element breaks the navigation's position.\n    // This fixes the problem by reading the 'top' value from the html element and adding it as a\n    // 'marginTop' to the navigation itself.\n    this._mutationObserver = new MutationObserver(mutations => {\n      mutations.forEach(mutation => {\n        const mutationTarget = mutation.target;\n        if (mutation.attributeName === 'class') {\n          if (mutationTarget.classList.contains('cdk-global-scrollblock')) {\n            const top = parseInt(mutationTarget.style.top, 10);\n            this._renderer2.setStyle(this._elementRef.nativeElement, 'margin-top', `${Math.abs(top)}px`);\n          } else {\n            this._renderer2.setStyle(this._elementRef.nativeElement, 'margin-top', null);\n          }\n        }\n      });\n    });\n    this._mutationObserver.observe(this._document.documentElement, {\n      attributes: true,\n      attributeFilter: ['class']\n    });\n  }\n  /**\n   * On destroy\n   */\n  ngOnDestroy() {\n    // Disconnect the mutation observer\n    this._mutationObserver.disconnect();\n    // Unsubscribe from all subscriptions\n    this._unsubscribeAll.next(null);\n    this._unsubscribeAll.complete();\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Public methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * Open the panel\n   */\n  open() {\n    // Return if the panel has already opened\n    if (this.opened) {\n      return;\n    }\n    // Open the panel\n    this._toggleOpened(true);\n  }\n  /**\n   * Close the panel\n   */\n  close() {\n    // Return if the panel has already closed\n    if (!this.opened) {\n      return;\n    }\n    // Close the panel\n    this._toggleOpened(false);\n  }\n  /**\n   * Toggle the panel\n   */\n  toggle() {\n    if (this.opened) {\n      this.close();\n    } else {\n      this.open();\n    }\n  }\n  /**\n   * Select the chat\n   *\n   * @param id\n   */\n  selectChat(id) {\n    // Open the panel\n    this._toggleOpened(true);\n    // Get the chat data\n    this._quickChatService.getChatById(id).subscribe();\n  }\n  /**\n   * Track by function for ngFor loops\n   *\n   * @param index\n   * @param item\n   */\n  trackByFn(index, item) {\n    return item.id || index;\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Private methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * Show the backdrop\n   *\n   * @private\n   */\n  _showOverlay() {\n    // Try hiding the overlay in case there is one already opened\n    this._hideOverlay();\n    // Create the backdrop element\n    this._overlay = this._renderer2.createElement('div');\n    // Return if overlay couldn't be create for some reason\n    if (!this._overlay) {\n      return;\n    }\n    // Add a class to the backdrop element\n    this._overlay.classList.add('quick-chat-overlay');\n    // Append the backdrop to the parent of the panel\n    this._renderer2.appendChild(this._elementRef.nativeElement.parentElement, this._overlay);\n    // Enable block scroll strategy\n    this._scrollStrategy.enable();\n    // Add an event listener to the overlay\n    this._overlay.addEventListener('click', () => {\n      this.close();\n    });\n  }\n  /**\n   * Hide the backdrop\n   *\n   * @private\n   */\n  _hideOverlay() {\n    if (!this._overlay) {\n      return;\n    }\n    // If the backdrop still exists...\n    if (this._overlay) {\n      // Remove the backdrop\n      this._overlay.parentNode.removeChild(this._overlay);\n      this._overlay = null;\n    }\n    // Disable block scroll strategy\n    this._scrollStrategy.disable();\n  }\n  /**\n   * Open/close the panel\n   *\n   * @param open\n   * @private\n   */\n  _toggleOpened(open) {\n    // Set the opened\n    this.opened = open;\n    // If the panel opens, show the overlay\n    if (open) {\n      this._showOverlay();\n    }\n    // Otherwise, hide the overlay\n    else {\n      this._hideOverlay();\n    }\n  }\n  static #_ = this.ɵfac = function QuickChatComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || QuickChatComponent)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1.QuickChatService), i0.ɵɵdirectiveInject(i2.ScrollStrategyOptions));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: QuickChatComponent,\n    selectors: [[\"quick-chat\"]],\n    viewQuery: function QuickChatComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messageInput = _t.first);\n      }\n    },\n    hostVars: 2,\n    hostBindings: function QuickChatComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"input\", function QuickChatComponent_input_HostBindingHandler() {\n          return ctx._resizeMessageInput();\n        })(\"ngModelChange\", function QuickChatComponent_ngModelChange_HostBindingHandler() {\n          return ctx._resizeMessageInput();\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.classList);\n      }\n    },\n    exportAs: [\"quickChat\"],\n    decls: 15,\n    vars: 9,\n    consts: [[\"speechBubbleExtension\", \"\"], [\"messageInput\", \"\"], [1, \"fixed\", \"bottom-0\", \"top-0\", \"w-full\", \"sm:w-96\", \"lg:sticky\", \"lg:left-full\", \"lg:h-screen\", \"lg:w-16\", \"lg:shadow\"], [1, \"bg-card\", \"flex\", \"h-full\", \"w-full\", \"flex-col\", \"transition-transform\", \"duration-400\", \"ease-drawer\", \"sm:w-96\", 3, \"ngClass\"], [1, \"quick-chat-header\", \"flex\", \"flex-0\", \"cursor-pointer\", \"items-center\", \"justify-start\", 3, \"click\"], [1, \"flex\", \"flex-auto\", \"items-center\", \"justify-center\"], [1, \"ml-3\", \"flex\", \"flex-auto\", \"items-center\"], [1, \"flex\", \"flex-auto\", \"overflow-hidden\", \"border-t\"], [\"fuseScrollbar\", \"\", 1, \"h-full\", \"w-16\", \"flex-0\", \"overflow-y-auto\", \"overscroll-y-contain\", \"sm:overflow-hidden\", \"sm:overscroll-auto\", 3, \"fuseScrollbarOptions\"], [1, \"flex-auto\"], [1, \"flex\", \"cursor-pointer\", \"items-center\", \"px-4\", \"py-3\", 3, \"ngClass\"], [1, \"flex\", \"flex-auto\", \"flex-col\", \"overflow-hidden\", \"border-l\", \"bg-gray-50\", \"dark:bg-transparent\"], [1, \"flex\", \"h-full\", \"w-full\", \"flex-auto\", \"flex-col\", \"items-center\", \"justify-center\", \"p-4\"], [1, \"flex\", \"w-16\", \"flex-0\", \"items-center\", \"justify-center\"], [1, \"icon-size-6\", 3, \"svgIcon\"], [1, \"text-secondary\", \"text-lg\", \"font-medium\"], [\"mat-icon-button\", \"\", 1, \"ml-auto\", \"mr-4\"], [3, \"svgIcon\"], [1, \"relative\", \"flex\", \"h-10\", \"w-10\", \"flex-0\", \"items-center\", \"justify-center\"], [\"alt\", \"Contact avatar\", 1, \"h-full\", \"w-full\", \"rounded-full\", \"object-cover\", 3, \"src\"], [1, \"flex\", \"h-full\", \"w-full\", \"items-center\", \"justify-center\", \"rounded-full\", \"bg-gray-200\", \"text-lg\", \"uppercase\", \"text-gray-600\", \"dark:bg-gray-700\", \"dark:text-gray-200\"], [1, \"ml-4\", \"truncate\", \"text-lg\", \"font-medium\", \"leading-5\"], [1, \"flex\", \"cursor-pointer\", \"items-center\", \"px-4\", \"py-3\", 3, \"click\", \"ngClass\"], [1, \"relative\", \"flex\", \"h-8\", \"w-8\", \"flex-0\", \"items-center\", \"justify-center\"], [1, \"ring-bg-card\", \"absolute\", \"bottom-0\", \"right-0\", \"-ml-0.5\", \"h-2\", \"w-2\", \"flex-0\", \"rounded-full\", \"bg-primary\", \"text-on-primary\", \"ring-2\", \"dark:bg-primary-500\", \"dark:ring-gray-900\", 3, \"ring-primary-50\"], [1, \"ring-bg-card\", \"absolute\", \"bottom-0\", \"right-0\", \"-ml-0.5\", \"h-2\", \"w-2\", \"flex-0\", \"rounded-full\", \"bg-primary\", \"text-on-primary\", \"ring-2\", \"dark:bg-primary-500\", \"dark:ring-gray-900\"], [1, \"flex\", \"flex-col-reverse\", \"overflow-y-auto\", \"overscroll-y-contain\"], [1, \"flex\", \"flex-auto\", \"shrink\", \"flex-col\", \"p-6\"], [1, \"flex\", \"items-end\", \"border-t\", \"bg-gray-50\", \"p-4\", \"dark:bg-transparent\"], [1, \"fuse-mat-dense\", \"fuse-mat-rounded\", \"fuse-mat-bold\", \"w-full\", 3, \"subscriptSizing\"], [\"matInput\", \"\", \"cdkTextareaAutosize\", \"\"], [1, \"my-px\", \"ml-4\", \"flex\", \"h-11\", \"items-center\"], [\"mat-icon-button\", \"\"], [1, \"-mx-6\", \"my-3\", \"flex\", \"items-center\", \"justify-center\"], [1, \"flex\", \"flex-col\", 3, \"ngClass\"], [1, \"relative\", \"max-w-3/4\", \"rounded-lg\", \"px-3\", \"py-2\", 3, \"ngClass\"], [1, \"absolute\", \"bottom-0\", \"w-3\", 3, \"ngClass\"], [1, \"min-w-4\", \"leading-5\", 3, \"innerHTML\"], [1, \"text-secondary\", \"my-0.5\", \"text-sm\", \"font-medium\", 3, \"ngClass\"], [1, \"flex-auto\", \"border-b\"], [1, \"text-secondary\", \"mx-4\", \"flex-0\", \"text-sm\", \"font-medium\", \"leading-5\"], [4, \"ngTemplateOutlet\"], [1, \"icon-size-24\", 3, \"svgIcon\"], [1, \"text-secondary\", \"mt-4\", \"text-center\", \"text-xl\", \"font-medium\", \"tracking-tight\"], [\"width\", \"100%\", \"height\", \"100%\", \"viewBox\", \"0 0 66 66\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"id\", \"Page-1\", \"stroke\", \"none\", \"stroke-width\", \"1\", \"fill\", \"none\", \"fill-rule\", \"evenodd\"], [\"d\", \"M1.01522827,0.516204834 C-8.83532715,54.3062744 61.7609863,70.5215302 64.8009949,64.3061218 C68.8074951,54.8859711 30.1663208,52.9997559 37.5036011,0.516204834 L1.01522827,0.516204834 Z\", \"fill\", \"currentColor\", \"fill-rule\", \"nonzero\"]],\n    template: function QuickChatComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n        i0.ɵɵlistener(\"click\", function QuickChatComponent_Template_div_click_2_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.toggle());\n        });\n        i0.ɵɵconditionalCreate(3, QuickChatComponent_Conditional_3_Template, 7, 2, \"div\", 5);\n        i0.ɵɵconditionalCreate(4, QuickChatComponent_Conditional_4_Template, 8, 4, \"div\", 6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"div\", 7)(6, \"div\", 8)(7, \"div\", 9);\n        i0.ɵɵrepeaterCreate(8, QuickChatComponent_For_9_Template, 5, 7, \"div\", 10, ctx.trackByFn, true);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 11);\n        i0.ɵɵconditionalCreate(11, QuickChatComponent_Conditional_11_Template, 11, 2)(12, QuickChatComponent_Conditional_12_Template, 4, 1, \"div\", 12);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵtemplate(13, QuickChatComponent_ng_template_13_Template, 3, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(5, _c1, ctx.opened, !ctx.opened));\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(!ctx.opened || ctx.opened && !ctx.selectedChat ? 3 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.opened && ctx.selectedChat ? 4 : -1);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"fuseScrollbarOptions\", i0.ɵɵpureFunction0(8, _c2));\n        i0.ɵɵadvance(2);\n        i0.ɵɵrepeater(ctx.chats);\n        i0.ɵɵadvance(3);\n        i0.ɵɵconditional(ctx.chat ? 11 : 12);\n      }\n    },\n    dependencies: [NgClass, MatIconModule, i3.MatIcon, MatButtonModule, i4.MatIconButton, FuseScrollbarDirective, NgTemplateOutlet, MatFormFieldModule, i5.MatFormField, MatInputModule, i6.MatInput, i7.CdkTextareaAutosize, TextFieldModule, DatePipe],\n    styles: [\"var resource;\\n/******/ (() => { // webpackBootstrap\\n/******/ \\tvar __webpack_modules__ = ({\\n\\n/***/ 926:\\n/*!***********************************************************************************************!*\\\\\\n  !*** ./apps/eiot-admin/src/app/layout/common/quick-chat/quick-chat.component.scss?ngResource ***!\\n  \\\\***********************************************************************************************/\\n/***/ (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/postcss-loader/dist/cjs.js):\\\\nError: Cannot find module 'chroma-js'\\\\nRequire stack:\\\\n- D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\apps\\\\\\\\eiot-admin\\\\\\\\src\\\\\\\\@fuse\\\\\\\\tailwind\\\\\\\\utils\\\\\\\\generate-palette.js\\\\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\\\\n    at Function.resolve (node:internal/modules/helpers:193:19)\\\\n    at _resolve (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:246378)\\\\n    at jiti (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:249092)\\\\n    at D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\apps\\\\\\\\eiot-admin\\\\\\\\src\\\\\\\\@fuse\\\\\\\\tailwind\\\\\\\\utils\\\\\\\\generate-palette.js:1:91\\\\n    at evalModule (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:251913)\\\\n    at jiti (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:249841)\\\\n    at D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\apps\\\\\\\\eiot-admin\\\\\\\\tailwind.config.js:4:25\\\\n    at evalModule (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:251913)\\\\n    at jiti (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:249841)\\\\n    at D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\tailwindcss\\\\\\\\lib\\\\\\\\lib\\\\\\\\load-config.js:48:30\\\\n    at loadConfig (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\tailwindcss\\\\\\\\lib\\\\\\\\lib\\\\\\\\load-config.js:50:6)\\\\n    at getTailwindConfig (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\tailwindcss\\\\\\\\lib\\\\\\\\lib\\\\\\\\setupTrackingContext.js:71:116)\\\\n    at D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\tailwindcss\\\\\\\\lib\\\\\\\\lib\\\\\\\\setupTrackingContext.js:100:92\\\\n    at D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\tailwindcss\\\\\\\\lib\\\\\\\\processTailwindFeatures.js:48:11\\\\n    at plugins (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\tailwindcss\\\\\\\\lib\\\\\\\\plugin.js:38:69)\\\\n    at LazyResult.runOnRoot (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\@angular-devkit\\\\\\\\build-angular\\\\\\\\node_modules\\\\\\\\postcss\\\\\\\\lib\\\\\\\\lazy-result.js:361:16)\\\\n    at LazyResult.runAsync (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\@angular-devkit\\\\\\\\build-angular\\\\\\\\node_modules\\\\\\\\postcss\\\\\\\\lib\\\\\\\\lazy-result.js:290:26)\\\\n    at LazyResult.async (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\@angular-devkit\\\\\\\\build-angular\\\\\\\\node_modules\\\\\\\\postcss\\\\\\\\lib\\\\\\\\lazy-result.js:192:30)\\\\n    at LazyResult.then (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\@angular-devkit\\\\\\\\build-angular\\\\\\\\node_modules\\\\\\\\postcss\\\\\\\\lib\\\\\\\\lazy-result.js:436:17)\\\");\\n\\n/***/ })\\n\\n/******/ \\t});\\n/************************************************************************/\\n/******/ \\t\\n/******/ \\t// startup\\n/******/ \\t// Load entry module and return exports\\n/******/ \\t// This entry module doesn't tell about it's top-level declarations so it can't be inlined\\n/******/ \\tvar __webpack_exports__ = {};\\n/******/ \\t__webpack_modules__[926]();\\n/******/ \\tresource = __webpack_exports__;\\n/******/ \\t\\n/******/ })()\\n;\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["TextFieldModule", "DOCUMENT", "DatePipe", "Ng<PERSON><PERSON>", "NgTemplateOutlet", "MatButtonModule", "MatFormFieldModule", "MatIconModule", "MatInputModule", "FuseScrollbarDirective", "Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "ctx_r1", "chat", "contact", "avatar", "ɵɵsanitizeUrl", "ɵɵtextInterpolate1", "name", "char<PERSON>t", "ɵɵconditionalCreate", "QuickChatComponent_Conditional_4_Conditional_2_Template", "QuickChatComponent_Conditional_4_Conditional_3_Template", "ɵɵconditional", "ɵɵclassProp", "selectedC<PERSON>", "id", "chat_r4", "ɵɵlistener", "QuickChatComponent_For_9_Template_div_click_0_listener", "ɵɵrestoreView", "_r3", "$implicit", "ɵɵnextContext", "ɵɵresetView", "selectChat", "QuickChatComponent_For_9_Conditional_2_Template", "QuickChatComponent_For_9_Conditional_3_Template", "QuickChatComponent_For_9_Conditional_4_Template", "ɵɵpureFunction2", "_c3", "unreadCount", "ɵɵpipeBind2", "message_r5", "createdAt", "ɵɵelementContainer", "ɵɵtemplate", "QuickChatComponent_Conditional_11_For_3_Conditional_5_ng_container_1_Template", "_c6", "isMine", "speechBubbleExtension_r6", "_c7", "QuickChatComponent_Conditional_11_For_3_Conditional_0_Template", "QuickChatComponent_Conditional_11_For_3_Conditional_5_Template", "QuickChatComponent_Conditional_11_For_3_Conditional_7_Template", "ɵ$index_68_r7", "messages", "ɵɵpureFunction4", "_c4", "_c5", "ɵ$count_68_r8", "value", "ɵɵsanitizeHtml", "ɵɵrepeaterCreate", "QuickChatComponent_Conditional_11_For_3_Template", "ɵɵcomponentInstance", "trackByFn", "ɵɵrepeater", "QuickChatComponent", "constructor", "_document", "_elementRef", "_renderer2", "_ngZone", "_quickChatService", "_scrollStrategyOptions", "opened", "_scrollStrategy", "block", "_unsubscribeAll", "classList", "_resizeMessageInput", "runOutsideAngular", "setTimeout", "messageInput", "nativeElement", "style", "height", "scrollHeight", "ngOnInit", "chat$", "pipe", "subscribe", "chats$", "chats", "ngAfterViewInit", "_mutationObserver", "MutationObserver", "mutations", "for<PERSON>ach", "mutation", "<PERSON><PERSON><PERSON><PERSON>", "target", "attributeName", "contains", "top", "parseInt", "setStyle", "Math", "abs", "observe", "documentElement", "attributes", "attributeFilter", "ngOnDestroy", "disconnect", "next", "complete", "open", "_toggleOpened", "close", "toggle", "getChatById", "index", "item", "_showOverlay", "_hideOverlay", "_overlay", "createElement", "add", "append<PERSON><PERSON><PERSON>", "parentElement", "enable", "addEventListener", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "disable", "_", "ɵɵdirectiveInject", "ElementRef", "Renderer2", "NgZone", "i1", "QuickChatService", "i2", "ScrollStrategyOptions", "_2", "selectors", "viewQuery", "QuickChatComponent_Query", "rf", "ctx", "QuickChatComponent_input_HostBindingHandler", "QuickChatComponent_ngModelChange_HostBindingHandler", "ɵɵclassMap", "QuickChatComponent_Template_div_click_2_listener", "_r1", "QuickChatComponent_Conditional_3_Template", "QuickChatComponent_Conditional_4_Template", "QuickChatComponent_For_9_Template", "QuickChatComponent_Conditional_11_Template", "QuickChatComponent_Conditional_12_Template", "QuickChatComponent_ng_template_13_Template", "ɵɵtemplateRefExtractor", "_c1", "ɵɵpureFunction0", "_c2", "i3", "MatIcon", "i4", "MatIconButton", "i5", "MatFormField", "i6", "MatInput", "i7", "CdkTextareaAutosize", "styles", "encapsulation"], "sources": ["D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\app\\layout\\common\\quick-chat\\quick-chat.component.ts", "D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\app\\layout\\common\\quick-chat\\quick-chat.component.html"], "sourcesContent": ["import { ScrollStrategy, ScrollStrategyOptions } from '@angular/cdk/overlay';\nimport { TextFieldModule } from '@angular/cdk/text-field';\nimport { DOCUMENT, DatePipe, NgClass, NgTemplateOutlet } from '@angular/common';\nimport {\n    AfterViewInit,\n    Component,\n    ElementRef,\n    HostBinding,\n    HostListener,\n    Inject,\n    NgZone,\n    OnDestroy,\n    OnInit,\n    Renderer2,\n    ViewChild,\n    ViewEncapsulation,\n} from '@angular/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { FuseScrollbarDirective } from '@fuse/directives/scrollbar';\nimport { QuickChatService } from 'app/layout/common/quick-chat/quick-chat.service';\nimport { Chat } from 'app/layout/common/quick-chat/quick-chat.types';\nimport { Subject, takeUntil } from 'rxjs';\n\n@Component({\n    selector: 'quick-chat',\n    templateUrl: './quick-chat.component.html',\n    styleUrls: ['./quick-chat.component.scss'],\n    encapsulation: ViewEncapsulation.None,\n    exportAs: 'quickChat',\n    imports: [\n        NgClass,\n        MatIconModule,\n        MatButtonModule,\n        FuseScrollbarDirective,\n        NgTemplateOutlet,\n        MatFormFieldModule,\n        MatInputModule,\n        TextFieldModule,\n        DatePipe,\n    ],\n})\nexport class QuickChatComponent implements OnInit, AfterViewInit, OnDestroy {\n    @ViewChild('messageInput') messageInput: ElementRef;\n    chat: Chat;\n    chats: Chat[];\n    opened: boolean = false;\n    selectedChat: Chat;\n    private _mutationObserver: MutationObserver;\n    private _scrollStrategy: ScrollStrategy =\n        this._scrollStrategyOptions.block();\n    private _overlay: HTMLElement;\n    private _unsubscribeAll: Subject<any> = new Subject<any>();\n\n    /**\n     * Constructor\n     */\n    constructor(\n        @Inject(DOCUMENT) private _document: Document,\n        private _elementRef: ElementRef,\n        private _renderer2: Renderer2,\n        private _ngZone: NgZone,\n        private _quickChatService: QuickChatService,\n        private _scrollStrategyOptions: ScrollStrategyOptions\n    ) {}\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Decorated methods\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * Host binding for component classes\n     */\n    @HostBinding('class') get classList(): any {\n        return {\n            'quick-chat-opened': this.opened,\n        };\n    }\n\n    /**\n     * Resize on 'input' and 'ngModelChange' events\n     *\n     * @private\n     */\n    @HostListener('input')\n    @HostListener('ngModelChange')\n    _resizeMessageInput(): void {\n        // This doesn't need to trigger Angular's change detection by itself\n        this._ngZone.runOutsideAngular(() => {\n            setTimeout(() => {\n                // Set the height to 'auto' so we can correctly read the scrollHeight\n                this.messageInput.nativeElement.style.height = 'auto';\n\n                // Get the scrollHeight and subtract the vertical padding\n                this.messageInput.nativeElement.style.height = `${this.messageInput.nativeElement.scrollHeight}px`;\n            });\n        });\n    }\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Lifecycle hooks\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * On init\n     */\n    ngOnInit(): void {\n        // Chat\n        this._quickChatService.chat$\n            .pipe(takeUntil(this._unsubscribeAll))\n            .subscribe((chat: Chat) => {\n                this.chat = chat;\n            });\n\n        // Chats\n        this._quickChatService.chats$\n            .pipe(takeUntil(this._unsubscribeAll))\n            .subscribe((chats: Chat[]) => {\n                this.chats = chats;\n            });\n\n        // Selected chat\n        this._quickChatService.chat$\n            .pipe(takeUntil(this._unsubscribeAll))\n            .subscribe((chat: Chat) => {\n                this.selectedChat = chat;\n            });\n    }\n\n    /**\n     * After view init\n     */\n    ngAfterViewInit(): void {\n        // Fix for Firefox.\n        //\n        // Because 'position: sticky' doesn't work correctly inside a 'position: fixed' parent,\n        // adding the '.cdk-global-scrollblock' to the html element breaks the navigation's position.\n        // This fixes the problem by reading the 'top' value from the html element and adding it as a\n        // 'marginTop' to the navigation itself.\n        this._mutationObserver = new MutationObserver((mutations) => {\n            mutations.forEach((mutation) => {\n                const mutationTarget = mutation.target as HTMLElement;\n                if (mutation.attributeName === 'class') {\n                    if (\n                        mutationTarget.classList.contains(\n                            'cdk-global-scrollblock'\n                        )\n                    ) {\n                        const top = parseInt(mutationTarget.style.top, 10);\n                        this._renderer2.setStyle(\n                            this._elementRef.nativeElement,\n                            'margin-top',\n                            `${Math.abs(top)}px`\n                        );\n                    } else {\n                        this._renderer2.setStyle(\n                            this._elementRef.nativeElement,\n                            'margin-top',\n                            null\n                        );\n                    }\n                }\n            });\n        });\n        this._mutationObserver.observe(this._document.documentElement, {\n            attributes: true,\n            attributeFilter: ['class'],\n        });\n    }\n\n    /**\n     * On destroy\n     */\n    ngOnDestroy(): void {\n        // Disconnect the mutation observer\n        this._mutationObserver.disconnect();\n\n        // Unsubscribe from all subscriptions\n        this._unsubscribeAll.next(null);\n        this._unsubscribeAll.complete();\n    }\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Public methods\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * Open the panel\n     */\n    open(): void {\n        // Return if the panel has already opened\n        if (this.opened) {\n            return;\n        }\n\n        // Open the panel\n        this._toggleOpened(true);\n    }\n\n    /**\n     * Close the panel\n     */\n    close(): void {\n        // Return if the panel has already closed\n        if (!this.opened) {\n            return;\n        }\n\n        // Close the panel\n        this._toggleOpened(false);\n    }\n\n    /**\n     * Toggle the panel\n     */\n    toggle(): void {\n        if (this.opened) {\n            this.close();\n        } else {\n            this.open();\n        }\n    }\n\n    /**\n     * Select the chat\n     *\n     * @param id\n     */\n    selectChat(id: string): void {\n        // Open the panel\n        this._toggleOpened(true);\n\n        // Get the chat data\n        this._quickChatService.getChatById(id).subscribe();\n    }\n\n    /**\n     * Track by function for ngFor loops\n     *\n     * @param index\n     * @param item\n     */\n    trackByFn(index: number, item: any): any {\n        return item.id || index;\n    }\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Private methods\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * Show the backdrop\n     *\n     * @private\n     */\n    private _showOverlay(): void {\n        // Try hiding the overlay in case there is one already opened\n        this._hideOverlay();\n\n        // Create the backdrop element\n        this._overlay = this._renderer2.createElement('div');\n\n        // Return if overlay couldn't be create for some reason\n        if (!this._overlay) {\n            return;\n        }\n\n        // Add a class to the backdrop element\n        this._overlay.classList.add('quick-chat-overlay');\n\n        // Append the backdrop to the parent of the panel\n        this._renderer2.appendChild(\n            this._elementRef.nativeElement.parentElement,\n            this._overlay\n        );\n\n        // Enable block scroll strategy\n        this._scrollStrategy.enable();\n\n        // Add an event listener to the overlay\n        this._overlay.addEventListener('click', () => {\n            this.close();\n        });\n    }\n\n    /**\n     * Hide the backdrop\n     *\n     * @private\n     */\n    private _hideOverlay(): void {\n        if (!this._overlay) {\n            return;\n        }\n\n        // If the backdrop still exists...\n        if (this._overlay) {\n            // Remove the backdrop\n            this._overlay.parentNode.removeChild(this._overlay);\n            this._overlay = null;\n        }\n\n        // Disable block scroll strategy\n        this._scrollStrategy.disable();\n    }\n\n    /**\n     * Open/close the panel\n     *\n     * @param open\n     * @private\n     */\n    private _toggleOpened(open: boolean): void {\n        // Set the opened\n        this.opened = open;\n\n        // If the panel opens, show the overlay\n        if (open) {\n            this._showOverlay();\n        }\n        // Otherwise, hide the overlay\n        else {\n            this._hideOverlay();\n        }\n    }\n}\n", "<div\n    class=\"fixed bottom-0 top-0 w-full sm:w-96 lg:sticky lg:left-full lg:h-screen lg:w-16 lg:shadow\"\n>\n    <div\n        class=\"bg-card flex h-full w-full flex-col transition-transform duration-400 ease-drawer sm:w-96\"\n        [ngClass]=\"{\n            '-translate-x-full shadow sm:-translate-x-96 lg:-translate-x-80':\n                opened,\n            'translate-x-0': !opened,\n        }\"\n    >\n        <!-- Header -->\n        <div\n            class=\"quick-chat-header flex flex-0 cursor-pointer items-center justify-start\"\n            (click)=\"toggle()\"\n        >\n            <!-- Toggle -->\n            @if (!opened || (opened && !selectedChat)) {\n                <div class=\"flex flex-auto items-center justify-center\">\n                    <div class=\"flex w-16 flex-0 items-center justify-center\">\n                        <mat-icon\n                            class=\"icon-size-6\"\n                            [svgIcon]=\"\n                                'heroicons_outline:chat-bubble-left-right'\n                            \"\n                        ></mat-icon>\n                    </div>\n                    <div class=\"text-secondary text-lg font-medium\">\n                        Team Chat\n                    </div>\n                    <button class=\"ml-auto mr-4\" mat-icon-button>\n                        <mat-icon\n                            [svgIcon]=\"'heroicons_outline:x-mark'\"\n                        ></mat-icon>\n                    </button>\n                </div>\n            }\n\n            <!-- Contact info -->\n            @if (opened && selectedChat) {\n                <div class=\"ml-3 flex flex-auto items-center\">\n                    <div\n                        class=\"relative flex h-10 w-10 flex-0 items-center justify-center\"\n                    >\n                        @if (chat.contact.avatar) {\n                            <img\n                                class=\"h-full w-full rounded-full object-cover\"\n                                [src]=\"chat.contact.avatar\"\n                                alt=\"Contact avatar\"\n                            />\n                        }\n                        @if (!chat.contact.avatar) {\n                            <div\n                                class=\"flex h-full w-full items-center justify-center rounded-full bg-gray-200 text-lg uppercase text-gray-600 dark:bg-gray-700 dark:text-gray-200\"\n                            >\n                                {{ chat.contact.name.charAt(0) }}\n                            </div>\n                        }\n                    </div>\n                    <div class=\"ml-4 truncate text-lg font-medium leading-5\">\n                        {{ chat.contact.name }}\n                    </div>\n                    <button class=\"ml-auto mr-4\" mat-icon-button>\n                        <mat-icon\n                            [svgIcon]=\"'heroicons_outline:x-mark'\"\n                        ></mat-icon>\n                    </button>\n                </div>\n            }\n        </div>\n\n        <!-- Content -->\n        <div class=\"flex flex-auto overflow-hidden border-t\">\n            <!-- Chat list -->\n            <div\n                class=\"h-full w-16 flex-0 overflow-y-auto overscroll-y-contain sm:overflow-hidden sm:overscroll-auto\"\n                fuseScrollbar\n                [fuseScrollbarOptions]=\"{ wheelPropagation: false }\"\n            >\n                <div class=\"flex-auto\">\n                    @for (chat of chats; track trackByFn($index, chat)) {\n                        <div\n                            class=\"flex cursor-pointer items-center px-4 py-3\"\n                            [ngClass]=\"{\n                                'dark:hover:bg-hover hover:bg-gray-100':\n                                    !selectedChat ||\n                                    selectedChat.id !== chat.id,\n                                'bg-primary-50 dark:bg-hover':\n                                    selectedChat && selectedChat.id === chat.id,\n                            }\"\n                            (click)=\"selectChat(chat.id)\"\n                        >\n                            <div\n                                class=\"relative flex h-8 w-8 flex-0 items-center justify-center\"\n                            >\n                                @if (chat.unreadCount > 0) {\n                                    <div\n                                        class=\"ring-bg-card absolute bottom-0 right-0 -ml-0.5 h-2 w-2 flex-0 rounded-full bg-primary text-on-primary ring-2 dark:bg-primary-500 dark:ring-gray-900\"\n                                        [class.ring-primary-50]=\"\n                                            selectedChat &&\n                                            selectedChat.id === chat.id\n                                        \"\n                                    ></div>\n                                }\n                                @if (chat.contact.avatar) {\n                                    <img\n                                        class=\"h-full w-full rounded-full object-cover\"\n                                        [src]=\"chat.contact.avatar\"\n                                        alt=\"Contact avatar\"\n                                    />\n                                }\n                                @if (!chat.contact.avatar) {\n                                    <div\n                                        class=\"flex h-full w-full items-center justify-center rounded-full bg-gray-200 text-lg uppercase text-gray-600 dark:bg-gray-700 dark:text-gray-200\"\n                                    >\n                                        {{ chat.contact.name.charAt(0) }}\n                                    </div>\n                                }\n                            </div>\n                        </div>\n                    }\n                </div>\n            </div>\n\n            <!-- Conversation -->\n            <div\n                class=\"flex flex-auto flex-col overflow-hidden border-l bg-gray-50 dark:bg-transparent\"\n            >\n                @if (chat) {\n                    <div\n                        class=\"flex flex-col-reverse overflow-y-auto overscroll-y-contain\"\n                    >\n                        <div class=\"flex flex-auto shrink flex-col p-6\">\n                            @for (\n                                message of chat.messages;\n                                track trackByFn(i, message);\n                                let i = $index;\n                                let first = $first;\n                                let last = $last\n                            ) {\n                                <!-- Start of the day -->\n                                @if (\n                                    first ||\n                                    (chat.messages[i - 1].createdAt\n                                        | date: 'd') !==\n                                        (message.createdAt | date: 'd')\n                                ) {\n                                    <div\n                                        class=\"-mx-6 my-3 flex items-center justify-center\"\n                                    >\n                                        <div class=\"flex-auto border-b\"></div>\n                                        <div\n                                            class=\"text-secondary mx-4 flex-0 text-sm font-medium leading-5\"\n                                        >\n                                            {{\n                                                message.createdAt\n                                                    | date: 'longDate'\n                                            }}\n                                        </div>\n                                        <div class=\"flex-auto border-b\"></div>\n                                    </div>\n                                }\n                                <div\n                                    class=\"flex flex-col\"\n                                    [ngClass]=\"{\n                                        'items-end': message.isMine,\n                                        'items-start': !message.isMine,\n                                        'mt-0.5':\n                                            i > 0 &&\n                                            chat.messages[i - 1].isMine ===\n                                                message.isMine,\n                                        'mt-3':\n                                            i > 0 &&\n                                            chat.messages[i - 1].isMine !==\n                                                message.isMine,\n                                    }\"\n                                >\n                                    <!-- Bubble -->\n                                    <div\n                                        class=\"relative max-w-3/4 rounded-lg px-3 py-2\"\n                                        [ngClass]=\"{\n                                            'bg-blue-500 text-blue-50':\n                                                message.isMine,\n                                            'bg-gray-500 text-gray-50':\n                                                !message.isMine,\n                                        }\"\n                                    >\n                                        <!-- Speech bubble tail -->\n                                        @if (\n                                            last ||\n                                            chat.messages[i + 1].isMine !==\n                                                message.isMine\n                                        ) {\n                                            <div\n                                                class=\"absolute bottom-0 w-3\"\n                                                [ngClass]=\"{\n                                                    '-right-1 -mr-px mb-px text-blue-500':\n                                                        message.isMine,\n                                                    '-left-1 -ml-px mb-px -scale-x-1 text-gray-500':\n                                                        !message.isMine,\n                                                }\"\n                                            >\n                                                <ng-container\n                                                    *ngTemplateOutlet=\"\n                                                        speechBubbleExtension\n                                                    \"\n                                                ></ng-container>\n                                            </div>\n                                        }\n                                        <!-- Message -->\n                                        <div\n                                            class=\"min-w-4 leading-5\"\n                                            [innerHTML]=\"message.value\"\n                                        ></div>\n                                    </div>\n                                    <!-- Time -->\n                                    @if (\n                                        first ||\n                                        last ||\n                                        chat.messages[i + 1].isMine !==\n                                            message.isMine ||\n                                        chat.messages[i + 1].createdAt !==\n                                            message.createdAt\n                                    ) {\n                                        <div\n                                            class=\"text-secondary my-0.5 text-sm font-medium\"\n                                            [ngClass]=\"{\n                                                'mr-3': message.isMine,\n                                                'ml-3': !message.isMine,\n                                            }\"\n                                        >\n                                            {{\n                                                message.createdAt\n                                                    | date: 'HH:mm'\n                                            }}\n                                        </div>\n                                    }\n                                </div>\n                            }\n                        </div>\n                    </div>\n\n                    <!-- Message field -->\n                    <div\n                        class=\"flex items-end border-t bg-gray-50 p-4 dark:bg-transparent\"\n                    >\n                        <mat-form-field\n                            class=\"fuse-mat-dense fuse-mat-rounded fuse-mat-bold w-full\"\n                            [subscriptSizing]=\"'dynamic'\"\n                        >\n                            <textarea\n                                matInput\n                                cdkTextareaAutosize\n                                #messageInput\n                            ></textarea>\n                        </mat-form-field>\n                        <div class=\"my-px ml-4 flex h-11 items-center\">\n                            <button mat-icon-button>\n                                <mat-icon\n                                    [svgIcon]=\"\n                                        'heroicons_outline:paper-airplane'\n                                    \"\n                                ></mat-icon>\n                            </button>\n                        </div>\n                    </div>\n                } @else {\n                    <div\n                        class=\"flex h-full w-full flex-auto flex-col items-center justify-center p-4\"\n                    >\n                        <mat-icon\n                            class=\"icon-size-24\"\n                            [svgIcon]=\"\n                                'heroicons_outline:chat-bubble-bottom-center-text'\n                            \"\n                        ></mat-icon>\n                        <div\n                            class=\"text-secondary mt-4 text-center text-xl font-medium tracking-tight\"\n                        >\n                            Select a conversation\n                        </div>\n                    </div>\n                }\n            </div>\n        </div>\n    </div>\n</div>\n\n<!-- Select chat or start new template -->\n\n<!-- Speech bubble tail SVG -->\n<!-- prettier-ignore -->\n<ng-template #speechBubbleExtension>\n    <svg width=\"100%\" height=\"100%\" viewBox=\"0 0 66 66\" xmlns=\"http://www.w3.org/2000/svg\">\n        <g id=\"Page-1\" stroke=\"none\" stroke-width=\"1\" fill=\"none\" fill-rule=\"evenodd\">\n            <path d=\"M1.01522827,0.516204834 C-8.83532715,54.3062744 61.7609863,70.5215302 64.8009949,64.3061218 C68.8074951,54.8859711 30.1663208,52.9997559 37.5036011,0.516204834 L1.01522827,0.516204834 Z\" fill=\"currentColor\" fill-rule=\"nonzero\"></path>\n        </g>\n    </svg>\n</ng-template>\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,yBAAyB;AACzD,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,gBAAgB,QAAQ,iBAAiB;AAe/E,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,sBAAsB,QAAQ,4BAA4B;AAGnE,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICLrBC,EADJ,CAAAC,cAAA,aAAwD,cACM;IACtDD,EAAA,CAAAE,SAAA,mBAKY;IAChBF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAgD;IAC5CD,EAAA,CAAAI,MAAA,kBACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,iBAA6C;IACzCD,EAAA,CAAAE,SAAA,mBAEY;IAEpBF,EADI,CAAAG,YAAA,EAAS,EACP;;;IAbMH,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAAM,UAAA,uDAEC;IAQDN,EAAA,CAAAK,SAAA,GAAsC;IAAtCL,EAAA,CAAAM,UAAA,uCAAsC;;;;;IAatCN,EAAA,CAAAE,SAAA,cAIE;;;;IAFEF,EAAA,CAAAM,UAAA,QAAAC,MAAA,CAAAC,IAAA,CAAAC,OAAA,CAAAC,MAAA,EAAAV,EAAA,CAAAW,aAAA,CAA2B;;;;;IAK/BX,EAAA,CAAAC,cAAA,cAEC;IACGD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAK,SAAA,EACJ;IADIL,EAAA,CAAAY,kBAAA,MAAAL,MAAA,CAAAC,IAAA,CAAAC,OAAA,CAAAI,IAAA,CAAAC,MAAA,SACJ;;;;;IAfRd,EADJ,CAAAC,cAAA,aAA8C,cAGzC;IACGD,EAAA,CAAAe,mBAAA,IAAAC,uDAAA,kBAA2B;IAO3BhB,EAAA,CAAAe,mBAAA,IAAAE,uDAAA,kBAA4B;IAOhCjB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAyD;IACrDD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,iBAA6C;IACzCD,EAAA,CAAAE,SAAA,mBAEY;IAEpBF,EADI,CAAAG,YAAA,EAAS,EACP;;;;IAvBEH,EAAA,CAAAK,SAAA,GAMC;IANDL,EAAA,CAAAkB,aAAA,CAAAX,MAAA,CAAAC,IAAA,CAAAC,OAAA,CAAAC,MAAA,UAMC;IACDV,EAAA,CAAAK,SAAA,EAMC;IANDL,EAAA,CAAAkB,aAAA,EAAAX,MAAA,CAAAC,IAAA,CAAAC,OAAA,CAAAC,MAAA,UAMC;IAGDV,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAY,kBAAA,MAAAL,MAAA,CAAAC,IAAA,CAAAC,OAAA,CAAAI,IAAA,MACJ;IAGQb,EAAA,CAAAK,SAAA,GAAsC;IAAtCL,EAAA,CAAAM,UAAA,uCAAsC;;;;;IAgC9BN,EAAA,CAAAE,SAAA,cAMO;;;;;IAJHF,EAAA,CAAAmB,WAAA,oBAAAZ,MAAA,CAAAa,YAAA,IAAAb,MAAA,CAAAa,YAAA,CAAAC,EAAA,KAAAC,OAAA,CAAAD,EAAA,CAGC;;;;;IAILrB,EAAA,CAAAE,SAAA,cAIE;;;;IAFEF,EAAA,CAAAM,UAAA,QAAAgB,OAAA,CAAAb,OAAA,CAAAC,MAAA,EAAAV,EAAA,CAAAW,aAAA,CAA2B;;;;;IAK/BX,EAAA,CAAAC,cAAA,cAEC;IACGD,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAK,SAAA,EACJ;IADIL,EAAA,CAAAY,kBAAA,MAAAU,OAAA,CAAAb,OAAA,CAAAI,IAAA,CAAAC,MAAA,SACJ;;;;;;IAnCZd,EAAA,CAAAC,cAAA,cAUC;IADGD,EAAA,CAAAuB,UAAA,mBAAAC,uDAAA;MAAA,MAAAF,OAAA,GAAAtB,EAAA,CAAAyB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAApB,MAAA,GAAAP,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAA6B,WAAA,CAAStB,MAAA,CAAAuB,UAAA,CAAAR,OAAA,CAAAD,EAAA,CAAmB;IAAA,EAAC;IAE7BrB,EAAA,CAAAC,cAAA,cAEC;IACGD,EAAA,CAAAe,mBAAA,IAAAgB,+CAAA,kBAA4B;IAS5B/B,EAAA,CAAAe,mBAAA,IAAAiB,+CAAA,kBAA2B;IAO3BhC,EAAA,CAAAe,mBAAA,IAAAkB,+CAAA,kBAA4B;IAQpCjC,EADI,CAAAG,YAAA,EAAM,EACJ;;;;;IApCFH,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAkC,eAAA,IAAAC,GAAA,GAAA5B,MAAA,CAAAa,YAAA,IAAAb,MAAA,CAAAa,YAAA,CAAAC,EAAA,KAAAC,OAAA,CAAAD,EAAA,EAAAd,MAAA,CAAAa,YAAA,IAAAb,MAAA,CAAAa,YAAA,CAAAC,EAAA,KAAAC,OAAA,CAAAD,EAAA,EAME;IAMErB,EAAA,CAAAK,SAAA,GAQC;IARDL,EAAA,CAAAkB,aAAA,CAAAI,OAAA,CAAAc,WAAA,cAQC;IACDpC,EAAA,CAAAK,SAAA,EAMC;IANDL,EAAA,CAAAkB,aAAA,CAAAI,OAAA,CAAAb,OAAA,CAAAC,MAAA,UAMC;IACDV,EAAA,CAAAK,SAAA,EAMC;IANDL,EAAA,CAAAkB,aAAA,EAAAI,OAAA,CAAAb,OAAA,CAAAC,MAAA,UAMC;;;;;IA8BGV,EAAA,CAAAC,cAAA,cAEC;IACGD,EAAA,CAAAE,SAAA,cAAsC;IACtCF,EAAA,CAAAC,cAAA,cAEC;IACGD,EAAA,CAAAI,MAAA,GAIJ;;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,SAAA,cAAsC;IAC1CF,EAAA,CAAAG,YAAA,EAAM;;;;IANEH,EAAA,CAAAK,SAAA,GAIJ;IAJIL,EAAA,CAAAY,kBAAA,MAAAZ,EAAA,CAAAqC,WAAA,OAAAC,UAAA,CAAAC,SAAA,mBAIJ;;;;;IA4CQvC,EAAA,CAAAwC,kBAAA,GAIgB;;;;;IAbpBxC,EAAA,CAAAC,cAAA,cAQC;IACGD,EAAA,CAAAyC,UAAA,IAAAC,6EAAA,2BAIC;IACL1C,EAAA,CAAAG,YAAA,EAAM;;;;;;IAZFH,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAkC,eAAA,IAAAS,GAAA,EAAAL,UAAA,CAAAM,MAAA,GAAAN,UAAA,CAAAM,MAAA,EAKE;IAGG5C,EAAA,CAAAK,SAAA,EAGnD;IAHmDL,EAAA,CAAAM,UAAA,qBAAAuC,wBAAA,CAGnD;;;;;IAkBsC7C,EAAA,CAAAC,cAAA,cAMC;IACGD,EAAA,CAAAI,MAAA,GAIJ;;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;IATFH,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAkC,eAAA,IAAAY,GAAA,EAAAR,UAAA,CAAAM,MAAA,GAAAN,UAAA,CAAAM,MAAA,EAGE;IAEF5C,EAAA,CAAAK,SAAA,EAIJ;IAJIL,EAAA,CAAAY,kBAAA,MAAAZ,EAAA,CAAAqC,WAAA,OAAAC,UAAA,CAAAC,SAAA,gBAIJ;;;;;IA9FRvC,EAAA,CAAAe,mBAAA,IAAAgC,8DAAA,kBAKG;;;IAgCC/C,EAhBJ,CAAAC,cAAA,cAcC,cAUI;IAEGD,EAAA,CAAAe,mBAAA,IAAAiC,8DAAA,kBAIG;IAkBHhD,EAAA,CAAAE,SAAA,cAGO;IACXF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAe,mBAAA,IAAAkC,8DAAA,kBAOG;IAcPjD,EAAA,CAAAG,YAAA,EAAM;;;;;;;IAhGNH,EAAA,CAAAkB,aAAA,CAAAgC,aAAA,UAAAlD,EAAA,CAAAqC,WAAA,OAAA9B,MAAA,CAAAC,IAAA,CAAA2C,QAAA,CAAAD,aAAA,MAAAX,SAAA,WAAAvC,EAAA,CAAAqC,WAAA,OAAAC,UAAA,CAAAC,SAAA,gBAoBC;IAGGvC,EAAA,CAAAK,SAAA,GAWE;IAXFL,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAoD,eAAA,KAAAC,GAAA,EAAAf,UAAA,CAAAM,MAAA,GAAAN,UAAA,CAAAM,MAAA,EAAAM,aAAA,QAAA3C,MAAA,CAAAC,IAAA,CAAA2C,QAAA,CAAAD,aAAA,MAAAN,MAAA,KAAAN,UAAA,CAAAM,MAAA,EAAAM,aAAA,QAAA3C,MAAA,CAAAC,IAAA,CAAA2C,QAAA,CAAAD,aAAA,MAAAN,MAAA,KAAAN,UAAA,CAAAM,MAAA,EAWE;IAKE5C,EAAA,CAAAK,SAAA,EAKE;IALFL,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAkC,eAAA,KAAAoB,GAAA,EAAAhB,UAAA,CAAAM,MAAA,GAAAN,UAAA,CAAAM,MAAA,EAKE;IAGF5C,EAAA,CAAAK,SAAA,EAoBC;IApBDL,EAAA,CAAAkB,aAAA,CAAAgC,aAAA,KAAAK,aAAA,QAAAhD,MAAA,CAAAC,IAAA,CAAA2C,QAAA,CAAAD,aAAA,MAAAN,MAAA,KAAAN,UAAA,CAAAM,MAAA,UAoBC;IAIG5C,EAAA,CAAAK,SAAA,EAA2B;IAA3BL,EAAA,CAAAM,UAAA,cAAAgC,UAAA,CAAAkB,KAAA,EAAAxD,EAAA,CAAAyD,cAAA,CAA2B;IAInCzD,EAAA,CAAAK,SAAA,EAoBC;IApBDL,EAAA,CAAAkB,aAAA,CAAAgC,aAAA,UAAAA,aAAA,KAAAK,aAAA,QAAAhD,MAAA,CAAAC,IAAA,CAAA2C,QAAA,CAAAD,aAAA,MAAAN,MAAA,KAAAN,UAAA,CAAAM,MAAA,IAAArC,MAAA,CAAAC,IAAA,CAAA2C,QAAA,CAAAD,aAAA,MAAAX,SAAA,KAAAD,UAAA,CAAAC,SAAA,UAoBC;;;;;IAxGbvC,EAHJ,CAAAC,cAAA,cAEC,cACmD;IAC5CD,EAAA,CAAA0D,gBAAA,IAAAC,gDAAA,qBAAA3D,EAAA,CAAA4D,mBAAA,GAAAC,SAAA,OAyGC;IAET7D,EADI,CAAAG,YAAA,EAAM,EACJ;IAMFH,EAHJ,CAAAC,cAAA,cAEC,yBAII;IACGD,EAAA,CAAAE,SAAA,sBAIY;IAChBF,EAAA,CAAAG,YAAA,EAAiB;IAEbH,EADJ,CAAAC,cAAA,cAA+C,iBACnB;IACpBD,EAAA,CAAAE,SAAA,oBAIY;IAGxBF,EAFQ,CAAAG,YAAA,EAAS,EACP,EACJ;;;;IApIEH,EAAA,CAAAK,SAAA,GAyGC;IAzGDL,EAAA,CAAA8D,UAAA,CAAAvD,MAAA,CAAAC,IAAA,CAAA2C,QAAA,CAyGC;IAUDnD,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAM,UAAA,8BAA6B;IAWrBN,EAAA,CAAAK,SAAA,GAEC;IAFDL,EAAA,CAAAM,UAAA,+CAEC;;;;;IAMjBN,EAAA,CAAAC,cAAA,cAEC;IACGD,EAAA,CAAAE,SAAA,mBAKY;IACZF,EAAA,CAAAC,cAAA,cAEC;IACGD,EAAA,CAAAI,MAAA,8BACJ;IACJJ,EADI,CAAAG,YAAA,EAAM,EACJ;;;IATEH,EAAA,CAAAK,SAAA,EAEC;IAFDL,EAAA,CAAAM,UAAA,+DAEC;;;;;;IAoBrBN,EADJ,CAAAC,cAAA,cAAuF,YACL;IAC1ED,EAAA,CAAAE,SAAA,eAAmP;IAE3PF,EADI,CAAAG,YAAA,EAAI,EACF;;;AD7PV,OAAM,MAAO4D,kBAAkB;EAY3B;;;EAGAC,YAC8BC,SAAmB,EACrCC,WAAuB,EACvBC,UAAqB,EACrBC,OAAe,EACfC,iBAAmC,EACnCC,sBAA6C;IAL3B,KAAAL,SAAS,GAATA,SAAS;IAC3B,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,sBAAsB,GAAtBA,sBAAsB;IAjBlC,KAAAC,MAAM,GAAY,KAAK;IAGf,KAAAC,eAAe,GACnB,IAAI,CAACF,sBAAsB,CAACG,KAAK,EAAE;IAE/B,KAAAC,eAAe,GAAiB,IAAI5E,OAAO,EAAO;EAYvD;EAEH;EACA;EACA;EAEA;;;EAGA,IAA0B6E,SAASA,CAAA;IAC/B,OAAO;MACH,mBAAmB,EAAE,IAAI,CAACJ;KAC7B;EACL;EAEA;;;;;EAOAK,mBAAmBA,CAAA;IACf;IACA,IAAI,CAACR,OAAO,CAACS,iBAAiB,CAAC,MAAK;MAChCC,UAAU,CAAC,MAAK;QACZ;QACA,IAAI,CAACC,YAAY,CAACC,aAAa,CAACC,KAAK,CAACC,MAAM,GAAG,MAAM;QAErD;QACA,IAAI,CAACH,YAAY,CAACC,aAAa,CAACC,KAAK,CAACC,MAAM,GAAG,GAAG,IAAI,CAACH,YAAY,CAACC,aAAa,CAACG,YAAY,IAAI;MACtG,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EAEA;EACA;EACA;EAEA;;;EAGAC,QAAQA,CAAA;IACJ;IACA,IAAI,CAACf,iBAAiB,CAACgB,KAAK,CACvBC,IAAI,CAACvF,SAAS,CAAC,IAAI,CAAC2E,eAAe,CAAC,CAAC,CACrCa,SAAS,CAAE/E,IAAU,IAAI;MACtB,IAAI,CAACA,IAAI,GAAGA,IAAI;IACpB,CAAC,CAAC;IAEN;IACA,IAAI,CAAC6D,iBAAiB,CAACmB,MAAM,CACxBF,IAAI,CAACvF,SAAS,CAAC,IAAI,CAAC2E,eAAe,CAAC,CAAC,CACrCa,SAAS,CAAEE,KAAa,IAAI;MACzB,IAAI,CAACA,KAAK,GAAGA,KAAK;IACtB,CAAC,CAAC;IAEN;IACA,IAAI,CAACpB,iBAAiB,CAACgB,KAAK,CACvBC,IAAI,CAACvF,SAAS,CAAC,IAAI,CAAC2E,eAAe,CAAC,CAAC,CACrCa,SAAS,CAAE/E,IAAU,IAAI;MACtB,IAAI,CAACY,YAAY,GAAGZ,IAAI;IAC5B,CAAC,CAAC;EACV;EAEA;;;EAGAkF,eAAeA,CAAA;IACX;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACC,iBAAiB,GAAG,IAAIC,gBAAgB,CAAEC,SAAS,IAAI;MACxDA,SAAS,CAACC,OAAO,CAAEC,QAAQ,IAAI;QAC3B,MAAMC,cAAc,GAAGD,QAAQ,CAACE,MAAqB;QACrD,IAAIF,QAAQ,CAACG,aAAa,KAAK,OAAO,EAAE;UACpC,IACIF,cAAc,CAACrB,SAAS,CAACwB,QAAQ,CAC7B,wBAAwB,CAC3B,EACH;YACE,MAAMC,GAAG,GAAGC,QAAQ,CAACL,cAAc,CAACf,KAAK,CAACmB,GAAG,EAAE,EAAE,CAAC;YAClD,IAAI,CAACjC,UAAU,CAACmC,QAAQ,CACpB,IAAI,CAACpC,WAAW,CAACc,aAAa,EAC9B,YAAY,EACZ,GAAGuB,IAAI,CAACC,GAAG,CAACJ,GAAG,CAAC,IAAI,CACvB;UACL,CAAC,MAAM;YACH,IAAI,CAACjC,UAAU,CAACmC,QAAQ,CACpB,IAAI,CAACpC,WAAW,CAACc,aAAa,EAC9B,YAAY,EACZ,IAAI,CACP;UACL;QACJ;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IACF,IAAI,CAACW,iBAAiB,CAACc,OAAO,CAAC,IAAI,CAACxC,SAAS,CAACyC,eAAe,EAAE;MAC3DC,UAAU,EAAE,IAAI;MAChBC,eAAe,EAAE,CAAC,OAAO;KAC5B,CAAC;EACN;EAEA;;;EAGAC,WAAWA,CAAA;IACP;IACA,IAAI,CAAClB,iBAAiB,CAACmB,UAAU,EAAE;IAEnC;IACA,IAAI,CAACpC,eAAe,CAACqC,IAAI,CAAC,IAAI,CAAC;IAC/B,IAAI,CAACrC,eAAe,CAACsC,QAAQ,EAAE;EACnC;EAEA;EACA;EACA;EAEA;;;EAGAC,IAAIA,CAAA;IACA;IACA,IAAI,IAAI,CAAC1C,MAAM,EAAE;MACb;IACJ;IAEA;IACA,IAAI,CAAC2C,aAAa,CAAC,IAAI,CAAC;EAC5B;EAEA;;;EAGAC,KAAKA,CAAA;IACD;IACA,IAAI,CAAC,IAAI,CAAC5C,MAAM,EAAE;MACd;IACJ;IAEA;IACA,IAAI,CAAC2C,aAAa,CAAC,KAAK,CAAC;EAC7B;EAEA;;;EAGAE,MAAMA,CAAA;IACF,IAAI,IAAI,CAAC7C,MAAM,EAAE;MACb,IAAI,CAAC4C,KAAK,EAAE;IAChB,CAAC,MAAM;MACH,IAAI,CAACF,IAAI,EAAE;IACf;EACJ;EAEA;;;;;EAKAnF,UAAUA,CAACT,EAAU;IACjB;IACA,IAAI,CAAC6F,aAAa,CAAC,IAAI,CAAC;IAExB;IACA,IAAI,CAAC7C,iBAAiB,CAACgD,WAAW,CAAChG,EAAE,CAAC,CAACkE,SAAS,EAAE;EACtD;EAEA;;;;;;EAMA1B,SAASA,CAACyD,KAAa,EAAEC,IAAS;IAC9B,OAAOA,IAAI,CAAClG,EAAE,IAAIiG,KAAK;EAC3B;EAEA;EACA;EACA;EAEA;;;;;EAKQE,YAAYA,CAAA;IAChB;IACA,IAAI,CAACC,YAAY,EAAE;IAEnB;IACA,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACvD,UAAU,CAACwD,aAAa,CAAC,KAAK,CAAC;IAEpD;IACA,IAAI,CAAC,IAAI,CAACD,QAAQ,EAAE;MAChB;IACJ;IAEA;IACA,IAAI,CAACA,QAAQ,CAAC/C,SAAS,CAACiD,GAAG,CAAC,oBAAoB,CAAC;IAEjD;IACA,IAAI,CAACzD,UAAU,CAAC0D,WAAW,CACvB,IAAI,CAAC3D,WAAW,CAACc,aAAa,CAAC8C,aAAa,EAC5C,IAAI,CAACJ,QAAQ,CAChB;IAED;IACA,IAAI,CAAClD,eAAe,CAACuD,MAAM,EAAE;IAE7B;IACA,IAAI,CAACL,QAAQ,CAACM,gBAAgB,CAAC,OAAO,EAAE,MAAK;MACzC,IAAI,CAACb,KAAK,EAAE;IAChB,CAAC,CAAC;EACN;EAEA;;;;;EAKQM,YAAYA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;MAChB;IACJ;IAEA;IACA,IAAI,IAAI,CAACA,QAAQ,EAAE;MACf;MACA,IAAI,CAACA,QAAQ,CAACO,UAAU,CAACC,WAAW,CAAC,IAAI,CAACR,QAAQ,CAAC;MACnD,IAAI,CAACA,QAAQ,GAAG,IAAI;IACxB;IAEA;IACA,IAAI,CAAClD,eAAe,CAAC2D,OAAO,EAAE;EAClC;EAEA;;;;;;EAMQjB,aAAaA,CAACD,IAAa;IAC/B;IACA,IAAI,CAAC1C,MAAM,GAAG0C,IAAI;IAElB;IACA,IAAIA,IAAI,EAAE;MACN,IAAI,CAACO,YAAY,EAAE;IACvB;IACA;IAAA,KACK;MACD,IAAI,CAACC,YAAY,EAAE;IACvB;EACJ;EAAC,QAAAW,CAAA,G;qCA1RQrE,kBAAkB,EAAA/D,EAAA,CAAAqI,iBAAA,CAgBfhJ,QAAQ,GAAAW,EAAA,CAAAqI,iBAAA,CAAArI,EAAA,CAAAsI,UAAA,GAAAtI,EAAA,CAAAqI,iBAAA,CAAArI,EAAA,CAAAuI,SAAA,GAAAvI,EAAA,CAAAqI,iBAAA,CAAArI,EAAA,CAAAwI,MAAA,GAAAxI,EAAA,CAAAqI,iBAAA,CAAAI,EAAA,CAAAC,gBAAA,GAAA1I,EAAA,CAAAqI,iBAAA,CAAAM,EAAA,CAAAC,qBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAhBX9E,kBAAkB;IAAA+E,SAAA;IAAAC,SAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;QAAlBjJ,EAAA,CAAAuB,UAAA,mBAAA4H,4CAAA;UAAA,OAAAD,GAAA,CAAAtE,mBAAA,EAAqB;QAAA,EAAH,2BAAAwE,oDAAA;UAAA,OAAlBF,GAAA,CAAAtE,mBAAA,EAAqB;QAAA,EAAH;;;QAAlB5E,EAAA,CAAAqJ,UAAA,CAAAH,GAAA,CAAAvE,SAAA,CAAkB;;;;;;;;;;QChCvB3E,EAZR,CAAAC,cAAA,aAEC,aAQI,aAKI;QADGD,EAAA,CAAAuB,UAAA,mBAAA+H,iDAAA;UAAAtJ,EAAA,CAAAyB,aAAA,CAAA8H,GAAA;UAAA,OAAAvJ,EAAA,CAAA6B,WAAA,CAASqH,GAAA,CAAA9B,MAAA,EAAQ;QAAA,EAAC;QAGlBpH,EAAA,CAAAe,mBAAA,IAAAyI,yCAAA,iBAA4C;QAsB5CxJ,EAAA,CAAAe,mBAAA,IAAA0I,yCAAA,iBAA8B;QA8BlCzJ,EAAA,CAAAG,YAAA,EAAM;QAUEH,EAPR,CAAAC,cAAA,aAAqD,aAMhD,aAC0B;QACnBD,EAAA,CAAA0D,gBAAA,IAAAgG,iCAAA,mBAAAR,GAAA,CAAArF,SAAA,OAwCC;QAET7D,EADI,CAAAG,YAAA,EAAM,EACJ;QAGNH,EAAA,CAAAC,cAAA,eAEC;QA2IKD,EA1IF,CAAAe,mBAAA,KAAA4I,0CAAA,QAAY,KAAAC,0CAAA,kBA0IH;QAoBzB5J,EAHY,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ;QAMNH,EAAA,CAAAyC,UAAA,KAAAoH,0CAAA,gCAAA7J,EAAA,CAAA8J,sBAAA,CAAoC;;;QA/R5B9J,EAAA,CAAAK,SAAA,EAIE;QAJFL,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAkC,eAAA,IAAA6H,GAAA,EAAAb,GAAA,CAAA3E,MAAA,GAAA2E,GAAA,CAAA3E,MAAA,EAIE;QAQEvE,EAAA,CAAAK,SAAA,GAmBC;QAnBDL,EAAA,CAAAkB,aAAA,EAAAgI,GAAA,CAAA3E,MAAA,IAAA2E,GAAA,CAAA3E,MAAA,KAAA2E,GAAA,CAAA9H,YAAA,UAmBC;QAGDpB,EAAA,CAAAK,SAAA,EA6BC;QA7BDL,EAAA,CAAAkB,aAAA,CAAAgI,GAAA,CAAA3E,MAAA,IAAA2E,GAAA,CAAA9H,YAAA,UA6BC;QASGpB,EAAA,CAAAK,SAAA,GAAoD;QAApDL,EAAA,CAAAM,UAAA,yBAAAN,EAAA,CAAAgK,eAAA,IAAAC,GAAA,EAAoD;QAGhDjK,EAAA,CAAAK,SAAA,GAwCC;QAxCDL,EAAA,CAAA8D,UAAA,CAAAoF,GAAA,CAAAzD,KAAA,CAwCC;QAQLzF,EAAA,CAAAK,SAAA,GA0JC;QA1JDL,EAAA,CAAAkB,aAAA,CAAAgI,GAAA,CAAA1I,IAAA,WA0JC;;;mBDzPTjB,OAAO,EACPI,aAAa,EAAAuK,EAAA,CAAAC,OAAA,EACb1K,eAAe,EAAA2K,EAAA,CAAAC,aAAA,EACfxK,sBAAsB,EACtBL,gBAAgB,EAChBE,kBAAkB,EAAA4K,EAAA,CAAAC,YAAA,EAClB3K,cAAc,EAAA4K,EAAA,CAAAC,QAAA,EAAAC,EAAA,CAAAC,mBAAA,EACdvL,eAAe,EACfE,QAAQ;IAAAsL,MAAA;IAAAC,aAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}