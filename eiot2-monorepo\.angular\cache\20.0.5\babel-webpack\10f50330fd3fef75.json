{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class App {\n  constructor() {\n    this.title = 'eiot-admin';\n  }\n  static #_ = this.ɵfac = function App_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || App)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: App,\n    selectors: [[\"app-root\"]],\n    decls: 1,\n    vars: 0,\n    template: function App_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"router-outlet\");\n      }\n    },\n    dependencies: [RouterModule, i1.RouterOutlet],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["RouterModule", "App", "constructor", "title", "_", "_2", "selectors", "decls", "vars", "template", "App_Template", "rf", "ctx", "i0", "ɵɵelement", "i1", "RouterOutlet", "styles"], "sources": ["D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\app\\app.ts", "D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\app\\app.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { RouterModule } from '@angular/router';\n\n@Component({\n  imports: [RouterModule],\n  selector: 'app-root',\n  templateUrl: './app.html',\n  styleUrl: './app.scss',\n})\nexport class App {\n  protected title = 'eiot-admin';\n}\n", "<router-outlet></router-outlet>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;;AAQ9C,OAAM,MAAOC,GAAG;EANhBC,YAAA;IAOY,KAAAC,KAAK,GAAG,YAAY;;EAC/B,QAAAC,CAAA,G;qCAFYH,GAAG;EAAA;EAAA,QAAAI,EAAA,G;UAAHJ,GAAG;IAAAK,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,aAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCThBE,EAAA,CAAAC,SAAA,oBAA+B;;;mBDInBd,YAAY,EAAAe,EAAA,CAAAC,YAAA;IAAAC,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}