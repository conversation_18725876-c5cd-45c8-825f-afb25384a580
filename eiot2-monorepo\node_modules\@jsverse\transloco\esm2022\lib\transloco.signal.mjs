import { assertInInjectionContext, computed, inject, Injector, isSignal, runInInjectionContext, } from '@angular/core';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { switchMap } from 'rxjs';
import { TRANSLOCO_SCOPE } from './transloco-scope';
import { TranslocoService } from './transloco.service';
/**
 * Gets the translated value of a key as Signal
 *
 * @example
 * text = translateSignal('hello');
 * textList = translateSignal(['green', 'blue']);
 * textVar = translateSignal('hello', { variable: 'world' });
 * textSpanish = translateSignal('hello', { variable: 'world' }, 'es');
 * textTodosScope = translateSignal('hello', { variable: 'world' }, { scope: 'todos' });
 *
 * @example
 * dynamicKey = signal('hello');
 * dynamicParam = signal({ variable: 'world' });
 * text = translateSignal(this.dynamicKey, this.dynamicParam);
 *
 */
export function translateSignal(key, params, lang, injector) {
    if (!injector) {
        assertInInjectionContext(translateSignal);
    }
    injector ??= inject(Injector);
    const result = runInInjectionContext(injector, () => {
        const service = inject(TranslocoService);
        const scope = resolveScope(lang);
        return toObservable(computerKeysAndParams(key, params)).pipe(switchMap((dynamic) => service.selectTranslate(dynamic.key, dynamic.params, scope)));
    });
    return toSignal(result, { initialValue: Array.isArray(key) ? [''] : '' });
}
/**
 * Gets the translated object of a key as Signal
 *
 * @example
 * object = translateObjectSignal('nested.object');
 * title = object().title;
 *
 * @example
 * dynamicKey = signal('nested.object');
 * dynamicParam = signal({ variable: 'world' });
 * object = translateObjectSignal(this.dynamicKey, this.dynamicParam);
 */
export function translateObjectSignal(key, params, lang, injector) {
    if (!injector) {
        assertInInjectionContext(translateObjectSignal);
    }
    injector ??= inject(Injector);
    const result = runInInjectionContext(injector, () => {
        const service = inject(TranslocoService);
        const scope = resolveScope(lang);
        return toObservable(computerKeysAndParams(key, params)).pipe(switchMap((dynamic) => service.selectTranslateObject(dynamic.key, dynamic.params, scope)));
    });
    return toSignal(result, { initialValue: Array.isArray(key) ? [] : {} });
}
function computerParams(params) {
    if (isSignal(params)) {
        return computed(() => params());
    }
    return computed(() => {
        return Object.entries(params).reduce((acc, [key, value]) => {
            acc[key] = isSignal(value) ? value() : value;
            return acc;
        }, {});
    });
}
function computerKeys(keys) {
    if (Array.isArray(keys)) {
        return computed(() => keys.map((key) => (isSignal(key) ? key() : key)));
    }
    return computed(() => keys());
}
function isSignalKey(key) {
    return Array.isArray(key) ? key.some(isSignal) : isSignal(key);
}
function isSignalParams(params) {
    return params
        ? isSignal(params) || Object.values(params).some(isSignal)
        : false;
}
function computerKeysAndParams(key, params) {
    const computedKeys = isSignalKey(key)
        ? computerKeys(key)
        : computed(() => key);
    const computedParams = isSignalParams(params)
        ? computerParams(params)
        : computed(() => params);
    return computed(() => ({ key: computedKeys(), params: computedParams() }));
}
function resolveScope(scope) {
    if (typeof scope === 'undefined' || scope === '') {
        const translocoScope = inject(TRANSLOCO_SCOPE, { optional: true });
        return translocoScope ?? undefined;
    }
    return scope;
}
//# sourceMappingURL=data:application/json;base64,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