import { isBrowser } from './helpers';
/**
 * Returns the language code name from the browser, e.g. "en"
 */
export function getBrowserLang() {
    let browserLang = getBrowserCultureLang();
    if (!browserLang || !isBrowser()) {
        return undefined;
    }
    if (browserLang.indexOf('-') !== -1) {
        browserLang = browserLang.split('-')[0];
    }
    if (browserLang.indexOf('_') !== -1) {
        browserLang = browserLang.split('_')[0];
    }
    return browserLang;
}
/**
 * Returns the culture language code name from the browser, e.g. "en-US"
 */
export function getBrowserCultureLang() {
    if (!isBrowser()) {
        return '';
    }
    const navigator = window.navigator;
    return navigator.languages?.[0] ?? navigator.language;
}
//# sourceMappingURL=data:application/json;base64,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