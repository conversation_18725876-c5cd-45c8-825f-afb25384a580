{"name": "@angular/material-luxon-adapter", "version": "20.0.5", "description": "Angular Material Luxon Adapter", "repository": {"type": "git", "url": "https://github.com/angular/components.git"}, "license": "MIT", "bugs": {"url": "https://github.com/angular/components/issues"}, "homepage": "https://github.com/angular/components#readme", "peerDependencies": {"@angular/material": "20.0.5", "@angular/core": "^20.0.0 || ^21.0.0", "luxon": "^3.0.0"}, "devDependencies": {"@angular/material": "workspace:*"}, "dependencies": {"tslib": "^2.3.0"}, "ng-update": {"packageGroup": ["@angular/material", "@angular/cdk", "@angular/cdk-experimental", "@angular/material-experimental", "@angular/material-luxon-adapter", "@angular/material-moment-adapter", "@angular/material-date-fns-adapter"]}, "schematics": "./schematics/collection.json", "sideEffects": false, "module": "./fesm2022/material-luxon-adapter.mjs", "typings": "./index.d.ts", "type": "module", "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "default": "./fesm2022/material-luxon-adapter.mjs"}}}