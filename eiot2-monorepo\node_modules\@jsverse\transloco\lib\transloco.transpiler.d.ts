import { InjectionToken, Injector } from '@angular/core';
import { HashMap, Translation } from './types';
import { TranslocoConfig } from './transloco.config';
import * as i0 from "@angular/core";
export declare const TRANSLOCO_TRANSPILER: InjectionToken<TranslocoTranspiler>;
export interface TranslocoTranspiler {
    transpile(params: TranspileParams): any;
    onLangChanged?(lang: string): void;
}
export interface TranspileParams<V = unknown> {
    value: V;
    params?: HashMap;
    translation: Translation;
    key: string;
}
export declare class DefaultTranspiler implements TranslocoTranspiler {
    protected config: TranslocoConfig;
    protected get interpolationMatcher(): RegExp;
    transpile({ value, params, translation, key }: TranspileParams): any;
    /**
     *
     * @example
     *
     * const en = {
     *  a: {
     *    b: {
     *      c: "Hello {{ value }}"
     *    }
     *  }
     * }
     *
     * const params =  {
     *  "b.c": { value: "Transloco "}
     * }
     *
     * service.selectTranslate('a', params);
     *
     * // the first param will be the result of `en.a`.
     * // the second param will be `params`.
     * parser.transpile(value, params, {});
     *
     *
     */
    protected handleObject({ value, params, translation, key, }: TranspileParams<Record<any, any>>): Record<any, any>;
    protected handleArray({ value, ...rest }: TranspileParams<unknown[]>): any[];
    static ɵfac: i0.ɵɵFactoryDeclaration<DefaultTranspiler, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<DefaultTranspiler>;
}
export interface TranslocoTranspilerFunction {
    transpile(...args: string[]): any;
}
export declare function getFunctionArgs(argsString: string): string[];
export declare class FunctionalTranspiler extends DefaultTranspiler implements TranslocoTranspiler {
    protected injector: Injector;
    transpile({ value, ...rest }: TranspileParams): any;
    static ɵfac: i0.ɵɵFactoryDeclaration<FunctionalTranspiler, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<FunctionalTranspiler>;
}
