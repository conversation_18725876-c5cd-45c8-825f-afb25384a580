{"ast": null, "code": "import { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { inject } from '@angular/core';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { FuseLoadingService } from '@fuse/services/loading';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/progress-bar\";\nfunction FuseLoadingBarComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-progress-bar\", 0);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"mode\", ctx_r0.mode)(\"value\", ctx_r0.progress);\n  }\n}\nexport class FuseLoadingBarComponent {\n  constructor() {\n    this._fuseLoadingService = inject(FuseLoadingService);\n    this.autoMode = true;\n    this.progress = 0;\n    this.show = false;\n    this._unsubscribeAll = new Subject();\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Lifecycle hooks\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * On changes\n   *\n   * @param changes\n   */\n  ngOnChanges(changes) {\n    // Auto mode\n    if ('autoMode' in changes) {\n      // Set the auto mode in the service\n      this._fuseLoadingService.setAutoMode(coerceBooleanProperty(changes.autoMode.currentValue));\n    }\n  }\n  /**\n   * On init\n   */\n  ngOnInit() {\n    // Subscribe to the service\n    this._fuseLoadingService.mode$.pipe(takeUntil(this._unsubscribeAll)).subscribe(value => {\n      this.mode = value;\n    });\n    this._fuseLoadingService.progress$.pipe(takeUntil(this._unsubscribeAll)).subscribe(value => {\n      this.progress = value;\n    });\n    this._fuseLoadingService.show$.pipe(takeUntil(this._unsubscribeAll)).subscribe(value => {\n      this.show = value;\n    });\n  }\n  /**\n   * On destroy\n   */\n  ngOnDestroy() {\n    // Unsubscribe from all subscriptions\n    this._unsubscribeAll.next(null);\n    this._unsubscribeAll.complete();\n  }\n  static #_ = this.ɵfac = function FuseLoadingBarComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FuseLoadingBarComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: FuseLoadingBarComponent,\n    selectors: [[\"fuse-loading-bar\"]],\n    inputs: {\n      autoMode: \"autoMode\"\n    },\n    exportAs: [\"fuseLoadingBar\"],\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 1,\n    vars: 1,\n    consts: [[3, \"mode\", \"value\"]],\n    template: function FuseLoadingBarComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵconditionalCreate(0, FuseLoadingBarComponent_Conditional_0_Template, 1, 2, \"mat-progress-bar\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵconditional(ctx.show ? 0 : -1);\n      }\n    },\n    dependencies: [MatProgressBarModule, i1.MatProgressBar],\n    styles: [\"fuse-loading-bar {\\n  position: fixed;\\n  top: 0;\\n  z-index: 999;\\n  width: 100%;\\n  height: 6px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL2FwcHMvZWlvdC1hZG1pbi9zcmMvQGZ1c2UvY29tcG9uZW50cy9sb2FkaW5nLWJhci9sb2FkaW5nLWJhci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLGVBQUE7RUFDQSxNQUFBO0VBQ0EsWUFBQTtFQUNBLFdBQUE7RUFDQSxXQUFBO0FBQ0oiLCJzb3VyY2VzQ29udGVudCI6WyJmdXNlLWxvYWRpbmctYmFyIHtcbiAgICBwb3NpdGlvbjogZml4ZWQ7XG4gICAgdG9wOiAwO1xuICAgIHotaW5kZXg6IDk5OTtcbiAgICB3aWR0aDogMTAwJTtcbiAgICBoZWlnaHQ6IDZweDtcbn1cbiJdLCJzb3VyY2VSb290IjoiIn0= */\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["coerceBooleanProperty", "inject", "MatProgressBarModule", "FuseLoadingService", "Subject", "takeUntil", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r0", "mode", "progress", "FuseLoadingBarComponent", "constructor", "_fuseLoadingService", "autoMode", "show", "_unsubscribeAll", "ngOnChanges", "changes", "setAutoMode", "currentValue", "ngOnInit", "mode$", "pipe", "subscribe", "value", "progress$", "show$", "ngOnDestroy", "next", "complete", "_", "_2", "selectors", "inputs", "exportAs", "features", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "FuseLoadingBarComponent_Template", "rf", "ctx", "ɵɵconditionalCreate", "FuseLoadingBarComponent_Conditional_0_Template", "ɵɵconditional", "i1", "MatProgressBar", "styles", "encapsulation"], "sources": ["D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\@fuse\\components\\loading-bar\\loading-bar.component.ts", "D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\@fuse\\components\\loading-bar\\loading-bar.component.html"], "sourcesContent": ["import { coerceBooleanProperty } from '@angular/cdk/coercion';\n\nimport {\n    Component,\n    inject,\n    Input,\n    OnChanges,\n    OnDestroy,\n    OnInit,\n    SimpleChanges,\n    ViewEncapsulation,\n} from '@angular/core';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { FuseLoadingService } from '@fuse/services/loading';\nimport { Subject, takeUntil } from 'rxjs';\n\n@Component({\n    selector: 'fuse-loading-bar',\n    templateUrl: './loading-bar.component.html',\n    styleUrls: ['./loading-bar.component.scss'],\n    encapsulation: ViewEncapsulation.None,\n    exportAs: 'fuseLoadingBar',\n    imports: [MatProgressBarModule],\n})\nexport class FuseLoadingBarComponent implements OnChanges, OnInit, OnDestroy {\n    private _fuseLoadingService = inject(FuseLoadingService);\n\n    @Input() autoMode: boolean = true;\n    mode: 'determinate' | 'indeterminate';\n    progress: number = 0;\n    show: boolean = false;\n    private _unsubscribeAll: Subject<any> = new Subject<any>();\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Lifecycle hooks\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * On changes\n     *\n     * @param changes\n     */\n    ngOnChanges(changes: SimpleChanges): void {\n        // Auto mode\n        if ('autoMode' in changes) {\n            // Set the auto mode in the service\n            this._fuseLoadingService.setAutoMode(\n                coerceBooleanProperty(changes.autoMode.currentValue)\n            );\n        }\n    }\n\n    /**\n     * On init\n     */\n    ngOnInit(): void {\n        // Subscribe to the service\n        this._fuseLoadingService.mode$\n            .pipe(takeUntil(this._unsubscribeAll))\n            .subscribe((value) => {\n                this.mode = value;\n            });\n\n        this._fuseLoadingService.progress$\n            .pipe(takeUntil(this._unsubscribeAll))\n            .subscribe((value) => {\n                this.progress = value;\n            });\n\n        this._fuseLoadingService.show$\n            .pipe(takeUntil(this._unsubscribeAll))\n            .subscribe((value) => {\n                this.show = value;\n            });\n    }\n\n    /**\n     * On destroy\n     */\n    ngOnDestroy(): void {\n        // Unsubscribe from all subscriptions\n        this._unsubscribeAll.next(null);\n        this._unsubscribeAll.complete();\n    }\n}\n", "@if (show) {\n    <mat-progress-bar [mode]=\"mode\" [value]=\"progress\"></mat-progress-bar>\n}\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,uBAAuB;AAE7D,SAEIC,MAAM,QAOH,eAAe;AACtB,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;ICbrCC,EAAA,CAAAC,SAAA,0BAAsE;;;;IAAtCD,EAAd,CAAAE,UAAA,SAAAC,MAAA,CAAAC,IAAA,CAAa,UAAAD,MAAA,CAAAE,QAAA,CAAmB;;;ADuBtD,OAAM,MAAOC,uBAAuB;EARpCC,YAAA;IASY,KAAAC,mBAAmB,GAAGb,MAAM,CAACE,kBAAkB,CAAC;IAE/C,KAAAY,QAAQ,GAAY,IAAI;IAEjC,KAAAJ,QAAQ,GAAW,CAAC;IACpB,KAAAK,IAAI,GAAY,KAAK;IACb,KAAAC,eAAe,GAAiB,IAAIb,OAAO,EAAO;;EAE1D;EACA;EACA;EAEA;;;;;EAKAc,WAAWA,CAACC,OAAsB;IAC9B;IACA,IAAI,UAAU,IAAIA,OAAO,EAAE;MACvB;MACA,IAAI,CAACL,mBAAmB,CAACM,WAAW,CAChCpB,qBAAqB,CAACmB,OAAO,CAACJ,QAAQ,CAACM,YAAY,CAAC,CACvD;IACL;EACJ;EAEA;;;EAGAC,QAAQA,CAAA;IACJ;IACA,IAAI,CAACR,mBAAmB,CAACS,KAAK,CACzBC,IAAI,CAACnB,SAAS,CAAC,IAAI,CAACY,eAAe,CAAC,CAAC,CACrCQ,SAAS,CAAEC,KAAK,IAAI;MACjB,IAAI,CAAChB,IAAI,GAAGgB,KAAK;IACrB,CAAC,CAAC;IAEN,IAAI,CAACZ,mBAAmB,CAACa,SAAS,CAC7BH,IAAI,CAACnB,SAAS,CAAC,IAAI,CAACY,eAAe,CAAC,CAAC,CACrCQ,SAAS,CAAEC,KAAK,IAAI;MACjB,IAAI,CAACf,QAAQ,GAAGe,KAAK;IACzB,CAAC,CAAC;IAEN,IAAI,CAACZ,mBAAmB,CAACc,KAAK,CACzBJ,IAAI,CAACnB,SAAS,CAAC,IAAI,CAACY,eAAe,CAAC,CAAC,CACrCQ,SAAS,CAAEC,KAAK,IAAI;MACjB,IAAI,CAACV,IAAI,GAAGU,KAAK;IACrB,CAAC,CAAC;EACV;EAEA;;;EAGAG,WAAWA,CAAA;IACP;IACA,IAAI,CAACZ,eAAe,CAACa,IAAI,CAAC,IAAI,CAAC;IAC/B,IAAI,CAACb,eAAe,CAACc,QAAQ,EAAE;EACnC;EAAC,QAAAC,CAAA,G;qCA3DQpB,uBAAuB;EAAA;EAAA,QAAAqB,EAAA,G;UAAvBrB,uBAAuB;IAAAsB,SAAA;IAAAC,MAAA;MAAApB,QAAA;IAAA;IAAAqB,QAAA;IAAAC,QAAA,GAAA/B,EAAA,CAAAgC,oBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCxBpCtC,EAAA,CAAAwC,mBAAA,IAAAC,8CAAA,8BAAY;;;QAAZzC,EAAA,CAAA0C,aAAA,CAAAH,GAAA,CAAA7B,IAAA,UAEC;;;mBDoBad,oBAAoB,EAAA+C,EAAA,CAAAC,cAAA;IAAAC,MAAA;IAAAC,aAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}