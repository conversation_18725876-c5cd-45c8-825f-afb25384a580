import { InjectionToken } from '@angular/core';
import { AvailableLangs } from './types';
export interface TranslocoConfig {
    defaultLang: string;
    reRenderOnLangChange: boolean;
    prodMode: boolean;
    fallbackLang?: string | string[];
    failedRetries: number;
    availableLangs: AvailableLangs;
    flatten: {
        aot: boolean;
    };
    missingHandler: {
        logMissingKey: boolean;
        useFallbackTranslation: boolean;
        allowEmpty: boolean;
    };
    interpolation: [string, string];
    scopes: {
        keepCasing?: boolean;
    };
}
export declare const TRANSLOCO_CONFIG: InjectionToken<TranslocoConfig>;
export declare const defaultConfig: TranslocoConfig;
type DeepPartial<T> = T extends Array<any> ? T : T extends object ? {
    [P in keyof T]?: DeepPartial<T[P]>;
} : T;
export type PartialTranslocoConfig = DeepPartial<TranslocoConfig>;
export declare function translocoConfig(config?: PartialTranslocoConfig): TranslocoConfig;
export {};
