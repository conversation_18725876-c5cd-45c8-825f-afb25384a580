import { take } from 'rxjs';
import { hasInlineLoader, isString } from './helpers';
/*
 * @example
 *
 * given: lazy-page/en => lazy-page
 *
 */
export function getScopeFromLang(lang) {
    if (!lang) {
        return '';
    }
    const split = lang.split('/');
    split.pop();
    return split.join('/');
}
/*
 * @example
 *
 * given: lazy-page/en => en
 *
 */
export function getLangFromScope(lang) {
    if (!lang) {
        return '';
    }
    return lang.split('/').pop();
}
/**
 * @example
 *
 * getPipeValue('todos|scoped', 'scoped') [true, 'todos']
 * getPipeValue('en|static', 'static') [true, 'en']
 * getPipeValue('en', 'static') [false, 'en']
 */
export function getPipeValue(str, value, char = '|') {
    if (isString(str)) {
        const splitted = str.split(char);
        const lastItem = splitted.pop();
        return lastItem === value ? [true, splitted.toString()] : [false, lastItem];
    }
    return [false, ''];
}
export function shouldListenToLangChanges(service, lang) {
    const [hasStatic] = getPipeValue(lang, 'static');
    if (!hasStatic) {
        // If we didn't get 'lang|static' check if it's set in the global level
        return !!service.config.reRenderOnLangChange;
    }
    // We have 'lang|static' so don't listen to lang changes
    return false;
}
export function listenOrNotOperator(listenToLangChange) {
    return listenToLangChange ? (source) => source : take(1);
}
function prependScope(inlineLoader, scope) {
    return Object.keys(inlineLoader).reduce((acc, lang) => {
        acc[`${scope}/${lang}`] = inlineLoader[lang];
        return acc;
    }, {});
}
export function resolveInlineLoader(providerScope, scope) {
    return hasInlineLoader(providerScope)
        ? prependScope(providerScope.loader, scope)
        : undefined;
}
export function getEventPayload(lang) {
    return {
        scope: getScopeFromLang(lang) || null,
        langName: getLangFromScope(lang),
    };
}
//# sourceMappingURL=data:application/json;base64,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