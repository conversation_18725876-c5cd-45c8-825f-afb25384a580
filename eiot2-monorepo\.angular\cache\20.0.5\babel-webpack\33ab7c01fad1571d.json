{"ast": null, "code": "import { ChangeDetectorRef, inject } from '@angular/core';\nimport { fuseAnimations } from '@fuse/animations';\nimport { FuseNavigationService } from '@fuse/components/navigation/navigation.service';\nimport { FuseUtilsService } from '@fuse/services/utils/utils.service';\nimport { ReplaySubject, Subject } from 'rxjs';\nimport { FuseHorizontalNavigationBasicItemComponent } from './components/basic/basic.component';\nimport { FuseHorizontalNavigationBranchItemComponent } from './components/branch/branch.component';\nimport { FuseHorizontalNavigationSpacerItemComponent } from './components/spacer/spacer.component';\nimport * as i0 from \"@angular/core\";\nfunction FuseHorizontalNavigationComponent_For_2_Conditional_0_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"fuse-horizontal-navigation-basic-item\", 1);\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"item\", item_r1)(\"name\", ctx_r1.name);\n  }\n}\nfunction FuseHorizontalNavigationComponent_For_2_Conditional_0_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"fuse-horizontal-navigation-branch-item\", 1);\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"item\", item_r1)(\"name\", ctx_r1.name);\n  }\n}\nfunction FuseHorizontalNavigationComponent_For_2_Conditional_0_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"fuse-horizontal-navigation-spacer-item\", 1);\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"item\", item_r1)(\"name\", ctx_r1.name);\n  }\n}\nfunction FuseHorizontalNavigationComponent_For_2_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵconditionalCreate(0, FuseHorizontalNavigationComponent_For_2_Conditional_0_Conditional_0_Template, 1, 2, \"fuse-horizontal-navigation-basic-item\", 1);\n    i0.ɵɵconditionalCreate(1, FuseHorizontalNavigationComponent_For_2_Conditional_0_Conditional_1_Template, 1, 2, \"fuse-horizontal-navigation-branch-item\", 1);\n    i0.ɵɵconditionalCreate(2, FuseHorizontalNavigationComponent_For_2_Conditional_0_Conditional_2_Template, 1, 2, \"fuse-horizontal-navigation-spacer-item\", 1);\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵconditional(item_r1.type === \"basic\" ? 0 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(item_r1.type === \"aside\" || item_r1.type === \"collapsable\" || item_r1.type === \"group\" ? 1 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(item_r1.type === \"spacer\" ? 2 : -1);\n  }\n}\nfunction FuseHorizontalNavigationComponent_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵconditionalCreate(0, FuseHorizontalNavigationComponent_For_2_Conditional_0_Template, 3, 3);\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    i0.ɵɵconditional(item_r1.hidden && !item_r1.hidden(item_r1) || !item_r1.hidden ? 0 : -1);\n  }\n}\nexport class FuseHorizontalNavigationComponent {\n  constructor() {\n    this._changeDetectorRef = inject(ChangeDetectorRef);\n    this._fuseNavigationService = inject(FuseNavigationService);\n    this._fuseUtilsService = inject(FuseUtilsService);\n    this.name = this._fuseUtilsService.randomId();\n    this.onRefreshed = new ReplaySubject(1);\n    this._unsubscribeAll = new Subject();\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Lifecycle hooks\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * On changes\n   *\n   * @param changes\n   */\n  ngOnChanges(changes) {\n    // Navigation\n    if ('navigation' in changes) {\n      // Mark for check\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /**\n   * On init\n   */\n  ngOnInit() {\n    // Make sure the name input is not an empty string\n    if (this.name === '') {\n      this.name = this._fuseUtilsService.randomId();\n    }\n    // Register the navigation component\n    this._fuseNavigationService.registerComponent(this.name, this);\n  }\n  /**\n   * On destroy\n   */\n  ngOnDestroy() {\n    // Deregister the navigation component from the registry\n    this._fuseNavigationService.deregisterComponent(this.name);\n    // Unsubscribe from all subscriptions\n    this._unsubscribeAll.next(null);\n    this._unsubscribeAll.complete();\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Public methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * Refresh the component to apply the changes\n   */\n  refresh() {\n    // Mark for check\n    this._changeDetectorRef.markForCheck();\n    // Execute the observable\n    this.onRefreshed.next(true);\n  }\n  /**\n   * Track by function for ngFor loops\n   *\n   * @param index\n   * @param item\n   */\n  trackByFn(index, item) {\n    return item.id || index;\n  }\n  static #_ = this.ɵfac = function FuseHorizontalNavigationComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FuseHorizontalNavigationComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: FuseHorizontalNavigationComponent,\n    selectors: [[\"fuse-horizontal-navigation\"]],\n    inputs: {\n      name: \"name\",\n      navigation: \"navigation\"\n    },\n    exportAs: [\"fuseHorizontalNavigation\"],\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 3,\n    vars: 0,\n    consts: [[1, \"fuse-horizontal-navigation-wrapper\"], [1, \"fuse-horizontal-navigation-menu-item\", 3, \"item\", \"name\"]],\n    template: function FuseHorizontalNavigationComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵrepeaterCreate(1, FuseHorizontalNavigationComponent_For_2_Template, 1, 1, null, null, ctx.trackByFn, true);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵrepeater(ctx.navigation);\n      }\n    },\n    dependencies: [FuseHorizontalNavigationBasicItemComponent, FuseHorizontalNavigationBranchItemComponent, FuseHorizontalNavigationSpacerItemComponent],\n    styles: [\"var resource;\\n/******/ (() => { // webpackBootstrap\\n/******/ \\tvar __webpack_modules__ = ({\\n\\n/***/ 879:\\n/*!*********************************************************************************************************!*\\\\\\n  !*** ./apps/eiot-admin/src/@fuse/components/navigation/horizontal/horizontal.component.scss?ngResource ***!\\n  \\\\*********************************************************************************************************/\\n/***/ (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/postcss-loader/dist/cjs.js):\\\\nError: Cannot find module 'chroma-js'\\\\nRequire stack:\\\\n- D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\apps\\\\\\\\eiot-admin\\\\\\\\src\\\\\\\\@fuse\\\\\\\\tailwind\\\\\\\\utils\\\\\\\\generate-palette.js\\\\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\\\\n    at Function.resolve (node:internal/modules/helpers:193:19)\\\\n    at _resolve (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:246378)\\\\n    at jiti (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:249092)\\\\n    at D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\apps\\\\\\\\eiot-admin\\\\\\\\src\\\\\\\\@fuse\\\\\\\\tailwind\\\\\\\\utils\\\\\\\\generate-palette.js:1:91\\\\n    at evalModule (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:251913)\\\\n    at jiti (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:249841)\\\\n    at D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\apps\\\\\\\\eiot-admin\\\\\\\\tailwind.config.js:4:25\\\\n    at evalModule (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:251913)\\\\n    at jiti (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:249841)\\\");\\n\\n/***/ })\\n\\n/******/ \\t});\\n/************************************************************************/\\n/******/ \\t\\n/******/ \\t// startup\\n/******/ \\t// Load entry module and return exports\\n/******/ \\t// This entry module doesn't tell about it's top-level declarations so it can't be inlined\\n/******/ \\tvar __webpack_exports__ = {};\\n/******/ \\t__webpack_modules__[879]();\\n/******/ \\tresource = __webpack_exports__;\\n/******/ \\t\\n/******/ })()\\n;\"],\n    encapsulation: 2,\n    data: {\n      animation: fuseAnimations\n    },\n    changeDetection: 0\n  });\n}", "map": {"version": 3, "names": ["ChangeDetectorRef", "inject", "fuseAnimations", "FuseNavigationService", "FuseUtilsService", "ReplaySubject", "Subject", "FuseHorizontalNavigationBasicItemComponent", "FuseHorizontalNavigationBranchItemComponent", "FuseHorizontalNavigationSpacerItemComponent", "i0", "ɵɵelement", "ɵɵproperty", "item_r1", "ctx_r1", "name", "ɵɵconditionalCreate", "FuseHorizontalNavigationComponent_For_2_Conditional_0_Conditional_0_Template", "FuseHorizontalNavigationComponent_For_2_Conditional_0_Conditional_1_Template", "FuseHorizontalNavigationComponent_For_2_Conditional_0_Conditional_2_Template", "ɵɵconditional", "type", "ɵɵadvance", "FuseHorizontalNavigationComponent_For_2_Conditional_0_Template", "hidden", "FuseHorizontalNavigationComponent", "constructor", "_changeDetectorRef", "_fuseNavigationService", "_fuseUtilsService", "randomId", "onRefreshed", "_unsubscribeAll", "ngOnChanges", "changes", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "registerComponent", "ngOnDestroy", "deregisterComponent", "next", "complete", "refresh", "trackByFn", "index", "item", "id", "_", "_2", "selectors", "inputs", "navigation", "exportAs", "features", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "FuseHorizontalNavigationComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵrepeaterCreate", "FuseHorizontalNavigationComponent_For_2_Template", "ɵɵelementEnd", "ɵɵrepeater", "styles", "encapsulation", "data", "animation", "changeDetection"], "sources": ["D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\@fuse\\components\\navigation\\horizontal\\horizontal.component.ts", "D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\@fuse\\components\\navigation\\horizontal\\horizontal.component.html"], "sourcesContent": ["import {\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    Input,\n    OnChanges,\n    OnDestroy,\n    OnInit,\n    SimpleChanges,\n    ViewEncapsulation,\n    inject,\n} from '@angular/core';\nimport { fuseAnimations } from '@fuse/animations';\nimport { FuseNavigationService } from '@fuse/components/navigation/navigation.service';\nimport { FuseNavigationItem } from '@fuse/components/navigation/navigation.types';\nimport { FuseUtilsService } from '@fuse/services/utils/utils.service';\nimport { ReplaySubject, Subject } from 'rxjs';\nimport { FuseHorizontalNavigationBasicItemComponent } from './components/basic/basic.component';\nimport { FuseHorizontalNavigationBranchItemComponent } from './components/branch/branch.component';\nimport { FuseHorizontalNavigationSpacerItemComponent } from './components/spacer/spacer.component';\n\n@Component({\n    selector: 'fuse-horizontal-navigation',\n    templateUrl: './horizontal.component.html',\n    styleUrls: ['./horizontal.component.scss'],\n    animations: fuseAnimations,\n    encapsulation: ViewEncapsulation.None,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    exportAs: 'fuseHorizontalNavigation',\n    imports: [\n        FuseHorizontalNavigationBasicItemComponent,\n        FuseHorizontalNavigationBranchItemComponent,\n        FuseHorizontalNavigationSpacerItemComponent,\n    ],\n})\nexport class FuseHorizontalNavigationComponent\n    implements OnChanges, OnInit, OnDestroy\n{\n    private _changeDetectorRef = inject(ChangeDetectorRef);\n    private _fuseNavigationService = inject(FuseNavigationService);\n    private _fuseUtilsService = inject(FuseUtilsService);\n\n    @Input() name: string = this._fuseUtilsService.randomId();\n    @Input() navigation: FuseNavigationItem[];\n\n    onRefreshed: ReplaySubject<boolean> = new ReplaySubject<boolean>(1);\n    private _unsubscribeAll: Subject<any> = new Subject<any>();\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Lifecycle hooks\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * On changes\n     *\n     * @param changes\n     */\n    ngOnChanges(changes: SimpleChanges): void {\n        // Navigation\n        if ('navigation' in changes) {\n            // Mark for check\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n\n    /**\n     * On init\n     */\n    ngOnInit(): void {\n        // Make sure the name input is not an empty string\n        if (this.name === '') {\n            this.name = this._fuseUtilsService.randomId();\n        }\n\n        // Register the navigation component\n        this._fuseNavigationService.registerComponent(this.name, this);\n    }\n\n    /**\n     * On destroy\n     */\n    ngOnDestroy(): void {\n        // Deregister the navigation component from the registry\n        this._fuseNavigationService.deregisterComponent(this.name);\n\n        // Unsubscribe from all subscriptions\n        this._unsubscribeAll.next(null);\n        this._unsubscribeAll.complete();\n    }\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Public methods\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * Refresh the component to apply the changes\n     */\n    refresh(): void {\n        // Mark for check\n        this._changeDetectorRef.markForCheck();\n\n        // Execute the observable\n        this.onRefreshed.next(true);\n    }\n\n    /**\n     * Track by function for ngFor loops\n     *\n     * @param index\n     * @param item\n     */\n    trackByFn(index: number, item: any): any {\n        return item.id || index;\n    }\n}\n", "<div class=\"fuse-horizontal-navigation-wrapper\">\n    @for (item of navigation; track trackByFn($index, item)) {\n        <!-- Skip the hidden items -->\n        @if ((item.hidden && !item.hidden(item)) || !item.hidden) {\n            <!-- Basic -->\n            @if (item.type === 'basic') {\n                <fuse-horizontal-navigation-basic-item\n                    class=\"fuse-horizontal-navigation-menu-item\"\n                    [item]=\"item\"\n                    [name]=\"name\"\n                ></fuse-horizontal-navigation-basic-item>\n            }\n\n            <!-- Branch: aside, collapsable, group -->\n            @if (\n                item.type === 'aside' ||\n                item.type === 'collapsable' ||\n                item.type === 'group'\n            ) {\n                <fuse-horizontal-navigation-branch-item\n                    class=\"fuse-horizontal-navigation-menu-item\"\n                    [item]=\"item\"\n                    [name]=\"name\"\n                ></fuse-horizontal-navigation-branch-item>\n            }\n\n            <!-- Spacer -->\n            @if (item.type === 'spacer') {\n                <fuse-horizontal-navigation-spacer-item\n                    class=\"fuse-horizontal-navigation-menu-item\"\n                    [item]=\"item\"\n                    [name]=\"name\"\n                ></fuse-horizontal-navigation-spacer-item>\n            }\n        }\n    }\n</div>\n"], "mappings": "AAAA,SAEIA,iBAAiB,EAQjBC,MAAM,QACH,eAAe;AACtB,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,qBAAqB,QAAQ,gDAAgD;AAEtF,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,aAAa,EAAEC,OAAO,QAAQ,MAAM;AAC7C,SAASC,0CAA0C,QAAQ,oCAAoC;AAC/F,SAASC,2CAA2C,QAAQ,sCAAsC;AAClG,SAASC,2CAA2C,QAAQ,sCAAsC;;;;ICblFC,EAAA,CAAAC,SAAA,+CAIyC;;;;;IADrCD,EADA,CAAAE,UAAA,SAAAC,OAAA,CAAa,SAAAC,MAAA,CAAAC,IAAA,CACA;;;;;IAUjBL,EAAA,CAAAC,SAAA,gDAI0C;;;;;IADtCD,EADA,CAAAE,UAAA,SAAAC,OAAA,CAAa,SAAAC,MAAA,CAAAC,IAAA,CACA;;;;;IAMjBL,EAAA,CAAAC,SAAA,gDAI0C;;;;;IADtCD,EADA,CAAAE,UAAA,SAAAC,OAAA,CAAa,SAAAC,MAAA,CAAAC,IAAA,CACA;;;;;IA1BrBL,EAAA,CAAAM,mBAAA,IAAAC,4EAAA,mDAA6B;IAS7BP,EAAA,CAAAM,mBAAA,IAAAE,4EAAA,oDAIG;IASHR,EAAA,CAAAM,mBAAA,IAAAG,4EAAA,oDAA8B;;;;IAtB9BT,EAAA,CAAAU,aAAA,CAAAP,OAAA,CAAAQ,IAAA,sBAMC;IAGDX,EAAA,CAAAY,SAAA,EAUC;IAVDZ,EAAA,CAAAU,aAAA,CAAAP,OAAA,CAAAQ,IAAA,gBAAAR,OAAA,CAAAQ,IAAA,sBAAAR,OAAA,CAAAQ,IAAA,sBAUC;IAGDX,EAAA,CAAAY,SAAA,EAMC;IANDZ,EAAA,CAAAU,aAAA,CAAAP,OAAA,CAAAQ,IAAA,uBAMC;;;;;IA9BLX,EAAA,CAAAM,mBAAA,IAAAO,8DAAA,OAA2D;;;;IAA3Db,EAAA,CAAAU,aAAA,CAAAP,OAAA,CAAAW,MAAA,KAAAX,OAAA,CAAAW,MAAA,CAAAX,OAAA,MAAAA,OAAA,CAAAW,MAAA,UA+BC;;;ADCT,OAAM,MAAOC,iCAAiC;EAd9CC,YAAA;IAiBY,KAAAC,kBAAkB,GAAG1B,MAAM,CAACD,iBAAiB,CAAC;IAC9C,KAAA4B,sBAAsB,GAAG3B,MAAM,CAACE,qBAAqB,CAAC;IACtD,KAAA0B,iBAAiB,GAAG5B,MAAM,CAACG,gBAAgB,CAAC;IAE3C,KAAAW,IAAI,GAAW,IAAI,CAACc,iBAAiB,CAACC,QAAQ,EAAE;IAGzD,KAAAC,WAAW,GAA2B,IAAI1B,aAAa,CAAU,CAAC,CAAC;IAC3D,KAAA2B,eAAe,GAAiB,IAAI1B,OAAO,EAAO;;EAE1D;EACA;EACA;EAEA;;;;;EAKA2B,WAAWA,CAACC,OAAsB;IAC9B;IACA,IAAI,YAAY,IAAIA,OAAO,EAAE;MACzB;MACA,IAAI,CAACP,kBAAkB,CAACQ,YAAY,EAAE;IAC1C;EACJ;EAEA;;;EAGAC,QAAQA,CAAA;IACJ;IACA,IAAI,IAAI,CAACrB,IAAI,KAAK,EAAE,EAAE;MAClB,IAAI,CAACA,IAAI,GAAG,IAAI,CAACc,iBAAiB,CAACC,QAAQ,EAAE;IACjD;IAEA;IACA,IAAI,CAACF,sBAAsB,CAACS,iBAAiB,CAAC,IAAI,CAACtB,IAAI,EAAE,IAAI,CAAC;EAClE;EAEA;;;EAGAuB,WAAWA,CAAA;IACP;IACA,IAAI,CAACV,sBAAsB,CAACW,mBAAmB,CAAC,IAAI,CAACxB,IAAI,CAAC;IAE1D;IACA,IAAI,CAACiB,eAAe,CAACQ,IAAI,CAAC,IAAI,CAAC;IAC/B,IAAI,CAACR,eAAe,CAACS,QAAQ,EAAE;EACnC;EAEA;EACA;EACA;EAEA;;;EAGAC,OAAOA,CAAA;IACH;IACA,IAAI,CAACf,kBAAkB,CAACQ,YAAY,EAAE;IAEtC;IACA,IAAI,CAACJ,WAAW,CAACS,IAAI,CAAC,IAAI,CAAC;EAC/B;EAEA;;;;;;EAMAG,SAASA,CAACC,KAAa,EAAEC,IAAS;IAC9B,OAAOA,IAAI,CAACC,EAAE,IAAIF,KAAK;EAC3B;EAAC,QAAAG,CAAA,G;qCA9EQtB,iCAAiC;EAAA;EAAA,QAAAuB,EAAA,G;UAAjCvB,iCAAiC;IAAAwB,SAAA;IAAAC,MAAA;MAAAnC,IAAA;MAAAoC,UAAA;IAAA;IAAAC,QAAA;IAAAC,QAAA,GAAA3C,EAAA,CAAA4C,oBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,2CAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCnC9ClD,EAAA,CAAAoD,cAAA,aAAgD;QAC5CpD,EAAA,CAAAqD,gBAAA,IAAAC,gDAAA,oBAAAH,GAAA,CAAAlB,SAAA,OAkCC;QACLjC,EAAA,CAAAuD,YAAA,EAAM;;;QAnCFvD,EAAA,CAAAY,SAAA,EAkCC;QAlCDZ,EAAA,CAAAwD,UAAA,CAAAL,GAAA,CAAAV,UAAA,CAkCC;;;mBDLG5C,0CAA0C,EAC1CC,2CAA2C,EAC3CC,2CAA2C;IAAA0D,MAAA;IAAAC,aAAA;IAAAC,IAAA;MAAAC,SAAA,EAPnCpE;IAAc;IAAAqE,eAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}