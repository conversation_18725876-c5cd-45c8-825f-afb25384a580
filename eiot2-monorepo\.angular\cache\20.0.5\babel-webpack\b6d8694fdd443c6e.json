{"ast": null, "code": "import { NgClass } from '@angular/common';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { FuseDrawerComponent } from '@fuse/components/drawer';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@fuse/services/config\";\nimport * as i3 from \"@angular/material/icon\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/tooltip\";\nfunction SettingsComponent_For_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵlistener(\"click\", function SettingsComponent_For_16_Template_div_click_0_listener() {\n      const theme_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.setTheme(theme_r4.id));\n    });\n    i0.ɵɵelement(1, \"div\", 73);\n    i0.ɵɵelementStart(2, \"div\", 74);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const theme_r4 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"ring-2\", ctx_r4.config.theme === theme_r4.id);\n    i0.ɵɵproperty(\"ngClass\", theme_r4.id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"text-secondary\", ctx_r4.config.theme !== theme_r4.id);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", theme_r4.name, \" \");\n  }\n}\nexport class SettingsComponent {\n  /**\n   * Constructor\n   */\n  constructor(_router, _fuseConfigService) {\n    this._router = _router;\n    this._fuseConfigService = _fuseConfigService;\n    this._unsubscribeAll = new Subject();\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Lifecycle hooks\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * On init\n   */\n  ngOnInit() {\n    // Subscribe to config changes\n    this._fuseConfigService.config$.pipe(takeUntil(this._unsubscribeAll)).subscribe(config => {\n      // Store the config\n      this.config = config;\n    });\n  }\n  /**\n   * On destroy\n   */\n  ngOnDestroy() {\n    // Unsubscribe from all subscriptions\n    this._unsubscribeAll.next(null);\n    this._unsubscribeAll.complete();\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Public methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * Set the layout on the config\n   *\n   * @param layout\n   */\n  setLayout(layout) {\n    // Clear the 'layout' query param to allow layout changes\n    this._router.navigate([], {\n      queryParams: {\n        layout: null\n      },\n      queryParamsHandling: 'merge'\n    }).then(() => {\n      // Set the config\n      this._fuseConfigService.config = {\n        layout\n      };\n    });\n  }\n  /**\n   * Set the scheme on the config\n   *\n   * @param scheme\n   */\n  setScheme(scheme) {\n    this._fuseConfigService.config = {\n      scheme\n    };\n  }\n  /**\n   * Set the theme on the config\n   *\n   * @param theme\n   */\n  setTheme(theme) {\n    this._fuseConfigService.config = {\n      theme\n    };\n  }\n  static #_ = this.ɵfac = function SettingsComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || SettingsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.FuseConfigService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: SettingsComponent,\n    selectors: [[\"settings\"]],\n    decls: 219,\n    vars: 69,\n    consts: [[\"settingsDrawer\", \"\"], [1, \"settings-cog\", \"fixed\", \"right-0\", \"z-90\", \"flex\", \"h-10\", \"w-10\", \"cursor-pointer\", \"items-center\", \"justify-center\", \"rounded-l-lg\", \"bg-red-600\", \"bg-opacity-90\", \"shadow-lg\", \"print:hidden\", 2, \"top\", \"275px\", 3, \"click\"], [1, \"animate-spin-slow\", \"text-white\", \"icon-size-5\", 3, \"svgIcon\"], [\"fixed\", \"\", 1, \"z-999\", \"w-screen\", \"min-w-screen\", \"sm:w-100\", \"sm:min-w-100\", 3, \"mode\", \"name\", \"position\"], [1, \"bg-card\", \"flex\", \"w-full\", \"flex-col\", \"overflow-auto\"], [1, \"flex\", \"h-20\", \"min-h-20\", \"flex-row\", \"items-center\", \"bg-primary\", \"px-6\", \"text-white\"], [1, \"text-current\", \"icon-size-7\", 3, \"svgIcon\"], [1, \"ml-3\", \"text-2xl\", \"font-semibold\", \"tracking-tight\"], [\"mat-icon-button\", \"\", 1, \"ml-auto\", 3, \"click\"], [1, \"text-current\", 3, \"svgIcon\"], [1, \"flex\", \"flex-col\", \"p-6\"], [1, \"text-secondary\", \"text-md\", \"font-semibold\"], [1, \"mt-6\", \"grid\", \"grid-cols-2\", \"gap-3\", \"sm:grid-cols-3\"], [1, \"bg-hover\", \"flex\", \"cursor-pointer\", \"items-center\", \"justify-center\", \"rounded-full\", \"px-4\", \"py-3\", \"ring-inset\", \"ring-primary\", 3, \"ring-2\", \"ngClass\"], [1, \"my-8\"], [1, \"mt-6\", \"grid\", \"grid-cols-3\", \"justify-items-start\", \"gap-3\"], [\"matTooltip\", \"Automatically sets the scheme based on user's operating system's color scheme preference using 'prefer-color-scheme' media query.\", 1, \"bg-hover\", \"flex\", \"cursor-pointer\", \"items-center\", \"rounded-full\", \"py-3\", \"pl-5\", \"pr-6\", \"ring-inset\", \"ring-primary\", 3, \"click\"], [1, \"flex\", \"items-center\", \"overflow-hidden\", \"rounded-full\"], [1, \"icon-size-5\", 3, \"svgIcon\"], [1, \"ml-2\", \"flex\", \"items-center\", \"font-medium\", \"leading-5\"], [1, \"bg-hover\", \"flex\", \"cursor-pointer\", \"items-center\", \"rounded-full\", \"py-3\", \"pl-5\", \"pr-6\", \"ring-inset\", \"ring-primary\", 3, \"click\"], [1, \"mt-6\", \"grid\", \"grid-cols-3\", \"gap-3\"], [1, \"flex\", \"cursor-pointer\", \"flex-col\", 3, \"click\"], [1, \"flex\", \"h-20\", \"flex-col\", \"overflow-hidden\", \"rounded-md\", \"border-2\", \"hover:opacity-80\"], [1, \"flex\", \"flex-auto\", \"flex-col\", \"bg-gray-50\", \"dark:bg-gray-900\"], [1, \"text-secondary\", \"mt-2\", \"text-center\", \"text-md\", \"font-medium\"], [1, \"flex\", \"h-20\", \"overflow-hidden\", \"rounded-md\", \"border-2\", \"hover:opacity-80\"], [1, \"w-8\", \"bg-gray-100\", \"dark:bg-gray-800\"], [1, \"mx-1.5\", \"mt-3\", \"space-y-1\"], [1, \"h-1\", \"rounded-sm\", \"bg-gray-300\", \"dark:bg-gray-700\"], [1, \"flex\", \"flex-auto\", \"flex-col\", \"border-l\"], [1, \"h-3\", \"bg-gray-100\", \"dark:bg-gray-800\"], [1, \"mr-1.5\", \"flex\", \"h-full\", \"items-center\", \"justify-end\"], [1, \"ml-1\", \"h-1\", \"w-1\", \"rounded-full\", \"bg-gray-300\", \"dark:bg-gray-700\"], [1, \"flex\", \"flex-auto\", \"border-t\", \"bg-gray-50\", \"dark:bg-gray-900\"], [1, \"mx-1\", \"mt-1\", \"flex\", \"items-center\"], [1, \"h-1\", \"w-1\", \"rounded-full\", \"bg-gray-300\", \"dark:bg-gray-700\"], [1, \"ml-auto\", \"h-1\", \"w-1\", \"rounded-full\", \"bg-gray-300\", \"dark:bg-gray-700\"], [1, \"ml-0.5\", \"h-1\", \"w-1\", \"rounded-full\", \"bg-gray-300\", \"dark:bg-gray-700\"], [1, \"mx-auto\", \"mt-2.5\", \"h-4\", \"w-4\", \"rounded-full\", \"bg-gray-300\", \"dark:bg-gray-700\"], [1, \"mx-1\", \"mt-2\", \"space-y-1\"], [1, \"mr-2\", \"flex\", \"h-full\", \"items-center\", \"justify-end\"], [1, \"w-5\", \"bg-gray-100\", \"dark:bg-gray-800\"], [1, \"mx-auto\", \"mt-2\", \"h-3\", \"w-3\", \"rounded-sm\", \"bg-gray-300\", \"dark:bg-gray-700\"], [1, \"mt-2\", \"flex\", \"w-full\", \"flex-col\", \"items-center\", \"space-y-1\"], [1, \"h-2.5\", \"w-3\", \"rounded-sm\", \"bg-gray-300\", \"dark:bg-gray-700\"], [1, \"w-4\", \"bg-gray-100\", \"dark:bg-gray-800\"], [1, \"mx-auto\", \"mt-2\", \"h-2\", \"w-2\", \"rounded-sm\", \"bg-gray-300\", \"dark:bg-gray-700\"], [1, \"h-2\", \"w-2\", \"rounded-sm\", \"bg-gray-300\", \"dark:bg-gray-700\"], [1, \"flex\", \"h-full\", \"flex-auto\", \"flex-col\", \"space-y-1\", \"px-1.5\", \"py-3\"], [1, \"flex-auto\"], [1, \"w-3\", \"bg-gray-100\", \"dark:bg-gray-800\"], [1, \"mx-auto\", \"mt-2\", \"h-1.5\", \"w-1.5\", \"rounded-sm\", \"bg-gray-300\", \"dark:bg-gray-700\"], [1, \"h-1.5\", \"w-1.5\", \"rounded-full\", \"bg-gray-300\", \"dark:bg-gray-700\"], [1, \"col-span-2\"], [1, \"mx-2\", \"my-1\", \"flex\", \"flex-auto\", \"flex-col\", \"overflow-hidden\", \"rounded-md\", \"border\"], [1, \"flex\", \"h-3\", \"items-center\", \"bg-gray-100\", \"dark:bg-gray-800\"], [1, \"ml-1.5\", \"flex\"], [1, \"ml-1\", \"h-1\", \"w-3\", \"rounded-full\", \"bg-gray-300\", \"dark:bg-gray-700\"], [1, \"ml-auto\", \"mr-1.5\", \"flex\", \"items-center\", \"justify-end\"], [1, \"flex\", \"h-3\", \"items-center\", \"bg-gray-100\", \"px-2\", \"dark:bg-gray-800\"], [1, \"h-2\", \"w-2\", \"rounded-full\", \"bg-gray-300\", \"dark:bg-gray-700\"], [1, \"ml-auto\", \"flex\", \"items-center\", \"justify-end\", \"space-x-1\"], [1, \"flex\", \"h-3\", \"items-center\", \"space-x-1\", \"border-b\", \"border-t\", \"bg-gray-100\", \"px-2\", \"dark:bg-gray-800\"], [1, \"h-1\", \"w-3\", \"rounded-full\", \"bg-gray-300\", \"dark:bg-gray-700\"], [1, \"mx-2\", \"my-1\", \"flex\", \"flex-auto\", \"flex-col\", \"overflow-hidden\", \"rounded\", \"border\"], [1, \"flex\", \"flex-auto\", \"bg-gray-50\", \"dark:bg-gray-900\"], [1, \"flex\", \"h-4\", \"items-center\", \"bg-gray-100\", \"px-2\", \"dark:bg-gray-800\"], [1, \"flex\", \"h-2\", \"items-center\", \"space-x-1\", \"bg-gray-100\", \"px-2\", \"dark:bg-gray-800\"], [1, \"flex\", \"h-4\", \"items-center\", \"border-b\", \"bg-gray-100\", \"px-2\", \"dark:bg-gray-800\"], [1, \"ml-2\", \"flex\", \"h-3\", \"items-center\", \"space-x-1\"], [1, \"flex\", \"flex-auto\", \"flex-col\"], [1, \"bg-hover\", \"flex\", \"cursor-pointer\", \"items-center\", \"justify-center\", \"rounded-full\", \"px-4\", \"py-3\", \"ring-inset\", \"ring-primary\", 3, \"click\", \"ngClass\"], [1, \"h-3\", \"w-3\", \"flex-0\", \"rounded-full\", \"bg-primary\"], [1, \"ml-2.5\", \"truncate\", \"font-medium\", \"leading-5\"]],\n    template: function SettingsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 1);\n        i0.ɵɵlistener(\"click\", function SettingsComponent_Template_div_click_0_listener() {\n          i0.ɵɵrestoreView(_r1);\n          const settingsDrawer_r2 = i0.ɵɵreference(3);\n          return i0.ɵɵresetView(settingsDrawer_r2.toggle());\n        });\n        i0.ɵɵelement(1, \"mat-icon\", 2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(2, \"fuse-drawer\", 3, 0)(4, \"div\", 4)(5, \"div\", 5);\n        i0.ɵɵelement(6, \"mat-icon\", 6);\n        i0.ɵɵelementStart(7, \"div\", 7);\n        i0.ɵɵtext(8, \" Settings \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"button\", 8);\n        i0.ɵɵlistener(\"click\", function SettingsComponent_Template_button_click_9_listener() {\n          i0.ɵɵrestoreView(_r1);\n          const settingsDrawer_r2 = i0.ɵɵreference(3);\n          return i0.ɵɵresetView(settingsDrawer_r2.close());\n        });\n        i0.ɵɵelement(10, \"mat-icon\", 9);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"div\", 10)(12, \"div\", 11);\n        i0.ɵɵtext(13, \"THEME\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"div\", 12);\n        i0.ɵɵrepeaterCreate(15, SettingsComponent_For_16_Template, 4, 6, \"div\", 13, i0.ɵɵrepeaterTrackByIdentity);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(17, \"hr\", 14);\n        i0.ɵɵelementStart(18, \"div\", 11);\n        i0.ɵɵtext(19, \"SCHEME\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"div\", 15)(21, \"div\", 16);\n        i0.ɵɵlistener(\"click\", function SettingsComponent_Template_div_click_21_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.setScheme(\"auto\"));\n        });\n        i0.ɵɵelementStart(22, \"div\", 17);\n        i0.ɵɵelement(23, \"mat-icon\", 18);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(24, \"div\", 19);\n        i0.ɵɵtext(25, \" Auto \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(26, \"div\", 20);\n        i0.ɵɵlistener(\"click\", function SettingsComponent_Template_div_click_26_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.setScheme(\"dark\"));\n        });\n        i0.ɵɵelementStart(27, \"div\", 17);\n        i0.ɵɵelement(28, \"mat-icon\", 18);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(29, \"div\", 19);\n        i0.ɵɵtext(30, \" Dark \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(31, \"div\", 20);\n        i0.ɵɵlistener(\"click\", function SettingsComponent_Template_div_click_31_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.setScheme(\"light\"));\n        });\n        i0.ɵɵelementStart(32, \"div\", 17);\n        i0.ɵɵelement(33, \"mat-icon\", 18);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(34, \"div\", 19);\n        i0.ɵɵtext(35, \" Light \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelement(36, \"hr\", 14);\n        i0.ɵɵelementStart(37, \"div\", 11);\n        i0.ɵɵtext(38, \"LAYOUT\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(39, \"div\", 21)(40, \"div\", 22);\n        i0.ɵɵlistener(\"click\", function SettingsComponent_Template_div_click_40_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.setLayout(\"empty\"));\n        });\n        i0.ɵɵelementStart(41, \"div\", 23);\n        i0.ɵɵelement(42, \"div\", 24);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(43, \"div\", 25);\n        i0.ɵɵtext(44, \" Empty \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(45, \"div\", 22);\n        i0.ɵɵlistener(\"click\", function SettingsComponent_Template_div_click_45_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.setLayout(\"classic\"));\n        });\n        i0.ɵɵelementStart(46, \"div\", 26)(47, \"div\", 27)(48, \"div\", 28);\n        i0.ɵɵelement(49, \"div\", 29)(50, \"div\", 29)(51, \"div\", 29)(52, \"div\", 29)(53, \"div\", 29);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(54, \"div\", 30)(55, \"div\", 31)(56, \"div\", 32);\n        i0.ɵɵelement(57, \"div\", 33)(58, \"div\", 33)(59, \"div\", 33);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(60, \"div\", 34);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(61, \"div\", 25);\n        i0.ɵɵtext(62, \" Classic \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(63, \"div\", 22);\n        i0.ɵɵlistener(\"click\", function SettingsComponent_Template_div_click_63_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.setLayout(\"classy\"));\n        });\n        i0.ɵɵelementStart(64, \"div\", 26)(65, \"div\", 27)(66, \"div\", 35);\n        i0.ɵɵelement(67, \"div\", 36)(68, \"div\", 37)(69, \"div\", 38);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(70, \"div\", 39);\n        i0.ɵɵelementStart(71, \"div\", 40);\n        i0.ɵɵelement(72, \"div\", 29)(73, \"div\", 29)(74, \"div\", 29)(75, \"div\", 29);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(76, \"div\", 30)(77, \"div\", 31)(78, \"div\", 41);\n        i0.ɵɵelement(79, \"div\", 36);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(80, \"div\", 34);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(81, \"div\", 25);\n        i0.ɵɵtext(82, \" Classy \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(83, \"div\", 22);\n        i0.ɵɵlistener(\"click\", function SettingsComponent_Template_div_click_83_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.setLayout(\"compact\"));\n        });\n        i0.ɵɵelementStart(84, \"div\", 26)(85, \"div\", 42);\n        i0.ɵɵelement(86, \"div\", 43);\n        i0.ɵɵelementStart(87, \"div\", 44);\n        i0.ɵɵelement(88, \"div\", 45)(89, \"div\", 45)(90, \"div\", 45);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(91, \"div\", 30)(92, \"div\", 31)(93, \"div\", 32);\n        i0.ɵɵelement(94, \"div\", 33)(95, \"div\", 33)(96, \"div\", 33);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(97, \"div\", 34);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(98, \"div\", 25);\n        i0.ɵɵtext(99, \" Compact \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(100, \"div\", 22);\n        i0.ɵɵlistener(\"click\", function SettingsComponent_Template_div_click_100_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.setLayout(\"dense\"));\n        });\n        i0.ɵɵelementStart(101, \"div\", 26)(102, \"div\", 46);\n        i0.ɵɵelement(103, \"div\", 47);\n        i0.ɵɵelementStart(104, \"div\", 44);\n        i0.ɵɵelement(105, \"div\", 48)(106, \"div\", 48)(107, \"div\", 48);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(108, \"div\", 30)(109, \"div\", 31)(110, \"div\", 32);\n        i0.ɵɵelement(111, \"div\", 33)(112, \"div\", 33)(113, \"div\", 33);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(114, \"div\", 34);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(115, \"div\", 25);\n        i0.ɵɵtext(116, \" Dense \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(117, \"div\", 22);\n        i0.ɵɵlistener(\"click\", function SettingsComponent_Template_div_click_117_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.setLayout(\"futuristic\"));\n        });\n        i0.ɵɵelementStart(118, \"div\", 26)(119, \"div\", 27)(120, \"div\", 49);\n        i0.ɵɵelement(121, \"div\", 29)(122, \"div\", 29)(123, \"div\", 29)(124, \"div\", 50)(125, \"div\", 29);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(126, \"div\", 30)(127, \"div\", 31)(128, \"div\", 32);\n        i0.ɵɵelement(129, \"div\", 33)(130, \"div\", 33)(131, \"div\", 33);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(132, \"div\", 34);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(133, \"div\", 25);\n        i0.ɵɵtext(134, \" Futuristic \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(135, \"div\", 22);\n        i0.ɵɵlistener(\"click\", function SettingsComponent_Template_div_click_135_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.setLayout(\"thin\"));\n        });\n        i0.ɵɵelementStart(136, \"div\", 26)(137, \"div\", 51);\n        i0.ɵɵelement(138, \"div\", 52);\n        i0.ɵɵelementStart(139, \"div\", 44);\n        i0.ɵɵelement(140, \"div\", 53)(141, \"div\", 53)(142, \"div\", 53)(143, \"div\", 53)(144, \"div\", 53);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(145, \"div\", 30)(146, \"div\", 31)(147, \"div\", 32);\n        i0.ɵɵelement(148, \"div\", 33)(149, \"div\", 33)(150, \"div\", 33);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(151, \"div\", 34);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(152, \"div\", 25);\n        i0.ɵɵtext(153, \" Thin \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(154, \"div\", 54);\n        i0.ɵɵelementStart(155, \"div\", 22);\n        i0.ɵɵlistener(\"click\", function SettingsComponent_Template_div_click_155_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.setLayout(\"centered\"));\n        });\n        i0.ɵɵelementStart(156, \"div\", 26)(157, \"div\", 55)(158, \"div\", 56)(159, \"div\", 57);\n        i0.ɵɵelement(160, \"div\", 36)(161, \"div\", 58)(162, \"div\", 58)(163, \"div\", 58);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(164, \"div\", 59);\n        i0.ɵɵelement(165, \"div\", 33)(166, \"div\", 33);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(167, \"div\", 34);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(168, \"div\", 25);\n        i0.ɵɵtext(169, \" Centered \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(170, \"div\", 22);\n        i0.ɵɵlistener(\"click\", function SettingsComponent_Template_div_click_170_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.setLayout(\"enterprise\"));\n        });\n        i0.ɵɵelementStart(171, \"div\", 23)(172, \"div\", 60);\n        i0.ɵɵelement(173, \"div\", 61);\n        i0.ɵɵelementStart(174, \"div\", 62);\n        i0.ɵɵelement(175, \"div\", 36)(176, \"div\", 36)(177, \"div\", 36);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(178, \"div\", 63);\n        i0.ɵɵelement(179, \"div\", 64)(180, \"div\", 64)(181, \"div\", 64)(182, \"div\", 64);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(183, \"div\", 65);\n        i0.ɵɵelement(184, \"div\", 66);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(185, \"div\", 25);\n        i0.ɵɵtext(186, \" Enterprise \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(187, \"div\", 22);\n        i0.ɵɵlistener(\"click\", function SettingsComponent_Template_div_click_187_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.setLayout(\"material\"));\n        });\n        i0.ɵɵelementStart(188, \"div\", 23)(189, \"div\", 65)(190, \"div\", 67);\n        i0.ɵɵelement(191, \"div\", 61);\n        i0.ɵɵelementStart(192, \"div\", 62);\n        i0.ɵɵelement(193, \"div\", 36)(194, \"div\", 36)(195, \"div\", 36);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(196, \"div\", 68);\n        i0.ɵɵelement(197, \"div\", 64)(198, \"div\", 64)(199, \"div\", 64)(200, \"div\", 64);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(201, \"div\", 34);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(202, \"div\", 25);\n        i0.ɵɵtext(203, \" Material \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(204, \"div\", 22);\n        i0.ɵɵlistener(\"click\", function SettingsComponent_Template_div_click_204_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.setLayout(\"modern\"));\n        });\n        i0.ɵɵelementStart(205, \"div\", 23)(206, \"div\", 69);\n        i0.ɵɵelement(207, \"div\", 61);\n        i0.ɵɵelementStart(208, \"div\", 70);\n        i0.ɵɵelement(209, \"div\", 64)(210, \"div\", 64)(211, \"div\", 64);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(212, \"div\", 62);\n        i0.ɵɵelement(213, \"div\", 36)(214, \"div\", 36);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(215, \"div\", 71);\n        i0.ɵɵelement(216, \"div\", 66);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(217, \"div\", 25);\n        i0.ɵɵtext(218, \" Modern \");\n        i0.ɵɵelementEnd()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"lg:right-0\", ctx.config.layout === \"centered\" || ctx.config.layout === \"material\")(\"lg:right-16\", ctx.config.layout !== \"centered\" && ctx.config.layout !== \"material\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"svgIcon\", \"heroicons_solid:cog-8-tooth\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"mode\", \"over\")(\"name\", \"settingsDrawer\")(\"position\", \"right\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"svgIcon\", \"heroicons_solid:cog-8-tooth\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"svgIcon\", \"heroicons_outline:x-mark\");\n        i0.ɵɵadvance(5);\n        i0.ɵɵrepeater(ctx.config.themes);\n        i0.ɵɵadvance(6);\n        i0.ɵɵclassProp(\"ring-2\", ctx.config.scheme === \"auto\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"svgIcon\", \"heroicons_solid:bolt\");\n        i0.ɵɵadvance();\n        i0.ɵɵclassProp(\"text-secondary\", ctx.config.scheme !== \"auto\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵclassProp(\"ring-2\", ctx.config.scheme === \"dark\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"svgIcon\", \"heroicons_solid:moon\");\n        i0.ɵɵadvance();\n        i0.ɵɵclassProp(\"text-secondary\", ctx.config.scheme !== \"dark\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵclassProp(\"ring-2\", ctx.config.scheme === \"light\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"svgIcon\", \"heroicons_solid:sun\");\n        i0.ɵɵadvance();\n        i0.ɵɵclassProp(\"text-secondary\", ctx.config.scheme !== \"light\");\n        i0.ɵɵadvance(7);\n        i0.ɵɵclassProp(\"border-primary\", ctx.config.layout === \"empty\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵclassProp(\"text-primary\", ctx.config.layout === \"empty\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵclassProp(\"border-primary\", ctx.config.layout === \"classic\");\n        i0.ɵɵadvance(15);\n        i0.ɵɵclassProp(\"text-primary\", ctx.config.layout === \"classic\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵclassProp(\"border-primary\", ctx.config.layout === \"classy\");\n        i0.ɵɵadvance(17);\n        i0.ɵɵclassProp(\"text-primary\", ctx.config.layout === \"classy\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵclassProp(\"border-primary\", ctx.config.layout === \"compact\");\n        i0.ɵɵadvance(14);\n        i0.ɵɵclassProp(\"text-primary\", ctx.config.layout === \"compact\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵclassProp(\"border-primary\", ctx.config.layout === \"dense\");\n        i0.ɵɵadvance(14);\n        i0.ɵɵclassProp(\"text-primary\", ctx.config.layout === \"dense\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵclassProp(\"border-primary\", ctx.config.layout === \"futuristic\");\n        i0.ɵɵadvance(15);\n        i0.ɵɵclassProp(\"text-primary\", ctx.config.layout === \"futuristic\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵclassProp(\"border-primary\", ctx.config.layout === \"thin\");\n        i0.ɵɵadvance(16);\n        i0.ɵɵclassProp(\"text-primary\", ctx.config.layout === \"thin\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵclassProp(\"border-primary\", ctx.config.layout === \"centered\");\n        i0.ɵɵadvance(12);\n        i0.ɵɵclassProp(\"text-primary\", ctx.config.layout === \"centered\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵclassProp(\"border-primary\", ctx.config.layout === \"enterprise\");\n        i0.ɵɵadvance(14);\n        i0.ɵɵclassProp(\"text-primary\", ctx.config.layout === \"enterprise\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵclassProp(\"border-primary\", ctx.config.layout === \"material\");\n        i0.ɵɵadvance(14);\n        i0.ɵɵclassProp(\"text-primary\", ctx.config.layout === \"material\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵclassProp(\"border-primary\", ctx.config.layout === \"modern\");\n        i0.ɵɵadvance(12);\n        i0.ɵɵclassProp(\"text-primary\", ctx.config.layout === \"modern\");\n      }\n    },\n    dependencies: [MatIconModule, i3.MatIcon, FuseDrawerComponent, MatButtonModule, i4.MatIconButton, NgClass, MatTooltipModule, i5.MatTooltip],\n    styles: [\"var resource;\\n/******/ (() => { // webpackBootstrap\\n/******/ \\tvar __webpack_modules__ = ({\\n\\n/***/ 498:\\n/*!****************************************************************************************************************************************************************************************************************************************!*\\\\\\n  !*** ./apps/eiot-admin/src/app/layout/common/settings/settings.component.ts-2.scss?ngResource!=!./node_modules/@ngtools/webpack/src/loaders/inline-resource.js!./apps/eiot-admin/src/app/layout/common/settings/settings.component.ts ***!\\n  \\\\****************************************************************************************************************************************************************************************************************************************/\\n/***/ (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/postcss-loader/dist/cjs.js):\\\\nError: Cannot find module 'chroma-js'\\\\nRequire stack:\\\\n- D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\apps\\\\\\\\eiot-admin\\\\\\\\src\\\\\\\\@fuse\\\\\\\\tailwind\\\\\\\\utils\\\\\\\\generate-palette.js\\\\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\\\\n    at Function.resolve (node:internal/modules/helpers:193:19)\\\\n    at _resolve (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:246378)\\\\n    at jiti (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:249092)\\\\n    at D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\apps\\\\\\\\eiot-admin\\\\\\\\src\\\\\\\\@fuse\\\\\\\\tailwind\\\\\\\\utils\\\\\\\\generate-palette.js:1:91\\\\n    at evalModule (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:251913)\\\\n    at jiti (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:249841)\\\\n    at D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\apps\\\\\\\\eiot-admin\\\\\\\\tailwind.config.js:4:25\\\\n    at evalModule (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:251913)\\\\n    at jiti (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:249841)\\\");\\n\\n/***/ })\\n\\n/******/ \\t});\\n/************************************************************************/\\n/******/ \\t\\n/******/ \\t// startup\\n/******/ \\t// Load entry module and return exports\\n/******/ \\t// This entry module doesn't tell about it's top-level declarations so it can't be inlined\\n/******/ \\tvar __webpack_exports__ = {};\\n/******/ \\t__webpack_modules__[498]();\\n/******/ \\tresource = __webpack_exports__;\\n/******/ \\t\\n/******/ })()\\n;\\n/*# sourceMappingURL=settings.component.ts-angular-inline--2.css.map*/\"],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["Ng<PERSON><PERSON>", "MatButtonModule", "MatIconModule", "MatTooltipModule", "FuseDrawerComponent", "Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵlistener", "SettingsComponent_For_16_Template_div_click_0_listener", "theme_r4", "ɵɵrestoreView", "_r3", "$implicit", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "setTheme", "id", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵclassProp", "config", "theme", "ɵɵproperty", "ɵɵadvance", "ɵɵtextInterpolate1", "name", "SettingsComponent", "constructor", "_router", "_fuseConfigService", "_unsubscribeAll", "ngOnInit", "config$", "pipe", "subscribe", "ngOnDestroy", "next", "complete", "setLayout", "layout", "navigate", "queryParams", "queryParamsHandling", "then", "setScheme", "scheme", "_", "ɵɵdirectiveInject", "i1", "Router", "i2", "FuseConfigService", "_2", "selectors", "decls", "vars", "consts", "template", "SettingsComponent_Template", "rf", "ctx", "SettingsComponent_Template_div_click_0_listener", "_r1", "settingsDrawer_r2", "ɵɵreference", "toggle", "SettingsComponent_Template_button_click_9_listener", "close", "ɵɵrepeaterCreate", "SettingsComponent_For_16_Template", "ɵɵrepeaterTrackByIdentity", "SettingsComponent_Template_div_click_21_listener", "SettingsComponent_Template_div_click_26_listener", "SettingsComponent_Template_div_click_31_listener", "SettingsComponent_Template_div_click_40_listener", "SettingsComponent_Template_div_click_45_listener", "SettingsComponent_Template_div_click_63_listener", "SettingsComponent_Template_div_click_83_listener", "SettingsComponent_Template_div_click_100_listener", "SettingsComponent_Template_div_click_117_listener", "SettingsComponent_Template_div_click_135_listener", "SettingsComponent_Template_div_click_155_listener", "SettingsComponent_Template_div_click_170_listener", "SettingsComponent_Template_div_click_187_listener", "SettingsComponent_Template_div_click_204_listener", "ɵɵrepeater", "themes", "i3", "MatIcon", "i4", "MatIconButton", "i5", "MatTooltip", "styles", "encapsulation"], "sources": ["D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\app\\layout\\common\\settings\\settings.component.ts", "D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\app\\layout\\common\\settings\\settings.component.html"], "sourcesContent": ["import { NgClass } from '@angular/common';\nimport { Component, <PERSON><PERSON><PERSON>roy, OnInit, ViewEncapsulation } from '@angular/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { Router } from '@angular/router';\nimport { FuseDrawerComponent } from '@fuse/components/drawer';\nimport {\n    FuseConfig,\n    FuseConfigService,\n    Scheme,\n    Theme,\n    Themes,\n} from '@fuse/services/config';\n\nimport { Subject, takeUntil } from 'rxjs';\n\n@Component({\n    selector: 'settings',\n    templateUrl: './settings.component.html',\n    styles: [\n        `\n            settings {\n                position: static;\n                display: block;\n                flex: none;\n                width: auto;\n            }\n\n            @media (screen and min-width: 1280px) {\n                empty-layout + settings .settings-cog {\n                    right: 0 !important;\n                }\n            }\n        `,\n    ],\n    encapsulation: ViewEncapsulation.None,\n    imports: [\n        MatIconModule,\n        FuseDrawerComponent,\n        MatButtonModule,\n        NgClass,\n        MatTooltipModule,\n    ],\n})\nexport class SettingsComponent implements OnInit, OnD<PERSON>roy {\n    config: FuseConfig;\n    layout: string;\n    scheme: 'dark' | 'light';\n    theme: string;\n    themes: Themes;\n    private _unsubscribeAll: Subject<any> = new Subject<any>();\n\n    /**\n     * Constructor\n     */\n    constructor(\n        private _router: Router,\n        private _fuseConfigService: FuseConfigService\n    ) {}\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Lifecycle hooks\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * On init\n     */\n    ngOnInit(): void {\n        // Subscribe to config changes\n        this._fuseConfigService.config$\n            .pipe(takeUntil(this._unsubscribeAll))\n            .subscribe((config: FuseConfig) => {\n                // Store the config\n                this.config = config;\n            });\n    }\n\n    /**\n     * On destroy\n     */\n    ngOnDestroy(): void {\n        // Unsubscribe from all subscriptions\n        this._unsubscribeAll.next(null);\n        this._unsubscribeAll.complete();\n    }\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Public methods\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * Set the layout on the config\n     *\n     * @param layout\n     */\n    setLayout(layout: string): void {\n        // Clear the 'layout' query param to allow layout changes\n        this._router\n            .navigate([], {\n                queryParams: {\n                    layout: null,\n                },\n                queryParamsHandling: 'merge',\n            })\n            .then(() => {\n                // Set the config\n                this._fuseConfigService.config = { layout };\n            });\n    }\n\n    /**\n     * Set the scheme on the config\n     *\n     * @param scheme\n     */\n    setScheme(scheme: Scheme): void {\n        this._fuseConfigService.config = { scheme };\n    }\n\n    /**\n     * Set the theme on the config\n     *\n     * @param theme\n     */\n    setTheme(theme: Theme): void {\n        this._fuseConfigService.config = { theme };\n    }\n}\n", "<div\n    class=\"settings-cog fixed right-0 z-90 flex h-10 w-10 cursor-pointer items-center justify-center rounded-l-lg bg-red-600 bg-opacity-90 shadow-lg print:hidden\"\n    [class.lg:right-0]=\"\n        config.layout === 'centered' || config.layout === 'material'\n    \"\n    [class.lg:right-16]=\"\n        config.layout !== 'centered' && config.layout !== 'material'\n    \"\n    style=\"top: 275px\"\n    (click)=\"settingsDrawer.toggle()\"\n>\n    <mat-icon\n        class=\"animate-spin-slow text-white icon-size-5\"\n        [svgIcon]=\"'heroicons_solid:cog-8-tooth'\"\n    ></mat-icon>\n</div>\n\n<fuse-drawer\n    class=\"z-999 w-screen min-w-screen sm:w-100 sm:min-w-100\"\n    fixed\n    [mode]=\"'over'\"\n    [name]=\"'settingsDrawer'\"\n    [position]=\"'right'\"\n    #settingsDrawer\n>\n    <div class=\"bg-card flex w-full flex-col overflow-auto\">\n        <div\n            class=\"flex h-20 min-h-20 flex-row items-center bg-primary px-6 text-white\"\n        >\n            <mat-icon\n                class=\"text-current icon-size-7\"\n                [svgIcon]=\"'heroicons_solid:cog-8-tooth'\"\n            ></mat-icon>\n            <div class=\"ml-3 text-2xl font-semibold tracking-tight\">\n                Settings\n            </div>\n            <button\n                class=\"ml-auto\"\n                mat-icon-button\n                (click)=\"settingsDrawer.close()\"\n            >\n                <mat-icon\n                    class=\"text-current\"\n                    [svgIcon]=\"'heroicons_outline:x-mark'\"\n                ></mat-icon>\n            </button>\n        </div>\n\n        <div class=\"flex flex-col p-6\">\n            <!-- Theme -->\n            <div class=\"text-secondary text-md font-semibold\">THEME</div>\n            <div class=\"mt-6 grid grid-cols-2 gap-3 sm:grid-cols-3\">\n                @for (theme of config.themes; track theme) {\n                    <div\n                        class=\"bg-hover flex cursor-pointer items-center justify-center rounded-full px-4 py-3 ring-inset ring-primary\"\n                        [class.ring-2]=\"config.theme === theme.id\"\n                        [ngClass]=\"theme.id\"\n                        (click)=\"setTheme(theme.id)\"\n                    >\n                        <div\n                            class=\"h-3 w-3 flex-0 rounded-full bg-primary\"\n                        ></div>\n                        <div\n                            class=\"ml-2.5 truncate font-medium leading-5\"\n                            [class.text-secondary]=\"config.theme !== theme.id\"\n                        >\n                            {{ theme.name }}\n                        </div>\n                    </div>\n                }\n            </div>\n\n            <hr class=\"my-8\" />\n\n            <!-- Scheme -->\n            <div class=\"text-secondary text-md font-semibold\">SCHEME</div>\n            <div class=\"mt-6 grid grid-cols-3 justify-items-start gap-3\">\n                <!-- Auto -->\n                <div\n                    class=\"bg-hover flex cursor-pointer items-center rounded-full py-3 pl-5 pr-6 ring-inset ring-primary\"\n                    [class.ring-2]=\"config.scheme === 'auto'\"\n                    matTooltip=\"Automatically sets the scheme based on user's operating system's color scheme preference using 'prefer-color-scheme' media query.\"\n                    (click)=\"setScheme('auto')\"\n                >\n                    <div class=\"flex items-center overflow-hidden rounded-full\">\n                        <mat-icon\n                            class=\"icon-size-5\"\n                            [svgIcon]=\"'heroicons_solid:bolt'\"\n                        ></mat-icon>\n                    </div>\n                    <div\n                        class=\"ml-2 flex items-center font-medium leading-5\"\n                        [class.text-secondary]=\"config.scheme !== 'auto'\"\n                    >\n                        Auto\n                    </div>\n                </div>\n                <!-- Dark -->\n                <div\n                    class=\"bg-hover flex cursor-pointer items-center rounded-full py-3 pl-5 pr-6 ring-inset ring-primary\"\n                    [class.ring-2]=\"config.scheme === 'dark'\"\n                    (click)=\"setScheme('dark')\"\n                >\n                    <div class=\"flex items-center overflow-hidden rounded-full\">\n                        <mat-icon\n                            class=\"icon-size-5\"\n                            [svgIcon]=\"'heroicons_solid:moon'\"\n                        ></mat-icon>\n                    </div>\n                    <div\n                        class=\"ml-2 flex items-center font-medium leading-5\"\n                        [class.text-secondary]=\"config.scheme !== 'dark'\"\n                    >\n                        Dark\n                    </div>\n                </div>\n                <!-- Light -->\n                <div\n                    class=\"bg-hover flex cursor-pointer items-center rounded-full py-3 pl-5 pr-6 ring-inset ring-primary\"\n                    [class.ring-2]=\"config.scheme === 'light'\"\n                    (click)=\"setScheme('light')\"\n                >\n                    <div class=\"flex items-center overflow-hidden rounded-full\">\n                        <mat-icon\n                            class=\"icon-size-5\"\n                            [svgIcon]=\"'heroicons_solid:sun'\"\n                        ></mat-icon>\n                    </div>\n                    <div\n                        class=\"ml-2 flex items-center font-medium leading-5\"\n                        [class.text-secondary]=\"config.scheme !== 'light'\"\n                    >\n                        Light\n                    </div>\n                </div>\n            </div>\n\n            <hr class=\"my-8\" />\n\n            <!-- Layout -->\n            <div class=\"text-secondary text-md font-semibold\">LAYOUT</div>\n            <div class=\"mt-6 grid grid-cols-3 gap-3\">\n                <!-- Empty -->\n                <div\n                    class=\"flex cursor-pointer flex-col\"\n                    (click)=\"setLayout('empty')\"\n                >\n                    <div\n                        class=\"flex h-20 flex-col overflow-hidden rounded-md border-2 hover:opacity-80\"\n                        [class.border-primary]=\"config.layout === 'empty'\"\n                    >\n                        <div\n                            class=\"flex flex-auto flex-col bg-gray-50 dark:bg-gray-900\"\n                        ></div>\n                    </div>\n                    <div\n                        class=\"text-secondary mt-2 text-center text-md font-medium\"\n                        [class.text-primary]=\"config.layout === 'empty'\"\n                    >\n                        Empty\n                    </div>\n                </div>\n\n                <!-- Classic -->\n                <div\n                    class=\"flex cursor-pointer flex-col\"\n                    (click)=\"setLayout('classic')\"\n                >\n                    <div\n                        class=\"flex h-20 overflow-hidden rounded-md border-2 hover:opacity-80\"\n                        [class.border-primary]=\"config.layout === 'classic'\"\n                    >\n                        <div class=\"w-8 bg-gray-100 dark:bg-gray-800\">\n                            <div class=\"mx-1.5 mt-3 space-y-1\">\n                                <div\n                                    class=\"h-1 rounded-sm bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                                <div\n                                    class=\"h-1 rounded-sm bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                                <div\n                                    class=\"h-1 rounded-sm bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                                <div\n                                    class=\"h-1 rounded-sm bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                                <div\n                                    class=\"h-1 rounded-sm bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                            </div>\n                        </div>\n                        <div class=\"flex flex-auto flex-col border-l\">\n                            <div class=\"h-3 bg-gray-100 dark:bg-gray-800\">\n                                <div\n                                    class=\"mr-1.5 flex h-full items-center justify-end\"\n                                >\n                                    <div\n                                        class=\"ml-1 h-1 w-1 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                    ></div>\n                                    <div\n                                        class=\"ml-1 h-1 w-1 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                    ></div>\n                                    <div\n                                        class=\"ml-1 h-1 w-1 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                    ></div>\n                                </div>\n                            </div>\n                            <div\n                                class=\"flex flex-auto border-t bg-gray-50 dark:bg-gray-900\"\n                            ></div>\n                        </div>\n                    </div>\n                    <div\n                        class=\"text-secondary mt-2 text-center text-md font-medium\"\n                        [class.text-primary]=\"config.layout === 'classic'\"\n                    >\n                        Classic\n                    </div>\n                </div>\n\n                <!-- Classy -->\n                <div\n                    class=\"flex cursor-pointer flex-col\"\n                    (click)=\"setLayout('classy')\"\n                >\n                    <div\n                        class=\"flex h-20 overflow-hidden rounded-md border-2 hover:opacity-80\"\n                        [class.border-primary]=\"config.layout === 'classy'\"\n                    >\n                        <div class=\"w-8 bg-gray-100 dark:bg-gray-800\">\n                            <div class=\"mx-1 mt-1 flex items-center\">\n                                <div\n                                    class=\"h-1 w-1 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                                <div\n                                    class=\"ml-auto h-1 w-1 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                                <div\n                                    class=\"ml-0.5 h-1 w-1 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                            </div>\n                            <div\n                                class=\"mx-auto mt-2.5 h-4 w-4 rounded-full bg-gray-300 dark:bg-gray-700\"\n                            ></div>\n                            <div class=\"mx-1 mt-2 space-y-1\">\n                                <div\n                                    class=\"h-1 rounded-sm bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                                <div\n                                    class=\"h-1 rounded-sm bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                                <div\n                                    class=\"h-1 rounded-sm bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                                <div\n                                    class=\"h-1 rounded-sm bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                            </div>\n                        </div>\n                        <div class=\"flex flex-auto flex-col border-l\">\n                            <div class=\"h-3 bg-gray-100 dark:bg-gray-800\">\n                                <div\n                                    class=\"mr-2 flex h-full items-center justify-end\"\n                                >\n                                    <div\n                                        class=\"h-1 w-1 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                    ></div>\n                                </div>\n                            </div>\n                            <div\n                                class=\"flex flex-auto border-t bg-gray-50 dark:bg-gray-900\"\n                            ></div>\n                        </div>\n                    </div>\n                    <div\n                        class=\"text-secondary mt-2 text-center text-md font-medium\"\n                        [class.text-primary]=\"config.layout === 'classy'\"\n                    >\n                        Classy\n                    </div>\n                </div>\n\n                <!-- Compact -->\n                <div\n                    class=\"flex cursor-pointer flex-col\"\n                    (click)=\"setLayout('compact')\"\n                >\n                    <div\n                        class=\"flex h-20 overflow-hidden rounded-md border-2 hover:opacity-80\"\n                        [class.border-primary]=\"config.layout === 'compact'\"\n                    >\n                        <div class=\"w-5 bg-gray-100 dark:bg-gray-800\">\n                            <div\n                                class=\"mx-auto mt-2 h-3 w-3 rounded-sm bg-gray-300 dark:bg-gray-700\"\n                            ></div>\n                            <div\n                                class=\"mt-2 flex w-full flex-col items-center space-y-1\"\n                            >\n                                <div\n                                    class=\"h-2.5 w-3 rounded-sm bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                                <div\n                                    class=\"h-2.5 w-3 rounded-sm bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                                <div\n                                    class=\"h-2.5 w-3 rounded-sm bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                            </div>\n                        </div>\n                        <div class=\"flex flex-auto flex-col border-l\">\n                            <div class=\"h-3 bg-gray-100 dark:bg-gray-800\">\n                                <div\n                                    class=\"mr-1.5 flex h-full items-center justify-end\"\n                                >\n                                    <div\n                                        class=\"ml-1 h-1 w-1 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                    ></div>\n                                    <div\n                                        class=\"ml-1 h-1 w-1 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                    ></div>\n                                    <div\n                                        class=\"ml-1 h-1 w-1 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                    ></div>\n                                </div>\n                            </div>\n                            <div\n                                class=\"flex flex-auto border-t bg-gray-50 dark:bg-gray-900\"\n                            ></div>\n                        </div>\n                    </div>\n                    <div\n                        class=\"text-secondary mt-2 text-center text-md font-medium\"\n                        [class.text-primary]=\"config.layout === 'compact'\"\n                    >\n                        Compact\n                    </div>\n                </div>\n\n                <!-- Dense -->\n                <div\n                    class=\"flex cursor-pointer flex-col\"\n                    (click)=\"setLayout('dense')\"\n                >\n                    <div\n                        class=\"flex h-20 overflow-hidden rounded-md border-2 hover:opacity-80\"\n                        [class.border-primary]=\"config.layout === 'dense'\"\n                    >\n                        <div class=\"w-4 bg-gray-100 dark:bg-gray-800\">\n                            <div\n                                class=\"mx-auto mt-2 h-2 w-2 rounded-sm bg-gray-300 dark:bg-gray-700\"\n                            ></div>\n                            <div\n                                class=\"mt-2 flex w-full flex-col items-center space-y-1\"\n                            >\n                                <div\n                                    class=\"h-2 w-2 rounded-sm bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                                <div\n                                    class=\"h-2 w-2 rounded-sm bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                                <div\n                                    class=\"h-2 w-2 rounded-sm bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                            </div>\n                        </div>\n                        <div class=\"flex flex-auto flex-col border-l\">\n                            <div class=\"h-3 bg-gray-100 dark:bg-gray-800\">\n                                <div\n                                    class=\"mr-1.5 flex h-full items-center justify-end\"\n                                >\n                                    <div\n                                        class=\"ml-1 h-1 w-1 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                    ></div>\n                                    <div\n                                        class=\"ml-1 h-1 w-1 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                    ></div>\n                                    <div\n                                        class=\"ml-1 h-1 w-1 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                    ></div>\n                                </div>\n                            </div>\n                            <div\n                                class=\"flex flex-auto border-t bg-gray-50 dark:bg-gray-900\"\n                            ></div>\n                        </div>\n                    </div>\n                    <div\n                        class=\"text-secondary mt-2 text-center text-md font-medium\"\n                        [class.text-primary]=\"config.layout === 'dense'\"\n                    >\n                        Dense\n                    </div>\n                </div>\n\n                <!-- Futuristic -->\n                <div\n                    class=\"flex cursor-pointer flex-col\"\n                    (click)=\"setLayout('futuristic')\"\n                >\n                    <div\n                        class=\"flex h-20 overflow-hidden rounded-md border-2 hover:opacity-80\"\n                        [class.border-primary]=\"config.layout === 'futuristic'\"\n                    >\n                        <div class=\"w-8 bg-gray-100 dark:bg-gray-800\">\n                            <div\n                                class=\"flex h-full flex-auto flex-col space-y-1 px-1.5 py-3\"\n                            >\n                                <div\n                                    class=\"h-1 rounded-sm bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                                <div\n                                    class=\"h-1 rounded-sm bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                                <div\n                                    class=\"h-1 rounded-sm bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                                <div class=\"flex-auto\"></div>\n                                <div\n                                    class=\"h-1 rounded-sm bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                            </div>\n                        </div>\n                        <div class=\"flex flex-auto flex-col border-l\">\n                            <div class=\"h-3 bg-gray-100 dark:bg-gray-800\">\n                                <div\n                                    class=\"mr-1.5 flex h-full items-center justify-end\"\n                                >\n                                    <div\n                                        class=\"ml-1 h-1 w-1 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                    ></div>\n                                    <div\n                                        class=\"ml-1 h-1 w-1 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                    ></div>\n                                    <div\n                                        class=\"ml-1 h-1 w-1 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                    ></div>\n                                </div>\n                            </div>\n                            <div\n                                class=\"flex flex-auto border-t bg-gray-50 dark:bg-gray-900\"\n                            ></div>\n                        </div>\n                    </div>\n                    <div\n                        class=\"text-secondary mt-2 text-center text-md font-medium\"\n                        [class.text-primary]=\"config.layout === 'futuristic'\"\n                    >\n                        Futuristic\n                    </div>\n                </div>\n\n                <!-- Thin -->\n                <div\n                    class=\"flex cursor-pointer flex-col\"\n                    (click)=\"setLayout('thin')\"\n                >\n                    <div\n                        class=\"flex h-20 overflow-hidden rounded-md border-2 hover:opacity-80\"\n                        [class.border-primary]=\"config.layout === 'thin'\"\n                    >\n                        <div class=\"w-3 bg-gray-100 dark:bg-gray-800\">\n                            <div\n                                class=\"mx-auto mt-2 h-1.5 w-1.5 rounded-sm bg-gray-300 dark:bg-gray-700\"\n                            ></div>\n                            <div\n                                class=\"mt-2 flex w-full flex-col items-center space-y-1\"\n                            >\n                                <div\n                                    class=\"h-1.5 w-1.5 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                                <div\n                                    class=\"h-1.5 w-1.5 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                                <div\n                                    class=\"h-1.5 w-1.5 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                                <div\n                                    class=\"h-1.5 w-1.5 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                                <div\n                                    class=\"h-1.5 w-1.5 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                            </div>\n                        </div>\n                        <div class=\"flex flex-auto flex-col border-l\">\n                            <div class=\"h-3 bg-gray-100 dark:bg-gray-800\">\n                                <div\n                                    class=\"mr-1.5 flex h-full items-center justify-end\"\n                                >\n                                    <div\n                                        class=\"ml-1 h-1 w-1 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                    ></div>\n                                    <div\n                                        class=\"ml-1 h-1 w-1 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                    ></div>\n                                    <div\n                                        class=\"ml-1 h-1 w-1 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                    ></div>\n                                </div>\n                            </div>\n                            <div\n                                class=\"flex flex-auto border-t bg-gray-50 dark:bg-gray-900\"\n                            ></div>\n                        </div>\n                    </div>\n                    <div\n                        class=\"text-secondary mt-2 text-center text-md font-medium\"\n                        [class.text-primary]=\"config.layout === 'thin'\"\n                    >\n                        Thin\n                    </div>\n                </div>\n\n                <div class=\"col-span-2\"></div>\n\n                <!-- Centered -->\n                <div\n                    class=\"flex cursor-pointer flex-col\"\n                    (click)=\"setLayout('centered')\"\n                >\n                    <div\n                        class=\"flex h-20 overflow-hidden rounded-md border-2 hover:opacity-80\"\n                        [class.border-primary]=\"config.layout === 'centered'\"\n                    >\n                        <div\n                            class=\"mx-2 my-1 flex flex-auto flex-col overflow-hidden rounded-md border\"\n                        >\n                            <div\n                                class=\"flex h-3 items-center bg-gray-100 dark:bg-gray-800\"\n                            >\n                                <div class=\"ml-1.5 flex\">\n                                    <div\n                                        class=\"h-1 w-1 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                    ></div>\n                                    <div\n                                        class=\"ml-1 h-1 w-3 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                    ></div>\n                                    <div\n                                        class=\"ml-1 h-1 w-3 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                    ></div>\n                                    <div\n                                        class=\"ml-1 h-1 w-3 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                    ></div>\n                                </div>\n                                <div\n                                    class=\"ml-auto mr-1.5 flex items-center justify-end\"\n                                >\n                                    <div\n                                        class=\"ml-1 h-1 w-1 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                    ></div>\n                                    <div\n                                        class=\"ml-1 h-1 w-1 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                    ></div>\n                                </div>\n                            </div>\n                            <div\n                                class=\"flex flex-auto border-t bg-gray-50 dark:bg-gray-900\"\n                            ></div>\n                        </div>\n                    </div>\n                    <div\n                        class=\"text-secondary mt-2 text-center text-md font-medium\"\n                        [class.text-primary]=\"config.layout === 'centered'\"\n                    >\n                        Centered\n                    </div>\n                </div>\n\n                <!-- Enterprise -->\n                <div\n                    class=\"flex cursor-pointer flex-col\"\n                    (click)=\"setLayout('enterprise')\"\n                >\n                    <div\n                        class=\"flex h-20 flex-col overflow-hidden rounded-md border-2 hover:opacity-80\"\n                        [class.border-primary]=\"config.layout === 'enterprise'\"\n                    >\n                        <div\n                            class=\"flex h-3 items-center bg-gray-100 px-2 dark:bg-gray-800\"\n                        >\n                            <div\n                                class=\"h-2 w-2 rounded-full bg-gray-300 dark:bg-gray-700\"\n                            ></div>\n                            <div\n                                class=\"ml-auto flex items-center justify-end space-x-1\"\n                            >\n                                <div\n                                    class=\"h-1 w-1 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                                <div\n                                    class=\"h-1 w-1 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                                <div\n                                    class=\"h-1 w-1 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                            </div>\n                        </div>\n                        <div\n                            class=\"flex h-3 items-center space-x-1 border-b border-t bg-gray-100 px-2 dark:bg-gray-800\"\n                        >\n                            <div\n                                class=\"h-1 w-3 rounded-full bg-gray-300 dark:bg-gray-700\"\n                            ></div>\n                            <div\n                                class=\"h-1 w-3 rounded-full bg-gray-300 dark:bg-gray-700\"\n                            ></div>\n                            <div\n                                class=\"h-1 w-3 rounded-full bg-gray-300 dark:bg-gray-700\"\n                            ></div>\n                            <div\n                                class=\"h-1 w-3 rounded-full bg-gray-300 dark:bg-gray-700\"\n                            ></div>\n                        </div>\n                        <div\n                            class=\"mx-2 my-1 flex flex-auto flex-col overflow-hidden rounded border\"\n                        >\n                            <div\n                                class=\"flex flex-auto bg-gray-50 dark:bg-gray-900\"\n                            ></div>\n                        </div>\n                    </div>\n                    <div\n                        class=\"text-secondary mt-2 text-center text-md font-medium\"\n                        [class.text-primary]=\"config.layout === 'enterprise'\"\n                    >\n                        Enterprise\n                    </div>\n                </div>\n\n                <!-- Material -->\n                <div\n                    class=\"flex cursor-pointer flex-col\"\n                    (click)=\"setLayout('material')\"\n                >\n                    <div\n                        class=\"flex h-20 flex-col overflow-hidden rounded-md border-2 hover:opacity-80\"\n                        [class.border-primary]=\"config.layout === 'material'\"\n                    >\n                        <div\n                            class=\"mx-2 my-1 flex flex-auto flex-col overflow-hidden rounded border\"\n                        >\n                            <div\n                                class=\"flex h-4 items-center bg-gray-100 px-2 dark:bg-gray-800\"\n                            >\n                                <div\n                                    class=\"h-2 w-2 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                                <div\n                                    class=\"ml-auto flex items-center justify-end space-x-1\"\n                                >\n                                    <div\n                                        class=\"h-1 w-1 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                    ></div>\n                                    <div\n                                        class=\"h-1 w-1 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                    ></div>\n                                    <div\n                                        class=\"h-1 w-1 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                    ></div>\n                                </div>\n                            </div>\n                            <div\n                                class=\"flex h-2 items-center space-x-1 bg-gray-100 px-2 dark:bg-gray-800\"\n                            >\n                                <div\n                                    class=\"h-1 w-3 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                                <div\n                                    class=\"h-1 w-3 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                                <div\n                                    class=\"h-1 w-3 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                                <div\n                                    class=\"h-1 w-3 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                            </div>\n                            <div\n                                class=\"flex flex-auto border-t bg-gray-50 dark:bg-gray-900\"\n                            ></div>\n                        </div>\n                    </div>\n                    <div\n                        class=\"text-secondary mt-2 text-center text-md font-medium\"\n                        [class.text-primary]=\"config.layout === 'material'\"\n                    >\n                        Material\n                    </div>\n                </div>\n\n                <!-- Modern -->\n                <div\n                    class=\"flex cursor-pointer flex-col\"\n                    (click)=\"setLayout('modern')\"\n                >\n                    <div\n                        class=\"flex h-20 flex-col overflow-hidden rounded-md border-2 hover:opacity-80\"\n                        [class.border-primary]=\"config.layout === 'modern'\"\n                    >\n                        <div\n                            class=\"flex h-4 items-center border-b bg-gray-100 px-2 dark:bg-gray-800\"\n                        >\n                            <div\n                                class=\"h-2 w-2 rounded-full bg-gray-300 dark:bg-gray-700\"\n                            ></div>\n                            <div class=\"ml-2 flex h-3 items-center space-x-1\">\n                                <div\n                                    class=\"h-1 w-3 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                                <div\n                                    class=\"h-1 w-3 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                                <div\n                                    class=\"h-1 w-3 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                            </div>\n                            <div\n                                class=\"ml-auto flex items-center justify-end space-x-1\"\n                            >\n                                <div\n                                    class=\"h-1 w-1 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                                <div\n                                    class=\"h-1 w-1 rounded-full bg-gray-300 dark:bg-gray-700\"\n                                ></div>\n                            </div>\n                        </div>\n                        <div class=\"flex flex-auto flex-col\">\n                            <div\n                                class=\"flex flex-auto bg-gray-50 dark:bg-gray-900\"\n                            ></div>\n                        </div>\n                    </div>\n                    <div\n                        class=\"text-secondary mt-2 text-center text-md font-medium\"\n                        [class.text-primary]=\"config.layout === 'modern'\"\n                    >\n                        Modern\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n</fuse-drawer>\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,iBAAiB;AAEzC,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAE5D,SAASC,mBAAmB,QAAQ,yBAAyB;AAS7D,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;ICsCrBC,EAAA,CAAAC,cAAA,cAKC;IADGD,EAAA,CAAAE,UAAA,mBAAAC,uDAAA;MAAA,MAAAC,QAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,QAAA,CAAAP,QAAA,CAAAQ,EAAA,CAAkB;IAAA,EAAC;IAE5BZ,EAAA,CAAAa,SAAA,cAEO;IACPb,EAAA,CAAAC,cAAA,cAGC;IACGD,EAAA,CAAAc,MAAA,GACJ;IACJd,EADI,CAAAe,YAAA,EAAM,EACJ;;;;;IAbFf,EAAA,CAAAgB,WAAA,WAAAR,MAAA,CAAAS,MAAA,CAAAC,KAAA,KAAAd,QAAA,CAAAQ,EAAA,CAA0C;IAC1CZ,EAAA,CAAAmB,UAAA,YAAAf,QAAA,CAAAQ,EAAA,CAAoB;IAQhBZ,EAAA,CAAAoB,SAAA,GAAkD;IAAlDpB,EAAA,CAAAgB,WAAA,mBAAAR,MAAA,CAAAS,MAAA,CAAAC,KAAA,KAAAd,QAAA,CAAAQ,EAAA,CAAkD;IAElDZ,EAAA,CAAAoB,SAAA,EACJ;IADIpB,EAAA,CAAAqB,kBAAA,MAAAjB,QAAA,CAAAkB,IAAA,MACJ;;;ADtBxB,OAAM,MAAOC,iBAAiB;EAQ1B;;;EAGAC,YACYC,OAAe,EACfC,kBAAqC;IADrC,KAAAD,OAAO,GAAPA,OAAO;IACP,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAPtB,KAAAC,eAAe,GAAiB,IAAI7B,OAAO,EAAO;EAQvD;EAEH;EACA;EACA;EAEA;;;EAGA8B,QAAQA,CAAA;IACJ;IACA,IAAI,CAACF,kBAAkB,CAACG,OAAO,CAC1BC,IAAI,CAAC/B,SAAS,CAAC,IAAI,CAAC4B,eAAe,CAAC,CAAC,CACrCI,SAAS,CAAEd,MAAkB,IAAI;MAC9B;MACA,IAAI,CAACA,MAAM,GAAGA,MAAM;IACxB,CAAC,CAAC;EACV;EAEA;;;EAGAe,WAAWA,CAAA;IACP;IACA,IAAI,CAACL,eAAe,CAACM,IAAI,CAAC,IAAI,CAAC;IAC/B,IAAI,CAACN,eAAe,CAACO,QAAQ,EAAE;EACnC;EAEA;EACA;EACA;EAEA;;;;;EAKAC,SAASA,CAACC,MAAc;IACpB;IACA,IAAI,CAACX,OAAO,CACPY,QAAQ,CAAC,EAAE,EAAE;MACVC,WAAW,EAAE;QACTF,MAAM,EAAE;OACX;MACDG,mBAAmB,EAAE;KACxB,CAAC,CACDC,IAAI,CAAC,MAAK;MACP;MACA,IAAI,CAACd,kBAAkB,CAACT,MAAM,GAAG;QAAEmB;MAAM,CAAE;IAC/C,CAAC,CAAC;EACV;EAEA;;;;;EAKAK,SAASA,CAACC,MAAc;IACpB,IAAI,CAAChB,kBAAkB,CAACT,MAAM,GAAG;MAAEyB;IAAM,CAAE;EAC/C;EAEA;;;;;EAKA/B,QAAQA,CAACO,KAAY;IACjB,IAAI,CAACQ,kBAAkB,CAACT,MAAM,GAAG;MAAEC;IAAK,CAAE;EAC9C;EAAC,QAAAyB,CAAA,G;qCAlFQpB,iBAAiB,EAAAvB,EAAA,CAAA4C,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAA9C,EAAA,CAAA4C,iBAAA,CAAAG,EAAA,CAAAC,iBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAjB1B,iBAAiB;IAAA2B,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QC7C9BxD,EAAA,CAAAC,cAAA,aAUC;QADGD,EAAA,CAAAE,UAAA,mBAAAwD,gDAAA;UAAA1D,EAAA,CAAAK,aAAA,CAAAsD,GAAA;UAAA,MAAAC,iBAAA,GAAA5D,EAAA,CAAA6D,WAAA;UAAA,OAAA7D,EAAA,CAAAU,WAAA,CAASkD,iBAAA,CAAAE,MAAA,EAAuB;QAAA,EAAC;QAEjC9D,EAAA,CAAAa,SAAA,kBAGY;QAChBb,EAAA,CAAAe,YAAA,EAAM;QAWEf,EATR,CAAAC,cAAA,wBAOC,aAC2D,aAGnD;QACGD,EAAA,CAAAa,SAAA,kBAGY;QACZb,EAAA,CAAAC,cAAA,aAAwD;QACpDD,EAAA,CAAAc,MAAA,iBACJ;QAAAd,EAAA,CAAAe,YAAA,EAAM;QACNf,EAAA,CAAAC,cAAA,gBAIC;QADGD,EAAA,CAAAE,UAAA,mBAAA6D,mDAAA;UAAA/D,EAAA,CAAAK,aAAA,CAAAsD,GAAA;UAAA,MAAAC,iBAAA,GAAA5D,EAAA,CAAA6D,WAAA;UAAA,OAAA7D,EAAA,CAAAU,WAAA,CAASkD,iBAAA,CAAAI,KAAA,EAAsB;QAAA,EAAC;QAEhChE,EAAA,CAAAa,SAAA,mBAGY;QAEpBb,EADI,CAAAe,YAAA,EAAS,EACP;QAIFf,EAFJ,CAAAC,cAAA,eAA+B,eAEuB;QAAAD,EAAA,CAAAc,MAAA,aAAK;QAAAd,EAAA,CAAAe,YAAA,EAAM;QAC7Df,EAAA,CAAAC,cAAA,eAAwD;QACpDD,EAAA,CAAAiE,gBAAA,KAAAC,iCAAA,mBAAAlE,EAAA,CAAAmE,yBAAA,CAiBC;QACLnE,EAAA,CAAAe,YAAA,EAAM;QAENf,EAAA,CAAAa,SAAA,cAAmB;QAGnBb,EAAA,CAAAC,cAAA,eAAkD;QAAAD,EAAA,CAAAc,MAAA,cAAM;QAAAd,EAAA,CAAAe,YAAA,EAAM;QAG1Df,EAFJ,CAAAC,cAAA,eAA6D,eAOxD;QADGD,EAAA,CAAAE,UAAA,mBAAAkE,iDAAA;UAAApE,EAAA,CAAAK,aAAA,CAAAsD,GAAA;UAAA,OAAA3D,EAAA,CAAAU,WAAA,CAAS+C,GAAA,CAAAhB,SAAA,CAAU,MAAM,CAAC;QAAA,EAAC;QAE3BzC,EAAA,CAAAC,cAAA,eAA4D;QACxDD,EAAA,CAAAa,SAAA,oBAGY;QAChBb,EAAA,CAAAe,YAAA,EAAM;QACNf,EAAA,CAAAC,cAAA,eAGC;QACGD,EAAA,CAAAc,MAAA,cACJ;QACJd,EADI,CAAAe,YAAA,EAAM,EACJ;QAENf,EAAA,CAAAC,cAAA,eAIC;QADGD,EAAA,CAAAE,UAAA,mBAAAmE,iDAAA;UAAArE,EAAA,CAAAK,aAAA,CAAAsD,GAAA;UAAA,OAAA3D,EAAA,CAAAU,WAAA,CAAS+C,GAAA,CAAAhB,SAAA,CAAU,MAAM,CAAC;QAAA,EAAC;QAE3BzC,EAAA,CAAAC,cAAA,eAA4D;QACxDD,EAAA,CAAAa,SAAA,oBAGY;QAChBb,EAAA,CAAAe,YAAA,EAAM;QACNf,EAAA,CAAAC,cAAA,eAGC;QACGD,EAAA,CAAAc,MAAA,cACJ;QACJd,EADI,CAAAe,YAAA,EAAM,EACJ;QAENf,EAAA,CAAAC,cAAA,eAIC;QADGD,EAAA,CAAAE,UAAA,mBAAAoE,iDAAA;UAAAtE,EAAA,CAAAK,aAAA,CAAAsD,GAAA;UAAA,OAAA3D,EAAA,CAAAU,WAAA,CAAS+C,GAAA,CAAAhB,SAAA,CAAU,OAAO,CAAC;QAAA,EAAC;QAE5BzC,EAAA,CAAAC,cAAA,eAA4D;QACxDD,EAAA,CAAAa,SAAA,oBAGY;QAChBb,EAAA,CAAAe,YAAA,EAAM;QACNf,EAAA,CAAAC,cAAA,eAGC;QACGD,EAAA,CAAAc,MAAA,eACJ;QAERd,EAFQ,CAAAe,YAAA,EAAM,EACJ,EACJ;QAENf,EAAA,CAAAa,SAAA,cAAmB;QAGnBb,EAAA,CAAAC,cAAA,eAAkD;QAAAD,EAAA,CAAAc,MAAA,cAAM;QAAAd,EAAA,CAAAe,YAAA,EAAM;QAG1Df,EAFJ,CAAAC,cAAA,eAAyC,eAKpC;QADGD,EAAA,CAAAE,UAAA,mBAAAqE,iDAAA;UAAAvE,EAAA,CAAAK,aAAA,CAAAsD,GAAA;UAAA,OAAA3D,EAAA,CAAAU,WAAA,CAAS+C,GAAA,CAAAtB,SAAA,CAAU,OAAO,CAAC;QAAA,EAAC;QAE5BnC,EAAA,CAAAC,cAAA,eAGC;QACGD,EAAA,CAAAa,SAAA,eAEO;QACXb,EAAA,CAAAe,YAAA,EAAM;QACNf,EAAA,CAAAC,cAAA,eAGC;QACGD,EAAA,CAAAc,MAAA,eACJ;QACJd,EADI,CAAAe,YAAA,EAAM,EACJ;QAGNf,EAAA,CAAAC,cAAA,eAGC;QADGD,EAAA,CAAAE,UAAA,mBAAAsE,iDAAA;UAAAxE,EAAA,CAAAK,aAAA,CAAAsD,GAAA;UAAA,OAAA3D,EAAA,CAAAU,WAAA,CAAS+C,GAAA,CAAAtB,SAAA,CAAU,SAAS,CAAC;QAAA,EAAC;QAOtBnC,EALR,CAAAC,cAAA,eAGC,eACiD,eACP;QAa/BD,EAZA,CAAAa,SAAA,eAEO,eAGA,eAGA,eAGA,eAGA;QAEfb,EADI,CAAAe,YAAA,EAAM,EACJ;QAGEf,EAFR,CAAAC,cAAA,eAA8C,eACI,eAGzC;QAOGD,EANA,CAAAa,SAAA,eAEO,eAGA,eAGA;QAEfb,EADI,CAAAe,YAAA,EAAM,EACJ;QACNf,EAAA,CAAAa,SAAA,eAEO;QAEfb,EADI,CAAAe,YAAA,EAAM,EACJ;QACNf,EAAA,CAAAC,cAAA,eAGC;QACGD,EAAA,CAAAc,MAAA,iBACJ;QACJd,EADI,CAAAe,YAAA,EAAM,EACJ;QAGNf,EAAA,CAAAC,cAAA,eAGC;QADGD,EAAA,CAAAE,UAAA,mBAAAuE,iDAAA;UAAAzE,EAAA,CAAAK,aAAA,CAAAsD,GAAA;UAAA,OAAA3D,EAAA,CAAAU,WAAA,CAAS+C,GAAA,CAAAtB,SAAA,CAAU,QAAQ,CAAC;QAAA,EAAC;QAOrBnC,EALR,CAAAC,cAAA,eAGC,eACiD,eACD;QAOrCD,EANA,CAAAa,SAAA,eAEO,eAGA,eAGA;QACXb,EAAA,CAAAe,YAAA,EAAM;QACNf,EAAA,CAAAa,SAAA,eAEO;QACPb,EAAA,CAAAC,cAAA,eAAiC;QAU7BD,EATA,CAAAa,SAAA,eAEO,eAGA,eAGA,eAGA;QAEfb,EADI,CAAAe,YAAA,EAAM,EACJ;QAGEf,EAFR,CAAAC,cAAA,eAA8C,eACI,eAGzC;QACGD,EAAA,CAAAa,SAAA,eAEO;QAEfb,EADI,CAAAe,YAAA,EAAM,EACJ;QACNf,EAAA,CAAAa,SAAA,eAEO;QAEfb,EADI,CAAAe,YAAA,EAAM,EACJ;QACNf,EAAA,CAAAC,cAAA,eAGC;QACGD,EAAA,CAAAc,MAAA,gBACJ;QACJd,EADI,CAAAe,YAAA,EAAM,EACJ;QAGNf,EAAA,CAAAC,cAAA,eAGC;QADGD,EAAA,CAAAE,UAAA,mBAAAwE,iDAAA;UAAA1E,EAAA,CAAAK,aAAA,CAAAsD,GAAA;UAAA,OAAA3D,EAAA,CAAAU,WAAA,CAAS+C,GAAA,CAAAtB,SAAA,CAAU,SAAS,CAAC;QAAA,EAAC;QAM1BnC,EAJJ,CAAAC,cAAA,eAGC,eACiD;QAC1CD,EAAA,CAAAa,SAAA,eAEO;QACPb,EAAA,CAAAC,cAAA,eAEC;QAOGD,EANA,CAAAa,SAAA,eAEO,eAGA,eAGA;QAEfb,EADI,CAAAe,YAAA,EAAM,EACJ;QAGEf,EAFR,CAAAC,cAAA,eAA8C,eACI,eAGzC;QAOGD,EANA,CAAAa,SAAA,eAEO,eAGA,eAGA;QAEfb,EADI,CAAAe,YAAA,EAAM,EACJ;QACNf,EAAA,CAAAa,SAAA,eAEO;QAEfb,EADI,CAAAe,YAAA,EAAM,EACJ;QACNf,EAAA,CAAAC,cAAA,eAGC;QACGD,EAAA,CAAAc,MAAA,iBACJ;QACJd,EADI,CAAAe,YAAA,EAAM,EACJ;QAGNf,EAAA,CAAAC,cAAA,gBAGC;QADGD,EAAA,CAAAE,UAAA,mBAAAyE,kDAAA;UAAA3E,EAAA,CAAAK,aAAA,CAAAsD,GAAA;UAAA,OAAA3D,EAAA,CAAAU,WAAA,CAAS+C,GAAA,CAAAtB,SAAA,CAAU,OAAO,CAAC;QAAA,EAAC;QAMxBnC,EAJJ,CAAAC,cAAA,gBAGC,gBACiD;QAC1CD,EAAA,CAAAa,SAAA,gBAEO;QACPb,EAAA,CAAAC,cAAA,gBAEC;QAOGD,EANA,CAAAa,SAAA,gBAEO,gBAGA,gBAGA;QAEfb,EADI,CAAAe,YAAA,EAAM,EACJ;QAGEf,EAFR,CAAAC,cAAA,gBAA8C,gBACI,gBAGzC;QAOGD,EANA,CAAAa,SAAA,gBAEO,gBAGA,gBAGA;QAEfb,EADI,CAAAe,YAAA,EAAM,EACJ;QACNf,EAAA,CAAAa,SAAA,gBAEO;QAEfb,EADI,CAAAe,YAAA,EAAM,EACJ;QACNf,EAAA,CAAAC,cAAA,gBAGC;QACGD,EAAA,CAAAc,MAAA,gBACJ;QACJd,EADI,CAAAe,YAAA,EAAM,EACJ;QAGNf,EAAA,CAAAC,cAAA,gBAGC;QADGD,EAAA,CAAAE,UAAA,mBAAA0E,kDAAA;UAAA5E,EAAA,CAAAK,aAAA,CAAAsD,GAAA;UAAA,OAAA3D,EAAA,CAAAU,WAAA,CAAS+C,GAAA,CAAAtB,SAAA,CAAU,YAAY,CAAC;QAAA,EAAC;QAOzBnC,EALR,CAAAC,cAAA,gBAGC,gBACiD,gBAGzC;QAWGD,EAVA,CAAAa,SAAA,gBAEO,gBAGA,gBAGA,gBACsB,gBAGtB;QAEfb,EADI,CAAAe,YAAA,EAAM,EACJ;QAGEf,EAFR,CAAAC,cAAA,gBAA8C,gBACI,gBAGzC;QAOGD,EANA,CAAAa,SAAA,gBAEO,gBAGA,gBAGA;QAEfb,EADI,CAAAe,YAAA,EAAM,EACJ;QACNf,EAAA,CAAAa,SAAA,gBAEO;QAEfb,EADI,CAAAe,YAAA,EAAM,EACJ;QACNf,EAAA,CAAAC,cAAA,gBAGC;QACGD,EAAA,CAAAc,MAAA,qBACJ;QACJd,EADI,CAAAe,YAAA,EAAM,EACJ;QAGNf,EAAA,CAAAC,cAAA,gBAGC;QADGD,EAAA,CAAAE,UAAA,mBAAA2E,kDAAA;UAAA7E,EAAA,CAAAK,aAAA,CAAAsD,GAAA;UAAA,OAAA3D,EAAA,CAAAU,WAAA,CAAS+C,GAAA,CAAAtB,SAAA,CAAU,MAAM,CAAC;QAAA,EAAC;QAMvBnC,EAJJ,CAAAC,cAAA,gBAGC,gBACiD;QAC1CD,EAAA,CAAAa,SAAA,gBAEO;QACPb,EAAA,CAAAC,cAAA,gBAEC;QAaGD,EAZA,CAAAa,SAAA,gBAEO,gBAGA,gBAGA,gBAGA,gBAGA;QAEfb,EADI,CAAAe,YAAA,EAAM,EACJ;QAGEf,EAFR,CAAAC,cAAA,gBAA8C,gBACI,gBAGzC;QAOGD,EANA,CAAAa,SAAA,gBAEO,gBAGA,gBAGA;QAEfb,EADI,CAAAe,YAAA,EAAM,EACJ;QACNf,EAAA,CAAAa,SAAA,gBAEO;QAEfb,EADI,CAAAe,YAAA,EAAM,EACJ;QACNf,EAAA,CAAAC,cAAA,gBAGC;QACGD,EAAA,CAAAc,MAAA,eACJ;QACJd,EADI,CAAAe,YAAA,EAAM,EACJ;QAENf,EAAA,CAAAa,SAAA,gBAA8B;QAG9Bb,EAAA,CAAAC,cAAA,gBAGC;QADGD,EAAA,CAAAE,UAAA,mBAAA4E,kDAAA;UAAA9E,EAAA,CAAAK,aAAA,CAAAsD,GAAA;UAAA,OAAA3D,EAAA,CAAAU,WAAA,CAAS+C,GAAA,CAAAtB,SAAA,CAAU,UAAU,CAAC;QAAA,EAAC;QAYnBnC,EAVZ,CAAAC,cAAA,gBAGC,gBAGI,gBAGI,gBAC4B;QAUrBD,EATA,CAAAa,SAAA,gBAEO,gBAGA,gBAGA,gBAGA;QACXb,EAAA,CAAAe,YAAA,EAAM;QACNf,EAAA,CAAAC,cAAA,gBAEC;QAIGD,EAHA,CAAAa,SAAA,gBAEO,gBAGA;QAEfb,EADI,CAAAe,YAAA,EAAM,EACJ;QACNf,EAAA,CAAAa,SAAA,gBAEO;QAEfb,EADI,CAAAe,YAAA,EAAM,EACJ;QACNf,EAAA,CAAAC,cAAA,gBAGC;QACGD,EAAA,CAAAc,MAAA,mBACJ;QACJd,EADI,CAAAe,YAAA,EAAM,EACJ;QAGNf,EAAA,CAAAC,cAAA,gBAGC;QADGD,EAAA,CAAAE,UAAA,mBAAA6E,kDAAA;UAAA/E,EAAA,CAAAK,aAAA,CAAAsD,GAAA;UAAA,OAAA3D,EAAA,CAAAU,WAAA,CAAS+C,GAAA,CAAAtB,SAAA,CAAU,YAAY,CAAC;QAAA,EAAC;QAM7BnC,EAJJ,CAAAC,cAAA,gBAGC,gBAGI;QACGD,EAAA,CAAAa,SAAA,gBAEO;QACPb,EAAA,CAAAC,cAAA,gBAEC;QAOGD,EANA,CAAAa,SAAA,gBAEO,gBAGA,gBAGA;QAEfb,EADI,CAAAe,YAAA,EAAM,EACJ;QACNf,EAAA,CAAAC,cAAA,gBAEC;QAUGD,EATA,CAAAa,SAAA,gBAEO,gBAGA,gBAGA,gBAGA;QACXb,EAAA,CAAAe,YAAA,EAAM;QACNf,EAAA,CAAAC,cAAA,gBAEC;QACGD,EAAA,CAAAa,SAAA,gBAEO;QAEfb,EADI,CAAAe,YAAA,EAAM,EACJ;QACNf,EAAA,CAAAC,cAAA,gBAGC;QACGD,EAAA,CAAAc,MAAA,qBACJ;QACJd,EADI,CAAAe,YAAA,EAAM,EACJ;QAGNf,EAAA,CAAAC,cAAA,gBAGC;QADGD,EAAA,CAAAE,UAAA,mBAAA8E,kDAAA;UAAAhF,EAAA,CAAAK,aAAA,CAAAsD,GAAA;UAAA,OAAA3D,EAAA,CAAAU,WAAA,CAAS+C,GAAA,CAAAtB,SAAA,CAAU,UAAU,CAAC;QAAA,EAAC;QASvBnC,EAPR,CAAAC,cAAA,gBAGC,gBAGI,gBAGI;QACGD,EAAA,CAAAa,SAAA,gBAEO;QACPb,EAAA,CAAAC,cAAA,gBAEC;QAOGD,EANA,CAAAa,SAAA,gBAEO,gBAGA,gBAGA;QAEfb,EADI,CAAAe,YAAA,EAAM,EACJ;QACNf,EAAA,CAAAC,cAAA,gBAEC;QAUGD,EATA,CAAAa,SAAA,gBAEO,gBAGA,gBAGA,gBAGA;QACXb,EAAA,CAAAe,YAAA,EAAM;QACNf,EAAA,CAAAa,SAAA,gBAEO;QAEfb,EADI,CAAAe,YAAA,EAAM,EACJ;QACNf,EAAA,CAAAC,cAAA,gBAGC;QACGD,EAAA,CAAAc,MAAA,mBACJ;QACJd,EADI,CAAAe,YAAA,EAAM,EACJ;QAGNf,EAAA,CAAAC,cAAA,gBAGC;QADGD,EAAA,CAAAE,UAAA,mBAAA+E,kDAAA;UAAAjF,EAAA,CAAAK,aAAA,CAAAsD,GAAA;UAAA,OAAA3D,EAAA,CAAAU,WAAA,CAAS+C,GAAA,CAAAtB,SAAA,CAAU,QAAQ,CAAC;QAAA,EAAC;QAMzBnC,EAJJ,CAAAC,cAAA,gBAGC,gBAGI;QACGD,EAAA,CAAAa,SAAA,gBAEO;QACPb,EAAA,CAAAC,cAAA,gBAAkD;QAO9CD,EANA,CAAAa,SAAA,gBAEO,gBAGA,gBAGA;QACXb,EAAA,CAAAe,YAAA,EAAM;QACNf,EAAA,CAAAC,cAAA,gBAEC;QAIGD,EAHA,CAAAa,SAAA,gBAEO,gBAGA;QAEfb,EADI,CAAAe,YAAA,EAAM,EACJ;QACNf,EAAA,CAAAC,cAAA,gBAAqC;QACjCD,EAAA,CAAAa,SAAA,gBAEO;QAEfb,EADI,CAAAe,YAAA,EAAM,EACJ;QACNf,EAAA,CAAAC,cAAA,gBAGC;QACGD,EAAA,CAAAc,MAAA,iBACJ;QAKpBd,EALoB,CAAAe,YAAA,EAAM,EACJ,EACJ,EACJ,EACJ,EACI;;;QAluBVf,EAHA,CAAAgB,WAAA,eAAAyC,GAAA,CAAAxC,MAAA,CAAAmB,MAAA,mBAAAqB,GAAA,CAAAxC,MAAA,CAAAmB,MAAA,gBAEC,gBAAAqB,GAAA,CAAAxC,MAAA,CAAAmB,MAAA,mBAAAqB,GAAA,CAAAxC,MAAA,CAAAmB,MAAA,gBAGA;QAMGpC,EAAA,CAAAoB,SAAA,EAAyC;QAAzCpB,EAAA,CAAAmB,UAAA,0CAAyC;QAO7CnB,EAAA,CAAAoB,SAAA,EAAe;QAEfpB,EAFA,CAAAmB,UAAA,gBAAe,0BACU,qBACL;QASRnB,EAAA,CAAAoB,SAAA,GAAyC;QAAzCpB,EAAA,CAAAmB,UAAA,0CAAyC;QAYrCnB,EAAA,CAAAoB,SAAA,GAAsC;QAAtCpB,EAAA,CAAAmB,UAAA,uCAAsC;QAS1CnB,EAAA,CAAAoB,SAAA,GAiBC;QAjBDpB,EAAA,CAAAkF,UAAA,CAAAzB,GAAA,CAAAxC,MAAA,CAAAkE,MAAA,CAiBC;QAWGnF,EAAA,CAAAoB,SAAA,GAAyC;QAAzCpB,EAAA,CAAAgB,WAAA,WAAAyC,GAAA,CAAAxC,MAAA,CAAAyB,MAAA,YAAyC;QAOjC1C,EAAA,CAAAoB,SAAA,GAAkC;QAAlCpB,EAAA,CAAAmB,UAAA,mCAAkC;QAKtCnB,EAAA,CAAAoB,SAAA,EAAiD;QAAjDpB,EAAA,CAAAgB,WAAA,mBAAAyC,GAAA,CAAAxC,MAAA,CAAAyB,MAAA,YAAiD;QAQrD1C,EAAA,CAAAoB,SAAA,GAAyC;QAAzCpB,EAAA,CAAAgB,WAAA,WAAAyC,GAAA,CAAAxC,MAAA,CAAAyB,MAAA,YAAyC;QAMjC1C,EAAA,CAAAoB,SAAA,GAAkC;QAAlCpB,EAAA,CAAAmB,UAAA,mCAAkC;QAKtCnB,EAAA,CAAAoB,SAAA,EAAiD;QAAjDpB,EAAA,CAAAgB,WAAA,mBAAAyC,GAAA,CAAAxC,MAAA,CAAAyB,MAAA,YAAiD;QAQrD1C,EAAA,CAAAoB,SAAA,GAA0C;QAA1CpB,EAAA,CAAAgB,WAAA,WAAAyC,GAAA,CAAAxC,MAAA,CAAAyB,MAAA,aAA0C;QAMlC1C,EAAA,CAAAoB,SAAA,GAAiC;QAAjCpB,EAAA,CAAAmB,UAAA,kCAAiC;QAKrCnB,EAAA,CAAAoB,SAAA,EAAkD;QAAlDpB,EAAA,CAAAgB,WAAA,mBAAAyC,GAAA,CAAAxC,MAAA,CAAAyB,MAAA,aAAkD;QAmBlD1C,EAAA,CAAAoB,SAAA,GAAkD;QAAlDpB,EAAA,CAAAgB,WAAA,mBAAAyC,GAAA,CAAAxC,MAAA,CAAAmB,MAAA,aAAkD;QAQlDpC,EAAA,CAAAoB,SAAA,GAAgD;QAAhDpB,EAAA,CAAAgB,WAAA,iBAAAyC,GAAA,CAAAxC,MAAA,CAAAmB,MAAA,aAAgD;QAahDpC,EAAA,CAAAoB,SAAA,GAAoD;QAApDpB,EAAA,CAAAgB,WAAA,mBAAAyC,GAAA,CAAAxC,MAAA,CAAAmB,MAAA,eAAoD;QA4CpDpC,EAAA,CAAAoB,SAAA,IAAkD;QAAlDpB,EAAA,CAAAgB,WAAA,iBAAAyC,GAAA,CAAAxC,MAAA,CAAAmB,MAAA,eAAkD;QAalDpC,EAAA,CAAAoB,SAAA,GAAmD;QAAnDpB,EAAA,CAAAgB,WAAA,mBAAAyC,GAAA,CAAAxC,MAAA,CAAAmB,MAAA,cAAmD;QAiDnDpC,EAAA,CAAAoB,SAAA,IAAiD;QAAjDpB,EAAA,CAAAgB,WAAA,iBAAAyC,GAAA,CAAAxC,MAAA,CAAAmB,MAAA,cAAiD;QAajDpC,EAAA,CAAAoB,SAAA,GAAoD;QAApDpB,EAAA,CAAAgB,WAAA,mBAAAyC,GAAA,CAAAxC,MAAA,CAAAmB,MAAA,eAAoD;QA2CpDpC,EAAA,CAAAoB,SAAA,IAAkD;QAAlDpB,EAAA,CAAAgB,WAAA,iBAAAyC,GAAA,CAAAxC,MAAA,CAAAmB,MAAA,eAAkD;QAalDpC,EAAA,CAAAoB,SAAA,GAAkD;QAAlDpB,EAAA,CAAAgB,WAAA,mBAAAyC,GAAA,CAAAxC,MAAA,CAAAmB,MAAA,aAAkD;QA2ClDpC,EAAA,CAAAoB,SAAA,IAAgD;QAAhDpB,EAAA,CAAAgB,WAAA,iBAAAyC,GAAA,CAAAxC,MAAA,CAAAmB,MAAA,aAAgD;QAahDpC,EAAA,CAAAoB,SAAA,GAAuD;QAAvDpB,EAAA,CAAAgB,WAAA,mBAAAyC,GAAA,CAAAxC,MAAA,CAAAmB,MAAA,kBAAuD;QA4CvDpC,EAAA,CAAAoB,SAAA,IAAqD;QAArDpB,EAAA,CAAAgB,WAAA,iBAAAyC,GAAA,CAAAxC,MAAA,CAAAmB,MAAA,kBAAqD;QAarDpC,EAAA,CAAAoB,SAAA,GAAiD;QAAjDpB,EAAA,CAAAgB,WAAA,mBAAAyC,GAAA,CAAAxC,MAAA,CAAAmB,MAAA,YAAiD;QAiDjDpC,EAAA,CAAAoB,SAAA,IAA+C;QAA/CpB,EAAA,CAAAgB,WAAA,iBAAAyC,GAAA,CAAAxC,MAAA,CAAAmB,MAAA,YAA+C;QAe/CpC,EAAA,CAAAoB,SAAA,GAAqD;QAArDpB,EAAA,CAAAgB,WAAA,mBAAAyC,GAAA,CAAAxC,MAAA,CAAAmB,MAAA,gBAAqD;QAwCrDpC,EAAA,CAAAoB,SAAA,IAAmD;QAAnDpB,EAAA,CAAAgB,WAAA,iBAAAyC,GAAA,CAAAxC,MAAA,CAAAmB,MAAA,gBAAmD;QAanDpC,EAAA,CAAAoB,SAAA,GAAuD;QAAvDpB,EAAA,CAAAgB,WAAA,mBAAAyC,GAAA,CAAAxC,MAAA,CAAAmB,MAAA,kBAAuD;QAgDvDpC,EAAA,CAAAoB,SAAA,IAAqD;QAArDpB,EAAA,CAAAgB,WAAA,iBAAAyC,GAAA,CAAAxC,MAAA,CAAAmB,MAAA,kBAAqD;QAarDpC,EAAA,CAAAoB,SAAA,GAAqD;QAArDpB,EAAA,CAAAgB,WAAA,mBAAAyC,GAAA,CAAAxC,MAAA,CAAAmB,MAAA,gBAAqD;QAgDrDpC,EAAA,CAAAoB,SAAA,IAAmD;QAAnDpB,EAAA,CAAAgB,WAAA,iBAAAyC,GAAA,CAAAxC,MAAA,CAAAmB,MAAA,gBAAmD;QAanDpC,EAAA,CAAAoB,SAAA,GAAmD;QAAnDpB,EAAA,CAAAgB,WAAA,mBAAAyC,GAAA,CAAAxC,MAAA,CAAAmB,MAAA,cAAmD;QAsCnDpC,EAAA,CAAAoB,SAAA,IAAiD;QAAjDpB,EAAA,CAAAgB,WAAA,iBAAAyC,GAAA,CAAAxC,MAAA,CAAAmB,MAAA,cAAiD;;;mBDzrBjEzC,aAAa,EAAAyF,EAAA,CAAAC,OAAA,EACbxF,mBAAmB,EACnBH,eAAe,EAAA4F,EAAA,CAAAC,aAAA,EACf9F,OAAO,EACPG,gBAAgB,EAAA4F,EAAA,CAAAC,UAAA;IAAAC,MAAA;IAAAC,aAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}