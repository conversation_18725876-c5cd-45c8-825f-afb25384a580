import { isFunction } from './helpers';
export function resolveLoader(options) {
    const { path, inlineLoader, mainLoader, data } = options;
    if (inlineLoader) {
        const pathLoader = inlineLoader[path];
        if (isFunction(pathLoader) === false) {
            throw `You're using an inline loader but didn't provide a loader for ${path}`;
        }
        return inlineLoader[path]().then((res) => res.default ? res.default : res);
    }
    return mainLoader.getTranslation(path, data);
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicmVzb2x2ZS1sb2FkZXIuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi9saWJzL3RyYW5zbG9jby9zcmMvbGliL3Jlc29sdmUtbG9hZGVyLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUVBLE9BQU8sRUFBRSxVQUFVLEVBQUUsTUFBTSxXQUFXLENBQUM7QUFTdkMsTUFBTSxVQUFVLGFBQWEsQ0FBQyxPQUFnQjtJQUM1QyxNQUFNLEVBQUUsSUFBSSxFQUFFLFlBQVksRUFBRSxVQUFVLEVBQUUsSUFBSSxFQUFFLEdBQUcsT0FBTyxDQUFDO0lBRXpELElBQUksWUFBWSxFQUFFLENBQUM7UUFDakIsTUFBTSxVQUFVLEdBQUcsWUFBWSxDQUFDLElBQUksQ0FBQyxDQUFDO1FBQ3RDLElBQUksVUFBVSxDQUFDLFVBQVUsQ0FBQyxLQUFLLEtBQUssRUFBRSxDQUFDO1lBQ3JDLE1BQU0saUVBQWlFLElBQUksRUFBRSxDQUFDO1FBQ2hGLENBQUM7UUFFRCxPQUFPLFlBQVksQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQyxDQUFDLEdBQUcsRUFBRSxFQUFFLENBQ3ZDLEdBQUcsQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FDaEMsQ0FBQztJQUNKLENBQUM7SUFFRCxPQUFPLFVBQVUsQ0FBQyxjQUFjLENBQUMsSUFBSSxFQUFFLElBQUksQ0FBQyxDQUFDO0FBQy9DLENBQUMiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBUcmFuc2xvY29Mb2FkZXIsIFRyYW5zbG9jb0xvYWRlckRhdGEgfSBmcm9tICcuL3RyYW5zbG9jby5sb2FkZXInO1xuaW1wb3J0IHsgSW5saW5lTG9hZGVyIH0gZnJvbSAnLi90eXBlcyc7XG5pbXBvcnQgeyBpc0Z1bmN0aW9uIH0gZnJvbSAnLi9oZWxwZXJzJztcblxuaW50ZXJmYWNlIE9wdGlvbnMge1xuICBpbmxpbmVMb2FkZXI/OiBJbmxpbmVMb2FkZXI7XG4gIHBhdGg6IHN0cmluZztcbiAgbWFpbkxvYWRlcjogVHJhbnNsb2NvTG9hZGVyO1xuICBkYXRhPzogVHJhbnNsb2NvTG9hZGVyRGF0YTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHJlc29sdmVMb2FkZXIob3B0aW9uczogT3B0aW9ucykge1xuICBjb25zdCB7IHBhdGgsIGlubGluZUxvYWRlciwgbWFpbkxvYWRlciwgZGF0YSB9ID0gb3B0aW9ucztcblxuICBpZiAoaW5saW5lTG9hZGVyKSB7XG4gICAgY29uc3QgcGF0aExvYWRlciA9IGlubGluZUxvYWRlcltwYXRoXTtcbiAgICBpZiAoaXNGdW5jdGlvbihwYXRoTG9hZGVyKSA9PT0gZmFsc2UpIHtcbiAgICAgIHRocm93IGBZb3UncmUgdXNpbmcgYW4gaW5saW5lIGxvYWRlciBidXQgZGlkbid0IHByb3ZpZGUgYSBsb2FkZXIgZm9yICR7cGF0aH1gO1xuICAgIH1cblxuICAgIHJldHVybiBpbmxpbmVMb2FkZXJbcGF0aF0oKS50aGVuKChyZXMpID0+XG4gICAgICByZXMuZGVmYXVsdCA/IHJlcy5kZWZhdWx0IDogcmVzLFxuICAgICk7XG4gIH1cblxuICByZXR1cm4gbWFpbkxvYWRlci5nZXRUcmFuc2xhdGlvbihwYXRoLCBkYXRhKTtcbn1cbiJdfQ==