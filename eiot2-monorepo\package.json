{"name": "@eiot2-monorepo/source", "version": "0.0.0", "license": "MIT", "scripts": {}, "private": true, "dependencies": {"@angular-architects/module-federation": "^20.0.0", "@angular-devkit/build-angular": "~20.0.0", "@angular/animations": "^20.0.6", "@angular/cdk": "^20.0.5", "@angular/common": "~20.0.0", "@angular/compiler": "~20.0.0", "@angular/core": "~20.0.0", "@angular/forms": "~20.0.0", "@angular/material": "^20.0.5", "@angular/material-luxon-adapter": "^20.0.5", "@angular/platform-browser": "~20.0.0", "@angular/platform-browser-dynamic": "~20.0.0", "@angular/platform-server": "~20.0.0", "@angular/router": "~20.0.0", "@angular/ssr": "~20.0.0", "@jsverse/transloco": "^7.6.1", "@tailwindcss/typography": "^0.5.16", "@types/crypto-js": "^4.2.2", "@types/lodash-es": "^4.17.12", "chroma-js": "^3.1.2", "crypto-js": "^4.2.0", "express": "^4.21.2", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "moment": "^2.30.1", "perfect-scrollbar": "^1.5.6", "rxjs": "~7.8.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/core": "~20.0.0", "@angular-devkit/schematics": "~20.0.0", "@angular/build": "~20.0.0", "@angular/cli": "~20.0.0", "@angular/compiler-cli": "~20.0.0", "@angular/language-service": "~20.0.0", "@eslint/js": "^9.8.0", "@nx/angular": "^21.2.2", "@nx/devkit": "21.2.2", "@nx/eslint": "21.2.2", "@nx/eslint-plugin": "21.2.2", "@nx/jest": "21.2.2", "@nx/js": "21.2.2", "@nx/playwright": "21.2.2", "@nx/web": "21.2.2", "@nx/workspace": "21.2.2", "@playwright/test": "^1.36.0", "@schematics/angular": "~20.0.0", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/chroma-js": "^3.1.1", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.20", "@types/node": "18.16.9", "@typescript-eslint/utils": "^8.29.0", "angular-eslint": "^20.0.0", "autoprefixer": "^10.4.21", "eslint": "^9.8.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-playwright": "^1.6.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-preset-angular": "~14.6.0", "jsonc-eslint-parser": "^2.1.0", "ng-packagr": "~20.0.0", "nx": "21.2.2", "postcss": "^8.5.6", "postcss-url": "~10.1.3", "prettier": "^2.6.2", "tailwindcss": "^3.4.0", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "~5.8.2", "typescript-eslint": "^8.29.0"}}