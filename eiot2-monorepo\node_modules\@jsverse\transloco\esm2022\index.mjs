export { translate, translateObject, TranslocoService, } from './lib/transloco.service';
export { TranslocoDirective } from './lib/transloco.directive';
export { TranslocoPipe } from './lib/transloco.pipe';
export { TranslocoModule } from './lib/transloco.module';
export { TRANSLOCO_LOADER } from './lib/transloco.loader';
export { TRANSLOCO_CONFIG, defaultConfig, translocoConfig, } from './lib/transloco.config';
export { TRANSLOCO_TRANSPILER, DefaultTranspiler, FunctionalTranspiler, getFunctionArgs, } from './lib/transloco.transpiler';
export { TRANSLOCO_SCOPE } from './lib/transloco-scope';
export { TRANSLOCO_LOADING_TEMPLATE } from './lib/transloco-loading-template';
export { TRANSLOCO_LANG } from './lib/transloco-lang';
export { TestingLoader, TranslocoTestingModule, } from './lib/transloco-testing.module';
export { TRANSLOCO_INTERCEPTOR, DefaultInterceptor, } from './lib/transloco.interceptor';
export { TRANSLOCO_FALLBACK_STRATEGY, DefaultFallbackStrategy, } from './lib/transloco-fallback-strategy';
export { TRANSLOCO_MISSING_HANDLER, DefaultMissingHandler, } from './lib/transloco-missing-handler';
export { getBrowserCultureLang, getBrowserLang } from './lib/browser-lang';
export { getPipeValue, getLangFromScope, getScopeFromLang } from './lib/shared';
export * from './lib/types';
export * from './lib/helpers';
export { provideTranslocoFallbackStrategy, provideTranslocoInterceptor, provideTranslocoTranspiler, provideTranslocoMissingHandler, provideTranslocoLoadingTpl, provideTransloco, provideTranslocoConfig, provideTranslocoLoader, provideTranslocoScope, provideTranslocoLang, } from './lib/transloco.providers';
export { translateSignal, translateObjectSignal } from './lib/transloco.signal';
//# sourceMappingURL=data:application/json;base64,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