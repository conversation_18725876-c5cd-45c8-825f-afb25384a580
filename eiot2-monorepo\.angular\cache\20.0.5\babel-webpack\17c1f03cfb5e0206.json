{"ast": null, "code": "import { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { ChangeDetectorRef, EventEmitter, inject } from '@angular/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { fuseAnimations } from '@fuse/animations';\nimport { FuseAlertService } from '@fuse/components/alert/alert.service';\nimport { FuseUtilsService } from '@fuse/services/utils/utils.service';\nimport { Subject, filter, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/icon\";\nimport * as i2 from \"@angular/material/button\";\nconst _c0 = [[[\"\", \"fuseAlertTitle\", \"\"]], \"*\", [[\"\", \"fuseAlertIcon\", \"\"]]];\nconst _c1 = [\"[fuseAlertTitle]\", \"*\", \"[fuseAlertIcon]\"];\nfunction FuseAlertComponent_Conditional_0_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 1);\n  }\n}\nfunction FuseAlertComponent_Conditional_0_Conditional_2_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-icon\", 7);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"svgIcon\", \"heroicons_solid:check-circle\");\n  }\n}\nfunction FuseAlertComponent_Conditional_0_Conditional_2_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-icon\", 7);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"svgIcon\", \"heroicons_solid:check-circle\");\n  }\n}\nfunction FuseAlertComponent_Conditional_0_Conditional_2_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-icon\", 7);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"svgIcon\", \"heroicons_solid:x-circle\");\n  }\n}\nfunction FuseAlertComponent_Conditional_0_Conditional_2_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-icon\", 7);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"svgIcon\", \"heroicons_solid:check-circle\");\n  }\n}\nfunction FuseAlertComponent_Conditional_0_Conditional_2_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-icon\", 7);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"svgIcon\", \"heroicons_solid:information-circle\");\n  }\n}\nfunction FuseAlertComponent_Conditional_0_Conditional_2_Conditional_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-icon\", 7);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"svgIcon\", \"heroicons_solid:check-circle\");\n  }\n}\nfunction FuseAlertComponent_Conditional_0_Conditional_2_Conditional_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-icon\", 7);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"svgIcon\", \"heroicons_solid:exclamation-triangle\");\n  }\n}\nfunction FuseAlertComponent_Conditional_0_Conditional_2_Conditional_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-icon\", 7);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"svgIcon\", \"heroicons_solid:x-circle\");\n  }\n}\nfunction FuseAlertComponent_Conditional_0_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 8);\n    i0.ɵɵprojection(2, 2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 9);\n    i0.ɵɵconditionalCreate(4, FuseAlertComponent_Conditional_0_Conditional_2_Conditional_4_Template, 1, 1, \"mat-icon\", 7);\n    i0.ɵɵconditionalCreate(5, FuseAlertComponent_Conditional_0_Conditional_2_Conditional_5_Template, 1, 1, \"mat-icon\", 7);\n    i0.ɵɵconditionalCreate(6, FuseAlertComponent_Conditional_0_Conditional_2_Conditional_6_Template, 1, 1, \"mat-icon\", 7);\n    i0.ɵɵconditionalCreate(7, FuseAlertComponent_Conditional_0_Conditional_2_Conditional_7_Template, 1, 1, \"mat-icon\", 7);\n    i0.ɵɵconditionalCreate(8, FuseAlertComponent_Conditional_0_Conditional_2_Conditional_8_Template, 1, 1, \"mat-icon\", 7);\n    i0.ɵɵconditionalCreate(9, FuseAlertComponent_Conditional_0_Conditional_2_Conditional_9_Template, 1, 1, \"mat-icon\", 7);\n    i0.ɵɵconditionalCreate(10, FuseAlertComponent_Conditional_0_Conditional_2_Conditional_10_Template, 1, 1, \"mat-icon\", 7);\n    i0.ɵɵconditionalCreate(11, FuseAlertComponent_Conditional_0_Conditional_2_Conditional_11_Template, 1, 1, \"mat-icon\", 7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵconditional(ctx_r1.type === \"primary\" ? 4 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1.type === \"accent\" ? 5 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1.type === \"warn\" ? 6 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1.type === \"basic\" ? 7 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1.type === \"info\" ? 8 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1.type === \"success\" ? 9 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1.type === \"warning\" ? 10 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1.type === \"error\" ? 11 : -1);\n  }\n}\nfunction FuseAlertComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵconditionalCreate(1, FuseAlertComponent_Conditional_0_Conditional_1_Template, 1, 0, \"div\", 1);\n    i0.ɵɵconditionalCreate(2, FuseAlertComponent_Conditional_0_Conditional_2_Template, 12, 8, \"div\", 2);\n    i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4);\n    i0.ɵɵprojection(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 5);\n    i0.ɵɵprojection(7, 1);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 6);\n    i0.ɵɵlistener(\"click\", function FuseAlertComponent_Conditional_0_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.dismiss());\n    });\n    i0.ɵɵelement(9, \"mat-icon\", 7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@fadeIn\", !ctx_r1.dismissed)(\"@fadeOut\", !ctx_r1.dismissed);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1.appearance === \"border\" ? 1 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(ctx_r1.showIcon ? 2 : -1);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"svgIcon\", \"heroicons_solid:x-mark\");\n  }\n}\nexport class FuseAlertComponent {\n  constructor() {\n    /* eslint-enable @typescript-eslint/naming-convention */\n    this._changeDetectorRef = inject(ChangeDetectorRef);\n    this._fuseAlertService = inject(FuseAlertService);\n    this._fuseUtilsService = inject(FuseUtilsService);\n    this.appearance = 'soft';\n    this.dismissed = false;\n    this.dismissible = false;\n    this.name = this._fuseUtilsService.randomId();\n    this.showIcon = true;\n    this.type = 'primary';\n    this.dismissedChanged = new EventEmitter();\n    this._unsubscribeAll = new Subject();\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Accessors\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * Host binding for component classes\n   */\n  get classList() {\n    /* eslint-disable @typescript-eslint/naming-convention */\n    return {\n      'fuse-alert-appearance-border': this.appearance === 'border',\n      'fuse-alert-appearance-fill': this.appearance === 'fill',\n      'fuse-alert-appearance-outline': this.appearance === 'outline',\n      'fuse-alert-appearance-soft': this.appearance === 'soft',\n      'fuse-alert-dismissed': this.dismissed,\n      'fuse-alert-dismissible': this.dismissible,\n      'fuse-alert-show-icon': this.showIcon,\n      'fuse-alert-type-primary': this.type === 'primary',\n      'fuse-alert-type-accent': this.type === 'accent',\n      'fuse-alert-type-warn': this.type === 'warn',\n      'fuse-alert-type-basic': this.type === 'basic',\n      'fuse-alert-type-info': this.type === 'info',\n      'fuse-alert-type-success': this.type === 'success',\n      'fuse-alert-type-warning': this.type === 'warning',\n      'fuse-alert-type-error': this.type === 'error'\n    };\n    /* eslint-enable @typescript-eslint/naming-convention */\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Lifecycle hooks\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * On changes\n   *\n   * @param changes\n   */\n  ngOnChanges(changes) {\n    // Dismissed\n    if ('dismissed' in changes) {\n      // Coerce the value to a boolean\n      this.dismissed = coerceBooleanProperty(changes.dismissed.currentValue);\n      // Dismiss/show the alert\n      this._toggleDismiss(this.dismissed);\n    }\n    // Dismissible\n    if ('dismissible' in changes) {\n      // Coerce the value to a boolean\n      this.dismissible = coerceBooleanProperty(changes.dismissible.currentValue);\n    }\n    // Show icon\n    if ('showIcon' in changes) {\n      // Coerce the value to a boolean\n      this.showIcon = coerceBooleanProperty(changes.showIcon.currentValue);\n    }\n  }\n  /**\n   * On init\n   */\n  ngOnInit() {\n    // Subscribe to the dismiss calls\n    this._fuseAlertService.onDismiss.pipe(filter(name => this.name === name), takeUntil(this._unsubscribeAll)).subscribe(() => {\n      // Dismiss the alert\n      this.dismiss();\n    });\n    // Subscribe to the show calls\n    this._fuseAlertService.onShow.pipe(filter(name => this.name === name), takeUntil(this._unsubscribeAll)).subscribe(() => {\n      // Show the alert\n      this.show();\n    });\n  }\n  /**\n   * On destroy\n   */\n  ngOnDestroy() {\n    // Unsubscribe from all subscriptions\n    this._unsubscribeAll.next(null);\n    this._unsubscribeAll.complete();\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Public methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * Dismiss the alert\n   */\n  dismiss() {\n    // Return if the alert is already dismissed\n    if (this.dismissed) {\n      return;\n    }\n    // Dismiss the alert\n    this._toggleDismiss(true);\n  }\n  /**\n   * Show the dismissed alert\n   */\n  show() {\n    // Return if the alert is already showing\n    if (!this.dismissed) {\n      return;\n    }\n    // Show the alert\n    this._toggleDismiss(false);\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Private methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * Dismiss/show the alert\n   *\n   * @param dismissed\n   * @private\n   */\n  _toggleDismiss(dismissed) {\n    // Return if the alert is not dismissible\n    if (!this.dismissible) {\n      return;\n    }\n    // Set the dismissed\n    this.dismissed = dismissed;\n    // Execute the observable\n    this.dismissedChanged.next(this.dismissed);\n    // Notify the change detector\n    this._changeDetectorRef.markForCheck();\n  }\n  static #_ = this.ɵfac = function FuseAlertComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FuseAlertComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: FuseAlertComponent,\n    selectors: [[\"fuse-alert\"]],\n    hostVars: 2,\n    hostBindings: function FuseAlertComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.classList);\n      }\n    },\n    inputs: {\n      appearance: \"appearance\",\n      dismissed: \"dismissed\",\n      dismissible: \"dismissible\",\n      name: \"name\",\n      showIcon: \"showIcon\",\n      type: \"type\"\n    },\n    outputs: {\n      dismissedChanged: \"dismissedChanged\"\n    },\n    exportAs: [\"fuseAlert\"],\n    features: [i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c1,\n    decls: 1,\n    vars: 1,\n    consts: [[1, \"fuse-alert-container\"], [1, \"fuse-alert-border\"], [1, \"fuse-alert-icon\"], [1, \"fuse-alert-content\"], [1, \"fuse-alert-title\"], [1, \"fuse-alert-message\"], [\"mat-icon-button\", \"\", 1, \"fuse-alert-dismiss-button\", 3, \"click\"], [3, \"svgIcon\"], [1, \"fuse-alert-custom-icon\"], [1, \"fuse-alert-default-icon\"]],\n    template: function FuseAlertComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c0);\n        i0.ɵɵconditionalCreate(0, FuseAlertComponent_Conditional_0_Template, 10, 5, \"div\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵconditional(!ctx.dismissible || ctx.dismissible && !ctx.dismissed ? 0 : -1);\n      }\n    },\n    dependencies: [MatIconModule, i1.MatIcon, MatButtonModule, i2.MatIconButton],\n    styles: [\"var resource;\\n/******/ (() => { // webpackBootstrap\\n/******/ \\tvar __webpack_modules__ = ({\\n\\n/***/ 254:\\n/*!************************************************************************************!*\\\\\\n  !*** ./apps/eiot-admin/src/@fuse/components/alert/alert.component.scss?ngResource ***!\\n  \\\\************************************************************************************/\\n/***/ (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/postcss-loader/dist/cjs.js):\\\\nError: Cannot find module 'chroma-js'\\\\nRequire stack:\\\\n- D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\apps\\\\\\\\eiot-admin\\\\\\\\src\\\\\\\\@fuse\\\\\\\\tailwind\\\\\\\\utils\\\\\\\\generate-palette.js\\\\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\\\\n    at Function.resolve (node:internal/modules/helpers:193:19)\\\\n    at _resolve (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:246378)\\\\n    at jiti (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:249092)\\\\n    at D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\apps\\\\\\\\eiot-admin\\\\\\\\src\\\\\\\\@fuse\\\\\\\\tailwind\\\\\\\\utils\\\\\\\\generate-palette.js:1:91\\\\n    at evalModule (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:251913)\\\\n    at jiti (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:249841)\\\\n    at D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\apps\\\\\\\\eiot-admin\\\\\\\\tailwind.config.js:4:25\\\\n    at evalModule (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:251913)\\\\n    at jiti (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:249841)\\\");\\n\\n/***/ })\\n\\n/******/ \\t});\\n/************************************************************************/\\n/******/ \\t\\n/******/ \\t// startup\\n/******/ \\t// Load entry module and return exports\\n/******/ \\t// This entry module doesn't tell about it's top-level declarations so it can't be inlined\\n/******/ \\tvar __webpack_exports__ = {};\\n/******/ \\t__webpack_modules__[254]();\\n/******/ \\tresource = __webpack_exports__;\\n/******/ \\t\\n/******/ })()\\n;\"],\n    encapsulation: 2,\n    data: {\n      animation: fuseAnimations\n    },\n    changeDetection: 0\n  });\n}", "map": {"version": 3, "names": ["coerceBooleanProperty", "ChangeDetectorRef", "EventEmitter", "inject", "MatButtonModule", "MatIconModule", "fuseAnimations", "FuseAlertService", "FuseUtilsService", "Subject", "filter", "takeUntil", "i0", "ɵɵelement", "ɵɵproperty", "ɵɵelementStart", "ɵɵprojection", "ɵɵelementEnd", "ɵɵconditionalCreate", "FuseAlertComponent_Conditional_0_Conditional_2_Conditional_4_Template", "FuseAlertComponent_Conditional_0_Conditional_2_Conditional_5_Template", "FuseAlertComponent_Conditional_0_Conditional_2_Conditional_6_Template", "FuseAlertComponent_Conditional_0_Conditional_2_Conditional_7_Template", "FuseAlertComponent_Conditional_0_Conditional_2_Conditional_8_Template", "FuseAlertComponent_Conditional_0_Conditional_2_Conditional_9_Template", "FuseAlertComponent_Conditional_0_Conditional_2_Conditional_10_Template", "FuseAlertComponent_Conditional_0_Conditional_2_Conditional_11_Template", "ɵɵadvance", "ɵɵconditional", "ctx_r1", "type", "FuseAlertComponent_Conditional_0_Conditional_1_Template", "FuseAlertComponent_Conditional_0_Conditional_2_Template", "ɵɵlistener", "FuseAlertComponent_Conditional_0_Template_button_click_8_listener", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "ɵɵresetView", "dismiss", "dismissed", "appearance", "showIcon", "FuseAlertComponent", "constructor", "_changeDetectorRef", "_fuseAlertService", "_fuseUtilsService", "dismissible", "name", "randomId", "dismissed<PERSON><PERSON>ed", "_unsubscribeAll", "classList", "ngOnChanges", "changes", "currentValue", "_toggleDismiss", "ngOnInit", "on<PERSON><PERSON><PERSON>", "pipe", "subscribe", "onShow", "show", "ngOnDestroy", "next", "complete", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_", "_2", "selectors", "hostVars", "hostBindings", "FuseAlertComponent_HostBindings", "rf", "ctx", "ɵɵclassMap", "FuseAlertComponent_Conditional_0_Template", "i1", "MatIcon", "i2", "MatIconButton", "styles", "encapsulation", "data", "animation", "changeDetection"], "sources": ["D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\@fuse\\components\\alert\\alert.component.ts", "D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\@fuse\\components\\alert\\alert.component.html"], "sourcesContent": ["import { BooleanInput, coerceBooleanProperty } from '@angular/cdk/coercion';\n\nimport {\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    EventEmitter,\n    HostBinding,\n    Input,\n    OnChanges,\n    OnDestroy,\n    OnInit,\n    Output,\n    SimpleChanges,\n    ViewEncapsulation,\n    inject,\n} from '@angular/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { fuseAnimations } from '@fuse/animations';\nimport { FuseAlertService } from '@fuse/components/alert/alert.service';\nimport {\n    FuseAlertAppearance,\n    FuseAlertType,\n} from '@fuse/components/alert/alert.types';\nimport { FuseUtilsService } from '@fuse/services/utils/utils.service';\nimport { Subject, filter, takeUntil } from 'rxjs';\n\n@Component({\n    selector: 'fuse-alert',\n    templateUrl: './alert.component.html',\n    styleUrls: ['./alert.component.scss'],\n    encapsulation: ViewEncapsulation.None,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    animations: fuseAnimations,\n    exportAs: 'fuseAlert',\n    imports: [MatIconModule, MatButtonModule],\n})\nexport class FuseAlertComponent implements OnChanges, OnInit, OnDestroy {\n    /* eslint-disable @typescript-eslint/naming-convention */\n    static ngAcceptInputType_dismissible: BooleanInput;\n    static ngAcceptInputType_dismissed: BooleanInput;\n    static ngAcceptInputType_showIcon: BooleanInput;\n    /* eslint-enable @typescript-eslint/naming-convention */\n\n    private _changeDetectorRef = inject(ChangeDetectorRef);\n    private _fuseAlertService = inject(FuseAlertService);\n    private _fuseUtilsService = inject(FuseUtilsService);\n\n    @Input() appearance: FuseAlertAppearance = 'soft';\n    @Input() dismissed: boolean = false;\n    @Input() dismissible: boolean = false;\n    @Input() name: string = this._fuseUtilsService.randomId();\n    @Input() showIcon: boolean = true;\n    @Input() type: FuseAlertType = 'primary';\n    @Output() readonly dismissedChanged: EventEmitter<boolean> =\n        new EventEmitter<boolean>();\n\n    private _unsubscribeAll: Subject<any> = new Subject<any>();\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Accessors\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * Host binding for component classes\n     */\n    @HostBinding('class') get classList(): any {\n        /* eslint-disable @typescript-eslint/naming-convention */\n        return {\n            'fuse-alert-appearance-border': this.appearance === 'border',\n            'fuse-alert-appearance-fill': this.appearance === 'fill',\n            'fuse-alert-appearance-outline': this.appearance === 'outline',\n            'fuse-alert-appearance-soft': this.appearance === 'soft',\n            'fuse-alert-dismissed': this.dismissed,\n            'fuse-alert-dismissible': this.dismissible,\n            'fuse-alert-show-icon': this.showIcon,\n            'fuse-alert-type-primary': this.type === 'primary',\n            'fuse-alert-type-accent': this.type === 'accent',\n            'fuse-alert-type-warn': this.type === 'warn',\n            'fuse-alert-type-basic': this.type === 'basic',\n            'fuse-alert-type-info': this.type === 'info',\n            'fuse-alert-type-success': this.type === 'success',\n            'fuse-alert-type-warning': this.type === 'warning',\n            'fuse-alert-type-error': this.type === 'error',\n        };\n        /* eslint-enable @typescript-eslint/naming-convention */\n    }\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Lifecycle hooks\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * On changes\n     *\n     * @param changes\n     */\n    ngOnChanges(changes: SimpleChanges): void {\n        // Dismissed\n        if ('dismissed' in changes) {\n            // Coerce the value to a boolean\n            this.dismissed = coerceBooleanProperty(\n                changes.dismissed.currentValue\n            );\n\n            // Dismiss/show the alert\n            this._toggleDismiss(this.dismissed);\n        }\n\n        // Dismissible\n        if ('dismissible' in changes) {\n            // Coerce the value to a boolean\n            this.dismissible = coerceBooleanProperty(\n                changes.dismissible.currentValue\n            );\n        }\n\n        // Show icon\n        if ('showIcon' in changes) {\n            // Coerce the value to a boolean\n            this.showIcon = coerceBooleanProperty(\n                changes.showIcon.currentValue\n            );\n        }\n    }\n\n    /**\n     * On init\n     */\n    ngOnInit(): void {\n        // Subscribe to the dismiss calls\n        this._fuseAlertService.onDismiss\n            .pipe(\n                filter((name) => this.name === name),\n                takeUntil(this._unsubscribeAll)\n            )\n            .subscribe(() => {\n                // Dismiss the alert\n                this.dismiss();\n            });\n\n        // Subscribe to the show calls\n        this._fuseAlertService.onShow\n            .pipe(\n                filter((name) => this.name === name),\n                takeUntil(this._unsubscribeAll)\n            )\n            .subscribe(() => {\n                // Show the alert\n                this.show();\n            });\n    }\n\n    /**\n     * On destroy\n     */\n    ngOnDestroy(): void {\n        // Unsubscribe from all subscriptions\n        this._unsubscribeAll.next(null);\n        this._unsubscribeAll.complete();\n    }\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Public methods\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * Dismiss the alert\n     */\n    dismiss(): void {\n        // Return if the alert is already dismissed\n        if (this.dismissed) {\n            return;\n        }\n\n        // Dismiss the alert\n        this._toggleDismiss(true);\n    }\n\n    /**\n     * Show the dismissed alert\n     */\n    show(): void {\n        // Return if the alert is already showing\n        if (!this.dismissed) {\n            return;\n        }\n\n        // Show the alert\n        this._toggleDismiss(false);\n    }\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Private methods\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * Dismiss/show the alert\n     *\n     * @param dismissed\n     * @private\n     */\n    private _toggleDismiss(dismissed: boolean): void {\n        // Return if the alert is not dismissible\n        if (!this.dismissible) {\n            return;\n        }\n\n        // Set the dismissed\n        this.dismissed = dismissed;\n\n        // Execute the observable\n        this.dismissedChanged.next(this.dismissed);\n\n        // Notify the change detector\n        this._changeDetectorRef.markForCheck();\n    }\n}\n", "@if (!dismissible || (dismissible && !dismissed)) {\n    <div\n        class=\"fuse-alert-container\"\n        [@fadeIn]=\"!dismissed\"\n        [@fadeOut]=\"!dismissed\"\n    >\n        <!-- Border -->\n        @if (appearance === 'border') {\n            <div class=\"fuse-alert-border\"></div>\n        }\n\n        <!-- Icon -->\n        @if (showIcon) {\n            <div class=\"fuse-alert-icon\">\n                <!-- Custom icon -->\n                <div class=\"fuse-alert-custom-icon\">\n                    <ng-content select=\"[fuseAlertIcon]\"></ng-content>\n                </div>\n\n                <!-- Default icons -->\n                <div class=\"fuse-alert-default-icon\">\n                    @if (type === 'primary') {\n                        <mat-icon\n                            [svgIcon]=\"'heroicons_solid:check-circle'\"\n                        ></mat-icon>\n                    }\n\n                    @if (type === 'accent') {\n                        <mat-icon\n                            [svgIcon]=\"'heroicons_solid:check-circle'\"\n                        ></mat-icon>\n                    }\n\n                    @if (type === 'warn') {\n                        <mat-icon\n                            [svgIcon]=\"'heroicons_solid:x-circle'\"\n                        ></mat-icon>\n                    }\n\n                    @if (type === 'basic') {\n                        <mat-icon\n                            [svgIcon]=\"'heroicons_solid:check-circle'\"\n                        ></mat-icon>\n                    }\n\n                    @if (type === 'info') {\n                        <mat-icon\n                            [svgIcon]=\"'heroicons_solid:information-circle'\"\n                        ></mat-icon>\n                    }\n\n                    @if (type === 'success') {\n                        <mat-icon\n                            [svgIcon]=\"'heroicons_solid:check-circle'\"\n                        ></mat-icon>\n                    }\n\n                    @if (type === 'warning') {\n                        <mat-icon\n                            [svgIcon]=\"'heroicons_solid:exclamation-triangle'\"\n                        ></mat-icon>\n                    }\n\n                    @if (type === 'error') {\n                        <mat-icon\n                            [svgIcon]=\"'heroicons_solid:x-circle'\"\n                        ></mat-icon>\n                    }\n                </div>\n            </div>\n        }\n\n        <!-- Content -->\n        <div class=\"fuse-alert-content\">\n            <div class=\"fuse-alert-title\">\n                <ng-content select=\"[fuseAlertTitle]\"></ng-content>\n            </div>\n\n            <div class=\"fuse-alert-message\">\n                <ng-content></ng-content>\n            </div>\n        </div>\n\n        <!-- Dismiss button -->\n        <button\n            class=\"fuse-alert-dismiss-button\"\n            mat-icon-button\n            (click)=\"dismiss()\"\n        >\n            <mat-icon [svgIcon]=\"'heroicons_solid:x-mark'\"></mat-icon>\n        </button>\n    </div>\n}\n"], "mappings": "AAAA,SAAuBA,qBAAqB,QAAQ,uBAAuB;AAE3E,SAEIC,iBAAiB,EAEjBC,YAAY,EASZC,MAAM,QACH,eAAe;AACtB,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,gBAAgB,QAAQ,sCAAsC;AAKvE,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,OAAO,EAAEC,MAAM,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;IClBrCC,EAAA,CAAAC,SAAA,aAAqC;;;;;IAczBD,EAAA,CAAAC,SAAA,kBAEY;;;IADRD,EAAA,CAAAE,UAAA,2CAA0C;;;;;IAK9CF,EAAA,CAAAC,SAAA,kBAEY;;;IADRD,EAAA,CAAAE,UAAA,2CAA0C;;;;;IAK9CF,EAAA,CAAAC,SAAA,kBAEY;;;IADRD,EAAA,CAAAE,UAAA,uCAAsC;;;;;IAK1CF,EAAA,CAAAC,SAAA,kBAEY;;;IADRD,EAAA,CAAAE,UAAA,2CAA0C;;;;;IAK9CF,EAAA,CAAAC,SAAA,kBAEY;;;IADRD,EAAA,CAAAE,UAAA,iDAAgD;;;;;IAKpDF,EAAA,CAAAC,SAAA,kBAEY;;;IADRD,EAAA,CAAAE,UAAA,2CAA0C;;;;;IAK9CF,EAAA,CAAAC,SAAA,kBAEY;;;IADRD,EAAA,CAAAE,UAAA,mDAAkD;;;;;IAKtDF,EAAA,CAAAC,SAAA,kBAEY;;;IADRD,EAAA,CAAAE,UAAA,uCAAsC;;;;;IAlDlDF,EAFJ,CAAAG,cAAA,aAA6B,aAEW;IAChCH,EAAA,CAAAI,YAAA,MAAkD;IACtDJ,EAAA,CAAAK,YAAA,EAAM;IAGNL,EAAA,CAAAG,cAAA,aAAqC;IACjCH,EAAA,CAAAM,mBAAA,IAAAC,qEAAA,sBAA0B;IAM1BP,EAAA,CAAAM,mBAAA,IAAAE,qEAAA,sBAAyB;IAMzBR,EAAA,CAAAM,mBAAA,IAAAG,qEAAA,sBAAuB;IAMvBT,EAAA,CAAAM,mBAAA,IAAAI,qEAAA,sBAAwB;IAMxBV,EAAA,CAAAM,mBAAA,IAAAK,qEAAA,sBAAuB;IAMvBX,EAAA,CAAAM,mBAAA,IAAAM,qEAAA,sBAA0B;IAM1BZ,EAAA,CAAAM,mBAAA,KAAAO,sEAAA,sBAA0B;IAM1Bb,EAAA,CAAAM,mBAAA,KAAAQ,sEAAA,sBAAwB;IAMhCd,EADI,CAAAK,YAAA,EAAM,EACJ;;;;IAhDEL,EAAA,CAAAe,SAAA,GAIC;IAJDf,EAAA,CAAAgB,aAAA,CAAAC,MAAA,CAAAC,IAAA,wBAIC;IAEDlB,EAAA,CAAAe,SAAA,EAIC;IAJDf,EAAA,CAAAgB,aAAA,CAAAC,MAAA,CAAAC,IAAA,uBAIC;IAEDlB,EAAA,CAAAe,SAAA,EAIC;IAJDf,EAAA,CAAAgB,aAAA,CAAAC,MAAA,CAAAC,IAAA,qBAIC;IAEDlB,EAAA,CAAAe,SAAA,EAIC;IAJDf,EAAA,CAAAgB,aAAA,CAAAC,MAAA,CAAAC,IAAA,sBAIC;IAEDlB,EAAA,CAAAe,SAAA,EAIC;IAJDf,EAAA,CAAAgB,aAAA,CAAAC,MAAA,CAAAC,IAAA,qBAIC;IAEDlB,EAAA,CAAAe,SAAA,EAIC;IAJDf,EAAA,CAAAgB,aAAA,CAAAC,MAAA,CAAAC,IAAA,wBAIC;IAEDlB,EAAA,CAAAe,SAAA,EAIC;IAJDf,EAAA,CAAAgB,aAAA,CAAAC,MAAA,CAAAC,IAAA,yBAIC;IAEDlB,EAAA,CAAAe,SAAA,EAIC;IAJDf,EAAA,CAAAgB,aAAA,CAAAC,MAAA,CAAAC,IAAA,uBAIC;;;;;;IAlEjBlB,EAAA,CAAAG,cAAA,aAIC;IAEGH,EAAA,CAAAM,mBAAA,IAAAa,uDAAA,iBAA+B;IAK/BnB,EAAA,CAAAM,mBAAA,IAAAc,uDAAA,kBAAgB;IA8DZpB,EADJ,CAAAG,cAAA,aAAgC,aACE;IAC1BH,EAAA,CAAAI,YAAA,GAAmD;IACvDJ,EAAA,CAAAK,YAAA,EAAM;IAENL,EAAA,CAAAG,cAAA,aAAgC;IAC5BH,EAAA,CAAAI,YAAA,MAAyB;IAEjCJ,EADI,CAAAK,YAAA,EAAM,EACJ;IAGNL,EAAA,CAAAG,cAAA,gBAIC;IADGH,EAAA,CAAAqB,UAAA,mBAAAC,kEAAA;MAAAtB,EAAA,CAAAuB,aAAA,CAAAC,GAAA;MAAA,MAAAP,MAAA,GAAAjB,EAAA,CAAAyB,aAAA;MAAA,OAAAzB,EAAA,CAAA0B,WAAA,CAAST,MAAA,CAAAU,OAAA,EAAS;IAAA,EAAC;IAEnB3B,EAAA,CAAAC,SAAA,kBAA0D;IAElED,EADI,CAAAK,YAAA,EAAS,EACP;;;;IAvFFL,EADA,CAAAE,UAAA,aAAAe,MAAA,CAAAW,SAAA,CAAsB,cAAAX,MAAA,CAAAW,SAAA,CACC;IAGvB5B,EAAA,CAAAe,SAAA,EAEC;IAFDf,EAAA,CAAAgB,aAAA,CAAAC,MAAA,CAAAY,UAAA,uBAEC;IAGD7B,EAAA,CAAAe,SAAA,EA0DC;IA1DDf,EAAA,CAAAgB,aAAA,CAAAC,MAAA,CAAAa,QAAA,UA0DC;IAmBa9B,EAAA,CAAAe,SAAA,GAAoC;IAApCf,EAAA,CAAAE,UAAA,qCAAoC;;;ADnD1D,OAAM,MAAO6B,kBAAkB;EAV/BC,YAAA;IAeI;IAEQ,KAAAC,kBAAkB,GAAG1C,MAAM,CAACF,iBAAiB,CAAC;IAC9C,KAAA6C,iBAAiB,GAAG3C,MAAM,CAACI,gBAAgB,CAAC;IAC5C,KAAAwC,iBAAiB,GAAG5C,MAAM,CAACK,gBAAgB,CAAC;IAE3C,KAAAiC,UAAU,GAAwB,MAAM;IACxC,KAAAD,SAAS,GAAY,KAAK;IAC1B,KAAAQ,WAAW,GAAY,KAAK;IAC5B,KAAAC,IAAI,GAAW,IAAI,CAACF,iBAAiB,CAACG,QAAQ,EAAE;IAChD,KAAAR,QAAQ,GAAY,IAAI;IACxB,KAAAZ,IAAI,GAAkB,SAAS;IACrB,KAAAqB,gBAAgB,GAC/B,IAAIjD,YAAY,EAAW;IAEvB,KAAAkD,eAAe,GAAiB,IAAI3C,OAAO,EAAO;;EAE1D;EACA;EACA;EAEA;;;EAGA,IAA0B4C,SAASA,CAAA;IAC/B;IACA,OAAO;MACH,8BAA8B,EAAE,IAAI,CAACZ,UAAU,KAAK,QAAQ;MAC5D,4BAA4B,EAAE,IAAI,CAACA,UAAU,KAAK,MAAM;MACxD,+BAA+B,EAAE,IAAI,CAACA,UAAU,KAAK,SAAS;MAC9D,4BAA4B,EAAE,IAAI,CAACA,UAAU,KAAK,MAAM;MACxD,sBAAsB,EAAE,IAAI,CAACD,SAAS;MACtC,wBAAwB,EAAE,IAAI,CAACQ,WAAW;MAC1C,sBAAsB,EAAE,IAAI,CAACN,QAAQ;MACrC,yBAAyB,EAAE,IAAI,CAACZ,IAAI,KAAK,SAAS;MAClD,wBAAwB,EAAE,IAAI,CAACA,IAAI,KAAK,QAAQ;MAChD,sBAAsB,EAAE,IAAI,CAACA,IAAI,KAAK,MAAM;MAC5C,uBAAuB,EAAE,IAAI,CAACA,IAAI,KAAK,OAAO;MAC9C,sBAAsB,EAAE,IAAI,CAACA,IAAI,KAAK,MAAM;MAC5C,yBAAyB,EAAE,IAAI,CAACA,IAAI,KAAK,SAAS;MAClD,yBAAyB,EAAE,IAAI,CAACA,IAAI,KAAK,SAAS;MAClD,uBAAuB,EAAE,IAAI,CAACA,IAAI,KAAK;KAC1C;IACD;EACJ;EAEA;EACA;EACA;EAEA;;;;;EAKAwB,WAAWA,CAACC,OAAsB;IAC9B;IACA,IAAI,WAAW,IAAIA,OAAO,EAAE;MACxB;MACA,IAAI,CAACf,SAAS,GAAGxC,qBAAqB,CAClCuD,OAAO,CAACf,SAAS,CAACgB,YAAY,CACjC;MAED;MACA,IAAI,CAACC,cAAc,CAAC,IAAI,CAACjB,SAAS,CAAC;IACvC;IAEA;IACA,IAAI,aAAa,IAAIe,OAAO,EAAE;MAC1B;MACA,IAAI,CAACP,WAAW,GAAGhD,qBAAqB,CACpCuD,OAAO,CAACP,WAAW,CAACQ,YAAY,CACnC;IACL;IAEA;IACA,IAAI,UAAU,IAAID,OAAO,EAAE;MACvB;MACA,IAAI,CAACb,QAAQ,GAAG1C,qBAAqB,CACjCuD,OAAO,CAACb,QAAQ,CAACc,YAAY,CAChC;IACL;EACJ;EAEA;;;EAGAE,QAAQA,CAAA;IACJ;IACA,IAAI,CAACZ,iBAAiB,CAACa,SAAS,CAC3BC,IAAI,CACDlD,MAAM,CAAEuC,IAAI,IAAK,IAAI,CAACA,IAAI,KAAKA,IAAI,CAAC,EACpCtC,SAAS,CAAC,IAAI,CAACyC,eAAe,CAAC,CAClC,CACAS,SAAS,CAAC,MAAK;MACZ;MACA,IAAI,CAACtB,OAAO,EAAE;IAClB,CAAC,CAAC;IAEN;IACA,IAAI,CAACO,iBAAiB,CAACgB,MAAM,CACxBF,IAAI,CACDlD,MAAM,CAAEuC,IAAI,IAAK,IAAI,CAACA,IAAI,KAAKA,IAAI,CAAC,EACpCtC,SAAS,CAAC,IAAI,CAACyC,eAAe,CAAC,CAClC,CACAS,SAAS,CAAC,MAAK;MACZ;MACA,IAAI,CAACE,IAAI,EAAE;IACf,CAAC,CAAC;EACV;EAEA;;;EAGAC,WAAWA,CAAA;IACP;IACA,IAAI,CAACZ,eAAe,CAACa,IAAI,CAAC,IAAI,CAAC;IAC/B,IAAI,CAACb,eAAe,CAACc,QAAQ,EAAE;EACnC;EAEA;EACA;EACA;EAEA;;;EAGA3B,OAAOA,CAAA;IACH;IACA,IAAI,IAAI,CAACC,SAAS,EAAE;MAChB;IACJ;IAEA;IACA,IAAI,CAACiB,cAAc,CAAC,IAAI,CAAC;EAC7B;EAEA;;;EAGAM,IAAIA,CAAA;IACA;IACA,IAAI,CAAC,IAAI,CAACvB,SAAS,EAAE;MACjB;IACJ;IAEA;IACA,IAAI,CAACiB,cAAc,CAAC,KAAK,CAAC;EAC9B;EAEA;EACA;EACA;EAEA;;;;;;EAMQA,cAAcA,CAACjB,SAAkB;IACrC;IACA,IAAI,CAAC,IAAI,CAACQ,WAAW,EAAE;MACnB;IACJ;IAEA;IACA,IAAI,CAACR,SAAS,GAAGA,SAAS;IAE1B;IACA,IAAI,CAACW,gBAAgB,CAACc,IAAI,CAAC,IAAI,CAACzB,SAAS,CAAC;IAE1C;IACA,IAAI,CAACK,kBAAkB,CAACsB,YAAY,EAAE;EAC1C;EAAC,QAAAC,CAAA,G;qCAnLQzB,kBAAkB;EAAA;EAAA,QAAA0B,EAAA,G;UAAlB1B,kBAAkB;IAAA2B,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAlB9D,EAAA,CAAAgE,UAAA,CAAAD,GAAA,CAAAtB,SAAA,CAAkB;;;;;;;;;;;;;;;;;;;;;;;QCtC/BzC,EAAA,CAAAM,mBAAA,IAAA2D,yCAAA,kBAAmD;;;QAAnDjE,EAAA,CAAAgB,aAAA,EAAA+C,GAAA,CAAA3B,WAAA,IAAA2B,GAAA,CAAA3B,WAAA,KAAA2B,GAAA,CAAAnC,SAAA,UA4FC;;;mBDxDanC,aAAa,EAAAyE,EAAA,CAAAC,OAAA,EAAE3E,eAAe,EAAA4E,EAAA,CAAAC,aAAA;IAAAC,MAAA;IAAAC,aAAA;IAAAC,IAAA;MAAAC,SAAA,EAF5B/E;IAAc;IAAAgF,eAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}