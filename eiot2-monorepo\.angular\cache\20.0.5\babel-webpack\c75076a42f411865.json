{"ast": null, "code": "import { NgTemplateOutlet } from '@angular/common';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { take } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@fuse/components/navigation\";\nimport * as i2 from \"@jsverse/transloco\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/menu\";\nconst _c0 = a0 => ({\n  $implicit: a0\n});\nfunction LanguagesComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction LanguagesComponent_For_5_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction LanguagesComponent_For_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 6);\n    i0.ɵɵlistener(\"click\", function LanguagesComponent_For_5_Template_button_click_0_listener() {\n      const lang_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.setActiveLang(ctx_r2.getLangId(lang_r2)));\n    });\n    i0.ɵɵelementStart(1, \"span\", 7);\n    i0.ɵɵtemplate(2, LanguagesComponent_For_5_ng_container_2_Template, 1, 0, \"ng-container\", 3);\n    i0.ɵɵelementStart(3, \"span\", 8);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const lang_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    const flagImage_r4 = i0.ɵɵreference(7);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", flagImage_r4)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(3, _c0, ctx_r2.getLangId(lang_r2)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getLangLabel(lang_r2));\n  }\n}\nfunction LanguagesComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 9);\n    i0.ɵɵelement(1, \"span\", 10)(2, \"img\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const lang_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", \"images/flags/\" + ctx_r2.flagCodes[lang_r5].toUpperCase() + \".svg\", i0.ɵɵsanitizeUrl)(\"alt\", \"Flag image for \" + lang_r5);\n  }\n}\nexport class LanguagesComponent {\n  /**\n   * Constructor\n   */\n  constructor(_changeDetectorRef, _fuseNavigationService, _translocoService) {\n    this._changeDetectorRef = _changeDetectorRef;\n    this._fuseNavigationService = _fuseNavigationService;\n    this._translocoService = _translocoService;\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Lifecycle hooks\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * On init\n   */\n  ngOnInit() {\n    // Get the available languages from transloco\n    this.availableLangs = this._translocoService.getAvailableLangs();\n    // Subscribe to language changes\n    this._translocoService.langChanges$.subscribe(activeLang => {\n      // Get the active lang\n      this.activeLang = activeLang;\n      // Update the navigation\n      this._updateNavigation(activeLang);\n    });\n    // Set the country iso codes for languages for flags\n    this.flagCodes = {\n      en: 'us',\n      tr: 'tr'\n    };\n  }\n  /**\n   * On destroy\n   */\n  ngOnDestroy() {}\n  // -----------------------------------------------------------------------------------------------------\n  // @ Public methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * Set the active lang\n   *\n   * @param lang\n   */\n  setActiveLang(lang) {\n    // Set the active lang\n    this._translocoService.setActiveLang(lang);\n  }\n  /**\n   * Track by function for ngFor loops\n   *\n   * @param index\n   * @param item\n   */\n  trackByFn(index, item) {\n    return item.id || index;\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Private methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * Update the navigation\n   *\n   * @param lang\n   * @private\n   */\n  _updateNavigation(lang) {\n    // For the demonstration purposes, we will only update the Dashboard names\n    // from the navigation but you can do a full swap and change the entire\n    // navigation data.\n    //\n    // You can import the data from a file or request it from your backend,\n    // it's up to you.\n    // Get the component -> navigation data -> item\n    const navComponent = this._fuseNavigationService.getComponent('mainNavigation');\n    // Return if the navigation component does not exist\n    if (!navComponent) {\n      return null;\n    }\n    // Get the flat navigation data\n    const navigation = navComponent.navigation;\n    // Get the Project dashboard item and update its title\n    const projectDashboardItem = this._fuseNavigationService.getItem('dashboards.project', navigation);\n    if (projectDashboardItem) {\n      this._translocoService.selectTranslate('Project').pipe(take(1)).subscribe(translation => {\n        // Set the title\n        projectDashboardItem.title = translation;\n        // Refresh the navigation component\n        navComponent.refresh();\n      });\n    }\n    // Get the Analytics dashboard item and update its title\n    const analyticsDashboardItem = this._fuseNavigationService.getItem('dashboards.analytics', navigation);\n    if (analyticsDashboardItem) {\n      this._translocoService.selectTranslate('Analytics').pipe(take(1)).subscribe(translation => {\n        // Set the title\n        analyticsDashboardItem.title = translation;\n        // Refresh the navigation component\n        navComponent.refresh();\n      });\n    }\n  }\n  // Helper methods for template\n  getLangId(lang) {\n    return typeof lang === 'string' ? lang : lang.id;\n  }\n  getLangLabel(lang) {\n    return typeof lang === 'string' ? lang : lang.label;\n  }\n  static #_ = this.ɵfac = function LanguagesComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || LanguagesComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.FuseNavigationService), i0.ɵɵdirectiveInject(i2.TranslocoService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LanguagesComponent,\n    selectors: [[\"languages\"]],\n    exportAs: [\"languages\"],\n    decls: 8,\n    vars: 6,\n    consts: [[\"languages\", \"matMenu\"], [\"flagImage\", \"\"], [\"mat-icon-button\", \"\", 3, \"matMenuTriggerFor\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"xPosition\"], [\"mat-menu-item\", \"\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"flex\", \"items-center\"], [1, \"ml-3\"], [1, \"relative\", \"w-6\", \"overflow-hidden\", \"rounded-sm\", \"shadow\"], [1, \"absolute\", \"inset-0\", \"ring-1\", \"ring-inset\", \"ring-black\", \"ring-opacity-10\"], [1, \"w-full\", 3, \"src\", \"alt\"]],\n    template: function LanguagesComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"button\", 2);\n        i0.ɵɵtemplate(1, LanguagesComponent_ng_container_1_Template, 1, 0, \"ng-container\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(2, \"mat-menu\", 4, 0);\n        i0.ɵɵrepeaterCreate(4, LanguagesComponent_For_5_Template, 5, 5, \"button\", 5, ctx.trackByFn, true);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(6, LanguagesComponent_ng_template_6_Template, 3, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        const languages_r6 = i0.ɵɵreference(3);\n        const flagImage_r4 = i0.ɵɵreference(7);\n        i0.ɵɵproperty(\"matMenuTriggerFor\", languages_r6);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngTemplateOutlet\", flagImage_r4)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c0, ctx.activeLang));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"xPosition\", \"before\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵrepeater(ctx.availableLangs);\n      }\n    },\n    dependencies: [MatButtonModule, i3.MatIconButton, MatMenuModule, i4.MatMenu, i4.MatMenuItem, i4.MatMenuTrigger, NgTemplateOutlet],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}", "map": {"version": 3, "names": ["NgTemplateOutlet", "MatButtonModule", "MatMenuModule", "take", "i0", "ɵɵelementContainer", "ɵɵelementStart", "ɵɵlistener", "LanguagesComponent_For_5_Template_button_click_0_listener", "lang_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "setActiveLang", "getLangId", "ɵɵtemplate", "LanguagesComponent_For_5_ng_container_2_Template", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "flagImage_r4", "ɵɵpureFunction1", "_c0", "ɵɵtextInterpolate", "getLangLabel", "ɵɵelement", "flagCodes", "lang_r5", "toUpperCase", "ɵɵsanitizeUrl", "LanguagesComponent", "constructor", "_changeDetectorRef", "_fuseNavigationService", "_translocoService", "ngOnInit", "availableLangs", "getAvailableLangs", "langChanges$", "subscribe", "activeLang", "_updateNavigation", "en", "tr", "ngOnDestroy", "lang", "trackByFn", "index", "item", "id", "navComponent", "getComponent", "navigation", "projectDashboardItem", "getItem", "selectTranslate", "pipe", "translation", "title", "refresh", "analyticsDashboardItem", "label", "_", "ɵɵdirectiveInject", "ChangeDetectorRef", "i1", "FuseNavigationService", "i2", "TranslocoService", "_2", "selectors", "exportAs", "decls", "vars", "consts", "template", "LanguagesComponent_Template", "rf", "ctx", "LanguagesComponent_ng_container_1_Template", "ɵɵrepeaterCreate", "LanguagesComponent_For_5_Template", "LanguagesComponent_ng_template_6_Template", "ɵɵtemplateRefExtractor", "languages_r6", "ɵɵrepeater", "i3", "MatIconButton", "i4", "MatMenu", "MatMenuItem", "MatMenuTrigger", "encapsulation", "changeDetection"], "sources": ["D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\app\\layout\\common\\languages\\languages.component.ts", "D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\app\\layout\\common\\languages\\languages.component.html"], "sourcesContent": ["import { NgTemplateOutlet } from '@angular/common';\nimport {\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    OnDestroy,\n    OnInit,\n    ViewEncapsulation,\n} from '@angular/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatMenuModule } from '@angular/material/menu';\nimport {\n    FuseNavigationService,\n    FuseVerticalNavigationComponent,\n} from '@fuse/components/navigation';\nimport { AvailableLangs, TranslocoService, LangDefinition } from '@jsverse/transloco';\nimport { take } from 'rxjs';\n\n@Component({\n    selector: 'languages',\n    templateUrl: './languages.component.html',\n    encapsulation: ViewEncapsulation.None,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    exportAs: 'languages',\n    standalone: true,\n    imports: [MatButtonModule, MatMenuModule, NgTemplateOutlet],\n})\nexport class LanguagesComponent implements OnInit, OnDestroy {\n    availableLangs: AvailableLangs;\n    activeLang: string;\n    flagCodes: any;\n\n    /**\n     * Constructor\n     */\n    constructor(\n        private _changeDetectorRef: ChangeDetectorRef,\n        private _fuseNavigationService: FuseNavigationService,\n        private _translocoService: TranslocoService\n    ) {}\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Lifecycle hooks\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * On init\n     */\n    ngOnInit(): void {\n        // Get the available languages from transloco\n        this.availableLangs = this._translocoService.getAvailableLangs();\n\n        // Subscribe to language changes\n        this._translocoService.langChanges$.subscribe((activeLang) => {\n            // Get the active lang\n            this.activeLang = activeLang;\n\n            // Update the navigation\n            this._updateNavigation(activeLang);\n        });\n\n        // Set the country iso codes for languages for flags\n        this.flagCodes = {\n            en: 'us',\n            tr: 'tr',\n        };\n    }\n\n    /**\n     * On destroy\n     */\n    ngOnDestroy(): void {}\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Public methods\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * Set the active lang\n     *\n     * @param lang\n     */\n    setActiveLang(lang: string): void {\n        // Set the active lang\n        this._translocoService.setActiveLang(lang);\n    }\n\n    /**\n     * Track by function for ngFor loops\n     *\n     * @param index\n     * @param item\n     */\n    trackByFn(index: number, item: any): any {\n        return item.id || index;\n    }\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Private methods\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * Update the navigation\n     *\n     * @param lang\n     * @private\n     */\n    private _updateNavigation(lang: string): void {\n        // For the demonstration purposes, we will only update the Dashboard names\n        // from the navigation but you can do a full swap and change the entire\n        // navigation data.\n        //\n        // You can import the data from a file or request it from your backend,\n        // it's up to you.\n\n        // Get the component -> navigation data -> item\n        const navComponent =\n            this._fuseNavigationService.getComponent<FuseVerticalNavigationComponent>(\n                'mainNavigation'\n            );\n\n        // Return if the navigation component does not exist\n        if (!navComponent) {\n            return null;\n        }\n\n        // Get the flat navigation data\n        const navigation = navComponent.navigation;\n\n        // Get the Project dashboard item and update its title\n        const projectDashboardItem = this._fuseNavigationService.getItem(\n            'dashboards.project',\n            navigation\n        );\n        if (projectDashboardItem) {\n            this._translocoService\n                .selectTranslate('Project')\n                .pipe(take(1))\n                .subscribe((translation) => {\n                    // Set the title\n                    projectDashboardItem.title = translation;\n\n                    // Refresh the navigation component\n                    navComponent.refresh();\n                });\n        }\n\n        // Get the Analytics dashboard item and update its title\n        const analyticsDashboardItem = this._fuseNavigationService.getItem(\n            'dashboards.analytics',\n            navigation\n        );\n        if (analyticsDashboardItem) {\n            this._translocoService\n                .selectTranslate('Analytics')\n                .pipe(take(1))\n                .subscribe((translation) => {\n                    // Set the title\n                    analyticsDashboardItem.title = translation;\n\n                    // Refresh the navigation component\n                    navComponent.refresh();\n                });\n        }\n    }\n\n    // Helper methods for template\n    getLangId(lang: string | LangDefinition): string {\n        return typeof lang === 'string' ? lang : lang.id;\n    }\n\n    getLangLabel(lang: string | LangDefinition): string {\n        return typeof lang === 'string' ? lang : lang.label;\n    }\n}\n", "<!-- Button -->\n<button mat-icon-button [matMenuTriggerFor]=\"languages\">\n    <ng-container\n        *ngTemplateOutlet=\"flagImage; context: { $implicit: activeLang }\"\n    ></ng-container>\n</button>\n\n<!-- Language menu -->\n<mat-menu [xPosition]=\"'before'\" #languages=\"matMenu\">\n    @for (lang of availableLangs; track trackByFn($index, lang)) {\n        <button mat-menu-item (click)=\"setActiveLang(getLangId(lang))\">\n            <span class=\"flex items-center\">\n                <ng-container\n                    *ngTemplateOutlet=\"\n                        flagImage;\n                        context: { $implicit: getLangId(lang) }\n                    \"\n                ></ng-container>\n                <span class=\"ml-3\">{{ getLangLabel(lang) }}</span>\n            </span>\n        </button>\n    }\n</mat-menu>\n\n<!-- Flag image template -->\n<ng-template let-lang #flagImage>\n    <span class=\"relative w-6 overflow-hidden rounded-sm shadow\">\n        <span\n            class=\"absolute inset-0 ring-1 ring-inset ring-black ring-opacity-10\"\n        ></span>\n        <img\n            class=\"w-full\"\n            [src]=\"'images/flags/' + flagCodes[lang].toUpperCase() + '.svg'\"\n            [alt]=\"'Flag image for ' + lang\"\n        />\n    </span>\n</ng-template>\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,iBAAiB;AASlD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AAMtD,SAASC,IAAI,QAAQ,MAAM;;;;;;;;;;;ICdvBC,EAAA,CAAAC,kBAAA,GAEgB;;;;;IAQJD,EAAA,CAAAC,kBAAA,GAKgB;;;;;;IAPxBD,EAAA,CAAAE,cAAA,gBAA+D;IAAzCF,EAAA,CAAAG,UAAA,mBAAAC,0DAAA;MAAA,MAAAC,OAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,aAAA,CAAcH,MAAA,CAAAI,SAAA,CAAAR,OAAA,CAAe,CAAC;IAAA,EAAC;IAC1DL,EAAA,CAAAE,cAAA,cAAgC;IAC5BF,EAAA,CAAAc,UAAA,IAAAC,gDAAA,0BAKC;IACDf,EAAA,CAAAE,cAAA,cAAmB;IAAAF,EAAA,CAAAgB,MAAA,GAAwB;IAEnDhB,EAFmD,CAAAiB,YAAA,EAAO,EAC/C,EACF;;;;;;IAPIjB,EAAA,CAAAkB,SAAA,GAE4B;IAAAlB,EAF5B,CAAAmB,UAAA,qBAAAC,YAAA,CAE4B,4BAAApB,EAAA,CAAAqB,eAAA,IAAAC,GAAA,EAAAb,MAAA,CAAAI,SAAA,CAAAR,OAAA,GAE/C;IACiCL,EAAA,CAAAkB,SAAA,GAAwB;IAAxBlB,EAAA,CAAAuB,iBAAA,CAAAd,MAAA,CAAAe,YAAA,CAAAnB,OAAA,EAAwB;;;;;IAQvDL,EAAA,CAAAE,cAAA,cAA6D;IAIzDF,EAHA,CAAAyB,SAAA,eAEQ,cAKN;IACNzB,EAAA,CAAAiB,YAAA,EAAO;;;;;IAHCjB,EAAA,CAAAkB,SAAA,GAAgE;IAChElB,EADA,CAAAmB,UAAA,0BAAAV,MAAA,CAAAiB,SAAA,CAAAC,OAAA,EAAAC,WAAA,aAAA5B,EAAA,CAAA6B,aAAA,CAAgE,4BAAAF,OAAA,CAChC;;;ADN5C,OAAM,MAAOG,kBAAkB;EAK3B;;;EAGAC,YACYC,kBAAqC,EACrCC,sBAA6C,EAC7CC,iBAAmC;IAFnC,KAAAF,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACtB,KAAAC,iBAAiB,GAAjBA,iBAAiB;EAC1B;EAEH;EACA;EACA;EAEA;;;EAGAC,QAAQA,CAAA;IACJ;IACA,IAAI,CAACC,cAAc,GAAG,IAAI,CAACF,iBAAiB,CAACG,iBAAiB,EAAE;IAEhE;IACA,IAAI,CAACH,iBAAiB,CAACI,YAAY,CAACC,SAAS,CAAEC,UAAU,IAAI;MACzD;MACA,IAAI,CAACA,UAAU,GAAGA,UAAU;MAE5B;MACA,IAAI,CAACC,iBAAiB,CAACD,UAAU,CAAC;IACtC,CAAC,CAAC;IAEF;IACA,IAAI,CAACd,SAAS,GAAG;MACbgB,EAAE,EAAE,IAAI;MACRC,EAAE,EAAE;KACP;EACL;EAEA;;;EAGAC,WAAWA,CAAA,GAAU;EAErB;EACA;EACA;EAEA;;;;;EAKAhC,aAAaA,CAACiC,IAAY;IACtB;IACA,IAAI,CAACX,iBAAiB,CAACtB,aAAa,CAACiC,IAAI,CAAC;EAC9C;EAEA;;;;;;EAMAC,SAASA,CAACC,KAAa,EAAEC,IAAS;IAC9B,OAAOA,IAAI,CAACC,EAAE,IAAIF,KAAK;EAC3B;EAEA;EACA;EACA;EAEA;;;;;;EAMQN,iBAAiBA,CAACI,IAAY;IAClC;IACA;IACA;IACA;IACA;IACA;IAEA;IACA,MAAMK,YAAY,GACd,IAAI,CAACjB,sBAAsB,CAACkB,YAAY,CACpC,gBAAgB,CACnB;IAEL;IACA,IAAI,CAACD,YAAY,EAAE;MACf,OAAO,IAAI;IACf;IAEA;IACA,MAAME,UAAU,GAAGF,YAAY,CAACE,UAAU;IAE1C;IACA,MAAMC,oBAAoB,GAAG,IAAI,CAACpB,sBAAsB,CAACqB,OAAO,CAC5D,oBAAoB,EACpBF,UAAU,CACb;IACD,IAAIC,oBAAoB,EAAE;MACtB,IAAI,CAACnB,iBAAiB,CACjBqB,eAAe,CAAC,SAAS,CAAC,CAC1BC,IAAI,CAACzD,IAAI,CAAC,CAAC,CAAC,CAAC,CACbwC,SAAS,CAAEkB,WAAW,IAAI;QACvB;QACAJ,oBAAoB,CAACK,KAAK,GAAGD,WAAW;QAExC;QACAP,YAAY,CAACS,OAAO,EAAE;MAC1B,CAAC,CAAC;IACV;IAEA;IACA,MAAMC,sBAAsB,GAAG,IAAI,CAAC3B,sBAAsB,CAACqB,OAAO,CAC9D,sBAAsB,EACtBF,UAAU,CACb;IACD,IAAIQ,sBAAsB,EAAE;MACxB,IAAI,CAAC1B,iBAAiB,CACjBqB,eAAe,CAAC,WAAW,CAAC,CAC5BC,IAAI,CAACzD,IAAI,CAAC,CAAC,CAAC,CAAC,CACbwC,SAAS,CAAEkB,WAAW,IAAI;QACvB;QACAG,sBAAsB,CAACF,KAAK,GAAGD,WAAW;QAE1C;QACAP,YAAY,CAACS,OAAO,EAAE;MAC1B,CAAC,CAAC;IACV;EACJ;EAEA;EACA9C,SAASA,CAACgC,IAA6B;IACnC,OAAO,OAAOA,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAGA,IAAI,CAACI,EAAE;EACpD;EAEAzB,YAAYA,CAACqB,IAA6B;IACtC,OAAO,OAAOA,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAGA,IAAI,CAACgB,KAAK;EACvD;EAAC,QAAAC,CAAA,G;qCAlJQhC,kBAAkB,EAAA9B,EAAA,CAAA+D,iBAAA,CAAA/D,EAAA,CAAAgE,iBAAA,GAAAhE,EAAA,CAAA+D,iBAAA,CAAAE,EAAA,CAAAC,qBAAA,GAAAlE,EAAA,CAAA+D,iBAAA,CAAAI,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAlBvC,kBAAkB;IAAAwC,SAAA;IAAAC,QAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC1B/B7E,EAAA,CAAAE,cAAA,gBAAwD;QACpDF,EAAA,CAAAc,UAAA,IAAAiE,0CAAA,0BAEC;QACL/E,EAAA,CAAAiB,YAAA,EAAS;QAGTjB,EAAA,CAAAE,cAAA,qBAAsD;QAClDF,EAAA,CAAAgF,gBAAA,IAAAC,iCAAA,qBAAAH,GAAA,CAAAhC,SAAA,OAYC;QACL9C,EAAA,CAAAiB,YAAA,EAAW;QAGXjB,EAAA,CAAAc,UAAA,IAAAoE,yCAAA,gCAAAlF,EAAA,CAAAmF,sBAAA,CAAiC;;;;;QAxBTnF,EAAA,CAAAmB,UAAA,sBAAAiE,YAAA,CAA+B;QAE9CpF,EAAA,CAAAkB,SAAA,EAA6B;QAAAlB,EAA7B,CAAAmB,UAAA,qBAAAC,YAAA,CAA6B,4BAAApB,EAAA,CAAAqB,eAAA,IAAAC,GAAA,EAAAwD,GAAA,CAAAtC,UAAA,EAAkC;QAK9DxC,EAAA,CAAAkB,SAAA,EAAsB;QAAtBlB,EAAA,CAAAmB,UAAA,uBAAsB;QAC5BnB,EAAA,CAAAkB,SAAA,GAYC;QAZDlB,EAAA,CAAAqF,UAAA,CAAAP,GAAA,CAAA1C,cAAA,CAYC;;;mBDISvC,eAAe,EAAAyF,EAAA,CAAAC,aAAA,EAAEzF,aAAa,EAAA0F,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,WAAA,EAAAF,EAAA,CAAAG,cAAA,EAAE/F,gBAAgB;IAAAgG,aAAA;IAAAC,eAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}