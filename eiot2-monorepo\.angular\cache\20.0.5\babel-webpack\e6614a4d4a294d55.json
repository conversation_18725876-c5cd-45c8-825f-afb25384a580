{"ast": null, "code": "import { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { RouterOutlet } from '@angular/router';\nimport { FuseFullscreenComponent } from '@fuse/components/fullscreen';\nimport { FuseLoadingBarComponent } from '@fuse/components/loading-bar';\nimport { FuseVerticalNavigationComponent } from '@fuse/components/navigation';\nimport { LanguagesComponent } from 'app/layout/common/languages/languages.component';\nimport { MessagesComponent } from 'app/layout/common/messages/messages.component';\nimport { NotificationsComponent } from 'app/layout/common/notifications/notifications.component';\nimport { QuickChatComponent } from 'app/layout/common/quick-chat/quick-chat.component';\nimport { SearchComponent } from 'app/layout/common/search/search.component';\nimport { ShortcutsComponent } from 'app/layout/common/shortcuts/shortcuts.component';\nimport { UserComponent } from 'app/layout/common/user/user.component';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"app/core/navigation/navigation.service\";\nimport * as i3 from \"app/core/user/user.service\";\nimport * as i4 from \"@fuse/services/media-watcher\";\nimport * as i5 from \"@fuse/components/navigation\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/button\";\nfunction ClassyLayoutComponent_Conditional_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 10);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"src\", ctx_r1.user.avatar, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ClassyLayoutComponent_Conditional_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-icon\", 11);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"svgIcon\", \"heroicons_solid:user-circle\");\n  }\n}\nfunction ClassyLayoutComponent_Conditional_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"router-outlet\");\n  }\n}\nexport class ClassyLayoutComponent {\n  /**\n   * Constructor\n   */\n  constructor(_activatedRoute, _router, _navigationService, _userService, _fuseMediaWatcherService, _fuseNavigationService) {\n    this._activatedRoute = _activatedRoute;\n    this._router = _router;\n    this._navigationService = _navigationService;\n    this._userService = _userService;\n    this._fuseMediaWatcherService = _fuseMediaWatcherService;\n    this._fuseNavigationService = _fuseNavigationService;\n    this._unsubscribeAll = new Subject();\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Accessors\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * Getter for current year\n   */\n  get currentYear() {\n    return new Date().getFullYear();\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Lifecycle hooks\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * On init\n   */\n  ngOnInit() {\n    // Subscribe to navigation data\n    this._navigationService.navigation$.pipe(takeUntil(this._unsubscribeAll)).subscribe(navigation => {\n      this.navigation = navigation;\n    });\n    // Subscribe to the user service\n    this._userService.user$.pipe(takeUntil(this._unsubscribeAll)).subscribe(user => {\n      this.user = user;\n    });\n    // Subscribe to media changes\n    this._fuseMediaWatcherService.onMediaChange$.pipe(takeUntil(this._unsubscribeAll)).subscribe(({\n      matchingAliases\n    }) => {\n      // Check if the screen is small\n      this.isScreenSmall = !matchingAliases.includes('md');\n    });\n  }\n  /**\n   * On destroy\n   */\n  ngOnDestroy() {\n    // Unsubscribe from all subscriptions\n    this._unsubscribeAll.next(null);\n    this._unsubscribeAll.complete();\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Public methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * Toggle navigation\n   *\n   * @param name\n   */\n  toggleNavigation(name) {\n    // Get the navigation\n    const navigation = this._fuseNavigationService.getComponent(name);\n    if (navigation) {\n      // Toggle the opened status\n      navigation.toggle();\n    }\n  }\n  static #_ = this.ɵfac = function ClassyLayoutComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ClassyLayoutComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.NavigationService), i0.ɵɵdirectiveInject(i3.UserService), i0.ɵɵdirectiveInject(i4.FuseMediaWatcherService), i0.ɵɵdirectiveInject(i5.FuseNavigationService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ClassyLayoutComponent,\n    selectors: [[\"classy-layout\"]],\n    decls: 37,\n    vars: 13,\n    consts: [[\"quickChat\", \"quickChat\"], [1, \"dark\", \"bg-gray-900\", \"print:hidden\", 3, \"mode\", \"name\", \"navigation\", \"opened\"], [\"fuseVerticalNavigationContentHeader\", \"\"], [1, \"flex\", \"w-full\", \"items-center\", \"p-4\", \"pl-6\"], [1, \"flex\", \"items-center\", \"justify-center\"], [\"src\", \"images/logo/logo.svg\", 1, \"w-8\"], [1, \"ml-auto\", \"flex\", \"items-center\"], [3, \"showAvatar\"], [1, \"flex\", \"w-full\", \"flex-col\", \"items-center\", \"p-4\"], [1, \"relative\", \"h-24\", \"w-24\"], [\"alt\", \"User avatar\", 1, \"h-full\", \"w-full\", \"rounded-full\", 3, \"src\"], [1, \"icon-size-24\", 3, \"svgIcon\"], [1, \"mt-6\", \"flex\", \"w-full\", \"flex-col\", \"items-center\", \"justify-center\"], [1, \"w-full\", \"overflow-hidden\", \"text-ellipsis\", \"whitespace-nowrap\", \"text-center\", \"font-medium\", \"leading-normal\"], [1, \"text-secondary\", \"mt-0.5\", \"w-full\", \"overflow-hidden\", \"text-ellipsis\", \"whitespace-nowrap\", \"text-center\", \"text-md\", \"font-medium\", \"leading-normal\"], [\"fuseVerticalNavigationContentFooter\", \"\"], [1, \"mb-4\", \"mt-2\", \"flex\", \"h-16\", \"flex-0\", \"items-center\", \"justify-center\", \"pl-2\", \"pr-6\", \"opacity-12\"], [\"src\", \"images/logo/logo-text-on-dark.svg\", 1, \"max-w-36\"], [1, \"flex\", \"w-full\", \"min-w-0\", \"flex-auto\", \"flex-col\"], [1, \"bg-card\", \"relative\", \"z-49\", \"flex\", \"h-16\", \"w-full\", \"flex-0\", \"items-center\", \"px-4\", \"shadow\", \"dark:border-b\", \"dark:bg-transparent\", \"dark:shadow-none\", \"md:px-6\", \"print:hidden\"], [\"mat-icon-button\", \"\", 3, \"click\"], [3, \"svgIcon\"], [1, \"ml-auto\", \"flex\", \"items-center\", \"space-x-0.5\", \"pl-2\", \"sm:space-x-2\"], [1, \"hidden\", \"md:block\"], [3, \"appearance\"], [\"mat-icon-button\", \"\", 1, \"lg:hidden\", 3, \"click\"], [1, \"flex\", \"flex-auto\", \"flex-col\"]],\n    template: function ClassyLayoutComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelement(0, \"fuse-loading-bar\");\n        i0.ɵɵelementStart(1, \"fuse-vertical-navigation\", 1);\n        i0.ɵɵelementContainerStart(2, 2);\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4);\n        i0.ɵɵelement(5, \"img\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"div\", 6);\n        i0.ɵɵelement(7, \"notifications\")(8, \"user\", 7);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(9, \"div\", 8)(10, \"div\", 9);\n        i0.ɵɵconditionalCreate(11, ClassyLayoutComponent_Conditional_11_Template, 1, 1, \"img\", 10);\n        i0.ɵɵconditionalCreate(12, ClassyLayoutComponent_Conditional_12_Template, 1, 1, \"mat-icon\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"div\", 12)(14, \"div\", 13);\n        i0.ɵɵtext(15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(16, \"div\", 14);\n        i0.ɵɵtext(17);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementContainerStart(18, 15);\n        i0.ɵɵelementStart(19, \"div\", 16);\n        i0.ɵɵelement(20, \"img\", 17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(21, \"div\", 18)(22, \"div\", 19)(23, \"button\", 20);\n        i0.ɵɵlistener(\"click\", function ClassyLayoutComponent_Template_button_click_23_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.toggleNavigation(\"mainNavigation\"));\n        });\n        i0.ɵɵelement(24, \"mat-icon\", 21);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"div\", 22);\n        i0.ɵɵelement(26, \"languages\")(27, \"fuse-fullscreen\", 23)(28, \"search\", 24)(29, \"shortcuts\")(30, \"messages\");\n        i0.ɵɵelementStart(31, \"button\", 25);\n        i0.ɵɵlistener(\"click\", function ClassyLayoutComponent_Template_button_click_31_listener() {\n          i0.ɵɵrestoreView(_r1);\n          const quickChat_r3 = i0.ɵɵreference(36);\n          return i0.ɵɵresetView(quickChat_r3.toggle());\n        });\n        i0.ɵɵelement(32, \"mat-icon\", 21);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(33, \"div\", 26);\n        i0.ɵɵconditionalCreate(34, ClassyLayoutComponent_Conditional_34_Template, 1, 0, \"router-outlet\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(35, \"quick-chat\", null, 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"mode\", ctx.isScreenSmall ? \"over\" : \"side\")(\"name\", \"mainNavigation\")(\"navigation\", ctx.navigation.default)(\"opened\", !ctx.isScreenSmall);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"showAvatar\", false);\n        i0.ɵɵadvance(3);\n        i0.ɵɵconditional(ctx.user.avatar ? 11 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(!ctx.user.avatar ? 12 : -1);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", ctx.user.name, \" \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\" \", ctx.user.email, \" \");\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"svgIcon\", \"heroicons_outline:bars-3\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"appearance\", \"bar\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"svgIcon\", \"heroicons_outline:chat-bubble-left-right\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(true ? 34 : -1);\n      }\n    },\n    dependencies: [FuseLoadingBarComponent, FuseVerticalNavigationComponent, NotificationsComponent, UserComponent, MatIconModule, i6.MatIcon, MatButtonModule, i7.MatIconButton, LanguagesComponent, FuseFullscreenComponent, SearchComponent, ShortcutsComponent, MessagesComponent, RouterOutlet, QuickChatComponent],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["MatButtonModule", "MatIconModule", "RouterOutlet", "FuseFullscreenComponent", "FuseLoadingBarComponent", "FuseVerticalNavigationComponent", "LanguagesComponent", "MessagesComponent", "NotificationsComponent", "QuickChatComponent", "SearchComponent", "ShortcutsComponent", "UserComponent", "Subject", "takeUntil", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r1", "user", "avatar", "ɵɵsanitizeUrl", "ClassyLayoutComponent", "constructor", "_activatedRoute", "_router", "_navigationService", "_userService", "_fuseMediaWatcherService", "_fuseNavigationService", "_unsubscribeAll", "currentYear", "Date", "getFullYear", "ngOnInit", "navigation$", "pipe", "subscribe", "navigation", "user$", "onMediaChange$", "matchingAliases", "isScreenSmall", "includes", "ngOnDestroy", "next", "complete", "toggleNavigation", "name", "getComponent", "toggle", "_", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "NavigationService", "i3", "UserService", "i4", "FuseMediaWatcherService", "i5", "FuseNavigationService", "_2", "selectors", "decls", "vars", "consts", "template", "ClassyLayoutComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelementContainerStart", "ɵɵelementEnd", "ɵɵconditionalCreate", "ClassyLayoutComponent_Conditional_11_Template", "ClassyLayoutComponent_Conditional_12_Template", "ɵɵtext", "ɵɵlistener", "ClassyLayoutComponent_Template_button_click_23_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "ClassyLayoutComponent_Template_button_click_31_listener", "quickChat_r3", "ɵɵreference", "ClassyLayoutComponent_Conditional_34_Template", "ɵɵadvance", "default", "ɵɵconditional", "ɵɵtextInterpolate1", "email", "i6", "MatIcon", "i7", "MatIconButton", "encapsulation"], "sources": ["D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\app\\layout\\layouts\\vertical\\classy\\classy.component.ts", "D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\app\\layout\\layouts\\vertical\\classy\\classy.component.html"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>nit, ViewEncapsulation } from '@angular/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { ActivatedRoute, Router, RouterOutlet } from '@angular/router';\nimport { FuseFullscreenComponent } from '@fuse/components/fullscreen';\nimport { FuseLoadingBarComponent } from '@fuse/components/loading-bar';\nimport {\n    FuseNavigationService,\n    FuseVerticalNavigationComponent,\n} from '@fuse/components/navigation';\nimport { FuseMediaWatcherService } from '@fuse/services/media-watcher';\nimport { NavigationService } from 'app/core/navigation/navigation.service';\nimport { Navigation } from 'app/core/navigation/navigation.types';\nimport { UserService } from 'app/core/user/user.service';\nimport { User } from 'app/core/user/user.types';\nimport { LanguagesComponent } from 'app/layout/common/languages/languages.component';\nimport { MessagesComponent } from 'app/layout/common/messages/messages.component';\nimport { NotificationsComponent } from 'app/layout/common/notifications/notifications.component';\nimport { QuickChatComponent } from 'app/layout/common/quick-chat/quick-chat.component';\nimport { SearchComponent } from 'app/layout/common/search/search.component';\nimport { ShortcutsComponent } from 'app/layout/common/shortcuts/shortcuts.component';\nimport { UserComponent } from 'app/layout/common/user/user.component';\nimport { Subject, takeUntil } from 'rxjs';\n\n@Component({\n    selector: 'classy-layout',\n    templateUrl: './classy.component.html',\n    encapsulation: ViewEncapsulation.None,\n    imports: [\n        FuseLoadingBarComponent,\n        FuseVerticalNavigationComponent,\n        NotificationsComponent,\n        UserComponent,\n        MatIconModule,\n        MatButtonModule,\n        LanguagesComponent,\n        FuseFullscreenComponent,\n        SearchComponent,\n        ShortcutsComponent,\n        MessagesComponent,\n        RouterOutlet,\n        QuickChatComponent,\n    ],\n})\nexport class ClassyLayoutComponent implements OnInit, OnDestroy {\n    isScreenSmall: boolean;\n    navigation: Navigation;\n    user: User;\n    private _unsubscribeAll: Subject<any> = new Subject<any>();\n\n    /**\n     * Constructor\n     */\n    constructor(\n        private _activatedRoute: ActivatedRoute,\n        private _router: Router,\n        private _navigationService: NavigationService,\n        private _userService: UserService,\n        private _fuseMediaWatcherService: FuseMediaWatcherService,\n        private _fuseNavigationService: FuseNavigationService\n    ) {}\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Accessors\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * Getter for current year\n     */\n    get currentYear(): number {\n        return new Date().getFullYear();\n    }\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Lifecycle hooks\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * On init\n     */\n    ngOnInit(): void {\n        // Subscribe to navigation data\n        this._navigationService.navigation$\n            .pipe(takeUntil(this._unsubscribeAll))\n            .subscribe((navigation: Navigation) => {\n                this.navigation = navigation;\n            });\n\n        // Subscribe to the user service\n        this._userService.user$\n            .pipe(takeUntil(this._unsubscribeAll))\n            .subscribe((user: User) => {\n                this.user = user;\n            });\n\n        // Subscribe to media changes\n        this._fuseMediaWatcherService.onMediaChange$\n            .pipe(takeUntil(this._unsubscribeAll))\n            .subscribe(({ matchingAliases }) => {\n                // Check if the screen is small\n                this.isScreenSmall = !matchingAliases.includes('md');\n            });\n    }\n\n    /**\n     * On destroy\n     */\n    ngOnDestroy(): void {\n        // Unsubscribe from all subscriptions\n        this._unsubscribeAll.next(null);\n        this._unsubscribeAll.complete();\n    }\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Public methods\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * Toggle navigation\n     *\n     * @param name\n     */\n    toggleNavigation(name: string): void {\n        // Get the navigation\n        const navigation =\n            this._fuseNavigationService.getComponent<FuseVerticalNavigationComponent>(\n                name\n            );\n\n        if (navigation) {\n            // Toggle the opened status\n            navigation.toggle();\n        }\n    }\n}\n", "<!-- Loading bar -->\n<fuse-loading-bar></fuse-loading-bar>\n\n<!-- Navigation -->\n<fuse-vertical-navigation\n    class=\"dark bg-gray-900 print:hidden\"\n    [mode]=\"isScreenSmall ? 'over' : 'side'\"\n    [name]=\"'mainNavigation'\"\n    [navigation]=\"navigation.default\"\n    [opened]=\"!isScreenSmall\"\n>\n    <!-- Navigation header hook -->\n    <ng-container fuseVerticalNavigationContentHeader>\n        <div class=\"flex w-full items-center p-4 pl-6\">\n            <!-- Logo -->\n            <div class=\"flex items-center justify-center\">\n                <img class=\"w-8\" src=\"images/logo/logo.svg\" />\n            </div>\n            <!-- Components -->\n            <div class=\"ml-auto flex items-center\">\n                <notifications></notifications>\n                <user [showAvatar]=\"false\"></user>\n            </div>\n        </div>\n        <!-- User -->\n        <div class=\"flex w-full flex-col items-center p-4\">\n            <div class=\"relative h-24 w-24\">\n                @if (user.avatar) {\n                    <img\n                        class=\"h-full w-full rounded-full\"\n                        [src]=\"user.avatar\"\n                        alt=\"User avatar\"\n                    />\n                }\n                @if (!user.avatar) {\n                    <mat-icon\n                        class=\"icon-size-24\"\n                        [svgIcon]=\"'heroicons_solid:user-circle'\"\n                    ></mat-icon>\n                }\n            </div>\n            <div class=\"mt-6 flex w-full flex-col items-center justify-center\">\n                <div\n                    class=\"w-full overflow-hidden text-ellipsis whitespace-nowrap text-center font-medium leading-normal\"\n                >\n                    {{ user.name }}\n                </div>\n                <div\n                    class=\"text-secondary mt-0.5 w-full overflow-hidden text-ellipsis whitespace-nowrap text-center text-md font-medium leading-normal\"\n                >\n                    {{ user.email }}\n                </div>\n            </div>\n        </div>\n    </ng-container>\n    <!-- Navigation footer hook -->\n    <ng-container fuseVerticalNavigationContentFooter>\n        <div\n            class=\"mb-4 mt-2 flex h-16 flex-0 items-center justify-center pl-2 pr-6 opacity-12\"\n        >\n            <img class=\"max-w-36\" src=\"images/logo/logo-text-on-dark.svg\" />\n        </div>\n    </ng-container>\n</fuse-vertical-navigation>\n\n<!-- Wrapper -->\n<div class=\"flex w-full min-w-0 flex-auto flex-col\">\n    <!-- Header -->\n    <div\n        class=\"bg-card relative z-49 flex h-16 w-full flex-0 items-center px-4 shadow dark:border-b dark:bg-transparent dark:shadow-none md:px-6 print:hidden\"\n    >\n        <!-- Navigation toggle button -->\n        <button mat-icon-button (click)=\"toggleNavigation('mainNavigation')\">\n            <mat-icon [svgIcon]=\"'heroicons_outline:bars-3'\"></mat-icon>\n        </button>\n        <!-- Components -->\n        <div class=\"ml-auto flex items-center space-x-0.5 pl-2 sm:space-x-2\">\n            <languages></languages>\n            <fuse-fullscreen class=\"hidden md:block\"></fuse-fullscreen>\n            <search [appearance]=\"'bar'\"></search>\n            <shortcuts></shortcuts>\n            <messages></messages>\n            <button\n                class=\"lg:hidden\"\n                mat-icon-button\n                (click)=\"quickChat.toggle()\"\n            >\n                <mat-icon\n                    [svgIcon]=\"'heroicons_outline:chat-bubble-left-right'\"\n                ></mat-icon>\n            </button>\n        </div>\n    </div>\n\n    <!-- Content -->\n    <div class=\"flex flex-auto flex-col\">\n        <!-- *ngIf=\"true\" hack is required here for router-outlet to work correctly.\n             Otherwise, layout changes won't be registered and the view won't be updated! -->\n        @if (true) {\n            <router-outlet></router-outlet>\n        }\n    </div>\n\n    <!-- Footer -->\n    <!--<div class=\"relative flex flex-0 items-center justify-start w-full h-14 px-4 md:px-6 z-49 border-t bg-card dark:bg-transparent print:hidden\">\n        <span class=\"font-medium text-secondary\">Fuse &copy; {{currentYear}}</span>\n    </div>-->\n</div>\n\n<!-- Quick chat -->\n<quick-chat #quickChat=\"quickChat\"></quick-chat>\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAAiCC,YAAY,QAAQ,iBAAiB;AACtE,SAASC,uBAAuB,QAAQ,6BAA6B;AACrE,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAEIC,+BAA+B,QAC5B,6BAA6B;AAMpC,SAASC,kBAAkB,QAAQ,iDAAiD;AACpF,SAASC,iBAAiB,QAAQ,+CAA+C;AACjF,SAASC,sBAAsB,QAAQ,yDAAyD;AAChG,SAASC,kBAAkB,QAAQ,mDAAmD;AACtF,SAASC,eAAe,QAAQ,2CAA2C;AAC3E,SAASC,kBAAkB,QAAQ,iDAAiD;AACpF,SAASC,aAAa,QAAQ,uCAAuC;AACrE,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;ICMrBC,EAAA,CAAAC,SAAA,cAIE;;;;IAFED,EAAA,CAAAE,UAAA,QAAAC,MAAA,CAAAC,IAAA,CAAAC,MAAA,EAAAL,EAAA,CAAAM,aAAA,CAAmB;;;;;IAKvBN,EAAA,CAAAC,SAAA,mBAGY;;;IADRD,EAAA,CAAAE,UAAA,0CAAyC;;;;;IA8DrDF,EAAA,CAAAC,SAAA,oBAA+B;;;ADvD3C,OAAM,MAAOM,qBAAqB;EAM9B;;;EAGAC,YACYC,eAA+B,EAC/BC,OAAe,EACfC,kBAAqC,EACrCC,YAAyB,EACzBC,wBAAiD,EACjDC,sBAA6C;IAL7C,KAAAL,eAAe,GAAfA,eAAe;IACf,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,sBAAsB,GAAtBA,sBAAsB;IAX1B,KAAAC,eAAe,GAAiB,IAAIjB,OAAO,EAAO;EAYvD;EAEH;EACA;EACA;EAEA;;;EAGA,IAAIkB,WAAWA,CAAA;IACX,OAAO,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;EACnC;EAEA;EACA;EACA;EAEA;;;EAGAC,QAAQA,CAAA;IACJ;IACA,IAAI,CAACR,kBAAkB,CAACS,WAAW,CAC9BC,IAAI,CAACtB,SAAS,CAAC,IAAI,CAACgB,eAAe,CAAC,CAAC,CACrCO,SAAS,CAAEC,UAAsB,IAAI;MAClC,IAAI,CAACA,UAAU,GAAGA,UAAU;IAChC,CAAC,CAAC;IAEN;IACA,IAAI,CAACX,YAAY,CAACY,KAAK,CAClBH,IAAI,CAACtB,SAAS,CAAC,IAAI,CAACgB,eAAe,CAAC,CAAC,CACrCO,SAAS,CAAElB,IAAU,IAAI;MACtB,IAAI,CAACA,IAAI,GAAGA,IAAI;IACpB,CAAC,CAAC;IAEN;IACA,IAAI,CAACS,wBAAwB,CAACY,cAAc,CACvCJ,IAAI,CAACtB,SAAS,CAAC,IAAI,CAACgB,eAAe,CAAC,CAAC,CACrCO,SAAS,CAAC,CAAC;MAAEI;IAAe,CAAE,KAAI;MAC/B;MACA,IAAI,CAACC,aAAa,GAAG,CAACD,eAAe,CAACE,QAAQ,CAAC,IAAI,CAAC;IACxD,CAAC,CAAC;EACV;EAEA;;;EAGAC,WAAWA,CAAA;IACP;IACA,IAAI,CAACd,eAAe,CAACe,IAAI,CAAC,IAAI,CAAC;IAC/B,IAAI,CAACf,eAAe,CAACgB,QAAQ,EAAE;EACnC;EAEA;EACA;EACA;EAEA;;;;;EAKAC,gBAAgBA,CAACC,IAAY;IACzB;IACA,MAAMV,UAAU,GACZ,IAAI,CAACT,sBAAsB,CAACoB,YAAY,CACpCD,IAAI,CACP;IAEL,IAAIV,UAAU,EAAE;MACZ;MACAA,UAAU,CAACY,MAAM,EAAE;IACvB;EACJ;EAAC,QAAAC,CAAA,G;qCAzFQ7B,qBAAqB,EAAAP,EAAA,CAAAqC,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAvC,EAAA,CAAAqC,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAxC,EAAA,CAAAqC,iBAAA,CAAAI,EAAA,CAAAC,iBAAA,GAAA1C,EAAA,CAAAqC,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAA5C,EAAA,CAAAqC,iBAAA,CAAAQ,EAAA,CAAAC,uBAAA,GAAA9C,EAAA,CAAAqC,iBAAA,CAAAU,EAAA,CAAAC,qBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAArB1C,qBAAqB;IAAA2C,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QC3ClCxD,EAAA,CAAAC,SAAA,uBAAqC;QAGrCD,EAAA,CAAA0D,cAAA,kCAMC;QAEG1D,EAAA,CAAA2D,uBAAA,MAAkD;QAG1C3D,EAFJ,CAAA0D,cAAA,aAA+C,aAEG;QAC1C1D,EAAA,CAAAC,SAAA,aAA8C;QAClDD,EAAA,CAAA4D,YAAA,EAAM;QAEN5D,EAAA,CAAA0D,cAAA,aAAuC;QAEnC1D,EADA,CAAAC,SAAA,oBAA+B,cACG;QAE1CD,EADI,CAAA4D,YAAA,EAAM,EACJ;QAGF5D,EADJ,CAAA0D,cAAA,aAAmD,cACf;QAC5B1D,EAAA,CAAA6D,mBAAA,KAAAC,6CAAA,kBAAmB;QAOnB9D,EAAA,CAAA6D,mBAAA,KAAAE,6CAAA,uBAAoB;QAMxB/D,EAAA,CAAA4D,YAAA,EAAM;QAEF5D,EADJ,CAAA0D,cAAA,eAAmE,eAG9D;QACG1D,EAAA,CAAAgE,MAAA,IACJ;QAAAhE,EAAA,CAAA4D,YAAA,EAAM;QACN5D,EAAA,CAAA0D,cAAA,eAEC;QACG1D,EAAA,CAAAgE,MAAA,IACJ;QAERhE,EAFQ,CAAA4D,YAAA,EAAM,EACJ,EACJ;;QAGV5D,EAAA,CAAA2D,uBAAA,QAAkD;QAC9C3D,EAAA,CAAA0D,cAAA,eAEC;QACG1D,EAAA,CAAAC,SAAA,eAAgE;QACpED,EAAA,CAAA4D,YAAA,EAAM;;QAEd5D,EAAA,CAAA4D,YAAA,EAA2B;QASnB5D,EANR,CAAA0D,cAAA,eAAoD,eAI/C,kBAEwE;QAA7C1D,EAAA,CAAAiE,UAAA,mBAAAC,wDAAA;UAAAlE,EAAA,CAAAmE,aAAA,CAAAC,GAAA;UAAA,OAAApE,EAAA,CAAAqE,WAAA,CAASZ,GAAA,CAAAzB,gBAAA,CAAiB,gBAAgB,CAAC;QAAA,EAAC;QAChEhC,EAAA,CAAAC,SAAA,oBAA4D;QAChED,EAAA,CAAA4D,YAAA,EAAS;QAET5D,EAAA,CAAA0D,cAAA,eAAqE;QAKjE1D,EAJA,CAAAC,SAAA,iBAAuB,2BACoC,kBACrB,iBACf,gBACF;QACrBD,EAAA,CAAA0D,cAAA,kBAIC;QADG1D,EAAA,CAAAiE,UAAA,mBAAAK,wDAAA;UAAAtE,EAAA,CAAAmE,aAAA,CAAAC,GAAA;UAAA,MAAAG,YAAA,GAAAvE,EAAA,CAAAwE,WAAA;UAAA,OAAAxE,EAAA,CAAAqE,WAAA,CAASE,YAAA,CAAApC,MAAA,EAAkB;QAAA,EAAC;QAE5BnC,EAAA,CAAAC,SAAA,oBAEY;QAGxBD,EAFQ,CAAA4D,YAAA,EAAS,EACP,EACJ;QAGN5D,EAAA,CAAA0D,cAAA,eAAqC;QAGjC1D,EAAA,CAAA6D,mBAAA,KAAAY,6CAAA,wBAAY;QASpBzE,EANI,CAAA4D,YAAA,EAAM,EAMJ;QAGN5D,EAAA,CAAAC,SAAA,2BAAgD;;;QAxG5CD,EAAA,CAAA0E,SAAA,EAAwC;QAGxC1E,EAHA,CAAAE,UAAA,SAAAuD,GAAA,CAAA9B,aAAA,mBAAwC,0BACf,eAAA8B,GAAA,CAAAlC,UAAA,CAAAoD,OAAA,CACQ,YAAAlB,GAAA,CAAA9B,aAAA,CACR;QAYP3B,EAAA,CAAA0E,SAAA,GAAoB;QAApB1E,EAAA,CAAAE,UAAA,qBAAoB;QAM1BF,EAAA,CAAA0E,SAAA,GAMC;QAND1E,EAAA,CAAA4E,aAAA,CAAAnB,GAAA,CAAArD,IAAA,CAAAC,MAAA,WAMC;QACDL,EAAA,CAAA0E,SAAA,EAKC;QALD1E,EAAA,CAAA4E,aAAA,EAAAnB,GAAA,CAAArD,IAAA,CAAAC,MAAA,WAKC;QAMGL,EAAA,CAAA0E,SAAA,GACJ;QADI1E,EAAA,CAAA6E,kBAAA,MAAApB,GAAA,CAAArD,IAAA,CAAA6B,IAAA,MACJ;QAIIjC,EAAA,CAAA0E,SAAA,GACJ;QADI1E,EAAA,CAAA6E,kBAAA,MAAApB,GAAA,CAAArD,IAAA,CAAA0E,KAAA,MACJ;QAsBM9E,EAAA,CAAA0E,SAAA,GAAsC;QAAtC1E,EAAA,CAAAE,UAAA,uCAAsC;QAMxCF,EAAA,CAAA0E,SAAA,GAAoB;QAApB1E,EAAA,CAAAE,UAAA,qBAAoB;QASpBF,EAAA,CAAA0E,SAAA,GAAsD;QAAtD1E,EAAA,CAAAE,UAAA,uDAAsD;QAUlEF,EAAA,CAAA0E,SAAA,GAEC;QAFD1E,EAAA,CAAA4E,aAAA,gBAEC;;;mBDvEDvF,uBAAuB,EACvBC,+BAA+B,EAC/BG,sBAAsB,EACtBI,aAAa,EACbX,aAAa,EAAA6F,EAAA,CAAAC,OAAA,EACb/F,eAAe,EAAAgG,EAAA,CAAAC,aAAA,EACf3F,kBAAkB,EAClBH,uBAAuB,EACvBO,eAAe,EACfC,kBAAkB,EAClBJ,iBAAiB,EACjBL,YAAY,EACZO,kBAAkB;IAAAyF,aAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}