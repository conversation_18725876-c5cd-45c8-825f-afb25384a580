[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:15.665Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\0f0e9f9229ce501d05c6\d.sock
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:15.675Z - [WATCHER]: Subscribed to changes within: D:\EIOT2.SERVER\eiot2-monorepo (native)
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:15.681Z - Established a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:15.683Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:15.689Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:15.693Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:16.268Z - Time taken for 'Load Nx Plugin: D:\EIOT2.SERVER\eiot2-monorepo\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 544.5909ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:16.314Z - Time taken for 'Load Nx Plugin: D:\EIOT2.SERVER\eiot2-monorepo\node_modules\nx\src\plugins\package-json' 592.6238ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:16.393Z - Time taken for 'loadDefaultNxPlugins' 673.3200999999999ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:16.491Z - Time taken for 'Load Nx Plugin: @nx/eslint/plugin' 773.4707999999999ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:16.604Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:16.604Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:16.605Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:16.617Z - Time taken for 'loadSpecifiedNxPlugins' 904.6674999999999ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:16.618Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:16.619Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:16.620Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:16.624Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:16.625Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:16.625Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:16.625Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:16.626Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:16.626Z - Handled HASH_GLOB. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:16.629Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:16.630Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:16.630Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:16.635Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:16.635Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:16.636Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:28.144Z - [REQUEST]: Responding to the client. handleNxWorkspaceFiles
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:28.145Z - Done responding to the client handleNxWorkspaceFiles
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:28.145Z - Handled GET_FILES_IN_DIRECTORY. Handling time: 0. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:39.654Z - Time taken for 'build-project-configs' 23021.979000000003ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:39.935Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:39.937Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:39.939Z - Time taken for 'total for creating and serializing project graph' 24243.568ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:39.939Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:39.939Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 24244. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:16:39.956Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:04.121Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:04.122Z - [WATCHER]: package.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:04.224Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:04.224Z - [REQUEST]: package.json
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:04.224Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:04.238Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:04.242Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:04.242Z - Time taken for 'hash changed files from watcher' 0.18660000000090804ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:04.243Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:04.243Z - Handled HASH_GLOB. Handling time: 1. Response time: 5.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:04.244Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:04.244Z - Handled GLOB. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:04.250Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:04.250Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:04.251Z - Handled HASH_GLOB. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:04.364Z - Time taken for 'build-project-configs' 128.21140000000014ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:04.428Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:04.429Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:04.429Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:04.429Z - Time taken for 'total execution time for createProjectGraph()' 54.66979999999603ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:05.282Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:05.286Z - [WATCHER]: Stopping the watcher for D:\EIOT2.SERVER\eiot2-monorepo (sources)
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:05.287Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:05.287Z - Closed a connection. Number of open connections: 0
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:05.287Z - [WATCHER]: Stopping the watcher for D:\EIOT2.SERVER\eiot2-monorepo (outputs)
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:05.288Z - Server stopped because: "LOCK_FILES_CHANGED"
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:17.659Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\0f0e9f9229ce501d05c6\d.sock
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:17.664Z - [WATCHER]: Subscribed to changes within: D:\EIOT2.SERVER\eiot2-monorepo (native)
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:17.670Z - Established a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:17.672Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:17.676Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:17.679Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:18.161Z - Time taken for 'Load Nx Plugin: D:\EIOT2.SERVER\eiot2-monorepo\node_modules\nx\src\plugins\package-json' 475.3915ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:18.175Z - Time taken for 'Load Nx Plugin: D:\EIOT2.SERVER\eiot2-monorepo\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 490.55760000000004ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:18.197Z - Time taken for 'loadDefaultNxPlugins' 513.6582ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:18.295Z - Time taken for 'Load Nx Plugin: @nx/eslint/plugin' 612.6956ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:18.377Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:18.377Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:18.377Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:18.393Z - Time taken for 'loadSpecifiedNxPlugins' 695.9261ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:18.395Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:18.396Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:18.397Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:18.404Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:18.405Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:18.405Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:18.406Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:18.406Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:18.406Z - Handled HASH_GLOB. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:18.409Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:18.409Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:18.409Z - Handled GLOB. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:18.414Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:18.415Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:18.415Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:19.882Z - [REQUEST]: Responding to the client. handleNxWorkspaceFiles
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:19.883Z - Done responding to the client handleNxWorkspaceFiles
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:19.883Z - Handled GET_FILES_IN_DIRECTORY. Handling time: 0. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:20.439Z - Time taken for 'build-project-configs' 2031.4171000000001ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:20.619Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:20.622Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:20.623Z - Time taken for 'total for creating and serializing project graph' 2942.7324ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:20.624Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:20.624Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2943. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:37.203Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:37.204Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:37.205Z - Time taken for 'total for creating and serializing project graph' 0.7147000000004482ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:37.206Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:37.206Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:37.246Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:37.248Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:37.250Z - Time taken for 'total for creating and serializing project graph' 1.2109000000018568ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:37.251Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:37.251Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 3.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:37.533Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:37.534Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:37.535Z - Time taken for 'total for creating and serializing project graph' 0.7325000000018917ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:37.536Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:37.536Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:37.649Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:37.649Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:37.651Z - Time taken for 'total for creating and serializing project graph' 0.48130000000310247ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:37.651Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:37.651Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:37.679Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:37.680Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:37.682Z - Time taken for 'total for creating and serializing project graph' 0.6121999999995751ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:37.682Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:37.682Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:37.700Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:37.701Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:37.702Z - Time taken for 'total for creating and serializing project graph' 0.497799999997369ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:37.702Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:37.702Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:39.121Z - [WATCHER]: 0 file(s) created or restored, 18 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:39.125Z - [WATCHER]: 0 file(s) created or restored, 5 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:39.127Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:39.127Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:39.160Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:39.226Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:39.226Z - [REQUEST]: shell/public/favicon.ico,shell/src/app/nx-welcome.ts,shell/jest.config.ts,shell/tsconfig.app.json,shell/src/app/app.scss,shell/src/test-setup.ts,shell/src/app/app.ts,shell/tsconfig.spec.json,shell/src/app/app.routes.ts,shell/project.json,shell/src/main.ts,shell/src/app/app.html,shell/tsconfig.json,shell/src/app/app.config.ts,shell/src/app/app.spec.ts,shell/eslint.config.mjs,shell/src/index.html,shell/src/styles.scss,shell-e2e/src/example.spec.ts,shell-e2e/project.json,shell-e2e/playwright.config.ts,shell-e2e/eslint.config.mjs,shell-e2e/tsconfig.json
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:39.226Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:39.244Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:39.246Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:39.247Z - Time taken for 'hash changed files from watcher' 1.2471000000005006ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:39.247Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:39.248Z - Handled HASH_GLOB. Handling time: 1. Response time: 4.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:39.250Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:39.250Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:39.250Z - Handled GLOB. Handling time: 1. Response time: 4.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:39.255Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:39.255Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:39.255Z - Handled HASH_GLOB. Handling time: 1. Response time: 5.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:39.256Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:39.256Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:39.284Z - [REQUEST]: Responding to the client. handleNxWorkspaceFiles
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:39.285Z - Done responding to the client handleNxWorkspaceFiles
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:39.285Z - Handled GET_FILES_IN_DIRECTORY. Handling time: 0. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:39.331Z - Time taken for 'build-project-configs' 93.8205999999991ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:39.416Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:39.418Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:39.418Z - Time taken for 'total execution time for createProjectGraph()' 72.85419999999795ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:40.657Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:40.657Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:40.658Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:40.661Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:40.662Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:40.663Z - Time taken for 'total for creating and serializing project graph' 0.45010000000183936ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:40.664Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:40.664Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:44.722Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:44.723Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:44.724Z - Time taken for 'total for creating and serializing project graph' 0.4805000000014843ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:44.724Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:44.724Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:44.762Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:44.763Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:44.764Z - Time taken for 'total for creating and serializing project graph' 0.6212000000014086ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:44.765Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:44.765Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:44.861Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:44.862Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:44.863Z - Time taken for 'total for creating and serializing project graph' 0.4062000000012631ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:44.864Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:44.864Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:44.923Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:44.923Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:44.924Z - Time taken for 'total for creating and serializing project graph' 0.4912999999978638ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:44.925Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:44.925Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:44.949Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:44.949Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:44.950Z - Time taken for 'total for creating and serializing project graph' 0.5041999999994005ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:44.951Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:44.951Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:44.973Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:44.974Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:44.975Z - Time taken for 'total for creating and serializing project graph' 0.3683000000019092ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:44.976Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:44.976Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:45.933Z - [WATCHER]: 0 file(s) created or restored, 24 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:45.936Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:45.937Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:46.038Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:46.038Z - [REQUEST]: eiot-admin/src/app/app.routes.ts,eiot-admin-e2e/playwright.config.ts,eiot-admin/tsconfig.json,eiot-admin-e2e/eslint.config.mjs,nx.json,eiot-admin/src/app/app.ts,eiot-admin/src/app/app.scss,eiot-admin/src/main.ts,eiot-admin/src/app/app.html,eiot-admin-e2e/project.json,eiot-admin/tsconfig.app.json,eiot-admin/src/app/app.config.ts,eiot-admin/src/app/nx-welcome.ts,eiot-admin/src/app/app.spec.ts,eiot-admin/public/favicon.ico,eiot-admin-e2e/src/example.spec.ts,eiot-admin/jest.config.ts,eiot-admin/src/styles.scss,eiot-admin/project.json,eiot-admin/eslint.config.mjs,eiot-admin/tsconfig.spec.json,eiot-admin/src/test-setup.ts,eiot-admin/src/index.html,eiot-admin-e2e/tsconfig.json
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:46.038Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:46.049Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:46.053Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:46.053Z - Time taken for 'hash changed files from watcher' 0.9314999999987776ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:46.054Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:46.054Z - Handled HASH_GLOB. Handling time: 1. Response time: 5.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:46.056Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:46.056Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:46.056Z - Handled GLOB. Handling time: 1. Response time: 3.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:46.060Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:46.060Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:46.060Z - Handled HASH_GLOB. Handling time: 1. Response time: 4.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:46.060Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:46.060Z - Handled HASH_GLOB. Handling time: 3. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:46.080Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:46.081Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:46.081Z - Handled HASH_GLOB. Handling time: 0. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:46.082Z - [REQUEST]: Responding to the client. handleNxWorkspaceFiles
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:46.082Z - Done responding to the client handleNxWorkspaceFiles
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:46.082Z - Handled GET_FILES_IN_DIRECTORY. Handling time: 0. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:46.172Z - Time taken for 'build-project-configs' 115.56949999999779ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:46.251Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:46.252Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:17:46.252Z - Time taken for 'total execution time for createProjectGraph()' 65.10889999999927ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:04.900Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:04.904Z - [WATCHER]: package.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:05.106Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:05.106Z - [REQUEST]: package.json
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:05.106Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:05.124Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:05.126Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:05.127Z - Time taken for 'hash changed files from watcher' 0.31260000000474975ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:05.127Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:05.128Z - Handled HASH_GLOB. Handling time: 2. Response time: 4.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:05.130Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:05.130Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:05.131Z - Handled GLOB. Handling time: 1. Response time: 5.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:05.140Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:05.140Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:05.140Z - Handled HASH_GLOB. Handling time: 1. Response time: 10.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:05.142Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:05.156Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:05.156Z - Handled HASH_GLOB. Handling time: 8. Response time: 16.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:05.156Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:05.156Z - Handled HASH_GLOB. Handling time: 1. Response time: 14.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:05.213Z - Time taken for 'build-project-configs' 98.69479999999749ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:05.273Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:05.274Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:05.274Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:05.275Z - Time taken for 'total execution time for createProjectGraph()' 34.218100000005506ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:06.111Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:06.114Z - [WATCHER]: Stopping the watcher for D:\EIOT2.SERVER\eiot2-monorepo (sources)
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:06.114Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:06.114Z - Closed a connection. Number of open connections: 0
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:06.114Z - [WATCHER]: Stopping the watcher for D:\EIOT2.SERVER\eiot2-monorepo (outputs)
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:06.115Z - Server stopped because: "LOCK_FILES_CHANGED"
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:25.639Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\0f0e9f9229ce501d05c6\d.sock
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:25.644Z - [WATCHER]: Subscribed to changes within: D:\EIOT2.SERVER\eiot2-monorepo (native)
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:25.649Z - Established a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:25.654Z - Closed a connection. Number of open connections: 0
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:25.655Z - Established a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:25.658Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:26.232Z - Time taken for 'Load Nx Plugin: D:\EIOT2.SERVER\eiot2-monorepo\node_modules\nx\src\plugins\js' 567.5697ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:26.258Z - Time taken for 'Load Nx Plugin: D:\EIOT2.SERVER\eiot2-monorepo\node_modules\nx\src\plugins\package-json' 594.3006ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:26.319Z - Time taken for 'loadDefaultNxPlugins' 655.5518ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:26.470Z - Time taken for 'Load Nx Plugin: @nx/eslint/plugin' 808.1139999999999ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:26.608Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:26.609Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:26.609Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:26.630Z - Time taken for 'loadSpecifiedNxPlugins' 948.2537ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:26.631Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:26.631Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:26.633Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:26.637Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:26.638Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:26.639Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:26.640Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:26.640Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:26.640Z - Handled HASH_GLOB. Handling time: 0. Response time: 4.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:26.643Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:26.646Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:26.646Z - Handled GLOB. Handling time: 1. Response time: 3.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:26.650Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:26.651Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:26.651Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:28.156Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:28.157Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:28.157Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:28.201Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:28.202Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:28.202Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:28.227Z - [REQUEST]: Responding to the client. handleNxWorkspaceFiles
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:28.227Z - Done responding to the client handleNxWorkspaceFiles
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:28.227Z - Handled GET_FILES_IN_DIRECTORY. Handling time: 0. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:28.241Z - [REQUEST]: Responding to the client. handleNxWorkspaceFiles
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:28.241Z - Done responding to the client handleNxWorkspaceFiles
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:28.241Z - Handled GET_FILES_IN_DIRECTORY. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:28.244Z - [REQUEST]: Responding to the client. handleNxWorkspaceFiles
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:28.244Z - Done responding to the client handleNxWorkspaceFiles
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:28.244Z - Handled GET_FILES_IN_DIRECTORY. Handling time: 0. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:28.747Z - Time taken for 'build-project-configs' 2107.3634ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:28.896Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:28.898Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:28.900Z - Time taken for 'total for creating and serializing project graph' 3240.0474999999997ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:28.901Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:28.901Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 3240. Response time: 3.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:28.916Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:28.916Z - Time taken for 'preTasksExecution' 0.9989999999997963ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:28.917Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:28.917Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:29.034Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:29.035Z - Done responding to the client handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:29.035Z - Handled HASH_TASKS. Handling time: 29. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:29.080Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:29.081Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:29.081Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 20. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:40.229Z - Established a connection. Number of open connections: 4
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:40.234Z - Closed a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:40.235Z - Established a connection. Number of open connections: 4
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:40.239Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:40.240Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:40.241Z - Time taken for 'total for creating and serializing project graph' 0.6291999999994005ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:40.241Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:40.242Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:48.325Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:49.523Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:49.577Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:56.905Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:56.973Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:18:57.067Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:45.336Z - [WATCHER]: eiot-admin was deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:45.337Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:45.438Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:45.438Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:45.438Z - [REQUEST]: eiot-admin
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:45.456Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:45.456Z - Time taken for 'hash changed files from watcher' 0.21309999999357387ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:45.459Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:45.459Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:45.459Z - Handled HASH_GLOB. Handling time: 1. Response time: 3.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:45.463Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:45.463Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:45.464Z - Handled GLOB. Handling time: 1. Response time: 5.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:45.468Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:45.468Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:45.468Z - Handled HASH_GLOB. Handling time: 1. Response time: 5.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:45.471Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:45.471Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:45.471Z - Handled HASH_GLOB. Handling time: 3. Response time: 3.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:45.472Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:45.472Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:45.557Z - Time taken for 'build-project-configs' 102.19399999998859ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:45.639Z - Error detected when creating a project graph: Failed to create project graph. See above for errors
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:45.642Z - Time taken for 'createDependencies' 85.43910000001779ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:55.620Z - [WATCHER]: eiot-admin-e2e was deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:55.621Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:55.822Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:55.823Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:55.823Z - [REQUEST]: eiot-admin-e2e
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:55.835Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:55.839Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:55.839Z - Time taken for 'hash changed files from watcher' 0.1369999999878928ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:55.841Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:55.841Z - Handled HASH_GLOB. Handling time: 1. Response time: 6.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:55.843Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:55.843Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:55.843Z - Handled GLOB. Handling time: 1. Response time: 4.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:55.847Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:55.847Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:55.848Z - Handled HASH_GLOB. Handling time: 1. Response time: 5.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:55.848Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:55.848Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:55.906Z - Time taken for 'build-project-configs' 74.94219999999041ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:55.985Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:55.986Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:55.986Z - Time taken for 'total execution time for createProjectGraph()' 59.254200000024866ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:59.897Z - [WATCHER]: shell-e2e was deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T10:20:59.897Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:00.297Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:00.298Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:00.298Z - [REQUEST]: shell-e2e
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:00.309Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:00.315Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:00.316Z - Time taken for 'hash changed files from watcher' 0.2081000000180211ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:00.317Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:00.317Z - Handled HASH_GLOB. Handling time: 0. Response time: 8.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:00.317Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:00.317Z - Handled GLOB. Handling time: 4. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:00.321Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:00.322Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:00.322Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:00.388Z - Time taken for 'build-project-configs' 74.59810000000289ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:00.444Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:00.447Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:00.447Z - Time taken for 'total execution time for createProjectGraph()' 51.130999999993946ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:08.884Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:08.886Z - [WATCHER]: apps/eiot2-monorepo-e2e was deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:09.688Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:09.688Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:09.688Z - [REQUEST]: apps/eiot2-monorepo-e2e
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:09.696Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:09.697Z - Time taken for 'hash changed files from watcher' 0.1275000000023283ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:09.698Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:09.698Z - Handled GLOB. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:09.701Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:09.701Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:09.702Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:09.791Z - Time taken for 'build-project-configs' 91.54149999999208ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:09.860Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:09.861Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:09.861Z - Time taken for 'total execution time for createProjectGraph()' 57.30920000001788ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:13.897Z - [WATCHER]: apps/eiot2-monorepo was deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:13.897Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:15.498Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:15.498Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:15.498Z - [REQUEST]: apps/eiot2-monorepo
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:15.508Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:15.509Z - Time taken for 'hash changed files from watcher' 0.10469999999622814ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:15.509Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:15.510Z - Handled GLOB. Handling time: 0. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:15.513Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:15.513Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:15.513Z - Handled HASH_GLOB. Handling time: 2. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:15.589Z - Time taken for 'build-project-configs' 77.83289999997942ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:15.641Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:15.641Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:21:15.642Z - Time taken for 'total execution time for createProjectGraph()' 46.130300000018906ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:22:22.288Z - Closed a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T10:22:22.289Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:22:22.458Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:22:22.459Z - [WATCHER]: shell was deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T10:22:25.660Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:22:25.661Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:22:25.661Z - [REQUEST]: shell
[NX v21.2.2 Daemon Server] - 2025-07-07T10:22:25.666Z - Time taken for 'hash changed files from watcher' 0.09649999998509884ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:22:25.668Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:22:25.669Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:22:25.669Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:22:25.672Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:22:25.673Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:22:25.673Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:22:25.735Z - Time taken for 'build-project-configs' 64.95000000001164ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:22:25.772Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:22:25.773Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:22:25.773Z - Time taken for 'total execution time for createProjectGraph()' 32.01070000001346ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:27:53.173Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:27:53.185Z - [WATCHER]: apps/eiot-admin/shell/project.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:27:59.599Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:27:59.599Z - [REQUEST]: apps/eiot-admin/shell/project.json
[NX v21.2.2 Daemon Server] - 2025-07-07T10:27:59.599Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:27:59.604Z - Time taken for 'hash changed files from watcher' 9.158500000019558ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:27:59.622Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:27:59.623Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:27:59.623Z - Handled GLOB. Handling time: 0. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:27:59.625Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:27:59.626Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:27:59.626Z - Handled HASH_GLOB. Handling time: 0. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:27:59.753Z - Time taken for 'build-project-configs' 146.64689999993425ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:27:59.828Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:27:59.829Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:27:59.830Z - Time taken for 'total execution time for createProjectGraph()' 58.92000000004191ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:04.165Z - [WATCHER]: apps/eiot-admin/shell/project.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:04.165Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:10.578Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:10.578Z - [REQUEST]: apps/eiot-admin/shell/project.json
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:10.578Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:10.587Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:10.587Z - Time taken for 'hash changed files from watcher' 10.664800000027753ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:10.588Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:10.588Z - Handled GLOB. Handling time: 0. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:10.590Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:10.590Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:10.590Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:10.659Z - Time taken for 'build-project-configs' 71.97430000000168ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:10.710Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:10.711Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:10.711Z - Time taken for 'total execution time for createProjectGraph()' 45.5050999999512ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:15.718Z - [WATCHER]: apps/eiot-admin/shell/webpack.config.js was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:15.719Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:21.318Z - [WATCHER]: apps/eiot-admin/shell/src/main.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:21.319Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:22.138Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:22.138Z - [REQUEST]: apps/eiot-admin/shell/webpack.config.js,apps/eiot-admin/shell/src/main.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:22.138Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:22.146Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:22.146Z - Time taken for 'hash changed files from watcher' 17.622399999992922ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:22.147Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:22.147Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:22.149Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:22.150Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:22.150Z - Handled HASH_GLOB. Handling time: 0. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:22.244Z - Time taken for 'build-project-configs' 95.97620000003371ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:22.326Z - Time taken for 'total execution time for createProjectGraph()' 59.960800000000745ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:29.005Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:29.007Z - [WATCHER]: apps/eiot-admin/shell/src/bootstrap.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:35.424Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:35.424Z - [REQUEST]: apps/eiot-admin/shell/src/bootstrap.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:35.424Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:35.438Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:35.438Z - Time taken for 'hash changed files from watcher' 15.307299999985844ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:35.438Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:35.439Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:35.440Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:35.441Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:35.441Z - Handled HASH_GLOB. Handling time: 0. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:35.473Z - Time taken for 'build-project-configs' 37.87109999998938ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:35.521Z - Time taken for 'total execution time for createProjectGraph()' 41.045199999934994ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:36.127Z - [WATCHER]: apps/eiot-admin/shell/src/app/app.html was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:36.128Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:42.543Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:42.543Z - [REQUEST]: apps/eiot-admin/shell/src/app/app.html
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:42.543Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:42.552Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:42.553Z - Time taken for 'hash changed files from watcher' 13.901100000017323ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:42.554Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:42.554Z - Handled GLOB. Handling time: 0. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:42.557Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:42.558Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:42.558Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:42.595Z - Time taken for 'build-project-configs' 43.27230000006966ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:42.642Z - Time taken for 'total execution time for createProjectGraph()' 39.19129999994766ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:48.582Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:48.585Z - [WATCHER]: apps/eiot-admin/shell/src/app/app.scss was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:54.998Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:54.998Z - [REQUEST]: apps/eiot-admin/shell/src/app/app.scss
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:54.998Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:55.006Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:55.006Z - Time taken for 'hash changed files from watcher' 12.396000000066124ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:55.007Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:55.007Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:55.010Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:55.010Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:55.011Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:55.032Z - Time taken for 'build-project-configs' 25.762000000104308ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:28:55.080Z - Time taken for 'total execution time for createProjectGraph()' 40.31979999993928ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:01.631Z - [WATCHER]: apps/eiot-admin/shell/src/app/app.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:01.633Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:08.054Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:08.054Z - [REQUEST]: apps/eiot-admin/shell/src/app/app.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:08.054Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:08.073Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:08.074Z - Time taken for 'hash changed files from watcher' 21.78179999999702ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:08.075Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:08.075Z - Handled GLOB. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:08.077Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:08.078Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:08.078Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:08.117Z - Time taken for 'build-project-configs' 54.023999999975786ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:08.170Z - Time taken for 'total execution time for createProjectGraph()' 44.57999999995809ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:16.973Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:16.974Z - [WATCHER]: apps/eiot-admin/shell/src/app/home/<USER>
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:23.399Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:23.400Z - [REQUEST]: apps/eiot-admin/shell/src/app/home/<USER>
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:23.400Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:23.410Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:23.411Z - Time taken for 'hash changed files from watcher' 23.62749999994412ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:23.412Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:23.412Z - Handled GLOB. Handling time: 0. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:23.416Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:23.416Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:23.416Z - Handled HASH_GLOB. Handling time: 2. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:23.451Z - Time taken for 'build-project-configs' 42.15529999998398ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:23.499Z - Time taken for 'total execution time for createProjectGraph()' 38.88470000005327ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:25.228Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:25.230Z - [WATCHER]: apps/eiot-admin/shell/src/app/app.routes.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:31.661Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:31.661Z - [REQUEST]: apps/eiot-admin/shell/src/app/app.routes.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:31.661Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:31.673Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:31.673Z - Time taken for 'hash changed files from watcher' 29.725800000014715ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:31.674Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:31.675Z - Handled GLOB. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:31.678Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:31.679Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:31.679Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:31.713Z - Time taken for 'build-project-configs' 41.050400000065565ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:29:31.768Z - Time taken for 'total execution time for createProjectGraph()' 42.92260000004899ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:30:52.065Z - [WATCHER]: 0 file(s) created or restored, 21 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T10:30:52.066Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:30:58.527Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:30:58.527Z - [REQUEST]: apps/shell/public/favicon.ico,apps/shell/src/main.ts,apps/shell/tsconfig.spec.json,apps/shell/eslint.config.mjs,apps/shell/src/test-setup.ts,apps/shell/src/bootstrap.ts,apps/shell/src/app/app.html,apps/shell/src/app/app.scss,apps/shell/src/app/app.spec.ts,apps/shell/src/app/nx-welcome.ts,apps/shell/src/app/home/<USER>/shell/src/app/app.routes.ts,apps/shell/project.json,apps/shell/tsconfig.json,apps/shell/src/app/app.ts,apps/shell/webpack.config.js,apps/shell/tsconfig.app.json,apps/shell/src/app/app.config.ts,apps/shell/src/styles.scss,apps/shell/src/index.html,apps/shell/jest.config.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T10:30:58.527Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:30:58.537Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:30:58.538Z - Time taken for 'hash changed files from watcher' 60.64220000000205ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:30:58.538Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:30:58.538Z - Handled GLOB. Handling time: 0. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:30:58.554Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:30:58.555Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:30:58.555Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:30:58.571Z - Time taken for 'build-project-configs' 31.694900000002235ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:30:58.632Z - Time taken for 'total execution time for createProjectGraph()' 50.48470000002999ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:11.344Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:11.346Z - [WATCHER]: apps/shell/project.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:17.755Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:17.755Z - [REQUEST]: apps/shell/project.json
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:17.756Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:17.762Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:17.762Z - Time taken for 'hash changed files from watcher' 8.40659999998752ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:17.762Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:17.762Z - Handled GLOB. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:17.765Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:17.765Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:17.766Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:17.790Z - Time taken for 'build-project-configs' 27.666399999987334ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:17.827Z - Time taken for 'total execution time for createProjectGraph()' 30.86309999995865ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:21.352Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:21.353Z - [WATCHER]: apps/shell/project.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:27.764Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:27.764Z - [REQUEST]: apps/shell/project.json
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:27.764Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:27.772Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:27.772Z - Time taken for 'hash changed files from watcher' 8.922599999932572ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:27.773Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:27.773Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:27.775Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:27.776Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:27.776Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:27.806Z - Time taken for 'build-project-configs' 34.742399999988265ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:27.864Z - Time taken for 'total execution time for createProjectGraph()' 45.090899999951944ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:30.998Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:30.999Z - [WATCHER]: apps/shell/project.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:34.921Z - [WATCHER]: 0 file(s) created or restored, 0 file(s) modified, 26 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:34.923Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:37.410Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:37.410Z - [REQUEST]: apps/shell/project.json
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:37.410Z - [REQUEST]: apps/eiot-admin/shell/jest.config.ts,apps/eiot-admin/shell/src/app/home,apps/eiot-admin/shell/public,apps/eiot-admin/shell/src/test-setup.ts,apps/eiot-admin/shell/webpack.config.js,apps/eiot-admin/shell/src/bootstrap.ts,apps/eiot-admin/shell/src/styles.scss,apps/eiot-admin/shell/tsconfig.spec.json,apps/eiot-admin/shell/tsconfig.json,apps/eiot-admin/shell/src/app/app.html,apps/eiot-admin/shell/src/app/app.scss,apps/eiot-admin/shell/src/app/nx-welcome.ts,apps/eiot-admin/shell/src,apps/eiot-admin/shell/src/app/app.config.ts,apps/eiot-admin/shell/src/app/app.ts,apps/eiot-admin/shell/src/app,apps/eiot-admin/shell/src/app/app.spec.ts,apps/eiot-admin/shell,apps/eiot-admin/shell/tsconfig.app.json,apps/eiot-admin/shell/src/index.html,apps/eiot-admin/shell/src/app/app.routes.ts,apps/eiot-admin/shell/eslint.config.mjs,apps/eiot-admin/shell/public/favicon.ico,apps/eiot-admin/shell/src/main.ts,apps/eiot-admin/shell/src/app/home/<USER>/eiot-admin/shell/project.json
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:37.416Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:37.417Z - Time taken for 'hash changed files from watcher' 10.56559999997262ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:37.417Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:37.417Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:37.419Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:37.420Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:37.420Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:37.489Z - Time taken for 'build-project-configs' 72.7774999999674ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:37.550Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:37.551Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:37.551Z - Time taken for 'total execution time for createProjectGraph()' 46.384799999999814ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:40.387Z - [WATCHER]: apps/shell/project.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:40.387Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:46.796Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:46.796Z - [REQUEST]: apps/shell/project.json
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:46.796Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:46.803Z - Time taken for 'hash changed files from watcher' 9.209099999978207ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:46.808Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:46.809Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:46.809Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:46.812Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:46.813Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:46.813Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:46.890Z - Time taken for 'build-project-configs' 83.53110000002198ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:46.941Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:46.942Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:46.942Z - Time taken for 'total execution time for createProjectGraph()' 42.45899999991525ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:50.501Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:50.502Z - [WATCHER]: apps/shell/project.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:56.919Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:56.919Z - [REQUEST]: apps/shell/project.json
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:56.919Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:56.929Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:56.929Z - Time taken for 'hash changed files from watcher' 15.349500000011176ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:56.930Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:56.930Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:56.932Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:56.932Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:56.932Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:56.994Z - Time taken for 'build-project-configs' 65.40280000003986ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:57.043Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:57.044Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:31:57.044Z - Time taken for 'total execution time for createProjectGraph()' 41.90320000005886ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:00.019Z - [WATCHER]: apps/shell/tsconfig.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:00.019Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:06.427Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:06.427Z - [REQUEST]: apps/shell/tsconfig.json
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:06.427Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:06.433Z - Time taken for 'hash changed files from watcher' 8.135400000028312ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:06.434Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:06.435Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:06.435Z - Handled GLOB. Handling time: 0. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:06.437Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:06.438Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:06.438Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:06.506Z - Time taken for 'build-project-configs' 68.95600000000559ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:06.562Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:06.563Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:06.563Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:06.563Z - Time taken for 'total execution time for createProjectGraph()' 46.40540000004694ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:22.816Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:22.817Z - [WATCHER]: apps/shell/tsconfig.app.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:29.226Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:29.226Z - [REQUEST]: apps/shell/tsconfig.app.json
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:29.226Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:29.231Z - Time taken for 'hash changed files from watcher' 9.027600000030361ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:29.233Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:29.234Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:29.234Z - Handled GLOB. Handling time: 0. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:29.236Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:29.237Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:29.237Z - Handled HASH_GLOB. Handling time: 0. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:29.294Z - Time taken for 'build-project-configs' 61.74540000001434ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:29.350Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:29.351Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:29.351Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:32:29.351Z - Time taken for 'total execution time for createProjectGraph()' 40.178799999994226ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:33:23.116Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T10:33:23.117Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:33:23.118Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T10:33:23.121Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:33:23.122Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:33:23.123Z - Time taken for 'total for creating and serializing project graph' 0.7509999999310821ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:33:23.124Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:33:23.124Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:33:27.545Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:33:45.698Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T10:33:45.699Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:33:45.701Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T10:33:45.704Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:33:45.704Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:33:45.705Z - Time taken for 'total for creating and serializing project graph' 0.43000000005122274ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:33:45.706Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:33:45.706Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:02.306Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:02.307Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:02.308Z - Time taken for 'total for creating and serializing project graph' 0.7514000001829118ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:02.308Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:02.308Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:02.364Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:02.365Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:02.366Z - Time taken for 'total for creating and serializing project graph' 0.5470000000204891ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:02.366Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:02.366Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:02.646Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:02.647Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:02.648Z - Time taken for 'total for creating and serializing project graph' 0.582799999974668ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:02.649Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:02.649Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:02.848Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:02.849Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:02.850Z - Time taken for 'total for creating and serializing project graph' 0.4103000001050532ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:02.850Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:02.850Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:02.899Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:02.899Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:02.900Z - Time taken for 'total for creating and serializing project graph' 0.3663999999407679ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:02.900Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:02.901Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:02.919Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:02.919Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:02.920Z - Time taken for 'total for creating and serializing project graph' 0.3721000000368804ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:02.920Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:02.920Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:04.960Z - [WATCHER]: 0 file(s) created or restored, 23 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:04.964Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:04.983Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:05.069Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:05.069Z - [REQUEST]: apps-e2e/tsconfig.json,apps/tsconfig.app.json,apps/tsconfig.json,apps/src/app/app.ts,apps/public/favicon.ico,apps/src/app/app.routes.ts,apps/eslint.config.mjs,apps/src/test-setup.ts,apps/tsconfig.spec.json,apps/src/app/app.config.ts,apps/jest.config.ts,apps/src/main.ts,apps/src/styles.scss,apps-e2e/playwright.config.ts,apps-e2e/src/example.spec.ts,apps/src/app/app.scss,apps-e2e/eslint.config.mjs,apps/src/app/nx-welcome.ts,apps/project.json,apps/src/app/app.html,apps/src/app/app.spec.ts,apps-e2e/project.json,apps/src/index.html
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:05.069Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:05.077Z - Time taken for 'hash changed files from watcher' 3.7449000000488013ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:05.080Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:05.080Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:05.081Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:05.084Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:05.084Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:05.085Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:05.117Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:05.123Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:05.123Z - Handled HASH_GLOB. Handling time: 10. Response time: 6.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:05.247Z - [REQUEST]: Responding to the client. handleNxWorkspaceFiles
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:05.248Z - Done responding to the client handleNxWorkspaceFiles
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:05.248Z - Handled GET_FILES_IN_DIRECTORY. Handling time: 0. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:05.264Z - Time taken for 'build-project-configs' 183.1430999999866ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:05.356Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:05.357Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:36:05.357Z - Time taken for 'total execution time for createProjectGraph()' 73.42940000002272ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:26.228Z - [WATCHER]: 0 file(s) created or restored, 0 file(s) modified, 20 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:26.228Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:26.429Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:26.429Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:26.429Z - [REQUEST]: apps/public,apps/src,apps/tsconfig.spec.json,apps/src/index.html,apps/src/styles.scss,apps/src/app,apps/public/favicon.ico,apps/eslint.config.mjs,apps/src/app/app.scss,apps/tsconfig.app.json,apps/src/app/app.ts,apps/project.json,apps/src/app/app.spec.ts,apps/src/main.ts,apps/src/app/app.html,apps/src/test-setup.ts,apps/src/app/app.routes.ts,apps/jest.config.ts,apps/src/app/app.config.ts,apps/src/app/nx-welcome.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:26.437Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:26.438Z - Time taken for 'hash changed files from watcher' 0.3937999999616295ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:26.441Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:26.441Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:26.441Z - Handled HASH_GLOB. Handling time: 0. Response time: 4.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:26.442Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:26.442Z - Handled GLOB. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:26.445Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:26.445Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:26.445Z - Handled HASH_GLOB. Handling time: 2. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:26.525Z - Time taken for 'build-project-configs' 82.51530000008643ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:26.568Z - Error detected when creating a project graph: Failed to create project graph. See above for errors
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:26.572Z - Time taken for 'createDependencies' 46.75329999998212ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:44.396Z - [WATCHER]: apps/tsconfig.json was deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:44.397Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:44.798Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:44.798Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:44.798Z - [REQUEST]: apps/tsconfig.json
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:44.806Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:44.809Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:44.809Z - Time taken for 'hash changed files from watcher' 0.1218000000808388ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:44.810Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:44.810Z - Handled HASH_GLOB. Handling time: 0. Response time: 4.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:44.810Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:44.810Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:44.815Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:44.815Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:44.815Z - Handled HASH_GLOB. Handling time: 2. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:44.913Z - Time taken for 'build-project-configs' 105.3976000000257ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:44.965Z - Error detected when creating a project graph: Failed to create project graph. See above for errors
[NX v21.2.2 Daemon Server] - 2025-07-07T10:37:44.968Z - Time taken for 'createDependencies' 55.724500000011176ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:38:40.386Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:38:40.391Z - [WATCHER]: 0 file(s) created or restored, 21 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T10:38:41.264Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:38:41.265Z - [REQUEST]: apps/eiot-stub/tsconfig.spec.json,apps/eiot-stub/src/main.ts,apps/eiot-stub/webpack.config.js,apps/eiot-stub/src/app/app.routes.ts,apps/eiot-stub/tsconfig.json,apps/eiot-stub/src/app/app.scss,apps/eiot-stub/src/app/app.spec.ts,apps/eiot-stub/src/app/nx-welcome.ts,apps/eiot-stub/src/test-setup.ts,apps/eiot-stub/src/app/app.html,apps/eiot-stub/src/app/home/<USER>/eiot-stub/src/styles.scss,apps/eiot-stub/src/app/app.ts,apps/eiot-stub/jest.config.ts,apps/eiot-stub/public/favicon.ico,apps/eiot-stub/src/app/app.config.ts,apps/eiot-stub/src/bootstrap.ts,apps/eiot-stub/src/index.html,apps/eiot-stub/project.json,apps/eiot-stub/tsconfig.app.json,apps/eiot-stub/eslint.config.mjs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:38:41.265Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:38:41.273Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:38:41.277Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:38:41.278Z - Time taken for 'hash changed files from watcher' 69.9303999999538ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:38:41.278Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:38:41.278Z - Handled HASH_GLOB. Handling time: 0. Response time: 5.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:38:41.280Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:38:41.280Z - Handled GLOB. Handling time: 2. Response time: 3.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:38:41.283Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:38:41.284Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:38:41.284Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:38:41.365Z - Time taken for 'build-project-configs' 88.95140000013635ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:38:41.422Z - Error detected when creating a project graph: Failed to create project graph. See above for errors
[NX v21.2.2 Daemon Server] - 2025-07-07T10:38:41.426Z - Time taken for 'createDependencies' 61.79850000003353ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:04.714Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:04.715Z - [WATCHER]: apps/eiot-stub/project.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:06.327Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:06.327Z - [REQUEST]: apps/eiot-stub/project.json
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:06.327Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:06.337Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:06.338Z - Time taken for 'hash changed files from watcher' 9.972800000105053ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:06.339Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:06.339Z - Handled GLOB. Handling time: 0. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:06.350Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:06.352Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:06.352Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:06.352Z - Handled HASH_GLOB. Handling time: 10. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:06.353Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:06.353Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:06.423Z - Time taken for 'build-project-configs' 85.3589000001084ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:06.497Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:06.497Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:06.498Z - Time taken for 'total execution time for createProjectGraph()' 60.60620000003837ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:13.980Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:13.982Z - [WATCHER]: apps/eiot-stub/project.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:17.194Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:17.194Z - [REQUEST]: apps/eiot-stub/project.json
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:17.194Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:17.204Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:17.204Z - Time taken for 'hash changed files from watcher' 9.71169999986887ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:17.207Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:17.208Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:17.208Z - Handled HASH_GLOB. Handling time: 1. Response time: 4.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:17.208Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:17.208Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:17.213Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:17.213Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:17.213Z - Handled HASH_GLOB. Handling time: 3. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:17.301Z - Time taken for 'build-project-configs' 95.33909999998286ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:17.365Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:17.366Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:17.366Z - Time taken for 'total execution time for createProjectGraph()' 49.96390000008978ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:25.121Z - [WATCHER]: apps/eiot-stub/project.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:25.121Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:31.533Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:31.533Z - [REQUEST]: apps/eiot-stub/project.json
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:31.533Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:31.541Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:31.543Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:31.544Z - Time taken for 'hash changed files from watcher' 11.848800000036135ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:31.545Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:31.545Z - Handled HASH_GLOB. Handling time: 0. Response time: 4.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:31.545Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:31.545Z - Handled GLOB. Handling time: 0. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:31.548Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:31.548Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:31.549Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:31.619Z - Time taken for 'build-project-configs' 77.67190000019036ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:31.681Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:31.681Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:31.682Z - Time taken for 'total execution time for createProjectGraph()' 50.402299999957904ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:35.148Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:35.149Z - [WATCHER]: apps/eiot-stub/project.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:41.562Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:41.562Z - [REQUEST]: apps/eiot-stub/project.json
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:41.562Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:41.572Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:41.579Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:41.579Z - Time taken for 'hash changed files from watcher' 12.257500000065193ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:41.580Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:41.580Z - Handled HASH_GLOB. Handling time: 1. Response time: 8.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:41.581Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:41.581Z - Handled GLOB. Handling time: 6. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:41.585Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:41.586Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:41.586Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:41.654Z - Time taken for 'build-project-configs' 82.01729999994859ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:41.706Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:41.707Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:41.707Z - Time taken for 'total execution time for createProjectGraph()' 41.95659999991767ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:46.294Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:46.296Z - [WATCHER]: apps/eiot-stub/project.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:52.706Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:52.706Z - [REQUEST]: apps/eiot-stub/project.json
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:52.706Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:52.715Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:52.718Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:52.718Z - Time taken for 'hash changed files from watcher' 8.600000000093132ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:52.719Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:52.719Z - Handled HASH_GLOB. Handling time: 1. Response time: 4.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:52.719Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:52.720Z - Handled GLOB. Handling time: 2. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:52.722Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:52.723Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:52.723Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:52.808Z - Time taken for 'build-project-configs' 92.49729999992996ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:52.875Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:52.876Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:52.876Z - Time taken for 'total execution time for createProjectGraph()' 47.92530000000261ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:57.446Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:39:57.448Z - [WATCHER]: apps/eiot-stub/project.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:03.859Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:03.859Z - [REQUEST]: apps/eiot-stub/project.json
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:03.859Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:03.870Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:03.873Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:03.873Z - Time taken for 'hash changed files from watcher' 9.876900000032037ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:03.874Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:03.874Z - Handled HASH_GLOB. Handling time: 1. Response time: 4.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:03.874Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:03.874Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:03.877Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:03.877Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:03.877Z - Handled HASH_GLOB. Handling time: 2. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:03.954Z - Time taken for 'build-project-configs' 82.09660000004806ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:04.016Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:04.017Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:04.017Z - Time taken for 'total execution time for createProjectGraph()' 52.49470000015572ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:07.772Z - [WATCHER]: apps/eiot-stub/project.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:07.772Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:14.187Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:14.187Z - [REQUEST]: apps/eiot-stub/project.json
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:14.187Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:14.199Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:14.201Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:14.201Z - Time taken for 'hash changed files from watcher' 14.750900000100955ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:14.202Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:14.202Z - Handled HASH_GLOB. Handling time: 0. Response time: 3.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:14.202Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:14.203Z - Handled GLOB. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:14.205Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:14.205Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:14.206Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:14.265Z - Time taken for 'build-project-configs' 68.6317000000272ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:14.307Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:14.308Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:14.308Z - Time taken for 'total execution time for createProjectGraph()' 33.27179999998771ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:17.785Z - [WATCHER]: apps/eiot-stub/project.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:17.790Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:24.203Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:24.203Z - [REQUEST]: apps/eiot-stub/project.json
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:24.203Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:24.219Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:24.223Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:24.223Z - Time taken for 'hash changed files from watcher' 15.664199999999255ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:24.224Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:24.225Z - Handled HASH_GLOB. Handling time: 4. Response time: 6.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:24.225Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:24.225Z - Handled GLOB. Handling time: 2. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:24.240Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:24.246Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:24.246Z - Handled HASH_GLOB. Handling time: 14. Response time: 6.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:24.325Z - Time taken for 'build-project-configs' 108.56000000005588ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:24.387Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:24.388Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:24.388Z - Time taken for 'total execution time for createProjectGraph()' 52.98060000012629ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:28.397Z - [WATCHER]: apps/eiot-stub/project.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:28.398Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:34.807Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:34.807Z - [REQUEST]: apps/eiot-stub/project.json
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:34.807Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:34.813Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:34.816Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:34.816Z - Time taken for 'hash changed files from watcher' 9.187099999981001ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:34.817Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:34.817Z - Handled HASH_GLOB. Handling time: 0. Response time: 4.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:34.817Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:34.817Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:34.820Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:34.820Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:34.820Z - Handled HASH_GLOB. Handling time: 2. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:34.897Z - Time taken for 'build-project-configs' 79.91720000002533ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:34.969Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:34.969Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:34.969Z - Time taken for 'total execution time for createProjectGraph()' 52.30099999997765ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:43.621Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:43.623Z - [WATCHER]: apps/eiot-stub/webpack.config.js was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:50.086Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:50.086Z - [REQUEST]: apps/eiot-stub/webpack.config.js
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:50.086Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:50.093Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:50.096Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:50.096Z - Time taken for 'hash changed files from watcher' 61.567699999781325ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:50.097Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:50.097Z - Handled HASH_GLOB. Handling time: 1. Response time: 4.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:50.097Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:50.097Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:50.100Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:50.100Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:50.101Z - Handled HASH_GLOB. Handling time: 2. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:50.153Z - Time taken for 'build-project-configs' 59.944499999983236ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:50.193Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:50.193Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:50.194Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:50.194Z - Time taken for 'total execution time for createProjectGraph()' 31.59110000007786ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:55.478Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:40:55.481Z - [WATCHER]: apps/eiot-stub/src/app/app.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:02.029Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:02.030Z - [REQUEST]: apps/eiot-stub/src/app/app.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:02.030Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:02.432Z - Time taken for 'hash changed files from watcher' 60.96319999988191ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:02.697Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:02.734Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:02.741Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:02.741Z - Handled HASH_GLOB. Handling time: 131. Response time: 44.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:02.741Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:02.741Z - Handled GLOB. Handling time: 34. Response time: 7.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:02.792Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:02.792Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:02.792Z - Handled HASH_GLOB. Handling time: 50. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:02.899Z - Time taken for 'build-project-configs' 461.1779999998398ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:03.055Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:03.056Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:03.056Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:03.056Z - Time taken for 'total execution time for createProjectGraph()' 69.25659999996424ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:14.461Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:14.466Z - [WATCHER]: apps/eiot-stub/src/app/app.html was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:20.882Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:20.882Z - [REQUEST]: apps/eiot-stub/src/app/app.html
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:20.882Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:20.891Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:20.891Z - Time taken for 'hash changed files from watcher' 15.357100000139326ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:20.893Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:20.894Z - Done responding to the client handleHashGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:20.894Z - Handled HASH_GLOB. Handling time: 1. Response time: 3.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:20.894Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:20.894Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:20.901Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:20.901Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:20.902Z - Handled HASH_GLOB. Handling time: 4. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:20.970Z - Time taken for 'build-project-configs' 79.62779999990016ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:21.012Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:21.013Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:21.013Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:21.013Z - Time taken for 'total execution time for createProjectGraph()' 32.489300000015646ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:46.753Z - [WATCHER]: 0 file(s) created or restored, 0 file(s) modified, 7 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:46.753Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:53.153Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:53.153Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:53.153Z - [REQUEST]: apps-e2e/tsconfig.json,apps-e2e/eslint.config.mjs,apps-e2e,apps-e2e/playwright.config.ts,apps-e2e/src/example.spec.ts,apps-e2e/src,apps-e2e/project.json
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:53.161Z - Time taken for 'hash changed files from watcher' 0.1520999998319894ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:53.164Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:53.164Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:53.165Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:53.168Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:53.168Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:53.168Z - Handled HASH_GLOB. Handling time: 2. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:53.225Z - Time taken for 'build-project-configs' 61.876599999843165ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:53.277Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:53.278Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:41:53.278Z - Time taken for 'total execution time for createProjectGraph()' 42.705800000112504ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:29.503Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:29.505Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:29.505Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:29.508Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:29.509Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:29.510Z - Time taken for 'total for creating and serializing project graph' 0.48319999990053475ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:29.510Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:29.510Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:34.245Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:34.245Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:34.247Z - Time taken for 'total for creating and serializing project graph' 0.6754000000655651ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:34.247Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:34.247Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:34.264Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:34.264Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:34.265Z - Time taken for 'total for creating and serializing project graph' 0.4364999998360872ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:34.265Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:34.265Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:35.100Z - [WATCHER]: 0 file(s) created or restored, 15 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:35.101Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:35.203Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:35.203Z - [REQUEST]: package.json,libs/shared/ui/src/lib/ui/ui.css,libs/shared/ui/eslint.config.mjs,libs/shared/ui/ng-package.json,nx.json,libs/shared/ui/src/lib/ui/ui.html,libs/shared/ui/project.json,libs/shared/ui/package.json,libs/shared/ui/tsconfig.lib.prod.json,libs/shared/ui/tsconfig.json,libs/shared/ui/tsconfig.lib.json,libs/shared/ui/src/index.ts,tsconfig.base.json,libs/shared/ui/README.md,libs/shared/ui/src/lib/ui/ui.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:35.203Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:35.212Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:35.213Z - Time taken for 'hash changed files from watcher' 0.49369999999180436ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:35.213Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:35.213Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:35.217Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:35.218Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:35.218Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:35.346Z - Time taken for 'build-project-configs' 132.4605000000447ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:35.426Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:35.427Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:35.427Z - Time taken for 'total execution time for createProjectGraph()' 69.8456999999471ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:44.020Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:44.024Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:44.024Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:44.024Z - Closed a connection. Number of open connections: 0
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:44.025Z - [WATCHER]: Stopping the watcher for D:\EIOT2.SERVER\eiot2-monorepo (sources)
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:44.025Z - [WATCHER]: Stopping the watcher for D:\EIOT2.SERVER\eiot2-monorepo (outputs)
[NX v21.2.2 Daemon Server] - 2025-07-07T10:42:44.055Z - Server stopped because: "LOCK_FILES_CHANGED"
[NX v21.2.2 Daemon Server] - 2025-07-07T10:53:59.040Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\0f0e9f9229ce501d05c6\d.sock
[NX v21.2.2 Daemon Server] - 2025-07-07T10:53:59.047Z - [WATCHER]: Subscribed to changes within: D:\EIOT2.SERVER\eiot2-monorepo (native)
[NX v21.2.2 Daemon Server] - 2025-07-07T10:53:59.052Z - Established a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T10:53:59.054Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:53:59.057Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T10:53:59.060Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:53:59.558Z - Time taken for 'Load Nx Plugin: D:\EIOT2.SERVER\eiot2-monorepo\node_modules\nx\src\plugins\package-json' 489.34719999999993ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:53:59.567Z - Time taken for 'Load Nx Plugin: D:\EIOT2.SERVER\eiot2-monorepo\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 498.70449999999994ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:53:59.725Z - Time taken for 'loadDefaultNxPlugins' 658.6237ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:53:59.738Z - Time taken for 'Load Nx Plugin: @nx/eslint/plugin' 672.3639000000001ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:53:59.849Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:53:59.850Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:53:59.850Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:53:59.875Z - Time taken for 'loadSpecifiedNxPlugins' 786.8159ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:53:59.880Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:53:59.882Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T10:53:59.882Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:53:59.885Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:53:59.887Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:53:59.887Z - Handled GLOB. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:53:59.892Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:53:59.892Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:53:59.892Z - Handled HASH_GLOB. Handling time: 3. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:54:16.939Z - Time taken for 'build-project-configs' 17048.459199999998ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:54:17.141Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:54:17.142Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:54:17.144Z - Time taken for 'total for creating and serializing project graph' 18082.597700000002ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:54:17.144Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:54:17.144Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 18083. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:54:17.159Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T10:54:17.159Z - Time taken for 'preTasksExecution' 0.7612000000008265ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:54:17.159Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T10:54:17.159Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:54:17.206Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T10:54:44.624Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:54:44.625Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T10:54:44.626Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:54:44.630Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:54:44.631Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:54:44.632Z - Time taken for 'total for creating and serializing project graph' 0.6062000000019907ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:54:44.633Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:54:44.633Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:54:44.643Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T10:54:44.643Z - Time taken for 'preTasksExecution' 0.23399999999674037ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:54:44.643Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T10:54:44.643Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:54:44.676Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:11.062Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:11.063Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:11.064Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:11.072Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:11.073Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:11.075Z - Time taken for 'total for creating and serializing project graph' 0.8758000000088941ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:11.076Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:11.076Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 3.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:11.090Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:11.091Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:11.092Z - Time taken for 'total for creating and serializing project graph' 0.6577000000106636ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:11.092Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:11.093Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:11.215Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:11.216Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:11.218Z - Time taken for 'total for creating and serializing project graph' 0.5568999999959487ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:11.218Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:11.218Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:11.227Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:11.229Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:11.230Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:11.231Z - Time taken for 'total for creating and serializing project graph' 0.7363999999943189ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:11.233Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:11.233Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 3.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:41.201Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:41.201Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:43.770Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:43.771Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:43.772Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:43.776Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:43.776Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:43.778Z - Time taken for 'total for creating and serializing project graph' 0.5503000000026077ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:43.779Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:43.779Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 3.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:43.794Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:43.795Z - Time taken for 'preTasksExecution' 0.2729000000108499ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:43.795Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:43.795Z - Handled PRE_TASKS_EXECUTION. Handling time: 0. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:43.886Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:43.886Z - Done responding to the client handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:43.886Z - Handled HASH_TASKS. Handling time: 32. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:43.928Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:43.929Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:43.929Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 19. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:56.425Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:56.426Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:56.427Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:56.434Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:56.435Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:56.436Z - Time taken for 'total for creating and serializing project graph' 0.645699999993667ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:56.436Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:56.436Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:56.600Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:56.622Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:56.623Z - Done responding to the client recordOutputsHash
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:56.623Z - Handled RECORD_OUTPUTS_HASH. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:56.634Z - [REQUEST]: Responding to the client. handleRecordTaskRuns
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:56.634Z - Done responding to the client handleRecordTaskRuns
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:56.634Z - Handled RECORD_TASK_RUNS. Handling time: 2. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:56.637Z - [REQUEST]: Responding to the client. handleGetFlakyTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:56.637Z - Done responding to the client handleGetFlakyTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:56.637Z - Handled GET_FLAKY_TASKS. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:56.642Z - [REQUEST]: Responding to the client. handleRunPostTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:56.642Z - Time taken for 'postTasksExecution' 0.3371000000042841ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:56.642Z - Done responding to the client handleRunPostTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:56.642Z - Handled POST_TASKS_EXECUTION. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:56:56.651Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:08.414Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:08.414Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:08.416Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:08.419Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:08.420Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:08.422Z - Time taken for 'total for creating and serializing project graph' 0.6288000000058673ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:08.422Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:08.422Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:08.436Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:08.436Z - Time taken for 'preTasksExecution' 0.29090000002179295ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:08.436Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:08.436Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:08.488Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:08.489Z - Done responding to the client handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:08.489Z - Handled HASH_TASKS. Handling time: 14. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:08.507Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:08.507Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:08.507Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 0. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:09.780Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:09.783Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:09.784Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:09.786Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:09.786Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:09.787Z - Time taken for 'total for creating and serializing project graph' 0.3548000000009779ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:09.788Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:09.788Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:09.876Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:09.901Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:09.902Z - Done responding to the client recordOutputsHash
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:09.902Z - Handled RECORD_OUTPUTS_HASH. Handling time: 0. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:09.913Z - [REQUEST]: Responding to the client. handleRecordTaskRuns
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:09.914Z - Done responding to the client handleRecordTaskRuns
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:09.914Z - Handled RECORD_TASK_RUNS. Handling time: 3. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:09.916Z - [REQUEST]: Responding to the client. handleGetFlakyTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:09.916Z - Done responding to the client handleGetFlakyTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:09.916Z - Handled GET_FLAKY_TASKS. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:09.922Z - [REQUEST]: Responding to the client. handleRunPostTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:09.922Z - Time taken for 'postTasksExecution' 0.5632000000041444ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:09.923Z - Done responding to the client handleRunPostTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:09.923Z - Handled POST_TASKS_EXECUTION. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:09.931Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:24.469Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:24.470Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:24.471Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:24.476Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:24.477Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:24.478Z - Time taken for 'total for creating and serializing project graph' 0.9375ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:24.480Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:24.480Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 3.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:24.491Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:24.491Z - Time taken for 'preTasksExecution' 0.2507000000041444ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:24.492Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:24.492Z - Handled PRE_TASKS_EXECUTION. Handling time: 0. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:57:24.532Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:08.052Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:08.053Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:08.054Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:08.058Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:08.059Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:08.060Z - Time taken for 'total for creating and serializing project graph' 0.8218000000051688ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:08.060Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:08.061Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:08.096Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:19.355Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:19.357Z - [WATCHER]: apps/eiot-admin/project.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:19.487Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:19.488Z - [REQUEST]: apps/eiot-admin/project.json
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:19.488Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:19.540Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:19.540Z - Time taken for 'hash changed files from watcher' 28.10580000001937ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:19.541Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:19.541Z - Handled GLOB. Handling time: 21. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:19.561Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:19.567Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:19.567Z - Handled HASH_GLOB. Handling time: 19. Response time: 6.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:19.633Z - Time taken for 'build-project-configs' 130.9930999999924ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:19.690Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:19.691Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:19.691Z - Time taken for 'total execution time for createProjectGraph()' 44.432399999990594ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:31.928Z - [WATCHER]: apps/shell/project.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:31.928Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:32.139Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:32.139Z - [REQUEST]: apps/shell/project.json
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:32.139Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:32.153Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:32.154Z - Time taken for 'hash changed files from watcher' 8.658300000010058ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:32.155Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:32.155Z - Handled GLOB. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:32.159Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:32.159Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:32.159Z - Handled HASH_GLOB. Handling time: 3. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:32.219Z - Time taken for 'build-project-configs' 71.53800000000047ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:32.272Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:32.273Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:32.274Z - Time taken for 'total execution time for createProjectGraph()' 39.87849999999162ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:44.406Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:44.408Z - [WATCHER]: apps/eiot-stub/project.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:44.817Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:44.817Z - [REQUEST]: apps/eiot-stub/project.json
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:44.817Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:44.824Z - Time taken for 'hash changed files from watcher' 8.75179999996908ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:44.826Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:44.827Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:44.827Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:44.831Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:44.832Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:44.832Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:44.892Z - Time taken for 'build-project-configs' 67.57909999997355ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:44.934Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:44.935Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:58:44.935Z - Time taken for 'total execution time for createProjectGraph()' 33.430400000012014ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:00.838Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:00.838Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:00.839Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:00.843Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:00.843Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:00.844Z - Time taken for 'total for creating and serializing project graph' 0.5212999999639578ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:00.847Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:00.847Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 4.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:00.861Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:00.861Z - Time taken for 'preTasksExecution' 0.2890000000479631ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:00.862Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:00.862Z - Handled PRE_TASKS_EXECUTION. Handling time: 0. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:00.944Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:00.945Z - Done responding to the client handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:00.945Z - Handled HASH_TASKS. Handling time: 26. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:00.963Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:00.964Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:00.964Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 0. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:02.260Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:02.261Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:02.262Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:02.264Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:02.265Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:02.266Z - Time taken for 'total for creating and serializing project graph' 0.3740000000107102ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:02.266Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:02.266Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:11.122Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:11.177Z - [REQUEST]: Responding to the client. handleRecordTaskRuns
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:11.177Z - Done responding to the client handleRecordTaskRuns
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:11.177Z - Handled RECORD_TASK_RUNS. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:11.178Z - [REQUEST]: Responding to the client. handleGetFlakyTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:11.179Z - Done responding to the client handleGetFlakyTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:11.179Z - Handled GET_FLAKY_TASKS. Handling time: 0. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:11.185Z - [REQUEST]: Responding to the client. handleRunPostTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:11.185Z - Time taken for 'postTasksExecution' 0.3675999999977648ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:11.185Z - Done responding to the client handleRunPostTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:11.185Z - Handled POST_TASKS_EXECUTION. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:11.193Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:43.326Z - [WATCHER]: apps/eiot-admin/project.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:43.326Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:43.435Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:43.435Z - [REQUEST]: apps/eiot-admin/project.json
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:43.435Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:43.442Z - Time taken for 'hash changed files from watcher' 8.884499999985565ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:43.444Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:43.445Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:43.445Z - Handled GLOB. Handling time: 0. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:43.448Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:43.449Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:43.449Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:43.509Z - Time taken for 'build-project-configs' 66.21159999998054ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:43.554Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:43.555Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:43.555Z - Time taken for 'total execution time for createProjectGraph()' 36.226599999994505ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:57.435Z - [WATCHER]: apps/eiot-admin/project.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:57.435Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:57.643Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:57.643Z - [REQUEST]: apps/eiot-admin/project.json
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:57.643Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:57.655Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:57.655Z - Time taken for 'hash changed files from watcher' 8.471799999999348ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:57.655Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:57.655Z - Handled GLOB. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:57.658Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:57.659Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:57.659Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:57.716Z - Time taken for 'build-project-configs' 65.65770000003977ms
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:57.759Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:57.759Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T10:59:57.760Z - Time taken for 'total execution time for createProjectGraph()' 33.92300000000978ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:10.801Z - [WATCHER]: apps/eiot-admin/project.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:10.801Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:11.221Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:11.221Z - [REQUEST]: apps/eiot-admin/project.json
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:11.221Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:11.247Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:11.248Z - Time taken for 'hash changed files from watcher' 18.790099999983795ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:11.250Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:11.250Z - Handled GLOB. Handling time: 2. Response time: 3.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:11.255Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:11.255Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:11.255Z - Handled HASH_GLOB. Handling time: 3. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:11.318Z - Time taken for 'build-project-configs' 83.67940000002272ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:11.383Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:11.384Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:11.384Z - Time taken for 'total execution time for createProjectGraph()' 51.960000000020955ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:23.956Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:23.957Z - [WATCHER]: apps/eiot-admin/project.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:24.782Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:24.783Z - [REQUEST]: apps/eiot-admin/project.json
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:24.783Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:24.793Z - Time taken for 'hash changed files from watcher' 23.58049999998184ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:24.796Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:24.797Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:24.797Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:24.801Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:24.802Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:24.802Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:24.868Z - Time taken for 'build-project-configs' 74.38930000003893ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:24.930Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:24.931Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:24.931Z - Time taken for 'total execution time for createProjectGraph()' 46.527499999967404ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:48.115Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:48.116Z - [WATCHER]: apps/eiot-admin/project.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:49.728Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:49.728Z - [REQUEST]: apps/eiot-admin/project.json
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:49.728Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:49.741Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:49.742Z - Time taken for 'hash changed files from watcher' 11.229799999971874ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:49.743Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:49.743Z - Handled GLOB. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:49.750Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:49.750Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:49.751Z - Handled HASH_GLOB. Handling time: 5. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:49.815Z - Time taken for 'build-project-configs' 75.2954000000027ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:49.867Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:49.867Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:00:49.868Z - Time taken for 'total execution time for createProjectGraph()' 45.38540000002831ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:01.547Z - [WATCHER]: apps/eiot-admin/tsconfig.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:01.547Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:04.756Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:04.756Z - [REQUEST]: apps/eiot-admin/tsconfig.json
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:04.756Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:04.766Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:04.767Z - Time taken for 'hash changed files from watcher' 7.430699999968056ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:04.767Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:04.767Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:04.771Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:04.771Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:04.771Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:04.840Z - Time taken for 'build-project-configs' 69.74810000002617ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:04.884Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:04.885Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:04.885Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:04.885Z - Time taken for 'total execution time for createProjectGraph()' 34.384299999976065ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:13.814Z - [WATCHER]: apps/eiot-admin/tsconfig.app.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:13.815Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:20.220Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:20.220Z - [REQUEST]: apps/eiot-admin/tsconfig.app.json
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:20.220Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:20.260Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:20.260Z - Time taken for 'hash changed files from watcher' 3.709199999982957ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:20.261Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:20.261Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:20.268Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:20.269Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:20.269Z - Handled HASH_GLOB. Handling time: 5. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:20.352Z - Time taken for 'build-project-configs' 118.82579999999143ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:20.397Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:20.398Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:20.398Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:20.398Z - Time taken for 'total execution time for createProjectGraph()' 33.93420000001788ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:31.244Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:31.245Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:31.246Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:31.250Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:31.251Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:31.252Z - Time taken for 'total for creating and serializing project graph' 0.6343999999808148ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:31.253Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:31.253Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:31.263Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:31.263Z - Time taken for 'preTasksExecution' 0.4510000000009313ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:31.263Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:31.264Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:31.328Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:31.328Z - Done responding to the client handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:31.328Z - Handled HASH_TASKS. Handling time: 28. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:31.350Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:31.350Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:31.350Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 0. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:32.669Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:32.669Z - Established a connection. Number of open connections: 4
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:32.669Z - Closed a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:32.670Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:32.683Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:32.685Z - Time taken for 'total for creating and serializing project graph' 12.917000000015832ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:32.686Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:32.686Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 13. Response time: 3.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:34.215Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:34.272Z - [REQUEST]: Responding to the client. handleRecordTaskRuns
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:34.272Z - Done responding to the client handleRecordTaskRuns
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:34.272Z - Handled RECORD_TASK_RUNS. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:34.275Z - [REQUEST]: Responding to the client. handleGetFlakyTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:34.275Z - Done responding to the client handleGetFlakyTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:34.275Z - Handled GET_FLAKY_TASKS. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:34.282Z - [REQUEST]: Responding to the client. handleRunPostTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:34.282Z - Time taken for 'postTasksExecution' 0.8053000000072643ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:34.282Z - Done responding to the client handleRunPostTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:34.282Z - Handled POST_TASKS_EXECUTION. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:01:34.291Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:15.629Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:15.631Z - [WATCHER]: package.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:15.733Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:15.734Z - [REQUEST]: package.json
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:15.734Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:15.747Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:15.747Z - Time taken for 'hash changed files from watcher' 0.3260000000009313ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:15.748Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:15.748Z - Handled GLOB. Handling time: 3. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:15.756Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:15.756Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:15.756Z - Handled HASH_GLOB. Handling time: 7. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:15.837Z - Time taken for 'build-project-configs' 93.14039999997476ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:15.898Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:15.899Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:15.899Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:15.899Z - Time taken for 'total execution time for createProjectGraph()' 45.27899999998044ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:16.830Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:16.830Z - Closed a connection. Number of open connections: 0
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:16.830Z - [WATCHER]: Stopping the watcher for D:\EIOT2.SERVER\eiot2-monorepo (sources)
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:16.830Z - [WATCHER]: Stopping the watcher for D:\EIOT2.SERVER\eiot2-monorepo (outputs)
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:16.842Z - Server stopped because: "LOCK_FILES_CHANGED"
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:37.196Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\0f0e9f9229ce501d05c6\d.sock
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:37.200Z - [WATCHER]: Subscribed to changes within: D:\EIOT2.SERVER\eiot2-monorepo (native)
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:37.204Z - Established a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:37.208Z - Closed a connection. Number of open connections: 0
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:37.209Z - Established a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:37.213Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:37.663Z - Time taken for 'Load Nx Plugin: D:\EIOT2.SERVER\eiot2-monorepo\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 440.159ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:37.689Z - Time taken for 'Load Nx Plugin: D:\EIOT2.SERVER\eiot2-monorepo\node_modules\nx\src\plugins\package-json' 467.8267ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:37.701Z - Time taken for 'loadDefaultNxPlugins' 480.45840000000004ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:37.802Z - Time taken for 'Load Nx Plugin: @nx/eslint/plugin' 583.5364999999999ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:37.891Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:37.891Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:37.891Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:37.907Z - Time taken for 'loadSpecifiedNxPlugins' 675.322ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:37.910Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:37.911Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:37.911Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:37.914Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:37.915Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:37.915Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:37.920Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:37.920Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:37.920Z - Handled HASH_GLOB. Handling time: 3. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:40.285Z - Time taken for 'build-project-configs' 2362.2473ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:40.433Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:40.434Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:40.436Z - Time taken for 'total for creating and serializing project graph' 3221.0437ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:40.437Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:40.437Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 3221. Response time: 3.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:40.451Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:40.451Z - Time taken for 'preTasksExecution' 0.8413000000000466ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:40.452Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:40.452Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:40.521Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:40.522Z - Done responding to the client handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:40.522Z - Handled HASH_TASKS. Handling time: 30. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:40.561Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:40.561Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:40.562Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 19. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:41.825Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:41.826Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:41.827Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:41.830Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:41.830Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:41.831Z - Time taken for 'total for creating and serializing project graph' 0.4894000000003871ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:41.832Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:41.832Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:43.188Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:43.234Z - [REQUEST]: Responding to the client. handleRecordTaskRuns
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:43.234Z - Done responding to the client handleRecordTaskRuns
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:43.234Z - Handled RECORD_TASK_RUNS. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:43.236Z - [REQUEST]: Responding to the client. handleGetFlakyTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:43.236Z - Done responding to the client handleGetFlakyTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:43.236Z - Handled GET_FLAKY_TASKS. Handling time: 0. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:43.241Z - [REQUEST]: Responding to the client. handleRunPostTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:43.241Z - Time taken for 'postTasksExecution' 0.2838000000001557ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:43.241Z - Done responding to the client handleRunPostTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:43.241Z - Handled POST_TASKS_EXECUTION. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:02:43.248Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:46.256Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:46.257Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:46.258Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:46.264Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:46.264Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:46.266Z - Time taken for 'total for creating and serializing project graph' 0.8730000000068685ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:46.267Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:46.267Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 3.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:46.294Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:46.294Z - Time taken for 'preTasksExecution' 0.4133999999903608ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:46.294Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:46.294Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:46.352Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:46.352Z - Done responding to the client handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:46.352Z - Handled HASH_TASKS. Handling time: 16. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:46.373Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:46.373Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:46.373Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 0. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:47.960Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:47.965Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:47.965Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:47.968Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:47.968Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:47.969Z - Time taken for 'total for creating and serializing project graph' 0.4043999999994412ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:47.969Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:47.970Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:49.387Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:49.434Z - [REQUEST]: Responding to the client. handleRecordTaskRuns
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:49.434Z - Done responding to the client handleRecordTaskRuns
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:49.434Z - Handled RECORD_TASK_RUNS. Handling time: 0. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:49.435Z - [REQUEST]: Responding to the client. handleGetFlakyTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:49.436Z - Done responding to the client handleGetFlakyTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:49.436Z - Handled GET_FLAKY_TASKS. Handling time: 0. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:49.440Z - [REQUEST]: Responding to the client. handleRunPostTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:49.440Z - Time taken for 'postTasksExecution' 0.25510000000940636ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:49.440Z - Done responding to the client handleRunPostTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:49.440Z - Handled POST_TASKS_EXECUTION. Handling time: 0. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:03:49.448Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:01.513Z - [WATCHER]: postcss.config.js was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:01.514Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:01.639Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:01.639Z - [REQUEST]: postcss.config.js
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:01.639Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:01.650Z - Time taken for 'hash changed files from watcher' 24.444000000017695ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:01.654Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:01.654Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:01.654Z - Handled GLOB. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:01.657Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:01.657Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:01.658Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:01.742Z - Time taken for 'build-project-configs' 89.38959999999497ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:01.809Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:01.810Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:01.810Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:01.810Z - Time taken for 'total execution time for createProjectGraph()' 49.94140000001062ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:25.268Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:25.270Z - [WATCHER]: postcss.config.js was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:25.489Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:25.489Z - [REQUEST]: postcss.config.js
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:25.489Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:25.504Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:25.505Z - Time taken for 'hash changed files from watcher' 17.92949999999837ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:25.505Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:25.505Z - Handled GLOB. Handling time: 3. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:25.511Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:25.511Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:25.511Z - Handled HASH_GLOB. Handling time: 4. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:25.586Z - Time taken for 'build-project-configs' 85.76780000000144ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:25.631Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:25.632Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:25.632Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:25.632Z - Time taken for 'total execution time for createProjectGraph()' 38.00130000000354ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:46.655Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:46.657Z - [WATCHER]: package.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:47.058Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:47.059Z - [REQUEST]: package.json
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:47.059Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:47.081Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:47.082Z - Time taken for 'hash changed files from watcher' 0.2390999999770429ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:47.082Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:47.082Z - Handled GLOB. Handling time: 9. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:47.090Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:47.091Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:47.091Z - Handled HASH_GLOB. Handling time: 7. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:47.198Z - Time taken for 'build-project-configs' 117.25909999999567ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:47.270Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:47.271Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:47.271Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:47.271Z - Time taken for 'total execution time for createProjectGraph()' 64.41209999998682ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:48.606Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:48.607Z - Closed a connection. Number of open connections: 0
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:48.607Z - [WATCHER]: Stopping the watcher for D:\EIOT2.SERVER\eiot2-monorepo (sources)
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:48.607Z - [WATCHER]: Stopping the watcher for D:\EIOT2.SERVER\eiot2-monorepo (outputs)
[NX v21.2.2 Daemon Server] - 2025-07-07T11:06:48.616Z - Server stopped because: "LOCK_FILES_CHANGED"
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:20.843Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\0f0e9f9229ce501d05c6\d.sock
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:20.847Z - [WATCHER]: Subscribed to changes within: D:\EIOT2.SERVER\eiot2-monorepo (native)
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:20.852Z - Established a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:20.853Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:20.855Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:20.858Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:21.355Z - Time taken for 'Load Nx Plugin: D:\EIOT2.SERVER\eiot2-monorepo\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 489.5073ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:21.386Z - Time taken for 'Load Nx Plugin: D:\EIOT2.SERVER\eiot2-monorepo\node_modules\nx\src\plugins\package-json' 522.0871ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:21.391Z - Time taken for 'loadDefaultNxPlugins' 528.4893ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:21.500Z - Time taken for 'Load Nx Plugin: @nx/eslint/plugin' 639.0274ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:21.629Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:21.630Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:21.630Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:21.651Z - Time taken for 'loadSpecifiedNxPlugins' 769.9137000000001ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:21.654Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:21.655Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:21.656Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:21.659Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:21.659Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:21.659Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:21.668Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:21.668Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:21.668Z - Handled HASH_GLOB. Handling time: 4. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:24.164Z - Time taken for 'build-project-configs' 2502.8554ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:24.345Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:24.349Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:24.351Z - Time taken for 'total for creating and serializing project graph' 3490.8582ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:24.351Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:24.351Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 3491. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:24.369Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:24.369Z - Time taken for 'preTasksExecution' 1.4340000000001965ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:24.370Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:24.370Z - Handled PRE_TASKS_EXECUTION. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:24.476Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:24.477Z - Done responding to the client handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:24.477Z - Handled HASH_TASKS. Handling time: 38. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:24.518Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:24.519Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:24.519Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 19. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:26.446Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:26.449Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:26.450Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:26.455Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:26.455Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:26.456Z - Time taken for 'total for creating and serializing project graph' 0.45489999999972497ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:26.457Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:26.457Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:28.346Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:28.409Z - [REQUEST]: Responding to the client. handleRecordTaskRuns
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:28.410Z - Done responding to the client handleRecordTaskRuns
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:28.410Z - Handled RECORD_TASK_RUNS. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:28.412Z - [REQUEST]: Responding to the client. handleGetFlakyTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:28.412Z - Done responding to the client handleGetFlakyTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:28.412Z - Handled GET_FLAKY_TASKS. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:28.418Z - [REQUEST]: Responding to the client. handleRunPostTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:28.419Z - Time taken for 'postTasksExecution' 0.3867000000000189ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:28.419Z - Done responding to the client handleRunPostTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:28.419Z - Handled POST_TASKS_EXECUTION. Handling time: 0. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:07:28.428Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T11:08:43.920Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:08:43.922Z - [WATCHER]: package.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:08:44.025Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:08:44.025Z - [REQUEST]: package.json
[NX v21.2.2 Daemon Server] - 2025-07-07T11:08:44.025Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:08:44.048Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:08:44.048Z - Time taken for 'hash changed files from watcher' 0.8247000000119442ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:08:44.049Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:08:44.049Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:08:44.052Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:08:44.053Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:08:44.053Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:08:44.143Z - Time taken for 'build-project-configs' 102.42339999999967ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:08:44.197Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:08:44.198Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:08:44.198Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:08:44.198Z - Time taken for 'total execution time for createProjectGraph()' 45.713099999993574ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:08:45.036Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:08:45.037Z - Closed a connection. Number of open connections: 0
[NX v21.2.2 Daemon Server] - 2025-07-07T11:08:45.037Z - [WATCHER]: Stopping the watcher for D:\EIOT2.SERVER\eiot2-monorepo (sources)
[NX v21.2.2 Daemon Server] - 2025-07-07T11:08:45.037Z - [WATCHER]: Stopping the watcher for D:\EIOT2.SERVER\eiot2-monorepo (outputs)
[NX v21.2.2 Daemon Server] - 2025-07-07T11:08:45.042Z - Server stopped because: "LOCK_FILES_CHANGED"
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:01.104Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\0f0e9f9229ce501d05c6\d.sock
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:01.110Z - [WATCHER]: Subscribed to changes within: D:\EIOT2.SERVER\eiot2-monorepo (native)
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:01.115Z - Established a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:01.116Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:01.119Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:01.121Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:01.605Z - Time taken for 'Load Nx Plugin: D:\EIOT2.SERVER\eiot2-monorepo\node_modules\nx\src\plugins\package-json' 477.5792ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:01.632Z - Time taken for 'Load Nx Plugin: D:\EIOT2.SERVER\eiot2-monorepo\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 504.82219999999995ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:01.655Z - Time taken for 'loadDefaultNxPlugins' 529.4773ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:01.788Z - Time taken for 'Load Nx Plugin: @nx/eslint/plugin' 663.8446ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:01.892Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:01.893Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:01.893Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:01.917Z - Time taken for 'loadSpecifiedNxPlugins' 769.9390000000001ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:01.920Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:01.921Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:01.922Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:01.925Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:01.925Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:01.925Z - Handled GLOB. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:01.931Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:01.931Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:01.931Z - Handled HASH_GLOB. Handling time: 3. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:04.147Z - Time taken for 'build-project-configs' 2222.7896ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:04.281Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:04.283Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:04.284Z - Time taken for 'total for creating and serializing project graph' 3162.0466ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:04.285Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:04.285Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 3162. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:04.302Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:04.303Z - Time taken for 'preTasksExecution' 1.353900000000067ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:04.303Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:04.303Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:04.382Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:04.383Z - Done responding to the client handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:04.383Z - Handled HASH_TASKS. Handling time: 26. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:04.429Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:04.429Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:04.429Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 19. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:06.073Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:06.074Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:06.075Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:06.077Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:06.078Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:06.079Z - Time taken for 'total for creating and serializing project graph' 0.40279999999984284ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:06.080Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:06.080Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:39.494Z - Established a connection. Number of open connections: 4
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:39.495Z - Closed a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:39.497Z - Established a connection. Number of open connections: 4
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:39.501Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:39.502Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:39.504Z - Time taken for 'total for creating and serializing project graph' 0.643900000002759ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:39.505Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:39.505Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 3.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:39.516Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:39.516Z - Time taken for 'preTasksExecution' 0.36539999999513384ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:39.517Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:39.517Z - Handled PRE_TASKS_EXECUTION. Handling time: 0. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:39.581Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:39.582Z - Done responding to the client handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:39.582Z - Handled HASH_TASKS. Handling time: 13. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:39.601Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:39.601Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:39.601Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:40.534Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:40.607Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:41.059Z - Established a connection. Number of open connections: 5
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:41.064Z - Closed a connection. Number of open connections: 4
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:41.065Z - Established a connection. Number of open connections: 5
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:41.068Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:41.069Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:41.070Z - Time taken for 'total for creating and serializing project graph' 0.4801000000006752ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:41.070Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:41.070Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:41.291Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:41.677Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:41.738Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:41.812Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:41.863Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:41.949Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:42.540Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:42.611Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:42.707Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:42.793Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:42.918Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:43.017Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:43.176Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:43.233Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:43.340Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:43.705Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:43.757Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:43.841Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:44.148Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:44.794Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:44.980Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:45.188Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:45.272Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:45.324Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:45.390Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:45.489Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:45.613Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:46.025Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:46.103Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:46.189Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:47.919Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:47.976Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:48.267Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:48.336Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:48.580Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:49.170Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:50.388Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:50.440Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:50.505Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:50.562Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:50.616Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:50.666Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:50.726Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:50.786Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:50.839Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:50.898Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:50.953Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:51.019Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:51.071Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:51.128Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:51.185Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:51.287Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:51.363Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:51.483Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:51.574Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:52.087Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:52.155Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:52.219Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:52.271Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:52.329Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:52.382Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:52.435Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:52.489Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:52.547Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:52.601Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:52.673Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:52.796Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:52.901Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:52.957Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:53.025Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:53.087Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:53.164Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:53.223Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:53.451Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:53.506Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:53.560Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:53.663Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:53.720Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:53.784Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:53.854Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:53.906Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:59.278Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:10:59.396Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:12:57.625Z - [WATCHER]: apps/shell/src/app/app.routes.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:12:57.626Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:12:57.758Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:12:57.759Z - [REQUEST]: apps/shell/src/app/app.routes.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:12:57.759Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:12:57.789Z - Time taken for 'hash changed files from watcher' 32.79550000000745ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:12:57.837Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:12:57.837Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:12:57.837Z - Handled GLOB. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:12:57.842Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:12:57.842Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:12:57.842Z - Handled HASH_GLOB. Handling time: 2. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:12:57.972Z - Time taken for 'build-project-configs' 199.87669999999343ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:12:58.097Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:12:58.098Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:12:58.098Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:12:58.098Z - Time taken for 'total execution time for createProjectGraph()' 95.89979999998468ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:13:17.444Z - Closed a connection. Number of open connections: 4
[NX v21.2.2 Daemon Server] - 2025-07-07T11:13:17.445Z - Closed a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T11:13:38.011Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:13:38.014Z - [WATCHER]: apps/shell/src/app/home was deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:13:38.214Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:13:38.214Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:13:38.214Z - [REQUEST]: apps/shell/src/app/home
[NX v21.2.2 Daemon Server] - 2025-07-07T11:13:38.231Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:13:38.232Z - Time taken for 'hash changed files from watcher' 0.15450000000419095ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:13:38.232Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:13:38.232Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:13:38.237Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:13:38.238Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:13:38.238Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:13:38.321Z - Time taken for 'build-project-configs' 96.25819999998203ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:13:38.392Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:13:38.393Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:13:38.393Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:13:38.393Z - Time taken for 'total execution time for createProjectGraph()' 54.08869999999297ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:05.531Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:05.533Z - [WATCHER]: apps/eiot-admin/src/app/nx-welcome.ts was deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:05.935Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:05.935Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:05.935Z - [REQUEST]: apps/eiot-admin/src/app/nx-welcome.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:05.952Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:05.952Z - Time taken for 'hash changed files from watcher' 0.17089999999734573ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:05.953Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:05.953Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:05.960Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:05.961Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:05.961Z - Handled HASH_GLOB. Handling time: 5. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:06.056Z - Time taken for 'build-project-configs' 109.16180000000168ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:06.109Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:06.109Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:06.109Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:06.110Z - Time taken for 'total execution time for createProjectGraph()' 46.021100000012666ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:07.053Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:09.298Z - [WATCHER]: apps/eiot-admin/src/app/app.html was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:09.299Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:10.100Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:10.100Z - [REQUEST]: apps/eiot-admin/src/app/app.html
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:10.100Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:10.121Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:10.121Z - Time taken for 'hash changed files from watcher' 0.27909999998519197ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:10.122Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:10.122Z - Handled GLOB. Handling time: 9. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:10.141Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:10.145Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:10.145Z - Handled HASH_GLOB. Handling time: 16. Response time: 4.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:10.211Z - Time taken for 'build-project-configs' 103.9825000000128ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:10.272Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:10.273Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:10.273Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:10.273Z - Time taken for 'total execution time for createProjectGraph()' 50.63799999997718ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:19.872Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:19.873Z - [WATCHER]: apps/eiot-admin/src/app/app.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:20.551Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:21.474Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:21.474Z - [REQUEST]: apps/eiot-admin/src/app/app.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:21.474Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:21.485Z - Time taken for 'hash changed files from watcher' 0.23960000000079162ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:21.487Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:21.488Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:21.488Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:21.492Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:21.492Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:21.492Z - Handled HASH_GLOB. Handling time: 2. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:21.571Z - Time taken for 'build-project-configs' 88.96630000000005ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:21.624Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:21.625Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:21.625Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:21.625Z - Time taken for 'total execution time for createProjectGraph()' 42.29360000000452ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:38.387Z - Established a connection. Number of open connections: 4
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:38.388Z - Closed a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:38.389Z - Established a connection. Number of open connections: 4
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:38.393Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:38.393Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:38.395Z - Time taken for 'total for creating and serializing project graph' 0.6529999999911524ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:38.397Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:38.397Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 4.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:38.410Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:38.410Z - Time taken for 'preTasksExecution' 0.3802999999606982ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:38.410Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:38.410Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:38.493Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:38.494Z - Done responding to the client handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:38.494Z - Handled HASH_TASKS. Handling time: 24. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:38.515Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:38.515Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:38.515Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:40.291Z - Established a connection. Number of open connections: 5
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:40.294Z - Closed a connection. Number of open connections: 4
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:40.295Z - Established a connection. Number of open connections: 5
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:40.298Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:40.299Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:40.300Z - Time taken for 'total for creating and serializing project graph' 0.436199999996461ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:40.300Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:40.300Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:50.479Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:53.517Z - [WATCHER]: apps/shell/src/app/app.routes.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:53.518Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:53.738Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:53.738Z - [REQUEST]: apps/shell/src/app/app.routes.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:53.738Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:53.785Z - Time taken for 'hash changed files from watcher' 119.39970000000903ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:53.788Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:53.789Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:53.789Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:53.795Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:53.795Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:53.795Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:53.882Z - Time taken for 'build-project-configs' 96.49180000001797ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:53.967Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:53.968Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:53.968Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:53.968Z - Time taken for 'total execution time for createProjectGraph()' 66.63790000003064ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:57.209Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:57.555Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:57.647Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:14:57.738Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:09.872Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:09.881Z - [WATCHER]: apps/shell/src/app/app.routes.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:10.100Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:10.100Z - [REQUEST]: apps/shell/src/app/app.routes.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:10.100Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:10.118Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:10.118Z - Time taken for 'hash changed files from watcher' 16.79330000001937ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:10.119Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:10.119Z - Handled GLOB. Handling time: 7. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:10.127Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:10.127Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:10.128Z - Handled HASH_GLOB. Handling time: 7. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:10.323Z - Time taken for 'build-project-configs' 212.26620000001276ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:10.426Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:10.427Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:10.427Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:10.427Z - Time taken for 'total execution time for createProjectGraph()' 80.58329999999842ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:14.207Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:14.208Z - [WATCHER]: apps/shell/src/app/app.routes.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:14.616Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:14.616Z - [REQUEST]: apps/shell/src/app/app.routes.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:14.616Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:14.641Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:14.641Z - Time taken for 'hash changed files from watcher' 0.8932999999960884ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:14.642Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:14.642Z - Handled GLOB. Handling time: 10. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:14.655Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:14.660Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:14.661Z - Handled HASH_GLOB. Handling time: 12. Response time: 6.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:14.735Z - Time taken for 'build-project-configs' 105.33399999997346ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:14.806Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:14.807Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:14.807Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:14.807Z - Time taken for 'total execution time for createProjectGraph()' 47.40870000002906ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:16.118Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:16.216Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:20.443Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:20.445Z - [WATCHER]: apps/shell/src/app/app.routes.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:21.246Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:21.246Z - [REQUEST]: apps/shell/src/app/app.routes.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:21.246Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:21.256Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:21.256Z - Time taken for 'hash changed files from watcher' 0.30890000000363216ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:21.257Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:21.257Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:21.260Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:21.260Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:21.260Z - Handled HASH_GLOB. Handling time: 2. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:21.345Z - Time taken for 'build-project-configs' 87.74080000002868ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:21.405Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:21.406Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:21.406Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:21.406Z - Time taken for 'total execution time for createProjectGraph()' 48.621699999959674ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:44.768Z - [WATCHER]: apps/shell/src/app/home.component.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:44.768Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:46.369Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:46.369Z - [REQUEST]: apps/shell/src/app/home.component.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:46.369Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:46.380Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:46.381Z - Time taken for 'hash changed files from watcher' 0.22260000003734604ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:46.382Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:46.382Z - Handled GLOB. Handling time: 0. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:46.385Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:46.386Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:46.386Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:46.438Z - Time taken for 'build-project-configs' 62.129400000034366ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:46.485Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:46.485Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:46.486Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:15:46.486Z - Time taken for 'total execution time for createProjectGraph()' 35.5050999999512ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:22.174Z - [WATCHER]: apps/shell/src/app/app.routes.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:22.175Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:24.110Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:24.183Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:25.376Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:25.376Z - [REQUEST]: apps/shell/src/app/app.routes.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:25.376Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:25.389Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:25.389Z - Time taken for 'hash changed files from watcher' 0.22190000000409782ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:25.390Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:25.391Z - Handled GLOB. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:25.395Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:25.395Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:25.395Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:25.471Z - Time taken for 'build-project-configs' 86.32069999998203ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:25.526Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:25.527Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:25.527Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:25.527Z - Time taken for 'total execution time for createProjectGraph()' 46.71960000001127ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:49.750Z - Established a connection. Number of open connections: 6
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:49.751Z - Closed a connection. Number of open connections: 5
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:49.752Z - Established a connection. Number of open connections: 6
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:49.756Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:49.756Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:49.757Z - Time taken for 'total for creating and serializing project graph' 0.5471000000252388ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:49.758Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:49.759Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 0. Response time: 3.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:49.768Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:49.769Z - Time taken for 'preTasksExecution' 0.24689999996917322ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:49.769Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:49.769Z - Handled PRE_TASKS_EXECUTION. Handling time: 0. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:49.850Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:49.850Z - Done responding to the client handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:49.850Z - Handled HASH_TASKS. Handling time: 24. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:49.873Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:49.874Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:49.874Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 0. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:51.696Z - Established a connection. Number of open connections: 7
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:51.697Z - Closed a connection. Number of open connections: 6
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:51.698Z - Established a connection. Number of open connections: 7
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:51.701Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:51.702Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:51.703Z - Time taken for 'total for creating and serializing project graph' 0.4183000000193715ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:51.703Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:16:51.703Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:17:10.860Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:17:22.882Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:17:23.772Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:03.435Z - Closed a connection. Number of open connections: 6
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:03.435Z - Closed a connection. Number of open connections: 5
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:07.188Z - Established a connection. Number of open connections: 6
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:07.189Z - Closed a connection. Number of open connections: 5
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:07.189Z - Established a connection. Number of open connections: 6
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:07.196Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:07.197Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:07.199Z - Time taken for 'total for creating and serializing project graph' 1.2566000000224449ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:07.199Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:07.199Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:07.209Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:07.209Z - Time taken for 'preTasksExecution' 0.2442000000155531ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:07.210Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:07.210Z - Handled PRE_TASKS_EXECUTION. Handling time: 0. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:07.287Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:07.288Z - Done responding to the client handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:07.288Z - Handled HASH_TASKS. Handling time: 16. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:07.308Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:07.308Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:07.308Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:09.548Z - Established a connection. Number of open connections: 7
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:09.554Z - Closed a connection. Number of open connections: 6
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:09.554Z - Established a connection. Number of open connections: 7
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:09.558Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:09.559Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:09.561Z - Time taken for 'total for creating and serializing project graph' 0.8104999999632128ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:09.561Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:09.561Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:09.761Z - Closed a connection. Number of open connections: 6
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:09.783Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:09.784Z - Done responding to the client recordOutputsHash
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:09.784Z - Handled RECORD_OUTPUTS_HASH. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:09.795Z - [REQUEST]: Responding to the client. handleRecordTaskRuns
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:09.795Z - Done responding to the client handleRecordTaskRuns
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:09.795Z - Handled RECORD_TASK_RUNS. Handling time: 3. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:09.798Z - [REQUEST]: Responding to the client. handleGetFlakyTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:09.798Z - Done responding to the client handleGetFlakyTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:09.798Z - Handled GET_FLAKY_TASKS. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:09.803Z - [REQUEST]: Responding to the client. handleRunPostTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:09.804Z - Time taken for 'postTasksExecution' 0.2972000000299886ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:09.804Z - Done responding to the client handleRunPostTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:09.804Z - Handled POST_TASKS_EXECUTION. Handling time: 0. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:09.813Z - Closed a connection. Number of open connections: 5
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:19.263Z - Closed a connection. Number of open connections: 4
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:19.264Z - Closed a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:56.885Z - Established a connection. Number of open connections: 4
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:56.886Z - Closed a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:56.888Z - Established a connection. Number of open connections: 4
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:56.892Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:56.893Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:56.895Z - Time taken for 'total for creating and serializing project graph' 0.6742000000085682ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:56.895Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:56.895Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:56.923Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:56.923Z - Time taken for 'preTasksExecution' 0.49879999994300306ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:56.923Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:56.923Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:56.973Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:56.973Z - Done responding to the client handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:56.973Z - Handled HASH_TASKS. Handling time: 14. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:56.991Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:56.991Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:56.991Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:58.447Z - Established a connection. Number of open connections: 5
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:58.452Z - Closed a connection. Number of open connections: 4
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:58.453Z - Established a connection. Number of open connections: 5
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:58.456Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:58.457Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:58.458Z - Time taken for 'total for creating and serializing project graph' 0.4717999999411404ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:58.459Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:18:58.459Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:19:08.820Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:20:53.533Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:20:53.537Z - [WATCHER]: apps/shell/src/app/app.routes.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:20:53.676Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:20:53.676Z - [REQUEST]: apps/shell/src/app/app.routes.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:20:53.676Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:20:53.683Z - Time taken for 'hash changed files from watcher' 37.40669999993406ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:20:53.743Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:20:53.743Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:20:53.743Z - Handled GLOB. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:20:53.749Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:20:53.749Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:20:53.749Z - Handled HASH_GLOB. Handling time: 2. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:20:53.911Z - Time taken for 'build-project-configs' 224.66030000010505ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:20:53.992Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:20:53.993Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:20:53.993Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:20:53.993Z - Time taken for 'total execution time for createProjectGraph()' 64.22080000001006ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:21:54.266Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:21:54.322Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:21:54.396Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:04.326Z - [WATCHER]: apps/shell/src/app/app.routes.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:04.327Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:04.551Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:04.551Z - [REQUEST]: apps/shell/src/app/app.routes.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:04.551Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:04.565Z - Time taken for 'hash changed files from watcher' 22.389699999941513ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:04.586Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:04.587Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:04.587Z - Handled GLOB. Handling time: 19. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:04.592Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:04.598Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:04.598Z - Handled HASH_GLOB. Handling time: 3. Response time: 6.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:04.682Z - Time taken for 'build-project-configs' 117.88359999994282ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:04.765Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:04.766Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:04.766Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:04.766Z - Time taken for 'total execution time for createProjectGraph()' 57.9028999999864ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:17.481Z - [WATCHER]: apps/shell/src/app/app.routes.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:17.482Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:17.885Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:17.886Z - [REQUEST]: apps/shell/src/app/app.routes.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:17.886Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:17.906Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:17.906Z - Time taken for 'hash changed files from watcher' 1.1833000000333413ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:17.907Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:17.907Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:17.910Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:17.911Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:17.911Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:17.937Z - Time taken for 'build-project-configs' 39.36920000007376ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:18.011Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:18.013Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:18.013Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:18.013Z - Time taken for 'total execution time for createProjectGraph()' 59.008099999977276ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:35.945Z - [WATCHER]: apps/shell/src/app/home.component.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:35.945Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:36.746Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:36.746Z - [REQUEST]: apps/shell/src/app/home.component.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:36.747Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:36.758Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:36.758Z - Time taken for 'hash changed files from watcher' 0.33039999997708946ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:36.758Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:36.759Z - Handled GLOB. Handling time: 2. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:36.762Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:36.762Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:36.762Z - Handled HASH_GLOB. Handling time: 2. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:36.822Z - Time taken for 'build-project-configs' 68.4381000000285ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:36.873Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:36.873Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:36.873Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:36.874Z - Time taken for 'total execution time for createProjectGraph()' 38.07739999995101ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:49.975Z - [WATCHER]: apps/shell/src/app/home.component.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:49.976Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:51.577Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:51.578Z - [REQUEST]: apps/shell/src/app/home.component.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:51.578Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:51.606Z - Time taken for 'hash changed files from watcher' 0.7545000000391155ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:51.610Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:51.611Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:51.612Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:51.617Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:51.618Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:51.618Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:51.736Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:51.802Z - Time taken for 'build-project-configs' 209.1428999999771ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:51.852Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:51.853Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:51.853Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:22:51.853Z - Time taken for 'total execution time for createProjectGraph()' 40.60959999996703ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:23:50.543Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:05.725Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:05.742Z - [WATCHER]: libs/shared/ui/src/lib/fuse-layout/fuse-layout.component.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:08.966Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:08.966Z - [REQUEST]: libs/shared/ui/src/lib/fuse-layout/fuse-layout.component.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:08.966Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:09.001Z - Time taken for 'hash changed files from watcher' 17.786100000026636ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:09.020Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:09.021Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:09.021Z - Handled GLOB. Handling time: 17. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:09.028Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:09.034Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:09.034Z - Handled HASH_GLOB. Handling time: 6. Response time: 6.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:09.139Z - Time taken for 'build-project-configs' 133.83360000001267ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:09.276Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:09.277Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:09.277Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:09.277Z - Time taken for 'total execution time for createProjectGraph()' 80.2221000000136ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:25.434Z - [WATCHER]: libs/shared/ui/src/lib/fuse-card/fuse-card.component.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:25.435Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:31.859Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:31.859Z - [REQUEST]: libs/shared/ui/src/lib/fuse-card/fuse-card.component.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:31.859Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:31.866Z - Time taken for 'hash changed files from watcher' 23.622699999948964ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:31.868Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:31.869Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:31.869Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:31.872Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:31.872Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:31.872Z - Handled HASH_GLOB. Handling time: 2. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:31.930Z - Time taken for 'build-project-configs' 61.31750000000466ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:31.984Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:31.985Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:31.985Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:31.985Z - Time taken for 'total execution time for createProjectGraph()' 45.99070000008214ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:55.352Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:55.354Z - [WATCHER]: libs/shared/ui/src/lib/fuse-stats/fuse-stats.component.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:26:57.002Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:01.794Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:01.794Z - [REQUEST]: libs/shared/ui/src/lib/fuse-stats/fuse-stats.component.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:01.794Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:01.809Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:01.810Z - Time taken for 'hash changed files from watcher' 38.79869999992661ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:01.810Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:01.811Z - Handled GLOB. Handling time: 0. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:01.815Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:01.816Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:01.816Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:01.888Z - Time taken for 'build-project-configs' 81.67040000006091ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:01.956Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:01.956Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:01.957Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:01.957Z - Time taken for 'total execution time for createProjectGraph()' 57.61280000000261ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:06.952Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:06.954Z - [WATCHER]: libs/shared/ui/src/index.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:13.366Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:13.366Z - [REQUEST]: libs/shared/ui/src/index.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:13.366Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:13.376Z - Time taken for 'hash changed files from watcher' 8.891099999891594ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:13.380Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:13.381Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:13.381Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:13.384Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:13.385Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:13.385Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:13.463Z - Time taken for 'build-project-configs' 87.4719000000041ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:13.515Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:13.516Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:13.516Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:13.517Z - Time taken for 'total execution time for createProjectGraph()' 44.291499999933876ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:22.522Z - [WATCHER]: apps/eiot-admin/src/app/dashboard/dashboard.component.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:22.523Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:24.130Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:25.875Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:26.095Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:26.149Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:26.243Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:26.298Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:26.398Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:26.538Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:26.590Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:28.925Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:28.925Z - [REQUEST]: apps/eiot-admin/src/app/dashboard/dashboard.component.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:28.925Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:28.937Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:28.937Z - Time taken for 'hash changed files from watcher' 0.5058999999891967ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:28.937Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:28.938Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:28.944Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:28.945Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:28.945Z - Handled HASH_GLOB. Handling time: 5. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:29.039Z - Time taken for 'build-project-configs' 95.10949999990407ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:29.436Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:29.436Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:29.437Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:29.437Z - Time taken for 'total execution time for createProjectGraph()' 395.32649999985006ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:44.559Z - [WATCHER]: apps/eiot-admin/src/app/dashboard/dashboard.component.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:44.560Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:46.369Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:50.960Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:50.961Z - [REQUEST]: apps/eiot-admin/src/app/dashboard/dashboard.component.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:50.961Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:50.970Z - Time taken for 'hash changed files from watcher' 0.2395999999716878ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:50.973Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:50.974Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:50.974Z - Handled GLOB. Handling time: 0. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:50.977Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:50.977Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:50.977Z - Handled HASH_GLOB. Handling time: 2. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:51.035Z - Time taken for 'build-project-configs' 67.71540000010282ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:51.091Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:51.092Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:51.092Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:51.092Z - Time taken for 'total execution time for createProjectGraph()' 45.36740000010468ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:56.999Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:57.004Z - [WATCHER]: apps/eiot-admin/src/app/dashboard/dashboard.component.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:27:57.982Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:03.511Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:03.511Z - [REQUEST]: apps/eiot-admin/src/app/dashboard/dashboard.component.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:03.511Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:03.522Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:03.522Z - Time taken for 'hash changed files from watcher' 0.208199999993667ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:03.522Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:03.522Z - Handled GLOB. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:03.525Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:03.527Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:03.527Z - Handled HASH_GLOB. Handling time: 2. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:03.634Z - Time taken for 'build-project-configs' 113.77459999988787ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:03.689Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:03.689Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:03.689Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:03.689Z - Time taken for 'total execution time for createProjectGraph()' 47.46049999981187ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:46.644Z - [WATCHER]: apps/eiot-admin/src/app/dashboard/dashboard.component.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:46.645Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:48.041Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:48.211Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:48.302Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:48.333Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:48.411Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:48.454Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:50.102Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:50.493Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:50.651Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:50.734Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:50.785Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:53.047Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:53.047Z - [REQUEST]: apps/eiot-admin/src/app/dashboard/dashboard.component.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:53.047Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:53.058Z - Time taken for 'hash changed files from watcher' 0.3203999998513609ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:53.060Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:53.061Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:53.061Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:53.064Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:53.064Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:53.064Z - Handled HASH_GLOB. Handling time: 2. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:53.120Z - Time taken for 'build-project-configs' 64.73579999990761ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:53.189Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:53.189Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:53.189Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:28:53.190Z - Time taken for 'total execution time for createProjectGraph()' 53.917799999937415ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:10.211Z - [WATCHER]: apps/eiot-admin/src/app/dashboard/dashboard.component.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:10.211Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:11.143Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:16.613Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:16.613Z - [REQUEST]: apps/eiot-admin/src/app/dashboard/dashboard.component.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:16.613Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:16.626Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:16.626Z - Time taken for 'hash changed files from watcher' 0.2547999999951571ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:16.627Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:16.627Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:16.630Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:16.631Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:16.631Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:16.713Z - Time taken for 'build-project-configs' 90.8460999999661ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:16.763Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:16.764Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:16.764Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:16.764Z - Time taken for 'total execution time for createProjectGraph()' 41.44680000003427ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:22.699Z - [WATCHER]: apps/eiot-admin/src/app/auth/login/login.component.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:22.701Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:24.097Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:29.101Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:29.101Z - [REQUEST]: apps/eiot-admin/src/app/auth/login/login.component.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:29.102Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:29.109Z - Time taken for 'hash changed files from watcher' 0.21820000000298023ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:29.111Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:29.112Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:29.112Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:29.115Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:29.116Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:29.116Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:29.174Z - Time taken for 'build-project-configs' 65.75600000005215ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:29.227Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:29.228Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:29.228Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:29.229Z - Time taken for 'total execution time for createProjectGraph()' 40.403399999951944ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:36.762Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:36.763Z - [WATCHER]: apps/eiot-admin/src/app/auth/login/login.component.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:37.454Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:39.280Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:39.576Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:43.165Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:43.165Z - [REQUEST]: apps/eiot-admin/src/app/auth/login/login.component.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:43.165Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:43.179Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:43.179Z - Time taken for 'hash changed files from watcher' 0.32730000000447035ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:43.180Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:43.181Z - Handled GLOB. Handling time: 2. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:43.184Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:43.185Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:43.185Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:43.250Z - Time taken for 'build-project-configs' 74.73589999997057ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:43.301Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:43.302Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:43.302Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:29:43.302Z - Time taken for 'total execution time for createProjectGraph()' 44.79480000003241ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:15.608Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:15.610Z - [WATCHER]: apps/eiot-admin/src/app/auth/login/login.component.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:16.556Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:17.157Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:22.011Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:22.012Z - [REQUEST]: apps/eiot-admin/src/app/auth/login/login.component.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:22.012Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:22.020Z - Time taken for 'hash changed files from watcher' 0.2186000000219792ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:22.023Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:22.024Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:22.024Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:22.027Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:22.027Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:22.028Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:22.109Z - Time taken for 'build-project-configs' 89.00370000000112ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:22.164Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:22.165Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:22.165Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:22.165Z - Time taken for 'total execution time for createProjectGraph()' 46.24460000009276ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:27.143Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:27.145Z - [WATCHER]: apps/eiot-admin/src/app/auth/login/login.component.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:28.115Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:33.551Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:33.551Z - [REQUEST]: apps/eiot-admin/src/app/auth/login/login.component.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:33.551Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:33.577Z - Time taken for 'hash changed files from watcher' 0.7761999999638647ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:33.583Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:33.584Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:33.584Z - Handled GLOB. Handling time: 3. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:33.590Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:33.590Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:33.590Z - Handled HASH_GLOB. Handling time: 4. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:33.664Z - Time taken for 'build-project-configs' 100.11489999992773ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:33.727Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:33.728Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:33.728Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:30:33.728Z - Time taken for 'total execution time for createProjectGraph()' 48.18489999999292ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:31:27.722Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:31:28.794Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:31:28.950Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:31:29.000Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:33:22.172Z - Closed a connection. Number of open connections: 4
[NX v21.2.2 Daemon Server] - 2025-07-07T11:33:22.173Z - Closed a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T11:33:43.707Z - [WATCHER]: libs/fuse/README.md was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:33:43.708Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:33:50.153Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:33:50.154Z - [REQUEST]: libs/fuse/README.md
[NX v21.2.2 Daemon Server] - 2025-07-07T11:33:50.154Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:33:50.182Z - Time taken for 'hash changed files from watcher' 41.90529999998398ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:33:50.187Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:33:50.190Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:33:50.190Z - Handled GLOB. Handling time: 2. Response time: 3.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:33:50.195Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:33:50.196Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:33:50.196Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:33:50.277Z - Time taken for 'build-project-configs' 99.1577999999281ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:33:50.341Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:33:50.342Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:33:50.343Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:33:50.343Z - Time taken for 'total execution time for createProjectGraph()' 44.530199999921024ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:33:55.952Z - [WATCHER]: libs/fuse/package.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:33:55.953Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:02.368Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:02.368Z - [REQUEST]: libs/fuse/package.json
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:02.368Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:02.382Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:02.383Z - Time taken for 'hash changed files from watcher' 13.07620000001043ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:02.383Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:02.383Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:02.387Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:02.387Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:02.387Z - Handled HASH_GLOB. Handling time: 3. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:02.458Z - Time taken for 'build-project-configs' 82.09169999998994ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:02.501Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:02.502Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:02.502Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:02.502Z - Time taken for 'total execution time for createProjectGraph()' 32.52709999983199ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:08.514Z - [WATCHER]: libs/fuse/src/index.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:08.515Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:14.918Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:14.918Z - [REQUEST]: libs/fuse/src/index.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:14.918Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:14.940Z - Time taken for 'hash changed files from watcher' 0.6841000001877546ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:14.945Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:14.946Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:14.946Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:14.962Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:14.963Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:14.963Z - Handled HASH_GLOB. Handling time: 14. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:15.093Z - Time taken for 'build-project-configs' 163.08070000004955ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:15.155Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:15.156Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:15.156Z - Time taken for 'total execution time for createProjectGraph()' 50.01359999994747ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:44.696Z - [WATCHER]: apps/eiot-admin/src/app/fuse/README.md was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:44.696Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:51.142Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:51.142Z - [REQUEST]: apps/eiot-admin/src/app/fuse/README.md
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:51.142Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:51.154Z - Time taken for 'hash changed files from watcher' 44.82490000012331ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:51.156Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:51.158Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:51.158Z - Handled GLOB. Handling time: 0. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:51.163Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:51.163Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:51.163Z - Handled HASH_GLOB. Handling time: 4. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:51.230Z - Time taken for 'build-project-configs' 78.85040000011213ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:51.279Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:51.280Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:51.280Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:51.280Z - Time taken for 'total execution time for createProjectGraph()' 38.01179999997839ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:55.346Z - [WATCHER]: 0 file(s) created or restored, 11 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:55.347Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:55.413Z - [WATCHER]: 0 file(s) created or restored, 8 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:55.414Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:55.474Z - [WATCHER]: 0 file(s) created or restored, 6 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:55.475Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:55.533Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:55.535Z - [WATCHER]: 0 file(s) created or restored, 8 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:55.583Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:55.585Z - [WATCHER]: 0 file(s) created or restored, 6 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:55.636Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:55.638Z - [WATCHER]: 0 file(s) created or restored, 8 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:55.693Z - [WATCHER]: 0 file(s) created or restored, 8 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:55.693Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:55.751Z - [WATCHER]: 0 file(s) created or restored, 4 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:55.752Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:55.801Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:55.802Z - [WATCHER]: 0 file(s) created or restored, 4 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:55.858Z - [WATCHER]: 0 file(s) created or restored, 4 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:55.858Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:55.913Z - [WATCHER]: 0 file(s) created or restored, 4 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:55.915Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:55.961Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:55.963Z - [WATCHER]: 0 file(s) created or restored, 4 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:56.022Z - [WATCHER]: 0 file(s) created or restored, 5 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:56.024Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:56.074Z - [WATCHER]: 0 file(s) created or restored, 4 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:56.074Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:56.135Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:56.135Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:56.187Z - [WATCHER]: 0 file(s) created or restored, 5 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:56.188Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:56.245Z - [WATCHER]: 0 file(s) created or restored, 10 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:56.248Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:56.294Z - [WATCHER]: 0 file(s) created or restored, 9 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:56.297Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:56.347Z - [WATCHER]: 0 file(s) created or restored, 6 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:56.348Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:56.412Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:56.417Z - [WATCHER]: 0 file(s) created or restored, 8 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:56.506Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:56.522Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:56.554Z - [WATCHER]: 0 file(s) created or restored, 7 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:56.632Z - [WATCHER]: 0 file(s) created or restored, 10 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:56.671Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:56.677Z - [WATCHER]: 0 file(s) created or restored, 3 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:56.707Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:56.746Z - [WATCHER]: 0 file(s) created or restored, 3 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:56.764Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:56.819Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:56.820Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:56.880Z - [WATCHER]: 0 file(s) created or restored, 2 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:56.887Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:56.952Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:56.960Z - [WATCHER]: apps/eiot-admin/src/@fuse/tailwind/utils/generate-contrasts.js was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:57.006Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:57.009Z - [WATCHER]: 0 file(s) created or restored, 3 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:57.068Z - [WATCHER]: 0 file(s) created or restored, 7 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:57.070Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:57.118Z - [WATCHER]: 0 file(s) created or restored, 5 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:57.119Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:57.180Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:57.182Z - [WATCHER]: 0 file(s) created or restored, 6 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:57.224Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:57.227Z - [WATCHER]: 0 file(s) created or restored, 6 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:57.291Z - [WATCHER]: 0 file(s) created or restored, 3 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:57.291Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:57.344Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:57.345Z - [WATCHER]: 0 file(s) created or restored, 6 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:57.396Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:57.411Z - [WATCHER]: 0 file(s) created or restored, 4 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:57.514Z - [WATCHER]: 0 file(s) created or restored, 6 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:57.537Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:57.561Z - [WATCHER]: 0 file(s) created or restored, 4 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:57.591Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:57.642Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:57.682Z - [WATCHER]: 0 file(s) created or restored, 6 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:57.730Z - [WATCHER]: 0 file(s) created or restored, 3 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:57.731Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:57.787Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:57.829Z - [WATCHER]: 0 file(s) created or restored, 5 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:57.835Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:57.891Z - [WATCHER]: 0 file(s) created or restored, 6 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:57.909Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:57.939Z - [WATCHER]: 0 file(s) created or restored, 5 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:57.974Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.001Z - [WATCHER]: 0 file(s) created or restored, 4 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.028Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.084Z - [WATCHER]: 0 file(s) created or restored, 6 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.092Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.135Z - [WATCHER]: 0 file(s) created or restored, 9 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.136Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.190Z - [WATCHER]: 0 file(s) created or restored, 8 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.196Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.258Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.259Z - [WATCHER]: 0 file(s) created or restored, 5 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.309Z - [WATCHER]: 0 file(s) created or restored, 8 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.315Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.387Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.389Z - [WATCHER]: 0 file(s) created or restored, 4 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.500Z - [WATCHER]: 0 file(s) created or restored, 7 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.517Z - [WATCHER]: 0 file(s) created or restored, 6 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.519Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.530Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.574Z - [WATCHER]: 0 file(s) created or restored, 5 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.587Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.637Z - [WATCHER]: 0 file(s) created or restored, 5 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.716Z - [WATCHER]: 0 file(s) created or restored, 6 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.740Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.790Z - [WATCHER]: 0 file(s) created or restored, 5 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.795Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.870Z - [WATCHER]: 0 file(s) created or restored, 6 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.871Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.921Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.922Z - [WATCHER]: 0 file(s) created or restored, 5 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.978Z - [WATCHER]: 0 file(s) created or restored, 4 file(s) modified, 0 file(s) deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.981Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.981Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.982Z - Closed a connection. Number of open connections: 0
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.984Z - [WATCHER]: Stopping the watcher for D:\EIOT2.SERVER\eiot2-monorepo (sources)
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:58.989Z - [WATCHER]: Stopping the watcher for D:\EIOT2.SERVER\eiot2-monorepo (outputs)
[NX v21.2.2 Daemon Server] - 2025-07-07T11:34:59.026Z - Server stopped because: "Stopping the daemon the set of ignored files changed (native)"
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:15.055Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\0f0e9f9229ce501d05c6\d.sock
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:15.069Z - [WATCHER]: Subscribed to changes within: D:\EIOT2.SERVER\eiot2-monorepo (native)
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:15.072Z - Established a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:15.073Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:15.076Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:15.079Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:15.659Z - Time taken for 'Load Nx Plugin: D:\EIOT2.SERVER\eiot2-monorepo\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 544.1151ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:15.666Z - Time taken for 'Load Nx Plugin: D:\EIOT2.SERVER\eiot2-monorepo\node_modules\nx\src\plugins\package-json' 552.0549ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:15.917Z - Time taken for 'loadDefaultNxPlugins' 804.9379000000001ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:16.800Z - Time taken for 'Load Nx Plugin: @nx/eslint/plugin' 1701.5947999999999ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:18.130Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:18.130Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:18.131Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:18.152Z - Time taken for 'loadSpecifiedNxPlugins' 3046.953ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:18.159Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:18.160Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:18.161Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:18.166Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:18.167Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:18.167Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:18.177Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:18.177Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:18.178Z - Handled HASH_GLOB. Handling time: 4. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:35.963Z - Time taken for 'build-project-configs' 17793.2379ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:37.297Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:37.299Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:37.301Z - Time taken for 'total for creating and serializing project graph' 22220.022100000002ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:37.302Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:37.302Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 22220. Response time: 3.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:37.322Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:37.323Z - Time taken for 'preTasksExecution' 0.8004000000000815ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:37.323Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:37.323Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:37.501Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:37.502Z - Done responding to the client handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:37.502Z - Handled HASH_TASKS. Handling time: 47. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:37.544Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:37.545Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:37.545Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 10. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:50.481Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:50.486Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:50.488Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:50.491Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:50.492Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:50.493Z - Time taken for 'total for creating and serializing project graph' 0.7239999999947031ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:50.493Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:41:50.494Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:40.284Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:40.336Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:40.460Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:40.542Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:40.599Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:40.650Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:41.242Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:41.332Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:41.398Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:41.457Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:41.610Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:41.661Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:41.715Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:41.768Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:41.876Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:42.395Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:42.449Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:42.502Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:42.566Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:42.676Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:42.729Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:42.810Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:42.871Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:42.937Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:42.998Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:43.091Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:43.219Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:43.288Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:43.363Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:43.401Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:43.460Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:43.511Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:43.572Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:43.847Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:43.936Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:44.677Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:44.826Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:45.541Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:45.592Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:45.721Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:45.784Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:45.850Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:46.468Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:46.524Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:46.626Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:46.684Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:46.791Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:46.883Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:46.936Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:47.005Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:47.059Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:47.126Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:47.180Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:47.242Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:47.392Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:47.482Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:47.534Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:47.676Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:47.800Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:47.874Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:47.993Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:48.049Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:48.121Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:48.179Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:48.238Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:48.303Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:48.381Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:49.437Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:49.489Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:49.593Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:49.655Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:49.745Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:50.037Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:50.092Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:50.157Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:50.215Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:50.286Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:50.340Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:50.467Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:50.524Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:50.619Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:50.839Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:51.141Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:51.199Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:51.289Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:51.335Z - [WATCHER]: apps/eiot-admin/src/app/app.config.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:51.431Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:51.462Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:51.462Z - [REQUEST]: apps/eiot-admin/src/app/app.config.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:51.462Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:51.485Z - Time taken for 'hash changed files from watcher' 24.30349999999453ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:51.498Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:51.499Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:51.500Z - Handled GLOB. Handling time: 11. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:51.527Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:51.528Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:51.533Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:51.534Z - Handled HASH_GLOB. Handling time: 24. Response time: 7.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:51.608Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:51.775Z - Time taken for 'build-project-configs' 252.24640000000363ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:51.943Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:51.944Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:51.944Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:51.944Z - Time taken for 'total execution time for createProjectGraph()' 155.67440000000352ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:51.944Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:52.069Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:52.238Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:52.425Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:52.537Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:52.590Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:52.651Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:52.737Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:52.794Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:53.018Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:53.093Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:53.148Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:53.232Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:53.281Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:53.333Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:53.392Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:53.446Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:53.574Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:53.672Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:53.704Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:53.801Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:53.963Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:53.966Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:54.017Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:54.084Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:54.162Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:54.288Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:54.568Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:54.991Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:55.224Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:55.293Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:55.355Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:55.406Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:55.472Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:55.538Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:55.585Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:55.646Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:55.721Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:55.774Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:55.826Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:55.888Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:55.987Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:56.048Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:56.192Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:56.270Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:56.342Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:56.414Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:56.505Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:56.567Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:56.756Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:56.809Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:56.993Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:57.088Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:57.242Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:57.294Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:57.353Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:57.405Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:57.459Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:57.521Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:57.572Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:57.624Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:57.687Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:57.745Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:57.820Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:57.869Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:57.914Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:57.969Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:58.026Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:58.190Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:58.246Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:58.374Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:58.497Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:58.550Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:58.631Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:58.706Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:58.759Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:58.818Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:58.878Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:58.957Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:59.003Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:59.052Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:59.144Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:59.304Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:59.385Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:59.468Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:59.565Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:59.615Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:42:59.679Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:00.130Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:00.183Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:00.257Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:00.330Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:00.391Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:00.508Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:00.575Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:00.644Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:00.700Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:00.781Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:00.881Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:00.957Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:01.021Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:01.117Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:01.145Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:01.212Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:01.427Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:01.671Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:01.739Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:01.835Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:01.881Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:01.944Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:02.016Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:02.105Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:02.285Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:02.376Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:02.439Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:02.498Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:02.565Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:02.622Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:02.716Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:02.766Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:02.830Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:02.972Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:03.059Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:03.120Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:03.199Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:03.280Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:03.549Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:03.669Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:03.691Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:03.814Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:03.865Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:03.984Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:04.035Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:04.280Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:04.337Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:04.438Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:04.494Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:04.568Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:04.675Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:04.838Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:04.969Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:05.330Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:05.474Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:05.542Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:05.596Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:05.677Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:05.802Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:05.867Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:05.926Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:06.040Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:06.130Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:06.182Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:06.240Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:06.318Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:06.378Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:06.489Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:06.541Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:06.649Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:06.785Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:06.834Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:06.924Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:07.063Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:07.136Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:07.201Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:07.273Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:07.329Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:07.408Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:07.548Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:07.681Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:07.734Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:07.786Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:07.843Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:07.905Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:07.975Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:08.033Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:08.125Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:08.175Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:08.249Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:08.304Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:08.360Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:08.415Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:08.486Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:08.541Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:08.597Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:08.673Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:08.773Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:08.824Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:08.892Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:09.046Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:09.104Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:09.192Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:09.303Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:09.434Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:09.526Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:09.585Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:09.646Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:14.983Z - [WATCHER]: apps/eiot-admin/src/app/app.config.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:14.984Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:15.208Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:15.208Z - [REQUEST]: apps/eiot-admin/src/app/app.config.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:15.208Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:15.223Z - Time taken for 'hash changed files from watcher' 22.326700000005076ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:15.227Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:15.229Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:15.229Z - Handled GLOB. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:15.252Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:15.258Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:15.258Z - Handled HASH_GLOB. Handling time: 20. Response time: 6.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:15.375Z - Time taken for 'build-project-configs' 149.47619999999006ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:15.484Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:15.484Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:15.485Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:15.485Z - Time taken for 'total execution time for createProjectGraph()' 92.93039999999746ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:25.240Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:25.335Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:31.574Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:31.579Z - [WATCHER]: apps/eiot-admin/src/app/app.config.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:32.001Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:32.002Z - [REQUEST]: apps/eiot-admin/src/app/app.config.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:32.002Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:32.046Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:32.047Z - Time taken for 'hash changed files from watcher' 20.808699999994133ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:32.048Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:32.048Z - Handled GLOB. Handling time: 8. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:32.066Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:32.067Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:32.067Z - Handled HASH_GLOB. Handling time: 8. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:32.190Z - Time taken for 'build-project-configs' 168.6524999999965ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:32.275Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:32.276Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:32.276Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:32.276Z - Time taken for 'total execution time for createProjectGraph()' 69.90869999999995ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:34.218Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:34.399Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:34.463Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:34.505Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:34.558Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:34.680Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:34.817Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:34.870Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:35.458Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:35.465Z - [WATCHER]: apps/eiot-admin/src/app/dashboard was deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:36.268Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:36.268Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:36.268Z - [REQUEST]: apps/eiot-admin/src/app/dashboard
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:36.367Z - Time taken for 'hash changed files from watcher' 2.4523999999801163ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:36.395Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:36.402Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:36.402Z - Handled GLOB. Handling time: 15. Response time: 7.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:36.465Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:36.465Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:36.465Z - Handled HASH_GLOB. Handling time: 61. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:36.549Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:36.800Z - Time taken for 'build-project-configs' 419.14139999999315ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:36.934Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:36.935Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:36.935Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:36.935Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:36.935Z - Time taken for 'total execution time for createProjectGraph()' 97.375ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:43.948Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:43:43.949Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:21.085Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:21.087Z - [WATCHER]: libs/shared/ui/src/lib/fuse-card was deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:22.689Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:22.689Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:22.689Z - [REQUEST]: libs/shared/ui/src/lib/fuse-card
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:22.697Z - Time taken for 'hash changed files from watcher' 1.0552000000025146ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:22.701Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:22.702Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:22.702Z - Handled GLOB. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:22.707Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:22.707Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:22.707Z - Handled HASH_GLOB. Handling time: 3. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:22.776Z - Time taken for 'build-project-configs' 76.00310000000172ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:22.830Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:22.831Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:22.831Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:22.831Z - Time taken for 'total execution time for createProjectGraph()' 37.788100000005215ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:26.506Z - [WATCHER]: libs/shared/ui/src/lib/fuse-layout was deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:26.506Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:29.709Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:29.709Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:29.709Z - [REQUEST]: libs/shared/ui/src/lib/fuse-layout
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:29.738Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:29.739Z - Time taken for 'hash changed files from watcher' 2.53870000000461ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:29.740Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:29.740Z - Handled GLOB. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:29.746Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:29.747Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:29.747Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:29.812Z - Time taken for 'build-project-configs' 86.28109999999288ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:29.876Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:29.877Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:29.877Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:29.877Z - Time taken for 'total execution time for createProjectGraph()' 43.55160000000615ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:30.375Z - [WATCHER]: libs/shared/ui/src/lib/fuse-stats was deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:30.376Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:36.777Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:36.777Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:36.778Z - [REQUEST]: libs/shared/ui/src/lib/fuse-stats
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:36.789Z - Time taken for 'hash changed files from watcher' 0.9864999999990687ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:36.795Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:36.796Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:36.796Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:36.803Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:36.804Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:36.804Z - Handled HASH_GLOB. Handling time: 4. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:36.877Z - Time taken for 'build-project-configs' 88.76780000000144ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:36.916Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:36.917Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:36.917Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:36.917Z - Time taken for 'total execution time for createProjectGraph()' 30.408800000004703ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:38.005Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:44:38.065Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:45:48.809Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:45:48.811Z - [WATCHER]: apps/eiot-admin/src/app/fuse/styles/_fuse.scss was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:45:55.212Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:45:55.213Z - [REQUEST]: apps/eiot-admin/src/app/fuse/styles/_fuse.scss
[NX v21.2.2 Daemon Server] - 2025-07-07T11:45:55.213Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:45:55.223Z - Time taken for 'hash changed files from watcher' 0.9330000000190921ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:45:55.227Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:45:55.229Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:45:55.229Z - Handled GLOB. Handling time: 2. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:45:55.239Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:45:55.239Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:45:55.239Z - Handled HASH_GLOB. Handling time: 7. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:45:55.305Z - Time taken for 'build-project-configs' 80.81289999996079ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:45:55.361Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:45:55.362Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:45:55.362Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:45:55.362Z - Time taken for 'total execution time for createProjectGraph()' 45.3691000000108ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:46:22.665Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:46:22.667Z - [WATCHER]: apps/eiot-admin/src/main.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:46:24.620Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:46:24.822Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:46:24.944Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:46:29.071Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:46:29.071Z - [REQUEST]: apps/eiot-admin/src/main.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:46:29.071Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:46:29.087Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:46:29.087Z - Time taken for 'hash changed files from watcher' 1.6253000000142492ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:46:29.088Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:46:29.088Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:46:29.099Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:46:29.100Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:46:29.100Z - Handled HASH_GLOB. Handling time: 8. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:46:29.171Z - Time taken for 'build-project-configs' 86.2099000000162ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:46:29.226Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:46:29.227Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:46:29.227Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:46:29.227Z - Time taken for 'total execution time for createProjectGraph()' 45.12989999999991ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:47:14.422Z - [WATCHER]: package.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:47:14.423Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:47:14.937Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:47:15.186Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:47:15.634Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:47:16.424Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:47:16.431Z - Closed a connection. Number of open connections: 0
[NX v21.2.2 Daemon Server] - 2025-07-07T11:47:16.432Z - [WATCHER]: Stopping the watcher for D:\EIOT2.SERVER\eiot2-monorepo (sources)
[NX v21.2.2 Daemon Server] - 2025-07-07T11:47:16.432Z - [WATCHER]: Stopping the watcher for D:\EIOT2.SERVER\eiot2-monorepo (outputs)
[NX v21.2.2 Daemon Server] - 2025-07-07T11:47:16.456Z - Server stopped because: "LOCK_FILES_CHANGED"
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:05.952Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\0f0e9f9229ce501d05c6\d.sock
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:05.966Z - [WATCHER]: Subscribed to changes within: D:\EIOT2.SERVER\eiot2-monorepo (native)
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:05.970Z - Established a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:05.971Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:05.975Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:05.980Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:06.663Z - Time taken for 'Load Nx Plugin: D:\EIOT2.SERVER\eiot2-monorepo\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 643.5929000000001ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:06.679Z - Time taken for 'Load Nx Plugin: D:\EIOT2.SERVER\eiot2-monorepo\node_modules\nx\src\plugins\package-json' 662.2024ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:06.944Z - Time taken for 'loadDefaultNxPlugins' 928.3648ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:07.843Z - Time taken for 'Load Nx Plugin: @nx/eslint/plugin' 1843.4074ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:09.185Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:09.186Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:09.186Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:09.205Z - Time taken for 'loadSpecifiedNxPlugins' 3200.6686999999997ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:09.210Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:09.211Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:09.215Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:09.218Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:09.218Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:09.219Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:09.230Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:09.230Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:09.231Z - Handled HASH_GLOB. Handling time: 4. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:17.534Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:29.377Z - Time taken for 'build-project-configs' 20148.6228ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:29.941Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:29.942Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:29.944Z - Time taken for 'total for creating and serializing project graph' 23962.348100000003ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:29.945Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:29.945Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 23964. Response time: 3.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:29.965Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:29.966Z - Time taken for 'preTasksExecution' 1.115300000001298ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:29.966Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:29.966Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:30.088Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:30.089Z - Done responding to the client handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:30.089Z - Handled HASH_TASKS. Handling time: 39. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:30.124Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:30.124Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:30.124Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 12. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:44.576Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:44.584Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:44.586Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:44.588Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:44.589Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:44.590Z - Time taken for 'total for creating and serializing project graph' 0.4639999999999418ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:44.590Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:53:44.590Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:54:45.448Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:54:45.455Z - [WATCHER]: apps/eiot-admin/src/app/app.config.ts was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:54:45.579Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:54:45.580Z - [REQUEST]: apps/eiot-admin/src/app/app.config.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:54:45.580Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:54:45.606Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:54:45.606Z - Time taken for 'hash changed files from watcher' 21.77390000000014ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:54:45.607Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:54:45.607Z - Handled GLOB. Handling time: 4. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:54:45.619Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:54:45.620Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:54:45.620Z - Handled HASH_GLOB. Handling time: 8. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:54:45.721Z - Time taken for 'build-project-configs' 124.23649999999907ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:54:45.816Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:54:45.817Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:54:45.817Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:54:45.817Z - Time taken for 'total execution time for createProjectGraph()' 81.03910000000906ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:54:48.159Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:54:51.191Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:54:51.281Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:54:51.343Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:54:51.403Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:54:51.469Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:54:51.660Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:54:54.443Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:54:54.569Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:54:59.809Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:00.089Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:18.276Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:18.368Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:18.560Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:18.774Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:18.828Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:18.888Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:18.972Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:19.014Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:19.067Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:19.164Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:23.742Z - [WATCHER]: apps/eiot-admin/src/app/auth/login/login.component.ts was deleted
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:23.743Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:23.948Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:23.948Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:23.948Z - [REQUEST]: apps/eiot-admin/src/app/auth/login/login.component.ts
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:24.017Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:24.018Z - Time taken for 'hash changed files from watcher' 2.502999999996973ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:24.018Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:24.018Z - Handled GLOB. Handling time: 17. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:24.046Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:24.047Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:24.047Z - Handled HASH_GLOB. Handling time: 21. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:24.339Z - Time taken for 'build-project-configs' 358.7163999999757ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:24.451Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:24.452Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:24.452Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:24.452Z - Time taken for 'total execution time for createProjectGraph()' 80.01509999998962ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:50.485Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:50.622Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:51.639Z - [WATCHER]: package.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:51.639Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:51.716Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:51.821Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:52.043Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:52.043Z - [REQUEST]: package.json
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:52.043Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:52.102Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:52.103Z - Time taken for 'hash changed files from watcher' 1.5671999999904074ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:52.103Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:52.103Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:52.155Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:52.161Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:52.161Z - Handled HASH_GLOB. Handling time: 5. Response time: 6.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:52.281Z - Time taken for 'build-project-configs' 179.312900000019ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:52.389Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:52.390Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:52.390Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:52.390Z - Time taken for 'total execution time for createProjectGraph()' 67.61540000000969ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:52.623Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:52.660Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:52.772Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:52.831Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:53.705Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:54.135Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:54.136Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:54.136Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:54.136Z - Closed a connection. Number of open connections: 0
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:54.138Z - [WATCHER]: Stopping the watcher for D:\EIOT2.SERVER\eiot2-monorepo (sources)
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:54.139Z - [WATCHER]: Stopping the watcher for D:\EIOT2.SERVER\eiot2-monorepo (outputs)
[NX v21.2.2 Daemon Server] - 2025-07-07T11:55:54.181Z - Server stopped because: "LOCK_FILES_CHANGED"
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:34.352Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\0f0e9f9229ce501d05c6\d.sock
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:34.364Z - [WATCHER]: Subscribed to changes within: D:\EIOT2.SERVER\eiot2-monorepo (native)
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:34.369Z - Established a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:34.369Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:34.372Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:34.375Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:34.897Z - Time taken for 'Load Nx Plugin: D:\EIOT2.SERVER\eiot2-monorepo\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 511.2296000000001ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:34.926Z - Time taken for 'Load Nx Plugin: D:\EIOT2.SERVER\eiot2-monorepo\node_modules\nx\src\plugins\package-json' 541.5678999999999ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:35.161Z - Time taken for 'loadDefaultNxPlugins' 777.5013ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:35.934Z - Time taken for 'Load Nx Plugin: @nx/eslint/plugin' 1553.7353999999998ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:37.116Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:37.117Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:37.117Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:37.136Z - Time taken for 'loadSpecifiedNxPlugins' 2737.3754ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:37.142Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:37.143Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:37.143Z - Established a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:37.150Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:37.151Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:37.151Z - Handled GLOB. Handling time: 2. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:37.169Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:37.169Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:37.169Z - Handled HASH_GLOB. Handling time: 10. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:56.512Z - Time taken for 'build-project-configs' 19352.406899999998ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:56.951Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:56.952Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:56.954Z - Time taken for 'total for creating and serializing project graph' 22577.6313ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:56.954Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:56.954Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 22577. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:56.973Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:56.973Z - Time taken for 'preTasksExecution' 0.8863999999994121ms
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:56.973Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:56.973Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:57.076Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:57.076Z - Done responding to the client handleHashTasks
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:57.076Z - Handled HASH_TASKS. Handling time: 33. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:57.111Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:57.111Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v21.2.2 Daemon Server] - 2025-07-07T11:59:57.111Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 10. Response time: 0.
[NX v21.2.2 Daemon Server] - 2025-07-07T12:00:11.069Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T12:00:11.070Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T12:00:11.071Z - Established a connection. Number of open connections: 3
[NX v21.2.2 Daemon Server] - 2025-07-07T12:00:11.078Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.2 Daemon Server] - 2025-07-07T12:00:11.079Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T12:00:11.080Z - Time taken for 'total for creating and serializing project graph' 0.6572000000014668ms
[NX v21.2.2 Daemon Server] - 2025-07-07T12:00:11.081Z - Done responding to the client project-graph
[NX v21.2.2 Daemon Server] - 2025-07-07T12:00:11.081Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:21.069Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:21.071Z - [WATCHER]: apps/eiot-admin/src/@fuse/styles/components/example-viewer.scss was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:21.184Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:21.184Z - [REQUEST]: apps/eiot-admin/src/@fuse/styles/components/example-viewer.scss
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:21.184Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:21.198Z - Time taken for 'hash changed files from watcher' 10.343399999983376ms
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:21.201Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:21.202Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:21.203Z - Handled GLOB. Handling time: 1. Response time: 2.
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:21.210Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:21.211Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:21.211Z - Handled HASH_GLOB. Handling time: 4. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:21.281Z - Time taken for 'build-project-configs' 83.64869999999064ms
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:21.359Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:21.360Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:21.360Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:21.361Z - Time taken for 'total execution time for createProjectGraph()' 66.44390000001295ms
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:42.639Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:42.647Z - [WATCHER]: package.json was modified
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:42.856Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:42.856Z - [REQUEST]: package.json
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:42.856Z - [REQUEST]: 
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:42.877Z - [REQUEST]: Responding to the client. handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:42.877Z - Time taken for 'hash changed files from watcher' 1.6875ms
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:42.878Z - Done responding to the client handleGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:42.878Z - Handled GLOB. Handling time: 3. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:42.887Z - [REQUEST]: Responding to the client. handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:42.888Z - Done responding to the client handleHashMultiGlob
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:42.888Z - Handled HASH_GLOB. Handling time: 5. Response time: 1.
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:42.973Z - Time taken for 'build-project-configs' 99.29089999999269ms
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:43.053Z - [SYNC]: collect registered sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:43.054Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:43.054Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:43.054Z - Time taken for 'total execution time for createProjectGraph()' 59.64360000001034ms
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:45.025Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:45.030Z - Closed a connection. Number of open connections: 2
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:45.030Z - Closed a connection. Number of open connections: 1
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:45.030Z - Closed a connection. Number of open connections: 0
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:45.031Z - [WATCHER]: Stopping the watcher for D:\EIOT2.SERVER\eiot2-monorepo (sources)
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:45.031Z - [WATCHER]: Stopping the watcher for D:\EIOT2.SERVER\eiot2-monorepo (outputs)
[NX v21.2.2 Daemon Server] - 2025-07-07T12:02:45.034Z - Server stopped because: "LOCK_FILES_CHANGED"
