import { InjectionToken } from '@angular/core';
import { TranslocoConfig } from './transloco.config';
import { HashMap } from './types';
import * as i0 from "@angular/core";
export declare const TRANSLOCO_MISSING_HANDLER: InjectionToken<TranslocoMissingHandlerData>;
export interface TranslocoMissingHandlerData extends TranslocoConfig {
    activeLang: string;
}
export interface TranslocoMissingHandler {
    handle(key: string, data: TranslocoMissingHandlerData, params?: HashMap): any;
}
export declare class DefaultMissingHandler implements TranslocoMissingHandler {
    handle(key: string, config: TranslocoConfig): string;
    static ɵfac: i0.ɵɵFactoryDeclaration<DefaultMissingHandler, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<DefaultMissingHandler>;
}
