{"ast": null, "code": "import { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { RouterOutlet } from '@angular/router';\nimport { FuseFullscreenComponent } from '@fuse/components/fullscreen';\nimport { FuseLoadingBarComponent } from '@fuse/components/loading-bar';\nimport { FuseVerticalNavigationComponent } from '@fuse/components/navigation';\nimport { LanguagesComponent } from 'app/layout/common/languages/languages.component';\nimport { MessagesComponent } from 'app/layout/common/messages/messages.component';\nimport { NotificationsComponent } from 'app/layout/common/notifications/notifications.component';\nimport { QuickChatComponent } from 'app/layout/common/quick-chat/quick-chat.component';\nimport { SearchComponent } from 'app/layout/common/search/search.component';\nimport { ShortcutsComponent } from 'app/layout/common/shortcuts/shortcuts.component';\nimport { UserComponent } from 'app/layout/common/user/user.component';\nimport { Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"app/core/navigation/navigation.service\";\nimport * as i3 from \"app/core/user/user.service\";\nimport * as i4 from \"@fuse/services/media-watcher\";\nimport * as i5 from \"@fuse/components/navigation\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/icon\";\nfunction FuturisticLayoutComponent_Conditional_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"router-outlet\");\n  }\n}\nexport class FuturisticLayoutComponent {\n  /**\n   * Constructor\n   */\n  constructor(_activatedRoute, _router, _navigationService, _userService, _fuseMediaWatcherService, _fuseNavigationService) {\n    this._activatedRoute = _activatedRoute;\n    this._router = _router;\n    this._navigationService = _navigationService;\n    this._userService = _userService;\n    this._fuseMediaWatcherService = _fuseMediaWatcherService;\n    this._fuseNavigationService = _fuseNavigationService;\n    this._unsubscribeAll = new Subject();\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Accessors\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * Getter for current year\n   */\n  get currentYear() {\n    return new Date().getFullYear();\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Lifecycle hooks\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * On init\n   */\n  ngOnInit() {\n    // Subscribe to navigation data\n    this._navigationService.navigation$.pipe(takeUntil(this._unsubscribeAll)).subscribe(navigation => {\n      this.navigation = navigation;\n    });\n    // Subscribe to the user service\n    this._userService.user$.pipe(takeUntil(this._unsubscribeAll)).subscribe(user => {\n      this.user = user;\n    });\n    // Subscribe to media changes\n    this._fuseMediaWatcherService.onMediaChange$.pipe(takeUntil(this._unsubscribeAll)).subscribe(({\n      matchingAliases\n    }) => {\n      // Check if the screen is small\n      this.isScreenSmall = !matchingAliases.includes('md');\n    });\n  }\n  /**\n   * On destroy\n   */\n  ngOnDestroy() {\n    // Unsubscribe from all subscriptions\n    this._unsubscribeAll.next(null);\n    this._unsubscribeAll.complete();\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Public methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * Toggle navigation\n   *\n   * @param name\n   */\n  toggleNavigation(name) {\n    // Get the navigation\n    const navigation = this._fuseNavigationService.getComponent(name);\n    if (navigation) {\n      // Toggle the opened status\n      navigation.toggle();\n    }\n  }\n  static #_ = this.ɵfac = function FuturisticLayoutComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FuturisticLayoutComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.NavigationService), i0.ɵɵdirectiveInject(i3.UserService), i0.ɵɵdirectiveInject(i4.FuseMediaWatcherService), i0.ɵɵdirectiveInject(i5.FuseNavigationService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: FuturisticLayoutComponent,\n    selectors: [[\"futuristic-layout\"]],\n    decls: 33,\n    vars: 10,\n    consts: [[\"quickChat\", \"quickChat\"], [1, \"dark\", \"bg-indigo-800\", \"text-white\", \"print:hidden\", 3, \"mode\", \"name\", \"navigation\", \"opened\"], [\"fuseVerticalNavigationHeader\", \"\"], [1, \"flex\", \"h-20\", \"items-center\", \"p-6\", \"pb-0\"], [\"src\", \"images/logo/logo-text-on-dark.svg\", 1, \"w-30\"], [\"fuseVerticalNavigationFooter\", \"\"], [1, \"flex\", \"w-full\", \"items-center\", \"border-t\", \"px-6\", \"py-8\"], [1, \"ml-4\", \"flex\", \"w-full\", \"flex-col\", \"overflow-hidden\"], [1, \"w-full\", \"overflow-hidden\", \"text-ellipsis\", \"whitespace-nowrap\", \"leading-normal\", \"text-current\", \"opacity-80\"], [1, \"mt-0.5\", \"w-full\", \"overflow-hidden\", \"text-ellipsis\", \"whitespace-nowrap\", \"text-sm\", \"leading-normal\", \"text-current\", \"opacity-50\"], [1, \"flex\", \"w-full\", \"min-w-0\", \"flex-auto\", \"flex-col\"], [1, \"bg-card\", \"relative\", \"z-49\", \"flex\", \"h-16\", \"w-full\", \"flex-0\", \"items-center\", \"px-4\", \"shadow\", \"dark:border-b\", \"dark:bg-transparent\", \"dark:shadow-none\", \"md:px-6\", \"print:hidden\"], [\"mat-icon-button\", \"\", 1, \"mr-2\", 3, \"click\"], [3, \"svgIcon\"], [1, \"ml-auto\", \"flex\", \"items-center\", \"space-x-0.5\", \"pl-2\", \"sm:space-x-2\"], [1, \"hidden\", \"md:block\"], [3, \"appearance\"], [\"mat-icon-button\", \"\", 1, \"lg:hidden\", 3, \"click\"], [1, \"flex\", \"flex-auto\", \"flex-col\"], [1, \"bg-card\", \"relative\", \"z-49\", \"flex\", \"h-14\", \"w-full\", \"flex-0\", \"items-center\", \"justify-start\", \"border-t\", \"px-4\", \"dark:bg-transparent\", \"md:px-6\", \"print:hidden\"], [1, \"text-secondary\", \"font-medium\"]],\n    template: function FuturisticLayoutComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelement(0, \"fuse-loading-bar\");\n        i0.ɵɵelementStart(1, \"fuse-vertical-navigation\", 1);\n        i0.ɵɵelementContainerStart(2, 2);\n        i0.ɵɵelementStart(3, \"div\", 3);\n        i0.ɵɵelement(4, \"img\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementContainerStart(5, 5);\n        i0.ɵɵelementStart(6, \"div\", 6);\n        i0.ɵɵelement(7, \"user\");\n        i0.ɵɵelementStart(8, \"div\", 7)(9, \"div\", 8);\n        i0.ɵɵtext(10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"div\", 9);\n        i0.ɵɵtext(12, \" <EMAIL> \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"div\", 10)(14, \"div\", 11)(15, \"button\", 12);\n        i0.ɵɵlistener(\"click\", function FuturisticLayoutComponent_Template_button_click_15_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.toggleNavigation(\"mainNavigation\"));\n        });\n        i0.ɵɵelement(16, \"mat-icon\", 13);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"div\", 14);\n        i0.ɵɵelement(18, \"languages\")(19, \"fuse-fullscreen\", 15)(20, \"search\", 16)(21, \"shortcuts\")(22, \"messages\")(23, \"notifications\");\n        i0.ɵɵelementStart(24, \"button\", 17);\n        i0.ɵɵlistener(\"click\", function FuturisticLayoutComponent_Template_button_click_24_listener() {\n          i0.ɵɵrestoreView(_r1);\n          const quickChat_r2 = i0.ɵɵreference(32);\n          return i0.ɵɵresetView(quickChat_r2.toggle());\n        });\n        i0.ɵɵelement(25, \"mat-icon\", 13);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(26, \"div\", 18);\n        i0.ɵɵconditionalCreate(27, FuturisticLayoutComponent_Conditional_27_Template, 1, 0, \"router-outlet\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(28, \"div\", 19)(29, \"span\", 20);\n        i0.ɵɵtext(30);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelement(31, \"quick-chat\", null, 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"mode\", ctx.isScreenSmall ? \"over\" : \"side\")(\"name\", \"mainNavigation\")(\"navigation\", ctx.navigation.futuristic)(\"opened\", !ctx.isScreenSmall);\n        i0.ɵɵadvance(9);\n        i0.ɵɵtextInterpolate1(\" \", ctx.user.name, \" \");\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"svgIcon\", \"heroicons_outline:bars-3\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"appearance\", \"bar\");\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"svgIcon\", \"heroicons_outline:chat-bubble-left-right\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(true ? 27 : -1);\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\"Fuse \\u00A9 \", ctx.currentYear);\n      }\n    },\n    dependencies: [FuseLoadingBarComponent, FuseVerticalNavigationComponent, UserComponent, MatButtonModule, i6.MatIconButton, MatIconModule, i7.MatIcon, LanguagesComponent, FuseFullscreenComponent, SearchComponent, ShortcutsComponent, MessagesComponent, NotificationsComponent, RouterOutlet, QuickChatComponent],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["MatButtonModule", "MatIconModule", "RouterOutlet", "FuseFullscreenComponent", "FuseLoadingBarComponent", "FuseVerticalNavigationComponent", "LanguagesComponent", "MessagesComponent", "NotificationsComponent", "QuickChatComponent", "SearchComponent", "ShortcutsComponent", "UserComponent", "Subject", "takeUntil", "i0", "ɵɵelement", "FuturisticLayoutComponent", "constructor", "_activatedRoute", "_router", "_navigationService", "_userService", "_fuseMediaWatcherService", "_fuseNavigationService", "_unsubscribeAll", "currentYear", "Date", "getFullYear", "ngOnInit", "navigation$", "pipe", "subscribe", "navigation", "user$", "user", "onMediaChange$", "matchingAliases", "isScreenSmall", "includes", "ngOnDestroy", "next", "complete", "toggleNavigation", "name", "getComponent", "toggle", "_", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "NavigationService", "i3", "UserService", "i4", "FuseMediaWatcherService", "i5", "FuseNavigationService", "_2", "selectors", "decls", "vars", "consts", "template", "FuturisticLayoutComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelementContainerStart", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "FuturisticLayoutComponent_Template_button_click_15_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "FuturisticLayoutComponent_Template_button_click_24_listener", "quickChat_r2", "ɵɵreference", "ɵɵconditionalCreate", "FuturisticLayoutComponent_Conditional_27_Template", "ɵɵadvance", "ɵɵproperty", "futuristic", "ɵɵtextInterpolate1", "ɵɵconditional", "i6", "MatIconButton", "i7", "MatIcon", "encapsulation"], "sources": ["D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\app\\layout\\layouts\\vertical\\futuristic\\futuristic.component.ts", "D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\app\\layout\\layouts\\vertical\\futuristic\\futuristic.component.html"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>nit, ViewEncapsulation } from '@angular/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { ActivatedRoute, Router, RouterOutlet } from '@angular/router';\nimport { FuseFullscreenComponent } from '@fuse/components/fullscreen';\nimport { FuseLoadingBarComponent } from '@fuse/components/loading-bar';\nimport {\n    FuseNavigationService,\n    FuseVerticalNavigationComponent,\n} from '@fuse/components/navigation';\nimport { FuseMediaWatcherService } from '@fuse/services/media-watcher';\nimport { NavigationService } from 'app/core/navigation/navigation.service';\nimport { Navigation } from 'app/core/navigation/navigation.types';\nimport { UserService } from 'app/core/user/user.service';\nimport { User } from 'app/core/user/user.types';\nimport { LanguagesComponent } from 'app/layout/common/languages/languages.component';\nimport { MessagesComponent } from 'app/layout/common/messages/messages.component';\nimport { NotificationsComponent } from 'app/layout/common/notifications/notifications.component';\nimport { QuickChatComponent } from 'app/layout/common/quick-chat/quick-chat.component';\nimport { SearchComponent } from 'app/layout/common/search/search.component';\nimport { ShortcutsComponent } from 'app/layout/common/shortcuts/shortcuts.component';\nimport { UserComponent } from 'app/layout/common/user/user.component';\nimport { Subject, takeUntil } from 'rxjs';\n\n@Component({\n    selector: 'futuristic-layout',\n    templateUrl: './futuristic.component.html',\n    encapsulation: ViewEncapsulation.None,\n    imports: [\n        FuseLoadingBarComponent,\n        FuseVerticalNavigationComponent,\n        UserComponent,\n        MatButtonModule,\n        MatIconModule,\n        LanguagesComponent,\n        FuseFullscreenComponent,\n        SearchComponent,\n        ShortcutsComponent,\n        MessagesComponent,\n        NotificationsComponent,\n        RouterOutlet,\n        QuickChatComponent,\n    ],\n})\nexport class FuturisticLayoutComponent implements OnInit, OnDestroy {\n    isScreenSmall: boolean;\n    navigation: Navigation;\n    user: User;\n    private _unsubscribeAll: Subject<any> = new Subject<any>();\n\n    /**\n     * Constructor\n     */\n    constructor(\n        private _activatedRoute: ActivatedRoute,\n        private _router: Router,\n        private _navigationService: NavigationService,\n        private _userService: UserService,\n        private _fuseMediaWatcherService: FuseMediaWatcherService,\n        private _fuseNavigationService: FuseNavigationService\n    ) {}\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Accessors\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * Getter for current year\n     */\n    get currentYear(): number {\n        return new Date().getFullYear();\n    }\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Lifecycle hooks\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * On init\n     */\n    ngOnInit(): void {\n        // Subscribe to navigation data\n        this._navigationService.navigation$\n            .pipe(takeUntil(this._unsubscribeAll))\n            .subscribe((navigation: Navigation) => {\n                this.navigation = navigation;\n            });\n\n        // Subscribe to the user service\n        this._userService.user$\n            .pipe(takeUntil(this._unsubscribeAll))\n            .subscribe((user: User) => {\n                this.user = user;\n            });\n\n        // Subscribe to media changes\n        this._fuseMediaWatcherService.onMediaChange$\n            .pipe(takeUntil(this._unsubscribeAll))\n            .subscribe(({ matchingAliases }) => {\n                // Check if the screen is small\n                this.isScreenSmall = !matchingAliases.includes('md');\n            });\n    }\n\n    /**\n     * On destroy\n     */\n    ngOnDestroy(): void {\n        // Unsubscribe from all subscriptions\n        this._unsubscribeAll.next(null);\n        this._unsubscribeAll.complete();\n    }\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Public methods\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * Toggle navigation\n     *\n     * @param name\n     */\n    toggleNavigation(name: string): void {\n        // Get the navigation\n        const navigation =\n            this._fuseNavigationService.getComponent<FuseVerticalNavigationComponent>(\n                name\n            );\n\n        if (navigation) {\n            // Toggle the opened status\n            navigation.toggle();\n        }\n    }\n}\n", "<!-- Loading bar -->\n<fuse-loading-bar></fuse-loading-bar>\n\n<!-- Navigation -->\n<fuse-vertical-navigation\n    class=\"dark bg-indigo-800 text-white print:hidden\"\n    [mode]=\"isScreenSmall ? 'over' : 'side'\"\n    [name]=\"'mainNavigation'\"\n    [navigation]=\"navigation.futuristic\"\n    [opened]=\"!isScreenSmall\"\n>\n    <!-- Navigation header hook -->\n    <ng-container fuseVerticalNavigationHeader>\n        <!-- Logo -->\n        <div class=\"flex h-20 items-center p-6 pb-0\">\n            <img class=\"w-30\" src=\"images/logo/logo-text-on-dark.svg\" />\n        </div>\n    </ng-container>\n    <!-- Navigation footer hook -->\n    <ng-container fuseVerticalNavigationFooter>\n        <!-- User -->\n        <div class=\"flex w-full items-center border-t px-6 py-8\">\n            <user></user>\n            <div class=\"ml-4 flex w-full flex-col overflow-hidden\">\n                <div\n                    class=\"w-full overflow-hidden text-ellipsis whitespace-nowrap leading-normal text-current opacity-80\"\n                >\n                    {{ user.name }}\n                </div>\n                <div\n                    class=\"mt-0.5 w-full overflow-hidden text-ellipsis whitespace-nowrap text-sm leading-normal text-current opacity-50\"\n                >\n                    brian.hughes&#64;company.com\n                </div>\n            </div>\n        </div>\n    </ng-container>\n</fuse-vertical-navigation>\n\n<!-- Wrapper -->\n<div class=\"flex w-full min-w-0 flex-auto flex-col\">\n    <!-- Header -->\n    <div\n        class=\"bg-card relative z-49 flex h-16 w-full flex-0 items-center px-4 shadow dark:border-b dark:bg-transparent dark:shadow-none md:px-6 print:hidden\"\n    >\n        <!-- Navigation toggle button -->\n        <button\n            class=\"mr-2\"\n            mat-icon-button\n            (click)=\"toggleNavigation('mainNavigation')\"\n        >\n            <mat-icon [svgIcon]=\"'heroicons_outline:bars-3'\"></mat-icon>\n        </button>\n        <!-- Components -->\n        <div class=\"ml-auto flex items-center space-x-0.5 pl-2 sm:space-x-2\">\n            <languages></languages>\n            <fuse-fullscreen class=\"hidden md:block\"></fuse-fullscreen>\n            <search [appearance]=\"'bar'\"></search>\n            <shortcuts></shortcuts>\n            <messages></messages>\n            <notifications></notifications>\n            <button\n                class=\"lg:hidden\"\n                mat-icon-button\n                (click)=\"quickChat.toggle()\"\n            >\n                <mat-icon\n                    [svgIcon]=\"'heroicons_outline:chat-bubble-left-right'\"\n                ></mat-icon>\n            </button>\n        </div>\n    </div>\n\n    <!-- Content -->\n    <div class=\"flex flex-auto flex-col\">\n        <!-- *ngIf=\"true\" hack is required here for router-outlet to work correctly.\n             Otherwise, layout changes won't be registered and the view won't be updated! -->\n        @if (true) {\n            <router-outlet></router-outlet>\n        }\n    </div>\n\n    <!-- Footer -->\n    <div\n        class=\"bg-card relative z-49 flex h-14 w-full flex-0 items-center justify-start border-t px-4 dark:bg-transparent md:px-6 print:hidden\"\n    >\n        <span class=\"text-secondary font-medium\"\n            >Fuse &copy; {{ currentYear }}</span\n        >\n    </div>\n</div>\n\n<!-- Quick chat -->\n<quick-chat #quickChat=\"quickChat\"></quick-chat>\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAAiCC,YAAY,QAAQ,iBAAiB;AACtE,SAASC,uBAAuB,QAAQ,6BAA6B;AACrE,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAEIC,+BAA+B,QAC5B,6BAA6B;AAMpC,SAASC,kBAAkB,QAAQ,iDAAiD;AACpF,SAASC,iBAAiB,QAAQ,+CAA+C;AACjF,SAASC,sBAAsB,QAAQ,yDAAyD;AAChG,SAASC,kBAAkB,QAAQ,mDAAmD;AACtF,SAASC,eAAe,QAAQ,2CAA2C;AAC3E,SAASC,kBAAkB,QAAQ,iDAAiD;AACpF,SAASC,aAAa,QAAQ,uCAAuC;AACrE,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;;;;;;;;;;;ICwD7BC,EAAA,CAAAC,SAAA,oBAA+B;;;ADlC3C,OAAM,MAAOC,yBAAyB;EAMlC;;;EAGAC,YACYC,eAA+B,EAC/BC,OAAe,EACfC,kBAAqC,EACrCC,YAAyB,EACzBC,wBAAiD,EACjDC,sBAA6C;IAL7C,KAAAL,eAAe,GAAfA,eAAe;IACf,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,sBAAsB,GAAtBA,sBAAsB;IAX1B,KAAAC,eAAe,GAAiB,IAAIZ,OAAO,EAAO;EAYvD;EAEH;EACA;EACA;EAEA;;;EAGA,IAAIa,WAAWA,CAAA;IACX,OAAO,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;EACnC;EAEA;EACA;EACA;EAEA;;;EAGAC,QAAQA,CAAA;IACJ;IACA,IAAI,CAACR,kBAAkB,CAACS,WAAW,CAC9BC,IAAI,CAACjB,SAAS,CAAC,IAAI,CAACW,eAAe,CAAC,CAAC,CACrCO,SAAS,CAAEC,UAAsB,IAAI;MAClC,IAAI,CAACA,UAAU,GAAGA,UAAU;IAChC,CAAC,CAAC;IAEN;IACA,IAAI,CAACX,YAAY,CAACY,KAAK,CAClBH,IAAI,CAACjB,SAAS,CAAC,IAAI,CAACW,eAAe,CAAC,CAAC,CACrCO,SAAS,CAAEG,IAAU,IAAI;MACtB,IAAI,CAACA,IAAI,GAAGA,IAAI;IACpB,CAAC,CAAC;IAEN;IACA,IAAI,CAACZ,wBAAwB,CAACa,cAAc,CACvCL,IAAI,CAACjB,SAAS,CAAC,IAAI,CAACW,eAAe,CAAC,CAAC,CACrCO,SAAS,CAAC,CAAC;MAAEK;IAAe,CAAE,KAAI;MAC/B;MACA,IAAI,CAACC,aAAa,GAAG,CAACD,eAAe,CAACE,QAAQ,CAAC,IAAI,CAAC;IACxD,CAAC,CAAC;EACV;EAEA;;;EAGAC,WAAWA,CAAA;IACP;IACA,IAAI,CAACf,eAAe,CAACgB,IAAI,CAAC,IAAI,CAAC;IAC/B,IAAI,CAAChB,eAAe,CAACiB,QAAQ,EAAE;EACnC;EAEA;EACA;EACA;EAEA;;;;;EAKAC,gBAAgBA,CAACC,IAAY;IACzB;IACA,MAAMX,UAAU,GACZ,IAAI,CAACT,sBAAsB,CAACqB,YAAY,CACpCD,IAAI,CACP;IAEL,IAAIX,UAAU,EAAE;MACZ;MACAA,UAAU,CAACa,MAAM,EAAE;IACvB;EACJ;EAAC,QAAAC,CAAA,G;qCAzFQ9B,yBAAyB,EAAAF,EAAA,CAAAiC,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAnC,EAAA,CAAAiC,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAApC,EAAA,CAAAiC,iBAAA,CAAAI,EAAA,CAAAC,iBAAA,GAAAtC,EAAA,CAAAiC,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAAxC,EAAA,CAAAiC,iBAAA,CAAAQ,EAAA,CAAAC,uBAAA,GAAA1C,EAAA,CAAAiC,iBAAA,CAAAU,EAAA,CAAAC,qBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAzB3C,yBAAyB;IAAA4C,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QC3CtCpD,EAAA,CAAAC,SAAA,uBAAqC;QAGrCD,EAAA,CAAAsD,cAAA,kCAMC;QAEGtD,EAAA,CAAAuD,uBAAA,MAA2C;QAEvCvD,EAAA,CAAAsD,cAAA,aAA6C;QACzCtD,EAAA,CAAAC,SAAA,aAA4D;QAChED,EAAA,CAAAwD,YAAA,EAAM;;QAGVxD,EAAA,CAAAuD,uBAAA,MAA2C;QAEvCvD,EAAA,CAAAsD,cAAA,aAAyD;QACrDtD,EAAA,CAAAC,SAAA,WAAa;QAETD,EADJ,CAAAsD,cAAA,aAAuD,aAGlD;QACGtD,EAAA,CAAAyD,MAAA,IACJ;QAAAzD,EAAA,CAAAwD,YAAA,EAAM;QACNxD,EAAA,CAAAsD,cAAA,cAEC;QACGtD,EAAA,CAAAyD,MAAA,kCACJ;QAERzD,EAFQ,CAAAwD,YAAA,EAAM,EACJ,EACJ;;QAEdxD,EAAA,CAAAwD,YAAA,EAA2B;QASnBxD,EANR,CAAAsD,cAAA,eAAoD,eAI/C,kBAMI;QADGtD,EAAA,CAAA0D,UAAA,mBAAAC,4DAAA;UAAA3D,EAAA,CAAA4D,aAAA,CAAAC,GAAA;UAAA,OAAA7D,EAAA,CAAA8D,WAAA,CAAST,GAAA,CAAAzB,gBAAA,CAAiB,gBAAgB,CAAC;QAAA,EAAC;QAE5C5B,EAAA,CAAAC,SAAA,oBAA4D;QAChED,EAAA,CAAAwD,YAAA,EAAS;QAETxD,EAAA,CAAAsD,cAAA,eAAqE;QAMjEtD,EALA,CAAAC,SAAA,iBAAuB,2BACoC,kBACrB,iBACf,gBACF,qBACU;QAC/BD,EAAA,CAAAsD,cAAA,kBAIC;QADGtD,EAAA,CAAA0D,UAAA,mBAAAK,4DAAA;UAAA/D,EAAA,CAAA4D,aAAA,CAAAC,GAAA;UAAA,MAAAG,YAAA,GAAAhE,EAAA,CAAAiE,WAAA;UAAA,OAAAjE,EAAA,CAAA8D,WAAA,CAASE,YAAA,CAAAjC,MAAA,EAAkB;QAAA,EAAC;QAE5B/B,EAAA,CAAAC,SAAA,oBAEY;QAGxBD,EAFQ,CAAAwD,YAAA,EAAS,EACP,EACJ;QAGNxD,EAAA,CAAAsD,cAAA,eAAqC;QAGjCtD,EAAA,CAAAkE,mBAAA,KAAAC,iDAAA,wBAAY;QAGhBnE,EAAA,CAAAwD,YAAA,EAAM;QAMFxD,EAHJ,CAAAsD,cAAA,eAEC,gBAEQ;QAAAtD,EAAA,CAAAyD,MAAA,IAA6B;QAG1CzD,EAH0C,CAAAwD,YAAA,EACjC,EACC,EACJ;QAGNxD,EAAA,CAAAC,SAAA,2BAAgD;;;QAvF5CD,EAAA,CAAAoE,SAAA,EAAwC;QAGxCpE,EAHA,CAAAqE,UAAA,SAAAhB,GAAA,CAAA9B,aAAA,mBAAwC,0BACf,eAAA8B,GAAA,CAAAnC,UAAA,CAAAoD,UAAA,CACW,YAAAjB,GAAA,CAAA9B,aAAA,CACX;QAkBTvB,EAAA,CAAAoE,SAAA,GACJ;QADIpE,EAAA,CAAAuE,kBAAA,MAAAlB,GAAA,CAAAjC,IAAA,CAAAS,IAAA,MACJ;QAuBM7B,EAAA,CAAAoE,SAAA,GAAsC;QAAtCpE,EAAA,CAAAqE,UAAA,uCAAsC;QAMxCrE,EAAA,CAAAoE,SAAA,GAAoB;QAApBpE,EAAA,CAAAqE,UAAA,qBAAoB;QAUpBrE,EAAA,CAAAoE,SAAA,GAAsD;QAAtDpE,EAAA,CAAAqE,UAAA,uDAAsD;QAUlErE,EAAA,CAAAoE,SAAA,GAEC;QAFDpE,EAAA,CAAAwE,aAAA,gBAEC;QAQIxE,EAAA,CAAAoE,SAAA,GAA6B;QAA7BpE,EAAA,CAAAuE,kBAAA,iBAAAlB,GAAA,CAAA1C,WAAA,CAA6B;;;mBD1DlCtB,uBAAuB,EACvBC,+BAA+B,EAC/BO,aAAa,EACbZ,eAAe,EAAAwF,EAAA,CAAAC,aAAA,EACfxF,aAAa,EAAAyF,EAAA,CAAAC,OAAA,EACbrF,kBAAkB,EAClBH,uBAAuB,EACvBO,eAAe,EACfC,kBAAkB,EAClBJ,iBAAiB,EACjBC,sBAAsB,EACtBN,YAAY,EACZO,kBAAkB;IAAAmF,aAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}