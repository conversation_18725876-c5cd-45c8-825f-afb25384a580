{"ast": null, "code": "import { animate, AnimationBuilder, style } from '@angular/animations';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { ScrollStrategyOptions } from '@angular/cdk/overlay';\nimport { DOCUMENT } from '@angular/common';\nimport { ChangeDetectorRef, ElementRef, EventEmitter, inject, Renderer2 } from '@angular/core';\nimport { NavigationEnd, Router } from '@angular/router';\nimport { fuseAnimations } from '@fuse/animations';\nimport { FuseNavigationService } from '@fuse/components/navigation/navigation.service';\nimport { FuseVerticalNavigationAsideItemComponent } from '@fuse/components/navigation/vertical/components/aside/aside.component';\nimport { FuseVerticalNavigationBasicItemComponent } from '@fuse/components/navigation/vertical/components/basic/basic.component';\nimport { FuseVerticalNavigationCollapsableItemComponent } from '@fuse/components/navigation/vertical/components/collapsable/collapsable.component';\nimport { FuseVerticalNavigationDividerItemComponent } from '@fuse/components/navigation/vertical/components/divider/divider.component';\nimport { FuseVerticalNavigationGroupItemComponent } from '@fuse/components/navigation/vertical/components/group/group.component';\nimport { FuseVerticalNavigationSpacerItemComponent } from '@fuse/components/navigation/vertical/components/spacer/spacer.component';\nimport { FuseScrollbarDirective } from '@fuse/directives/scrollbar/scrollbar.directive';\nimport { FuseUtilsService } from '@fuse/services/utils/utils.service';\nimport { delay, filter, merge, ReplaySubject, Subject, takeUntil } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nconst _c0 = [\"navigationContent\"];\nconst _c1 = [[[\"\", \"fuseVerticalNavigationHeader\", \"\"]], [[\"\", \"fuseVerticalNavigationContentHeader\", \"\"]], [[\"\", \"fuseVerticalNavigationContentFooter\", \"\"]], [[\"\", \"fuseVerticalNavigationFooter\", \"\"]]];\nconst _c2 = [\"[fuseVerticalNavigationHeader]\", \"[fuseVerticalNavigationContentHeader]\", \"[fuseVerticalNavigationContentFooter]\", \"[fuseVerticalNavigationFooter]\"];\nconst _c3 = a0 => ({\n  wheelPropagation: a0,\n  suppressScrollX: true\n});\nconst _c4 = () => ({\n  wheelPropagation: false,\n  suppressScrollX: true\n});\nfunction FuseVerticalNavigationComponent_For_8_Conditional_0_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"fuse-vertical-navigation-aside-item\", 11);\n    i0.ɵɵlistener(\"click\", function FuseVerticalNavigationComponent_For_8_Conditional_0_Conditional_0_Template_fuse_vertical_navigation_aside_item_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const item_r2 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleAside(item_r2));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"item\", item_r2)(\"name\", ctx_r2.name)(\"activeItemId\", ctx_r2.activeAsideItemId)(\"autoCollapse\", ctx_r2.autoCollapse)(\"skipChildren\", true);\n  }\n}\nfunction FuseVerticalNavigationComponent_For_8_Conditional_0_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"fuse-vertical-navigation-basic-item\", 9);\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"item\", item_r2)(\"name\", ctx_r2.name);\n  }\n}\nfunction FuseVerticalNavigationComponent_For_8_Conditional_0_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"fuse-vertical-navigation-collapsable-item\", 10);\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"item\", item_r2)(\"name\", ctx_r2.name)(\"autoCollapse\", ctx_r2.autoCollapse);\n  }\n}\nfunction FuseVerticalNavigationComponent_For_8_Conditional_0_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"fuse-vertical-navigation-divider-item\", 9);\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"item\", item_r2)(\"name\", ctx_r2.name);\n  }\n}\nfunction FuseVerticalNavigationComponent_For_8_Conditional_0_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"fuse-vertical-navigation-group-item\", 10);\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"item\", item_r2)(\"name\", ctx_r2.name)(\"autoCollapse\", ctx_r2.autoCollapse);\n  }\n}\nfunction FuseVerticalNavigationComponent_For_8_Conditional_0_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"fuse-vertical-navigation-spacer-item\", 9);\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"item\", item_r2)(\"name\", ctx_r2.name);\n  }\n}\nfunction FuseVerticalNavigationComponent_For_8_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵconditionalCreate(0, FuseVerticalNavigationComponent_For_8_Conditional_0_Conditional_0_Template, 1, 5, \"fuse-vertical-navigation-aside-item\", 8);\n    i0.ɵɵconditionalCreate(1, FuseVerticalNavigationComponent_For_8_Conditional_0_Conditional_1_Template, 1, 2, \"fuse-vertical-navigation-basic-item\", 9);\n    i0.ɵɵconditionalCreate(2, FuseVerticalNavigationComponent_For_8_Conditional_0_Conditional_2_Template, 1, 3, \"fuse-vertical-navigation-collapsable-item\", 10);\n    i0.ɵɵconditionalCreate(3, FuseVerticalNavigationComponent_For_8_Conditional_0_Conditional_3_Template, 1, 2, \"fuse-vertical-navigation-divider-item\", 9);\n    i0.ɵɵconditionalCreate(4, FuseVerticalNavigationComponent_For_8_Conditional_0_Conditional_4_Template, 1, 3, \"fuse-vertical-navigation-group-item\", 10);\n    i0.ɵɵconditionalCreate(5, FuseVerticalNavigationComponent_For_8_Conditional_0_Conditional_5_Template, 1, 2, \"fuse-vertical-navigation-spacer-item\", 9);\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵconditional(item_r2.type === \"aside\" ? 0 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(item_r2.type === \"basic\" ? 1 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(item_r2.type === \"collapsable\" ? 2 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(item_r2.type === \"divider\" ? 3 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(item_r2.type === \"group\" ? 4 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(item_r2.type === \"spacer\" ? 5 : -1);\n  }\n}\nfunction FuseVerticalNavigationComponent_For_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵconditionalCreate(0, FuseVerticalNavigationComponent_For_8_Conditional_0_Template, 6, 6);\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵconditional(item_r2.hidden && !item_r2.hidden(item_r2) || !item_r2.hidden ? 0 : -1);\n  }\n}\nfunction FuseVerticalNavigationComponent_Conditional_13_For_2_Conditional_0_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"fuse-vertical-navigation-aside-item\", 10);\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"item\", item_r4)(\"name\", ctx_r2.name)(\"autoCollapse\", ctx_r2.autoCollapse);\n  }\n}\nfunction FuseVerticalNavigationComponent_Conditional_13_For_2_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵconditionalCreate(0, FuseVerticalNavigationComponent_Conditional_13_For_2_Conditional_0_Conditional_0_Template, 1, 3, \"fuse-vertical-navigation-aside-item\", 10);\n  }\n  if (rf & 2) {\n    const item_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵconditional(item_r4.type === \"aside\" && item_r4.id === ctx_r2.activeAsideItemId ? 0 : -1);\n  }\n}\nfunction FuseVerticalNavigationComponent_Conditional_13_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵconditionalCreate(0, FuseVerticalNavigationComponent_Conditional_13_For_2_Conditional_0_Template, 1, 1);\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    i0.ɵɵconditional(item_r4.hidden && !item_r4.hidden(item_r4) || !item_r4.hidden ? 0 : -1);\n  }\n}\nfunction FuseVerticalNavigationComponent_Conditional_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵrepeaterCreate(1, FuseVerticalNavigationComponent_Conditional_13_For_2_Template, 1, 1, null, null, i0.ɵɵcomponentInstance().trackByFn, true);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"fuseScrollbarOptions\", i0.ɵɵpureFunction0(5, _c4))(\"@fadeInLeft\", ctx_r2.position === \"left\")(\"@fadeInRight\", ctx_r2.position === \"right\")(\"@fadeOutLeft\", ctx_r2.position === \"left\")(\"@fadeOutRight\", ctx_r2.position === \"right\");\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r2.navigation);\n  }\n}\nexport class FuseVerticalNavigationComponent {\n  /**\n   * Constructor\n   */\n  constructor() {\n    /* eslint-enable @typescript-eslint/naming-convention */\n    this._animationBuilder = inject(AnimationBuilder);\n    this._changeDetectorRef = inject(ChangeDetectorRef);\n    this._document = inject(DOCUMENT);\n    this._elementRef = inject(ElementRef);\n    this._renderer2 = inject(Renderer2);\n    this._router = inject(Router);\n    this._scrollStrategyOptions = inject(ScrollStrategyOptions);\n    this._fuseNavigationService = inject(FuseNavigationService);\n    this._fuseUtilsService = inject(FuseUtilsService);\n    this.appearance = 'default';\n    this.autoCollapse = true;\n    this.inner = false;\n    this.mode = 'side';\n    this.name = this._fuseUtilsService.randomId();\n    this.opened = true;\n    this.position = 'left';\n    this.transparentOverlay = false;\n    this.appearanceChanged = new EventEmitter();\n    this.modeChanged = new EventEmitter();\n    this.openedChanged = new EventEmitter();\n    this.positionChanged = new EventEmitter();\n    this.activeAsideItemId = null;\n    this.onCollapsableItemCollapsed = new ReplaySubject(1);\n    this.onCollapsableItemExpanded = new ReplaySubject(1);\n    this.onRefreshed = new ReplaySubject(1);\n    this._animationsEnabled = false;\n    this._hovered = false;\n    this._scrollStrategy = this._scrollStrategyOptions.block();\n    this._unsubscribeAll = new Subject();\n    this._handleAsideOverlayClick = () => {\n      this.closeAside();\n    };\n    this._handleOverlayClick = () => {\n      this.close();\n    };\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Accessors\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * Host binding for component classes\n   */\n  get classList() {\n    /* eslint-disable @typescript-eslint/naming-convention */\n    return {\n      'fuse-vertical-navigation-animations-enabled': this._animationsEnabled,\n      [`fuse-vertical-navigation-appearance-${this.appearance}`]: true,\n      'fuse-vertical-navigation-hover': this._hovered,\n      'fuse-vertical-navigation-inner': this.inner,\n      'fuse-vertical-navigation-mode-over': this.mode === 'over',\n      'fuse-vertical-navigation-mode-side': this.mode === 'side',\n      'fuse-vertical-navigation-opened': this.opened,\n      'fuse-vertical-navigation-position-left': this.position === 'left',\n      'fuse-vertical-navigation-position-right': this.position === 'right'\n    };\n    /* eslint-enable @typescript-eslint/naming-convention */\n  }\n  /**\n   * Host binding for component inline styles\n   */\n  get styleList() {\n    return {\n      visibility: this.opened ? 'visible' : 'hidden'\n    };\n  }\n  /**\n   * Setter for fuseScrollbarDirectives\n   */\n  set fuseScrollbarDirectives(fuseScrollbarDirectives) {\n    // Store the directives\n    this._fuseScrollbarDirectives = fuseScrollbarDirectives;\n    // Return if there are no directives\n    if (fuseScrollbarDirectives.length === 0) {\n      return;\n    }\n    // Unsubscribe the previous subscriptions\n    if (this._fuseScrollbarDirectivesSubscription) {\n      this._fuseScrollbarDirectivesSubscription.unsubscribe();\n    }\n    // Update the scrollbars on collapsable items' collapse/expand\n    this._fuseScrollbarDirectivesSubscription = merge(this.onCollapsableItemCollapsed, this.onCollapsableItemExpanded).pipe(takeUntil(this._unsubscribeAll), delay(250)).subscribe(() => {\n      // Loop through the scrollbars and update them\n      fuseScrollbarDirectives.forEach(fuseScrollbarDirective => {\n        fuseScrollbarDirective.update();\n      });\n    });\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Decorated methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * On mouseenter\n   *\n   * @private\n   */\n  _onMouseenter() {\n    // Enable the animations\n    this._enableAnimations();\n    // Set the hovered\n    this._hovered = true;\n  }\n  /**\n   * On mouseleave\n   *\n   * @private\n   */\n  _onMouseleave() {\n    // Enable the animations\n    this._enableAnimations();\n    // Set the hovered\n    this._hovered = false;\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Lifecycle hooks\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * On changes\n   *\n   * @param changes\n   */\n  ngOnChanges(changes) {\n    // Appearance\n    if ('appearance' in changes) {\n      // Execute the observable\n      this.appearanceChanged.next(changes.appearance.currentValue);\n    }\n    // Inner\n    if ('inner' in changes) {\n      // Coerce the value to a boolean\n      this.inner = coerceBooleanProperty(changes.inner.currentValue);\n    }\n    // Mode\n    if ('mode' in changes) {\n      // Get the previous and current values\n      const currentMode = changes.mode.currentValue;\n      const previousMode = changes.mode.previousValue;\n      // Disable the animations\n      this._disableAnimations();\n      // If the mode changes: 'over -> side'\n      if (previousMode === 'over' && currentMode === 'side') {\n        // Hide the overlay\n        this._hideOverlay();\n      }\n      // If the mode changes: 'side -> over'\n      if (previousMode === 'side' && currentMode === 'over') {\n        // Close the aside\n        this.closeAside();\n        // If the navigation is opened\n        if (this.opened) {\n          // Show the overlay\n          this._showOverlay();\n        }\n      }\n      // Execute the observable\n      this.modeChanged.next(currentMode);\n      // Enable the animations after a delay\n      // The delay must be bigger than the current transition-duration\n      // to make sure nothing will be animated while the mode changing\n      setTimeout(() => {\n        this._enableAnimations();\n      }, 500);\n    }\n    // Navigation\n    if ('navigation' in changes) {\n      // Mark for check\n      this._changeDetectorRef.markForCheck();\n    }\n    // Opened\n    if ('opened' in changes) {\n      // Coerce the value to a boolean\n      this.opened = coerceBooleanProperty(changes.opened.currentValue);\n      // Open/close the navigation\n      this._toggleOpened(this.opened);\n    }\n    // Position\n    if ('position' in changes) {\n      // Execute the observable\n      this.positionChanged.next(changes.position.currentValue);\n    }\n    // Transparent overlay\n    if ('transparentOverlay' in changes) {\n      // Coerce the value to a boolean\n      this.transparentOverlay = coerceBooleanProperty(changes.transparentOverlay.currentValue);\n    }\n  }\n  /**\n   * On init\n   */\n  ngOnInit() {\n    // Make sure the name input is not an empty string\n    if (this.name === '') {\n      this.name = this._fuseUtilsService.randomId();\n    }\n    // Register the navigation component\n    this._fuseNavigationService.registerComponent(this.name, this);\n    // Subscribe to the 'NavigationEnd' event\n    this._router.events.pipe(filter(event => event instanceof NavigationEnd), takeUntil(this._unsubscribeAll)).subscribe(() => {\n      // If the mode is 'over' and the navigation is opened...\n      if (this.mode === 'over' && this.opened) {\n        // Close the navigation\n        this.close();\n      }\n      // If the mode is 'side' and the aside is active...\n      if (this.mode === 'side' && this.activeAsideItemId) {\n        // Close the aside\n        this.closeAside();\n      }\n    });\n  }\n  /**\n   * After view init\n   */\n  ngAfterViewInit() {\n    // Fix for Firefox.\n    //\n    // Because 'position: sticky' doesn't work correctly inside a 'position: fixed' parent,\n    // adding the '.cdk-global-scrollblock' to the html element breaks the navigation's position.\n    // This fixes the problem by reading the 'top' value from the html element and adding it as a\n    // 'marginTop' to the navigation itself.\n    this._mutationObserver = new MutationObserver(mutations => {\n      mutations.forEach(mutation => {\n        const mutationTarget = mutation.target;\n        if (mutation.attributeName === 'class') {\n          if (mutationTarget.classList.contains('cdk-global-scrollblock')) {\n            const top = parseInt(mutationTarget.style.top, 10);\n            this._renderer2.setStyle(this._elementRef.nativeElement, 'margin-top', `${Math.abs(top)}px`);\n          } else {\n            this._renderer2.setStyle(this._elementRef.nativeElement, 'margin-top', null);\n          }\n        }\n      });\n    });\n    this._mutationObserver.observe(this._document.documentElement, {\n      attributes: true,\n      attributeFilter: ['class']\n    });\n    setTimeout(() => {\n      // Return if 'navigation content' element does not exist\n      if (!this._navigationContentEl) {\n        return;\n      }\n      // If 'navigation content' element doesn't have\n      // perfect scrollbar activated on it...\n      if (!this._navigationContentEl.nativeElement.classList.contains('ps')) {\n        // Find the active item\n        const activeItem = this._navigationContentEl.nativeElement.querySelector('.fuse-vertical-navigation-item-active');\n        // If the active item exists, scroll it into view\n        if (activeItem) {\n          activeItem.scrollIntoView();\n        }\n      }\n      // Otherwise\n      else {\n        // Go through all the scrollbar directives\n        this._fuseScrollbarDirectives.forEach(fuseScrollbarDirective => {\n          // Skip if not enabled\n          if (!fuseScrollbarDirective.isEnabled()) {\n            return;\n          }\n          // Scroll to the active element\n          fuseScrollbarDirective.scrollToElement('.fuse-vertical-navigation-item-active', -120, true);\n        });\n      }\n    });\n  }\n  /**\n   * On destroy\n   */\n  ngOnDestroy() {\n    // Disconnect the mutation observer\n    this._mutationObserver.disconnect();\n    // Forcefully close the navigation and aside in case they are opened\n    this.close();\n    this.closeAside();\n    // Deregister the navigation component from the registry\n    this._fuseNavigationService.deregisterComponent(this.name);\n    // Unsubscribe from all subscriptions\n    this._unsubscribeAll.next(null);\n    this._unsubscribeAll.complete();\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Public methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * Refresh the component to apply the changes\n   */\n  refresh() {\n    // Mark for check\n    this._changeDetectorRef.markForCheck();\n    // Execute the observable\n    this.onRefreshed.next(true);\n  }\n  /**\n   * Open the navigation\n   */\n  open() {\n    // Return if the navigation is already open\n    if (this.opened) {\n      return;\n    }\n    // Set the opened\n    this._toggleOpened(true);\n  }\n  /**\n   * Close the navigation\n   */\n  close() {\n    // Return if the navigation is already closed\n    if (!this.opened) {\n      return;\n    }\n    // Close the aside\n    this.closeAside();\n    // Set the opened\n    this._toggleOpened(false);\n  }\n  /**\n   * Toggle the navigation\n   */\n  toggle() {\n    // Toggle\n    if (this.opened) {\n      this.close();\n    } else {\n      this.open();\n    }\n  }\n  /**\n   * Open the aside\n   *\n   * @param item\n   */\n  openAside(item) {\n    // Return if the item is disabled\n    if (item.disabled || !item.id) {\n      return;\n    }\n    // Open\n    this.activeAsideItemId = item.id;\n    // Show the aside overlay\n    this._showAsideOverlay();\n    // Mark for check\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Close the aside\n   */\n  closeAside() {\n    // Close\n    this.activeAsideItemId = null;\n    // Hide the aside overlay\n    this._hideAsideOverlay();\n    // Mark for check\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Toggle the aside\n   *\n   * @param item\n   */\n  toggleAside(item) {\n    // Toggle\n    if (this.activeAsideItemId === item.id) {\n      this.closeAside();\n    } else {\n      this.openAside(item);\n    }\n  }\n  /**\n   * Track by function for ngFor loops\n   *\n   * @param index\n   * @param item\n   */\n  trackByFn(index, item) {\n    return item.id || index;\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Private methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * Enable the animations\n   *\n   * @private\n   */\n  _enableAnimations() {\n    // Return if the animations are already enabled\n    if (this._animationsEnabled) {\n      return;\n    }\n    // Enable the animations\n    this._animationsEnabled = true;\n  }\n  /**\n   * Disable the animations\n   *\n   * @private\n   */\n  _disableAnimations() {\n    // Return if the animations are already disabled\n    if (!this._animationsEnabled) {\n      return;\n    }\n    // Disable the animations\n    this._animationsEnabled = false;\n  }\n  /**\n   * Show the overlay\n   *\n   * @private\n   */\n  _showOverlay() {\n    // Return if there is already an overlay\n    if (this._asideOverlay) {\n      return;\n    }\n    // Create the overlay element\n    this._overlay = this._renderer2.createElement('div');\n    // Add a class to the overlay element\n    this._overlay.classList.add('fuse-vertical-navigation-overlay');\n    // Add a class depending on the transparentOverlay option\n    if (this.transparentOverlay) {\n      this._overlay.classList.add('fuse-vertical-navigation-overlay-transparent');\n    }\n    // Append the overlay to the parent of the navigation\n    this._renderer2.appendChild(this._elementRef.nativeElement.parentElement, this._overlay);\n    // Enable block scroll strategy\n    this._scrollStrategy.enable();\n    // Create the enter animation and attach it to the player\n    this._player = this._animationBuilder.build([animate('300ms cubic-bezier(0.25, 0.8, 0.25, 1)', style({\n      opacity: 1\n    }))]).create(this._overlay);\n    // Play the animation\n    this._player.play();\n    // Add an event listener to the overlay\n    this._overlay.addEventListener('click', this._handleOverlayClick);\n  }\n  /**\n   * Hide the overlay\n   *\n   * @private\n   */\n  _hideOverlay() {\n    if (!this._overlay) {\n      return;\n    }\n    // Create the leave animation and attach it to the player\n    this._player = this._animationBuilder.build([animate('300ms cubic-bezier(0.25, 0.8, 0.25, 1)', style({\n      opacity: 0\n    }))]).create(this._overlay);\n    // Play the animation\n    this._player.play();\n    // Once the animation is done...\n    this._player.onDone(() => {\n      // If the overlay still exists...\n      if (this._overlay) {\n        // Remove the event listener\n        this._overlay.removeEventListener('click', this._handleOverlayClick);\n        // Remove the overlay\n        this._overlay.parentNode.removeChild(this._overlay);\n        this._overlay = null;\n      }\n      // Disable block scroll strategy\n      this._scrollStrategy.disable();\n    });\n  }\n  /**\n   * Show the aside overlay\n   *\n   * @private\n   */\n  _showAsideOverlay() {\n    // Return if there is already an overlay\n    if (this._asideOverlay) {\n      return;\n    }\n    // Create the aside overlay element\n    this._asideOverlay = this._renderer2.createElement('div');\n    // Add a class to the aside overlay element\n    this._asideOverlay.classList.add('fuse-vertical-navigation-aside-overlay');\n    // Append the aside overlay to the parent of the navigation\n    this._renderer2.appendChild(this._elementRef.nativeElement.parentElement, this._asideOverlay);\n    // Create the enter animation and attach it to the player\n    this._player = this._animationBuilder.build([animate('300ms cubic-bezier(0.25, 0.8, 0.25, 1)', style({\n      opacity: 1\n    }))]).create(this._asideOverlay);\n    // Play the animation\n    this._player.play();\n    // Add an event listener to the aside overlay\n    this._asideOverlay.addEventListener('click', this._handleAsideOverlayClick);\n  }\n  /**\n   * Hide the aside overlay\n   *\n   * @private\n   */\n  _hideAsideOverlay() {\n    if (!this._asideOverlay) {\n      return;\n    }\n    // Create the leave animation and attach it to the player\n    this._player = this._animationBuilder.build([animate('300ms cubic-bezier(0.25, 0.8, 0.25, 1)', style({\n      opacity: 0\n    }))]).create(this._asideOverlay);\n    // Play the animation\n    this._player.play();\n    // Once the animation is done...\n    this._player.onDone(() => {\n      // If the aside overlay still exists...\n      if (this._asideOverlay) {\n        // Remove the event listener\n        this._asideOverlay.removeEventListener('click', this._handleAsideOverlayClick);\n        // Remove the aside overlay\n        this._asideOverlay.parentNode.removeChild(this._asideOverlay);\n        this._asideOverlay = null;\n      }\n    });\n  }\n  /**\n   * Open/close the navigation\n   *\n   * @param open\n   * @private\n   */\n  _toggleOpened(open) {\n    // Set the opened\n    this.opened = open;\n    // Enable the animations\n    this._enableAnimations();\n    // If the navigation opened, and the mode\n    // is 'over', show the overlay\n    if (this.mode === 'over') {\n      if (this.opened) {\n        this._showOverlay();\n      } else {\n        this._hideOverlay();\n      }\n    }\n    // Execute the observable\n    this.openedChanged.next(open);\n  }\n  static #_ = this.ɵfac = function FuseVerticalNavigationComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FuseVerticalNavigationComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: FuseVerticalNavigationComponent,\n    selectors: [[\"fuse-vertical-navigation\"]],\n    viewQuery: function FuseVerticalNavigationComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(FuseScrollbarDirective, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._navigationContentEl = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.fuseScrollbarDirectives = _t);\n      }\n    },\n    hostVars: 4,\n    hostBindings: function FuseVerticalNavigationComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"mouseenter\", function FuseVerticalNavigationComponent_mouseenter_HostBindingHandler() {\n          return ctx._onMouseenter();\n        })(\"mouseleave\", function FuseVerticalNavigationComponent_mouseleave_HostBindingHandler() {\n          return ctx._onMouseleave();\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵstyleMap(ctx.styleList);\n        i0.ɵɵclassMap(ctx.classList);\n      }\n    },\n    inputs: {\n      appearance: \"appearance\",\n      autoCollapse: \"autoCollapse\",\n      inner: \"inner\",\n      mode: \"mode\",\n      name: \"name\",\n      navigation: \"navigation\",\n      opened: \"opened\",\n      position: \"position\",\n      transparentOverlay: \"transparentOverlay\"\n    },\n    outputs: {\n      appearanceChanged: \"appearanceChanged\",\n      modeChanged: \"modeChanged\",\n      openedChanged: \"openedChanged\",\n      positionChanged: \"positionChanged\"\n    },\n    exportAs: [\"fuseVerticalNavigation\"],\n    features: [i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c2,\n    decls: 14,\n    vars: 4,\n    consts: [[\"navigationContent\", \"\"], [1, \"fuse-vertical-navigation-wrapper\"], [1, \"fuse-vertical-navigation-header\"], [\"fuseScrollbar\", \"\", 1, \"fuse-vertical-navigation-content\", 3, \"fuseScrollbarOptions\"], [1, \"fuse-vertical-navigation-content-header\"], [1, \"fuse-vertical-navigation-content-footer\"], [1, \"fuse-vertical-navigation-footer\"], [\"fuseScrollbar\", \"\", 1, \"fuse-vertical-navigation-aside-wrapper\", 3, \"fuseScrollbarOptions\"], [3, \"item\", \"name\", \"activeItemId\", \"autoCollapse\", \"skipChildren\"], [3, \"item\", \"name\"], [3, \"item\", \"name\", \"autoCollapse\"], [3, \"click\", \"item\", \"name\", \"activeItemId\", \"autoCollapse\", \"skipChildren\"]],\n    template: function FuseVerticalNavigationComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c1);\n        i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n        i0.ɵɵprojection(2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"div\", 3, 0)(5, \"div\", 4);\n        i0.ɵɵprojection(6, 1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵrepeaterCreate(7, FuseVerticalNavigationComponent_For_8_Template, 1, 1, null, null, ctx.trackByFn, true);\n        i0.ɵɵelementStart(9, \"div\", 5);\n        i0.ɵɵprojection(10, 2);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"div\", 6);\n        i0.ɵɵprojection(12, 3);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵconditionalCreate(13, FuseVerticalNavigationComponent_Conditional_13_Template, 3, 6, \"div\", 7);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"fuseScrollbarOptions\", i0.ɵɵpureFunction1(2, _c3, ctx.inner));\n        i0.ɵɵadvance(4);\n        i0.ɵɵrepeater(ctx.navigation);\n        i0.ɵɵadvance(6);\n        i0.ɵɵconditional(ctx.activeAsideItemId ? 13 : -1);\n      }\n    },\n    dependencies: [FuseScrollbarDirective, FuseVerticalNavigationAsideItemComponent, FuseVerticalNavigationBasicItemComponent, FuseVerticalNavigationCollapsableItemComponent, FuseVerticalNavigationDividerItemComponent, FuseVerticalNavigationGroupItemComponent, FuseVerticalNavigationSpacerItemComponent],\n    styles: [\"var resource;\\n/******/ (() => { // webpackBootstrap\\n/******/ \\tvar __webpack_modules__ = ({\\n\\n/***/ 31:\\n/*!*****************************************************************************************************!*\\\\\\n  !*** ./apps/eiot-admin/src/@fuse/components/navigation/vertical/vertical.component.scss?ngResource ***!\\n  \\\\*****************************************************************************************************/\\n/***/ (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/postcss-loader/dist/cjs.js):\\\\nError: Cannot find module 'chroma-js'\\\\nRequire stack:\\\\n- D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\apps\\\\\\\\eiot-admin\\\\\\\\src\\\\\\\\@fuse\\\\\\\\tailwind\\\\\\\\utils\\\\\\\\generate-palette.js\\\\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\\\\n    at Function.resolve (node:internal/modules/helpers:193:19)\\\\n    at _resolve (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:246378)\\\\n    at jiti (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:249092)\\\\n    at D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\apps\\\\\\\\eiot-admin\\\\\\\\src\\\\\\\\@fuse\\\\\\\\tailwind\\\\\\\\utils\\\\\\\\generate-palette.js:1:91\\\\n    at evalModule (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:251913)\\\\n    at jiti (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:249841)\\\\n    at D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\apps\\\\\\\\eiot-admin\\\\\\\\tailwind.config.js:4:25\\\\n    at evalModule (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:251913)\\\\n    at jiti (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:249841)\\\\n    at D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\tailwindcss\\\\\\\\lib\\\\\\\\lib\\\\\\\\load-config.js:48:30\\\\n    at loadConfig (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\tailwindcss\\\\\\\\lib\\\\\\\\lib\\\\\\\\load-config.js:50:6)\\\\n    at getTailwindConfig (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\tailwindcss\\\\\\\\lib\\\\\\\\lib\\\\\\\\setupTrackingContext.js:71:116)\\\\n    at D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\tailwindcss\\\\\\\\lib\\\\\\\\lib\\\\\\\\setupTrackingContext.js:100:92\\\\n    at D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\tailwindcss\\\\\\\\lib\\\\\\\\processTailwindFeatures.js:48:11\\\\n    at plugins (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\tailwindcss\\\\\\\\lib\\\\\\\\plugin.js:38:69)\\\\n    at LazyResult.runOnRoot (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\@angular-devkit\\\\\\\\build-angular\\\\\\\\node_modules\\\\\\\\postcss\\\\\\\\lib\\\\\\\\lazy-result.js:361:16)\\\\n    at LazyResult.runAsync (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\@angular-devkit\\\\\\\\build-angular\\\\\\\\node_modules\\\\\\\\postcss\\\\\\\\lib\\\\\\\\lazy-result.js:290:26)\\\\n    at LazyResult.async (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\@angular-devkit\\\\\\\\build-angular\\\\\\\\node_modules\\\\\\\\postcss\\\\\\\\lib\\\\\\\\lazy-result.js:192:30)\\\\n    at LazyResult.then (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\@angular-devkit\\\\\\\\build-angular\\\\\\\\node_modules\\\\\\\\postcss\\\\\\\\lib\\\\\\\\lazy-result.js:436:17)\\\");\\n\\n/***/ })\\n\\n/******/ \\t});\\n/************************************************************************/\\n/******/ \\t\\n/******/ \\t// startup\\n/******/ \\t// Load entry module and return exports\\n/******/ \\t// This entry module doesn't tell about it's top-level declarations so it can't be inlined\\n/******/ \\tvar __webpack_exports__ = {};\\n/******/ \\t__webpack_modules__[31]();\\n/******/ \\tresource = __webpack_exports__;\\n/******/ \\t\\n/******/ })()\\n;\"],\n    encapsulation: 2,\n    data: {\n      animation: fuseAnimations\n    },\n    changeDetection: 0\n  });\n}", "map": {"version": 3, "names": ["animate", "AnimationBuilder", "style", "coerceBooleanProperty", "ScrollStrategyOptions", "DOCUMENT", "ChangeDetectorRef", "ElementRef", "EventEmitter", "inject", "Renderer2", "NavigationEnd", "Router", "fuseAnimations", "FuseNavigationService", "FuseVerticalNavigationAsideItemComponent", "FuseVerticalNavigationBasicItemComponent", "FuseVerticalNavigationCollapsableItemComponent", "FuseVerticalNavigationDividerItemComponent", "FuseVerticalNavigationGroupItemComponent", "FuseVerticalNavigationSpacerItemComponent", "FuseScrollbarDirective", "FuseUtilsService", "delay", "filter", "merge", "ReplaySubject", "Subject", "takeUntil", "i0", "ɵɵelementStart", "ɵɵlistener", "FuseVerticalNavigationComponent_For_8_Conditional_0_Conditional_0_Template_fuse_vertical_navigation_aside_item_click_0_listener", "ɵɵrestoreView", "_r1", "item_r2", "ɵɵnextContext", "$implicit", "ctx_r2", "ɵɵresetView", "toggleAside", "ɵɵelementEnd", "ɵɵproperty", "name", "activeAsideItemId", "autoCollapse", "ɵɵelement", "ɵɵconditionalCreate", "FuseVerticalNavigationComponent_For_8_Conditional_0_Conditional_0_Template", "FuseVerticalNavigationComponent_For_8_Conditional_0_Conditional_1_Template", "FuseVerticalNavigationComponent_For_8_Conditional_0_Conditional_2_Template", "FuseVerticalNavigationComponent_For_8_Conditional_0_Conditional_3_Template", "FuseVerticalNavigationComponent_For_8_Conditional_0_Conditional_4_Template", "FuseVerticalNavigationComponent_For_8_Conditional_0_Conditional_5_Template", "ɵɵconditional", "type", "ɵɵadvance", "FuseVerticalNavigationComponent_For_8_Conditional_0_Template", "hidden", "item_r4", "FuseVerticalNavigationComponent_Conditional_13_For_2_Conditional_0_Conditional_0_Template", "id", "FuseVerticalNavigationComponent_Conditional_13_For_2_Conditional_0_Template", "ɵɵrepeaterCreate", "FuseVerticalNavigationComponent_Conditional_13_For_2_Template", "ɵɵcomponentInstance", "trackByFn", "ɵɵpureFunction0", "_c4", "position", "ɵɵrepeater", "navigation", "FuseVerticalNavigationComponent", "constructor", "_animationBuilder", "_changeDetectorRef", "_document", "_elementRef", "_renderer2", "_router", "_scrollStrategyOptions", "_fuseNavigationService", "_fuseUtilsService", "appearance", "inner", "mode", "randomId", "opened", "transparentOverlay", "<PERSON><PERSON><PERSON><PERSON>", "modeChanged", "openedChanged", "positionChanged", "onCollapsableItemCollapsed", "onCollapsableItemExpanded", "onRefreshed", "_animationsEnabled", "_hovered", "_scrollStrategy", "block", "_unsubscribeAll", "_handleAsideOverlayClick", "closeAside", "_handleOverlayClick", "close", "classList", "styleList", "visibility", "fuseScrollbarDirectives", "_fuseScrollbarDirectives", "length", "_fuseScrollbarDirectivesSubscription", "unsubscribe", "pipe", "subscribe", "for<PERSON>ach", "fuseScrollbarDirective", "update", "_onMouseenter", "_enableAnimations", "_onMouseleave", "ngOnChanges", "changes", "next", "currentValue", "currentMode", "previousMode", "previousValue", "_disableAnimations", "_hideOverlay", "_showOverlay", "setTimeout", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_toggleOpened", "ngOnInit", "registerComponent", "events", "event", "ngAfterViewInit", "_mutationObserver", "MutationObserver", "mutations", "mutation", "<PERSON><PERSON><PERSON><PERSON>", "target", "attributeName", "contains", "top", "parseInt", "setStyle", "nativeElement", "Math", "abs", "observe", "documentElement", "attributes", "attributeFilter", "_navigationContentEl", "activeItem", "querySelector", "scrollIntoView", "isEnabled", "scrollToElement", "ngOnDestroy", "disconnect", "deregisterComponent", "complete", "refresh", "open", "toggle", "openAside", "item", "disabled", "_showAsideOverlay", "_hideAsideOverlay", "index", "_aside<PERSON><PERSON>lay", "_overlay", "createElement", "add", "append<PERSON><PERSON><PERSON>", "parentElement", "enable", "_player", "build", "opacity", "create", "play", "addEventListener", "onDone", "removeEventListener", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "disable", "_", "_2", "selectors", "viewQuery", "FuseVerticalNavigationComponent_Query", "rf", "ctx", "FuseVerticalNavigationComponent_mouseenter_HostBindingHandler", "FuseVerticalNavigationComponent_mouseleave_HostBindingHandler", "ɵɵstyleMap", "ɵɵclassMap", "ɵɵprojection", "FuseVerticalNavigationComponent_For_8_Template", "FuseVerticalNavigationComponent_Conditional_13_Template", "ɵɵpureFunction1", "_c3", "styles", "encapsulation", "data", "animation", "changeDetection"], "sources": ["D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\@fuse\\components\\navigation\\vertical\\vertical.component.ts", "D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\@fuse\\components\\navigation\\vertical\\vertical.component.html"], "sourcesContent": ["import {\n    animate,\n    AnimationBuilder,\n    AnimationPlayer,\n    style,\n} from '@angular/animations';\nimport { BooleanInput, coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { ScrollStrategy, ScrollStrategyOptions } from '@angular/cdk/overlay';\nimport { DOCUMENT } from '@angular/common';\nimport {\n    AfterViewInit,\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    ElementRef,\n    EventEmitter,\n    HostBinding,\n    HostListener,\n    inject,\n    Input,\n    OnChanges,\n    OnDestroy,\n    OnInit,\n    Output,\n    QueryList,\n    Renderer2,\n    SimpleChanges,\n    ViewChild,\n    ViewChildren,\n    ViewEncapsulation,\n} from '@angular/core';\nimport { NavigationEnd, Router } from '@angular/router';\nimport { fuseAnimations } from '@fuse/animations';\nimport { FuseNavigationService } from '@fuse/components/navigation/navigation.service';\nimport {\n    FuseNavigationItem,\n    FuseVerticalNavigationAppearance,\n    FuseVerticalNavigationMode,\n    FuseVerticalNavigationPosition,\n} from '@fuse/components/navigation/navigation.types';\nimport { FuseVerticalNavigationAsideItemComponent } from '@fuse/components/navigation/vertical/components/aside/aside.component';\nimport { FuseVerticalNavigationBasicItemComponent } from '@fuse/components/navigation/vertical/components/basic/basic.component';\nimport { FuseVerticalNavigationCollapsableItemComponent } from '@fuse/components/navigation/vertical/components/collapsable/collapsable.component';\nimport { FuseVerticalNavigationDividerItemComponent } from '@fuse/components/navigation/vertical/components/divider/divider.component';\nimport { FuseVerticalNavigationGroupItemComponent } from '@fuse/components/navigation/vertical/components/group/group.component';\nimport { FuseVerticalNavigationSpacerItemComponent } from '@fuse/components/navigation/vertical/components/spacer/spacer.component';\nimport { FuseScrollbarDirective } from '@fuse/directives/scrollbar/scrollbar.directive';\nimport { FuseUtilsService } from '@fuse/services/utils/utils.service';\nimport {\n    delay,\n    filter,\n    merge,\n    ReplaySubject,\n    Subject,\n    Subscription,\n    takeUntil,\n} from 'rxjs';\n\n@Component({\n    selector: 'fuse-vertical-navigation',\n    templateUrl: './vertical.component.html',\n    styleUrls: ['./vertical.component.scss'],\n    animations: fuseAnimations,\n    encapsulation: ViewEncapsulation.None,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    exportAs: 'fuseVerticalNavigation',\n    imports: [\n        FuseScrollbarDirective,\n        FuseVerticalNavigationAsideItemComponent,\n        FuseVerticalNavigationBasicItemComponent,\n        FuseVerticalNavigationCollapsableItemComponent,\n        FuseVerticalNavigationDividerItemComponent,\n        FuseVerticalNavigationGroupItemComponent,\n        FuseVerticalNavigationSpacerItemComponent,\n    ],\n})\nexport class FuseVerticalNavigationComponent\n    implements OnChanges, OnInit, AfterViewInit, OnDestroy\n{\n    /* eslint-disable @typescript-eslint/naming-convention */\n    static ngAcceptInputType_inner: BooleanInput;\n    static ngAcceptInputType_opened: BooleanInput;\n    static ngAcceptInputType_transparentOverlay: BooleanInput;\n    /* eslint-enable @typescript-eslint/naming-convention */\n\n    private _animationBuilder = inject(AnimationBuilder);\n    private _changeDetectorRef = inject(ChangeDetectorRef);\n    private _document = inject(DOCUMENT);\n    private _elementRef = inject(ElementRef);\n    private _renderer2 = inject(Renderer2);\n    private _router = inject(Router);\n    private _scrollStrategyOptions = inject(ScrollStrategyOptions);\n    private _fuseNavigationService = inject(FuseNavigationService);\n    private _fuseUtilsService = inject(FuseUtilsService);\n\n    @Input() appearance: FuseVerticalNavigationAppearance = 'default';\n    @Input() autoCollapse: boolean = true;\n    @Input() inner: boolean = false;\n    @Input() mode: FuseVerticalNavigationMode = 'side';\n    @Input() name: string = this._fuseUtilsService.randomId();\n    @Input() navigation: FuseNavigationItem[];\n    @Input() opened: boolean = true;\n    @Input() position: FuseVerticalNavigationPosition = 'left';\n    @Input() transparentOverlay: boolean = false;\n    @Output()\n    readonly appearanceChanged: EventEmitter<FuseVerticalNavigationAppearance> =\n        new EventEmitter<FuseVerticalNavigationAppearance>();\n    @Output() readonly modeChanged: EventEmitter<FuseVerticalNavigationMode> =\n        new EventEmitter<FuseVerticalNavigationMode>();\n    @Output() readonly openedChanged: EventEmitter<boolean> =\n        new EventEmitter<boolean>();\n    @Output()\n    readonly positionChanged: EventEmitter<FuseVerticalNavigationPosition> =\n        new EventEmitter<FuseVerticalNavigationPosition>();\n    @ViewChild('navigationContent') private _navigationContentEl: ElementRef;\n\n    activeAsideItemId: string | null = null;\n    onCollapsableItemCollapsed: ReplaySubject<FuseNavigationItem> =\n        new ReplaySubject<FuseNavigationItem>(1);\n    onCollapsableItemExpanded: ReplaySubject<FuseNavigationItem> =\n        new ReplaySubject<FuseNavigationItem>(1);\n    onRefreshed: ReplaySubject<boolean> = new ReplaySubject<boolean>(1);\n    private _animationsEnabled: boolean = false;\n    private _asideOverlay: HTMLElement;\n    private readonly _handleAsideOverlayClick: any;\n    private readonly _handleOverlayClick: any;\n    private _hovered: boolean = false;\n    private _mutationObserver: MutationObserver;\n    private _overlay: HTMLElement;\n    private _player: AnimationPlayer;\n    private _scrollStrategy: ScrollStrategy =\n        this._scrollStrategyOptions.block();\n    private _fuseScrollbarDirectives!: QueryList<FuseScrollbarDirective>;\n    private _fuseScrollbarDirectivesSubscription: Subscription;\n    private _unsubscribeAll: Subject<any> = new Subject<any>();\n\n    /**\n     * Constructor\n     */\n    constructor() {\n        this._handleAsideOverlayClick = (): void => {\n            this.closeAside();\n        };\n        this._handleOverlayClick = (): void => {\n            this.close();\n        };\n    }\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Accessors\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * Host binding for component classes\n     */\n    @HostBinding('class') get classList(): any {\n        /* eslint-disable @typescript-eslint/naming-convention */\n        return {\n            'fuse-vertical-navigation-animations-enabled':\n                this._animationsEnabled,\n            [`fuse-vertical-navigation-appearance-${this.appearance}`]: true,\n            'fuse-vertical-navigation-hover': this._hovered,\n            'fuse-vertical-navigation-inner': this.inner,\n            'fuse-vertical-navigation-mode-over': this.mode === 'over',\n            'fuse-vertical-navigation-mode-side': this.mode === 'side',\n            'fuse-vertical-navigation-opened': this.opened,\n            'fuse-vertical-navigation-position-left': this.position === 'left',\n            'fuse-vertical-navigation-position-right':\n                this.position === 'right',\n        };\n        /* eslint-enable @typescript-eslint/naming-convention */\n    }\n\n    /**\n     * Host binding for component inline styles\n     */\n    @HostBinding('style') get styleList(): any {\n        return {\n            visibility: this.opened ? 'visible' : 'hidden',\n        };\n    }\n\n    /**\n     * Setter for fuseScrollbarDirectives\n     */\n    @ViewChildren(FuseScrollbarDirective)\n    set fuseScrollbarDirectives(\n        fuseScrollbarDirectives: QueryList<FuseScrollbarDirective>\n    ) {\n        // Store the directives\n        this._fuseScrollbarDirectives = fuseScrollbarDirectives;\n\n        // Return if there are no directives\n        if (fuseScrollbarDirectives.length === 0) {\n            return;\n        }\n\n        // Unsubscribe the previous subscriptions\n        if (this._fuseScrollbarDirectivesSubscription) {\n            this._fuseScrollbarDirectivesSubscription.unsubscribe();\n        }\n\n        // Update the scrollbars on collapsable items' collapse/expand\n        this._fuseScrollbarDirectivesSubscription = merge(\n            this.onCollapsableItemCollapsed,\n            this.onCollapsableItemExpanded\n        )\n            .pipe(takeUntil(this._unsubscribeAll), delay(250))\n            .subscribe(() => {\n                // Loop through the scrollbars and update them\n                fuseScrollbarDirectives.forEach((fuseScrollbarDirective) => {\n                    fuseScrollbarDirective.update();\n                });\n            });\n    }\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Decorated methods\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * On mouseenter\n     *\n     * @private\n     */\n    @HostListener('mouseenter')\n    _onMouseenter(): void {\n        // Enable the animations\n        this._enableAnimations();\n\n        // Set the hovered\n        this._hovered = true;\n    }\n\n    /**\n     * On mouseleave\n     *\n     * @private\n     */\n    @HostListener('mouseleave')\n    _onMouseleave(): void {\n        // Enable the animations\n        this._enableAnimations();\n\n        // Set the hovered\n        this._hovered = false;\n    }\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Lifecycle hooks\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * On changes\n     *\n     * @param changes\n     */\n    ngOnChanges(changes: SimpleChanges): void {\n        // Appearance\n        if ('appearance' in changes) {\n            // Execute the observable\n            this.appearanceChanged.next(changes.appearance.currentValue);\n        }\n\n        // Inner\n        if ('inner' in changes) {\n            // Coerce the value to a boolean\n            this.inner = coerceBooleanProperty(changes.inner.currentValue);\n        }\n\n        // Mode\n        if ('mode' in changes) {\n            // Get the previous and current values\n            const currentMode = changes.mode.currentValue;\n            const previousMode = changes.mode.previousValue;\n\n            // Disable the animations\n            this._disableAnimations();\n\n            // If the mode changes: 'over -> side'\n            if (previousMode === 'over' && currentMode === 'side') {\n                // Hide the overlay\n                this._hideOverlay();\n            }\n\n            // If the mode changes: 'side -> over'\n            if (previousMode === 'side' && currentMode === 'over') {\n                // Close the aside\n                this.closeAside();\n\n                // If the navigation is opened\n                if (this.opened) {\n                    // Show the overlay\n                    this._showOverlay();\n                }\n            }\n\n            // Execute the observable\n            this.modeChanged.next(currentMode);\n\n            // Enable the animations after a delay\n            // The delay must be bigger than the current transition-duration\n            // to make sure nothing will be animated while the mode changing\n            setTimeout(() => {\n                this._enableAnimations();\n            }, 500);\n        }\n\n        // Navigation\n        if ('navigation' in changes) {\n            // Mark for check\n            this._changeDetectorRef.markForCheck();\n        }\n\n        // Opened\n        if ('opened' in changes) {\n            // Coerce the value to a boolean\n            this.opened = coerceBooleanProperty(changes.opened.currentValue);\n\n            // Open/close the navigation\n            this._toggleOpened(this.opened);\n        }\n\n        // Position\n        if ('position' in changes) {\n            // Execute the observable\n            this.positionChanged.next(changes.position.currentValue);\n        }\n\n        // Transparent overlay\n        if ('transparentOverlay' in changes) {\n            // Coerce the value to a boolean\n            this.transparentOverlay = coerceBooleanProperty(\n                changes.transparentOverlay.currentValue\n            );\n        }\n    }\n\n    /**\n     * On init\n     */\n    ngOnInit(): void {\n        // Make sure the name input is not an empty string\n        if (this.name === '') {\n            this.name = this._fuseUtilsService.randomId();\n        }\n\n        // Register the navigation component\n        this._fuseNavigationService.registerComponent(this.name, this);\n\n        // Subscribe to the 'NavigationEnd' event\n        this._router.events\n            .pipe(\n                filter((event) => event instanceof NavigationEnd),\n                takeUntil(this._unsubscribeAll)\n            )\n            .subscribe(() => {\n                // If the mode is 'over' and the navigation is opened...\n                if (this.mode === 'over' && this.opened) {\n                    // Close the navigation\n                    this.close();\n                }\n\n                // If the mode is 'side' and the aside is active...\n                if (this.mode === 'side' && this.activeAsideItemId) {\n                    // Close the aside\n                    this.closeAside();\n                }\n            });\n    }\n\n    /**\n     * After view init\n     */\n    ngAfterViewInit(): void {\n        // Fix for Firefox.\n        //\n        // Because 'position: sticky' doesn't work correctly inside a 'position: fixed' parent,\n        // adding the '.cdk-global-scrollblock' to the html element breaks the navigation's position.\n        // This fixes the problem by reading the 'top' value from the html element and adding it as a\n        // 'marginTop' to the navigation itself.\n        this._mutationObserver = new MutationObserver((mutations) => {\n            mutations.forEach((mutation) => {\n                const mutationTarget = mutation.target as HTMLElement;\n                if (mutation.attributeName === 'class') {\n                    if (\n                        mutationTarget.classList.contains(\n                            'cdk-global-scrollblock'\n                        )\n                    ) {\n                        const top = parseInt(mutationTarget.style.top, 10);\n                        this._renderer2.setStyle(\n                            this._elementRef.nativeElement,\n                            'margin-top',\n                            `${Math.abs(top)}px`\n                        );\n                    } else {\n                        this._renderer2.setStyle(\n                            this._elementRef.nativeElement,\n                            'margin-top',\n                            null\n                        );\n                    }\n                }\n            });\n        });\n        this._mutationObserver.observe(this._document.documentElement, {\n            attributes: true,\n            attributeFilter: ['class'],\n        });\n\n        setTimeout(() => {\n            // Return if 'navigation content' element does not exist\n            if (!this._navigationContentEl) {\n                return;\n            }\n\n            // If 'navigation content' element doesn't have\n            // perfect scrollbar activated on it...\n            if (\n                !this._navigationContentEl.nativeElement.classList.contains(\n                    'ps'\n                )\n            ) {\n                // Find the active item\n                const activeItem =\n                    this._navigationContentEl.nativeElement.querySelector(\n                        '.fuse-vertical-navigation-item-active'\n                    );\n\n                // If the active item exists, scroll it into view\n                if (activeItem) {\n                    activeItem.scrollIntoView();\n                }\n            }\n            // Otherwise\n            else {\n                // Go through all the scrollbar directives\n                this._fuseScrollbarDirectives.forEach(\n                    (fuseScrollbarDirective) => {\n                        // Skip if not enabled\n                        if (!fuseScrollbarDirective.isEnabled()) {\n                            return;\n                        }\n\n                        // Scroll to the active element\n                        fuseScrollbarDirective.scrollToElement(\n                            '.fuse-vertical-navigation-item-active',\n                            -120,\n                            true\n                        );\n                    }\n                );\n            }\n        });\n    }\n\n    /**\n     * On destroy\n     */\n    ngOnDestroy(): void {\n        // Disconnect the mutation observer\n        this._mutationObserver.disconnect();\n\n        // Forcefully close the navigation and aside in case they are opened\n        this.close();\n        this.closeAside();\n\n        // Deregister the navigation component from the registry\n        this._fuseNavigationService.deregisterComponent(this.name);\n\n        // Unsubscribe from all subscriptions\n        this._unsubscribeAll.next(null);\n        this._unsubscribeAll.complete();\n    }\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Public methods\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * Refresh the component to apply the changes\n     */\n    refresh(): void {\n        // Mark for check\n        this._changeDetectorRef.markForCheck();\n\n        // Execute the observable\n        this.onRefreshed.next(true);\n    }\n\n    /**\n     * Open the navigation\n     */\n    open(): void {\n        // Return if the navigation is already open\n        if (this.opened) {\n            return;\n        }\n\n        // Set the opened\n        this._toggleOpened(true);\n    }\n\n    /**\n     * Close the navigation\n     */\n    close(): void {\n        // Return if the navigation is already closed\n        if (!this.opened) {\n            return;\n        }\n\n        // Close the aside\n        this.closeAside();\n\n        // Set the opened\n        this._toggleOpened(false);\n    }\n\n    /**\n     * Toggle the navigation\n     */\n    toggle(): void {\n        // Toggle\n        if (this.opened) {\n            this.close();\n        } else {\n            this.open();\n        }\n    }\n\n    /**\n     * Open the aside\n     *\n     * @param item\n     */\n    openAside(item: FuseNavigationItem): void {\n        // Return if the item is disabled\n        if (item.disabled || !item.id) {\n            return;\n        }\n\n        // Open\n        this.activeAsideItemId = item.id;\n\n        // Show the aside overlay\n        this._showAsideOverlay();\n\n        // Mark for check\n        this._changeDetectorRef.markForCheck();\n    }\n\n    /**\n     * Close the aside\n     */\n    closeAside(): void {\n        // Close\n        this.activeAsideItemId = null;\n\n        // Hide the aside overlay\n        this._hideAsideOverlay();\n\n        // Mark for check\n        this._changeDetectorRef.markForCheck();\n    }\n\n    /**\n     * Toggle the aside\n     *\n     * @param item\n     */\n    toggleAside(item: FuseNavigationItem): void {\n        // Toggle\n        if (this.activeAsideItemId === item.id) {\n            this.closeAside();\n        } else {\n            this.openAside(item);\n        }\n    }\n\n    /**\n     * Track by function for ngFor loops\n     *\n     * @param index\n     * @param item\n     */\n    trackByFn(index: number, item: any): any {\n        return item.id || index;\n    }\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Private methods\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * Enable the animations\n     *\n     * @private\n     */\n    private _enableAnimations(): void {\n        // Return if the animations are already enabled\n        if (this._animationsEnabled) {\n            return;\n        }\n\n        // Enable the animations\n        this._animationsEnabled = true;\n    }\n\n    /**\n     * Disable the animations\n     *\n     * @private\n     */\n    private _disableAnimations(): void {\n        // Return if the animations are already disabled\n        if (!this._animationsEnabled) {\n            return;\n        }\n\n        // Disable the animations\n        this._animationsEnabled = false;\n    }\n\n    /**\n     * Show the overlay\n     *\n     * @private\n     */\n    private _showOverlay(): void {\n        // Return if there is already an overlay\n        if (this._asideOverlay) {\n            return;\n        }\n\n        // Create the overlay element\n        this._overlay = this._renderer2.createElement('div');\n\n        // Add a class to the overlay element\n        this._overlay.classList.add('fuse-vertical-navigation-overlay');\n\n        // Add a class depending on the transparentOverlay option\n        if (this.transparentOverlay) {\n            this._overlay.classList.add(\n                'fuse-vertical-navigation-overlay-transparent'\n            );\n        }\n\n        // Append the overlay to the parent of the navigation\n        this._renderer2.appendChild(\n            this._elementRef.nativeElement.parentElement,\n            this._overlay\n        );\n\n        // Enable block scroll strategy\n        this._scrollStrategy.enable();\n\n        // Create the enter animation and attach it to the player\n        this._player = this._animationBuilder\n            .build([\n                animate(\n                    '300ms cubic-bezier(0.25, 0.8, 0.25, 1)',\n                    style({ opacity: 1 })\n                ),\n            ])\n            .create(this._overlay);\n\n        // Play the animation\n        this._player.play();\n\n        // Add an event listener to the overlay\n        this._overlay.addEventListener('click', this._handleOverlayClick);\n    }\n\n    /**\n     * Hide the overlay\n     *\n     * @private\n     */\n    private _hideOverlay(): void {\n        if (!this._overlay) {\n            return;\n        }\n\n        // Create the leave animation and attach it to the player\n        this._player = this._animationBuilder\n            .build([\n                animate(\n                    '300ms cubic-bezier(0.25, 0.8, 0.25, 1)',\n                    style({ opacity: 0 })\n                ),\n            ])\n            .create(this._overlay);\n\n        // Play the animation\n        this._player.play();\n\n        // Once the animation is done...\n        this._player.onDone(() => {\n            // If the overlay still exists...\n            if (this._overlay) {\n                // Remove the event listener\n                this._overlay.removeEventListener(\n                    'click',\n                    this._handleOverlayClick\n                );\n\n                // Remove the overlay\n                this._overlay.parentNode.removeChild(this._overlay);\n                this._overlay = null;\n            }\n\n            // Disable block scroll strategy\n            this._scrollStrategy.disable();\n        });\n    }\n\n    /**\n     * Show the aside overlay\n     *\n     * @private\n     */\n    private _showAsideOverlay(): void {\n        // Return if there is already an overlay\n        if (this._asideOverlay) {\n            return;\n        }\n\n        // Create the aside overlay element\n        this._asideOverlay = this._renderer2.createElement('div');\n\n        // Add a class to the aside overlay element\n        this._asideOverlay.classList.add(\n            'fuse-vertical-navigation-aside-overlay'\n        );\n\n        // Append the aside overlay to the parent of the navigation\n        this._renderer2.appendChild(\n            this._elementRef.nativeElement.parentElement,\n            this._asideOverlay\n        );\n\n        // Create the enter animation and attach it to the player\n        this._player = this._animationBuilder\n            .build([\n                animate(\n                    '300ms cubic-bezier(0.25, 0.8, 0.25, 1)',\n                    style({ opacity: 1 })\n                ),\n            ])\n            .create(this._asideOverlay);\n\n        // Play the animation\n        this._player.play();\n\n        // Add an event listener to the aside overlay\n        this._asideOverlay.addEventListener(\n            'click',\n            this._handleAsideOverlayClick\n        );\n    }\n\n    /**\n     * Hide the aside overlay\n     *\n     * @private\n     */\n    private _hideAsideOverlay(): void {\n        if (!this._asideOverlay) {\n            return;\n        }\n\n        // Create the leave animation and attach it to the player\n        this._player = this._animationBuilder\n            .build([\n                animate(\n                    '300ms cubic-bezier(0.25, 0.8, 0.25, 1)',\n                    style({ opacity: 0 })\n                ),\n            ])\n            .create(this._asideOverlay);\n\n        // Play the animation\n        this._player.play();\n\n        // Once the animation is done...\n        this._player.onDone(() => {\n            // If the aside overlay still exists...\n            if (this._asideOverlay) {\n                // Remove the event listener\n                this._asideOverlay.removeEventListener(\n                    'click',\n                    this._handleAsideOverlayClick\n                );\n\n                // Remove the aside overlay\n                this._asideOverlay.parentNode.removeChild(this._asideOverlay);\n                this._asideOverlay = null;\n            }\n        });\n    }\n\n    /**\n     * Open/close the navigation\n     *\n     * @param open\n     * @private\n     */\n    private _toggleOpened(open: boolean): void {\n        // Set the opened\n        this.opened = open;\n\n        // Enable the animations\n        this._enableAnimations();\n\n        // If the navigation opened, and the mode\n        // is 'over', show the overlay\n        if (this.mode === 'over') {\n            if (this.opened) {\n                this._showOverlay();\n            } else {\n                this._hideOverlay();\n            }\n        }\n\n        // Execute the observable\n        this.openedChanged.next(open);\n    }\n}\n", "<div class=\"fuse-vertical-navigation-wrapper\">\n    <!-- Header -->\n    <div class=\"fuse-vertical-navigation-header\">\n        <ng-content select=\"[fuseVerticalNavigationHeader]\"></ng-content>\n    </div>\n\n    <!-- Content -->\n    <div\n        class=\"fuse-vertical-navigation-content\"\n        fuseScrollbar\n        [fuseScrollbarOptions]=\"{\n            wheelPropagation: inner,\n            suppressScrollX: true,\n        }\"\n        #navigationContent\n    >\n        <!-- Content header -->\n        <div class=\"fuse-vertical-navigation-content-header\">\n            <ng-content\n                select=\"[fuseVerticalNavigationContentHeader]\"\n            ></ng-content>\n        </div>\n\n        <!-- Items -->\n        @for (item of navigation; track trackByFn($index, item)) {\n            <!-- Skip the hidden items -->\n            @if ((item.hidden && !item.hidden(item)) || !item.hidden) {\n                <!-- Aside -->\n                @if (item.type === 'aside') {\n                    <fuse-vertical-navigation-aside-item\n                        [item]=\"item\"\n                        [name]=\"name\"\n                        [activeItemId]=\"activeAsideItemId\"\n                        [autoCollapse]=\"autoCollapse\"\n                        [skipChildren]=\"true\"\n                        (click)=\"toggleAside(item)\"\n                    ></fuse-vertical-navigation-aside-item>\n                }\n\n                <!-- Basic -->\n                @if (item.type === 'basic') {\n                    <fuse-vertical-navigation-basic-item\n                        [item]=\"item\"\n                        [name]=\"name\"\n                    ></fuse-vertical-navigation-basic-item>\n                }\n\n                <!-- Collapsable -->\n                @if (item.type === 'collapsable') {\n                    <fuse-vertical-navigation-collapsable-item\n                        [item]=\"item\"\n                        [name]=\"name\"\n                        [autoCollapse]=\"autoCollapse\"\n                    ></fuse-vertical-navigation-collapsable-item>\n                }\n\n                <!-- Divider -->\n                @if (item.type === 'divider') {\n                    <fuse-vertical-navigation-divider-item\n                        [item]=\"item\"\n                        [name]=\"name\"\n                    ></fuse-vertical-navigation-divider-item>\n                }\n\n                <!-- Group -->\n                @if (item.type === 'group') {\n                    <fuse-vertical-navigation-group-item\n                        [item]=\"item\"\n                        [name]=\"name\"\n                        [autoCollapse]=\"autoCollapse\"\n                    ></fuse-vertical-navigation-group-item>\n                }\n\n                <!-- Spacer -->\n                @if (item.type === 'spacer') {\n                    <fuse-vertical-navigation-spacer-item\n                        [item]=\"item\"\n                        [name]=\"name\"\n                    ></fuse-vertical-navigation-spacer-item>\n                }\n            }\n        }\n\n        <!-- Content footer -->\n        <div class=\"fuse-vertical-navigation-content-footer\">\n            <ng-content\n                select=\"[fuseVerticalNavigationContentFooter]\"\n            ></ng-content>\n        </div>\n    </div>\n\n    <!-- Footer -->\n    <div class=\"fuse-vertical-navigation-footer\">\n        <ng-content select=\"[fuseVerticalNavigationFooter]\"></ng-content>\n    </div>\n</div>\n\n<!-- Aside -->\n@if (activeAsideItemId) {\n    <div\n        class=\"fuse-vertical-navigation-aside-wrapper\"\n        fuseScrollbar\n        [fuseScrollbarOptions]=\"{\n            wheelPropagation: false,\n            suppressScrollX: true,\n        }\"\n        [@fadeInLeft]=\"position === 'left'\"\n        [@fadeInRight]=\"position === 'right'\"\n        [@fadeOutLeft]=\"position === 'left'\"\n        [@fadeOutRight]=\"position === 'right'\"\n    >\n        <!-- Items -->\n        @for (item of navigation; track trackByFn($index, item)) {\n            <!-- Skip the hidden items -->\n            @if ((item.hidden && !item.hidden(item)) || !item.hidden) {\n                <!-- Aside -->\n                @if (item.type === 'aside' && item.id === activeAsideItemId) {\n                    <fuse-vertical-navigation-aside-item\n                        [item]=\"item\"\n                        [name]=\"name\"\n                        [autoCollapse]=\"autoCollapse\"\n                    ></fuse-vertical-navigation-aside-item>\n                }\n            }\n        }\n    </div>\n}\n"], "mappings": "AAAA,SACIA,OAAO,EACPC,gBAAgB,EAEhBC,KAAK,QACF,qBAAqB;AAC5B,SAAuBC,qBAAqB,QAAQ,uBAAuB;AAC3E,SAAyBC,qBAAqB,QAAQ,sBAAsB;AAC5E,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAGIC,iBAAiB,EAEjBC,UAAU,EACVC,YAAY,EAGZC,MAAM,EAONC,SAAS,QAKN,eAAe;AACtB,SAASC,aAAa,EAAEC,MAAM,QAAQ,iBAAiB;AACvD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,qBAAqB,QAAQ,gDAAgD;AAOtF,SAASC,wCAAwC,QAAQ,uEAAuE;AAChI,SAASC,wCAAwC,QAAQ,uEAAuE;AAChI,SAASC,8CAA8C,QAAQ,mFAAmF;AAClJ,SAASC,0CAA0C,QAAQ,2EAA2E;AACtI,SAASC,wCAAwC,QAAQ,uEAAuE;AAChI,SAASC,yCAAyC,QAAQ,yEAAyE;AACnI,SAASC,sBAAsB,QAAQ,gDAAgD;AACvF,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SACIC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,aAAa,EACbC,OAAO,EAEPC,SAAS,QACN,MAAM;;;;;;;;;;;;;;;;IC3BOC,EAAA,CAAAC,cAAA,8CAOC;IADGD,EAAA,CAAAE,UAAA,mBAAAC,gIAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,OAAA,GAAAN,EAAA,CAAAO,aAAA,IAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASD,MAAA,CAAAE,WAAA,CAAAL,OAAA,CAAiB;IAAA,EAAC;IAC9BN,EAAA,CAAAY,YAAA,EAAsC;;;;;IAFnCZ,EAJA,CAAAa,UAAA,SAAAP,OAAA,CAAa,SAAAG,MAAA,CAAAK,IAAA,CACA,iBAAAL,MAAA,CAAAM,iBAAA,CACqB,iBAAAN,MAAA,CAAAO,YAAA,CACL,sBACR;;;;;IAOzBhB,EAAA,CAAAiB,SAAA,6CAGuC;;;;;IADnCjB,EADA,CAAAa,UAAA,SAAAP,OAAA,CAAa,SAAAG,MAAA,CAAAK,IAAA,CACA;;;;;IAMjBd,EAAA,CAAAiB,SAAA,oDAI6C;;;;;IADzCjB,EAFA,CAAAa,UAAA,SAAAP,OAAA,CAAa,SAAAG,MAAA,CAAAK,IAAA,CACA,iBAAAL,MAAA,CAAAO,YAAA,CACgB;;;;;IAMjChB,EAAA,CAAAiB,SAAA,+CAGyC;;;;;IADrCjB,EADA,CAAAa,UAAA,SAAAP,OAAA,CAAa,SAAAG,MAAA,CAAAK,IAAA,CACA;;;;;IAMjBd,EAAA,CAAAiB,SAAA,8CAIuC;;;;;IADnCjB,EAFA,CAAAa,UAAA,SAAAP,OAAA,CAAa,SAAAG,MAAA,CAAAK,IAAA,CACA,iBAAAL,MAAA,CAAAO,YAAA,CACgB;;;;;IAMjChB,EAAA,CAAAiB,SAAA,8CAGwC;;;;;IADpCjB,EADA,CAAAa,UAAA,SAAAP,OAAA,CAAa,SAAAG,MAAA,CAAAK,IAAA,CACA;;;;;IAjDrBd,EAAA,CAAAkB,mBAAA,IAAAC,0EAAA,iDAA6B;IAY7BnB,EAAA,CAAAkB,mBAAA,IAAAE,0EAAA,iDAA6B;IAQ7BpB,EAAA,CAAAkB,mBAAA,IAAAG,0EAAA,wDAAmC;IASnCrB,EAAA,CAAAkB,mBAAA,IAAAI,0EAAA,mDAA+B;IAQ/BtB,EAAA,CAAAkB,mBAAA,IAAAK,0EAAA,kDAA6B;IAS7BvB,EAAA,CAAAkB,mBAAA,IAAAM,0EAAA,kDAA8B;;;;IA9C9BxB,EAAA,CAAAyB,aAAA,CAAAnB,OAAA,CAAAoB,IAAA,sBASC;IAGD1B,EAAA,CAAA2B,SAAA,EAKC;IALD3B,EAAA,CAAAyB,aAAA,CAAAnB,OAAA,CAAAoB,IAAA,sBAKC;IAGD1B,EAAA,CAAA2B,SAAA,EAMC;IAND3B,EAAA,CAAAyB,aAAA,CAAAnB,OAAA,CAAAoB,IAAA,4BAMC;IAGD1B,EAAA,CAAA2B,SAAA,EAKC;IALD3B,EAAA,CAAAyB,aAAA,CAAAnB,OAAA,CAAAoB,IAAA,wBAKC;IAGD1B,EAAA,CAAA2B,SAAA,EAMC;IAND3B,EAAA,CAAAyB,aAAA,CAAAnB,OAAA,CAAAoB,IAAA,sBAMC;IAGD1B,EAAA,CAAA2B,SAAA,EAKC;IALD3B,EAAA,CAAAyB,aAAA,CAAAnB,OAAA,CAAAoB,IAAA,uBAKC;;;;;IArDL1B,EAAA,CAAAkB,mBAAA,IAAAU,4DAAA,OAA2D;;;;IAA3D5B,EAAA,CAAAyB,aAAA,CAAAnB,OAAA,CAAAuB,MAAA,KAAAvB,OAAA,CAAAuB,MAAA,CAAAvB,OAAA,MAAAA,OAAA,CAAAuB,MAAA,UAsDC;;;;;IAqCO7B,EAAA,CAAAiB,SAAA,8CAIuC;;;;;IADnCjB,EAFA,CAAAa,UAAA,SAAAiB,OAAA,CAAa,SAAArB,MAAA,CAAAK,IAAA,CACA,iBAAAL,MAAA,CAAAO,YAAA,CACgB;;;;;IAJrChB,EAAA,CAAAkB,mBAAA,IAAAa,yFAAA,kDAA8D;;;;;IAA9D/B,EAAA,CAAAyB,aAAA,CAAAK,OAAA,CAAAJ,IAAA,gBAAAI,OAAA,CAAAE,EAAA,KAAAvB,MAAA,CAAAM,iBAAA,UAMC;;;;;IARLf,EAAA,CAAAkB,mBAAA,IAAAe,2EAAA,OAA2D;;;;IAA3DjC,EAAA,CAAAyB,aAAA,CAAAK,OAAA,CAAAD,MAAA,KAAAC,OAAA,CAAAD,MAAA,CAAAC,OAAA,MAAAA,OAAA,CAAAD,MAAA,UASC;;;;;IAxBT7B,EAAA,CAAAC,cAAA,aAWC;IAEGD,EAAA,CAAAkC,gBAAA,IAAAC,6DAAA,oBAAAnC,EAAA,CAAAoC,mBAAA,GAAAC,SAAA,OAYC;IACLrC,EAAA,CAAAY,YAAA,EAAM;;;;IAhBFZ,EAPA,CAAAa,UAAA,yBAAAb,EAAA,CAAAsC,eAAA,IAAAC,GAAA,EAGE,gBAAA9B,MAAA,CAAA+B,QAAA,YACiC,iBAAA/B,MAAA,CAAA+B,QAAA,aACE,iBAAA/B,MAAA,CAAA+B,QAAA,YACD,kBAAA/B,MAAA,CAAA+B,QAAA,aACE;IAGtCxC,EAAA,CAAA2B,SAAA,EAYC;IAZD3B,EAAA,CAAAyC,UAAA,CAAAhC,MAAA,CAAAiC,UAAA,CAYC;;;ADhDT,OAAM,MAAOC,+BAA+B;EA4DxC;;;EAGAC,YAAA;IAxDA;IAEQ,KAAAC,iBAAiB,GAAGjE,MAAM,CAACR,gBAAgB,CAAC;IAC5C,KAAA0E,kBAAkB,GAAGlE,MAAM,CAACH,iBAAiB,CAAC;IAC9C,KAAAsE,SAAS,GAAGnE,MAAM,CAACJ,QAAQ,CAAC;IAC5B,KAAAwE,WAAW,GAAGpE,MAAM,CAACF,UAAU,CAAC;IAChC,KAAAuE,UAAU,GAAGrE,MAAM,CAACC,SAAS,CAAC;IAC9B,KAAAqE,OAAO,GAAGtE,MAAM,CAACG,MAAM,CAAC;IACxB,KAAAoE,sBAAsB,GAAGvE,MAAM,CAACL,qBAAqB,CAAC;IACtD,KAAA6E,sBAAsB,GAAGxE,MAAM,CAACK,qBAAqB,CAAC;IACtD,KAAAoE,iBAAiB,GAAGzE,MAAM,CAACa,gBAAgB,CAAC;IAE3C,KAAA6D,UAAU,GAAqC,SAAS;IACxD,KAAAtC,YAAY,GAAY,IAAI;IAC5B,KAAAuC,KAAK,GAAY,KAAK;IACtB,KAAAC,IAAI,GAA+B,MAAM;IACzC,KAAA1C,IAAI,GAAW,IAAI,CAACuC,iBAAiB,CAACI,QAAQ,EAAE;IAEhD,KAAAC,MAAM,GAAY,IAAI;IACtB,KAAAlB,QAAQ,GAAmC,MAAM;IACjD,KAAAmB,kBAAkB,GAAY,KAAK;IAEnC,KAAAC,iBAAiB,GACtB,IAAIjF,YAAY,EAAoC;IACrC,KAAAkF,WAAW,GAC1B,IAAIlF,YAAY,EAA8B;IAC/B,KAAAmF,aAAa,GAC5B,IAAInF,YAAY,EAAW;IAEtB,KAAAoF,eAAe,GACpB,IAAIpF,YAAY,EAAkC;IAGtD,KAAAoC,iBAAiB,GAAkB,IAAI;IACvC,KAAAiD,0BAA0B,GACtB,IAAInE,aAAa,CAAqB,CAAC,CAAC;IAC5C,KAAAoE,yBAAyB,GACrB,IAAIpE,aAAa,CAAqB,CAAC,CAAC;IAC5C,KAAAqE,WAAW,GAA2B,IAAIrE,aAAa,CAAU,CAAC,CAAC;IAC3D,KAAAsE,kBAAkB,GAAY,KAAK;IAInC,KAAAC,QAAQ,GAAY,KAAK;IAIzB,KAAAC,eAAe,GACnB,IAAI,CAAClB,sBAAsB,CAACmB,KAAK,EAAE;IAG/B,KAAAC,eAAe,GAAiB,IAAIzE,OAAO,EAAO;IAMtD,IAAI,CAAC0E,wBAAwB,GAAG,MAAW;MACvC,IAAI,CAACC,UAAU,EAAE;IACrB,CAAC;IACD,IAAI,CAACC,mBAAmB,GAAG,MAAW;MAClC,IAAI,CAACC,KAAK,EAAE;IAChB,CAAC;EACL;EAEA;EACA;EACA;EAEA;;;EAGA,IAA0BC,SAASA,CAAA;IAC/B;IACA,OAAO;MACH,6CAA6C,EACzC,IAAI,CAACT,kBAAkB;MAC3B,CAAC,uCAAuC,IAAI,CAACb,UAAU,EAAE,GAAG,IAAI;MAChE,gCAAgC,EAAE,IAAI,CAACc,QAAQ;MAC/C,gCAAgC,EAAE,IAAI,CAACb,KAAK;MAC5C,oCAAoC,EAAE,IAAI,CAACC,IAAI,KAAK,MAAM;MAC1D,oCAAoC,EAAE,IAAI,CAACA,IAAI,KAAK,MAAM;MAC1D,iCAAiC,EAAE,IAAI,CAACE,MAAM;MAC9C,wCAAwC,EAAE,IAAI,CAAClB,QAAQ,KAAK,MAAM;MAClE,yCAAyC,EACrC,IAAI,CAACA,QAAQ,KAAK;KACzB;IACD;EACJ;EAEA;;;EAGA,IAA0BqC,SAASA,CAAA;IAC/B,OAAO;MACHC,UAAU,EAAE,IAAI,CAACpB,MAAM,GAAG,SAAS,GAAG;KACzC;EACL;EAEA;;;EAGA,IACIqB,uBAAuBA,CACvBA,uBAA0D;IAE1D;IACA,IAAI,CAACC,wBAAwB,GAAGD,uBAAuB;IAEvD;IACA,IAAIA,uBAAuB,CAACE,MAAM,KAAK,CAAC,EAAE;MACtC;IACJ;IAEA;IACA,IAAI,IAAI,CAACC,oCAAoC,EAAE;MAC3C,IAAI,CAACA,oCAAoC,CAACC,WAAW,EAAE;IAC3D;IAEA;IACA,IAAI,CAACD,oCAAoC,GAAGtF,KAAK,CAC7C,IAAI,CAACoE,0BAA0B,EAC/B,IAAI,CAACC,yBAAyB,CACjC,CACImB,IAAI,CAACrF,SAAS,CAAC,IAAI,CAACwE,eAAe,CAAC,EAAE7E,KAAK,CAAC,GAAG,CAAC,CAAC,CACjD2F,SAAS,CAAC,MAAK;MACZ;MACAN,uBAAuB,CAACO,OAAO,CAAEC,sBAAsB,IAAI;QACvDA,sBAAsB,CAACC,MAAM,EAAE;MACnC,CAAC,CAAC;IACN,CAAC,CAAC;EACV;EAEA;EACA;EACA;EAEA;;;;;EAMAC,aAAaA,CAAA;IACT;IACA,IAAI,CAACC,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACtB,QAAQ,GAAG,IAAI;EACxB;EAEA;;;;;EAMAuB,aAAaA,CAAA;IACT;IACA,IAAI,CAACD,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACtB,QAAQ,GAAG,KAAK;EACzB;EAEA;EACA;EACA;EAEA;;;;;EAKAwB,WAAWA,CAACC,OAAsB;IAC9B;IACA,IAAI,YAAY,IAAIA,OAAO,EAAE;MACzB;MACA,IAAI,CAACjC,iBAAiB,CAACkC,IAAI,CAACD,OAAO,CAACvC,UAAU,CAACyC,YAAY,CAAC;IAChE;IAEA;IACA,IAAI,OAAO,IAAIF,OAAO,EAAE;MACpB;MACA,IAAI,CAACtC,KAAK,GAAGjF,qBAAqB,CAACuH,OAAO,CAACtC,KAAK,CAACwC,YAAY,CAAC;IAClE;IAEA;IACA,IAAI,MAAM,IAAIF,OAAO,EAAE;MACnB;MACA,MAAMG,WAAW,GAAGH,OAAO,CAACrC,IAAI,CAACuC,YAAY;MAC7C,MAAME,YAAY,GAAGJ,OAAO,CAACrC,IAAI,CAAC0C,aAAa;MAE/C;MACA,IAAI,CAACC,kBAAkB,EAAE;MAEzB;MACA,IAAIF,YAAY,KAAK,MAAM,IAAID,WAAW,KAAK,MAAM,EAAE;QACnD;QACA,IAAI,CAACI,YAAY,EAAE;MACvB;MAEA;MACA,IAAIH,YAAY,KAAK,MAAM,IAAID,WAAW,KAAK,MAAM,EAAE;QACnD;QACA,IAAI,CAACvB,UAAU,EAAE;QAEjB;QACA,IAAI,IAAI,CAACf,MAAM,EAAE;UACb;UACA,IAAI,CAAC2C,YAAY,EAAE;QACvB;MACJ;MAEA;MACA,IAAI,CAACxC,WAAW,CAACiC,IAAI,CAACE,WAAW,CAAC;MAElC;MACA;MACA;MACAM,UAAU,CAAC,MAAK;QACZ,IAAI,CAACZ,iBAAiB,EAAE;MAC5B,CAAC,EAAE,GAAG,CAAC;IACX;IAEA;IACA,IAAI,YAAY,IAAIG,OAAO,EAAE;MACzB;MACA,IAAI,CAAC/C,kBAAkB,CAACyD,YAAY,EAAE;IAC1C;IAEA;IACA,IAAI,QAAQ,IAAIV,OAAO,EAAE;MACrB;MACA,IAAI,CAACnC,MAAM,GAAGpF,qBAAqB,CAACuH,OAAO,CAACnC,MAAM,CAACqC,YAAY,CAAC;MAEhE;MACA,IAAI,CAACS,aAAa,CAAC,IAAI,CAAC9C,MAAM,CAAC;IACnC;IAEA;IACA,IAAI,UAAU,IAAImC,OAAO,EAAE;MACvB;MACA,IAAI,CAAC9B,eAAe,CAAC+B,IAAI,CAACD,OAAO,CAACrD,QAAQ,CAACuD,YAAY,CAAC;IAC5D;IAEA;IACA,IAAI,oBAAoB,IAAIF,OAAO,EAAE;MACjC;MACA,IAAI,CAAClC,kBAAkB,GAAGrF,qBAAqB,CAC3CuH,OAAO,CAAClC,kBAAkB,CAACoC,YAAY,CAC1C;IACL;EACJ;EAEA;;;EAGAU,QAAQA,CAAA;IACJ;IACA,IAAI,IAAI,CAAC3F,IAAI,KAAK,EAAE,EAAE;MAClB,IAAI,CAACA,IAAI,GAAG,IAAI,CAACuC,iBAAiB,CAACI,QAAQ,EAAE;IACjD;IAEA;IACA,IAAI,CAACL,sBAAsB,CAACsD,iBAAiB,CAAC,IAAI,CAAC5F,IAAI,EAAE,IAAI,CAAC;IAE9D;IACA,IAAI,CAACoC,OAAO,CAACyD,MAAM,CACdvB,IAAI,CACDzF,MAAM,CAAEiH,KAAK,IAAKA,KAAK,YAAY9H,aAAa,CAAC,EACjDiB,SAAS,CAAC,IAAI,CAACwE,eAAe,CAAC,CAClC,CACAc,SAAS,CAAC,MAAK;MACZ;MACA,IAAI,IAAI,CAAC7B,IAAI,KAAK,MAAM,IAAI,IAAI,CAACE,MAAM,EAAE;QACrC;QACA,IAAI,CAACiB,KAAK,EAAE;MAChB;MAEA;MACA,IAAI,IAAI,CAACnB,IAAI,KAAK,MAAM,IAAI,IAAI,CAACzC,iBAAiB,EAAE;QAChD;QACA,IAAI,CAAC0D,UAAU,EAAE;MACrB;IACJ,CAAC,CAAC;EACV;EAEA;;;EAGAoC,eAAeA,CAAA;IACX;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACC,iBAAiB,GAAG,IAAIC,gBAAgB,CAAEC,SAAS,IAAI;MACxDA,SAAS,CAAC1B,OAAO,CAAE2B,QAAQ,IAAI;QAC3B,MAAMC,cAAc,GAAGD,QAAQ,CAACE,MAAqB;QACrD,IAAIF,QAAQ,CAACG,aAAa,KAAK,OAAO,EAAE;UACpC,IACIF,cAAc,CAACtC,SAAS,CAACyC,QAAQ,CAC7B,wBAAwB,CAC3B,EACH;YACE,MAAMC,GAAG,GAAGC,QAAQ,CAACL,cAAc,CAAC7I,KAAK,CAACiJ,GAAG,EAAE,EAAE,CAAC;YAClD,IAAI,CAACrE,UAAU,CAACuE,QAAQ,CACpB,IAAI,CAACxE,WAAW,CAACyE,aAAa,EAC9B,YAAY,EACZ,GAAGC,IAAI,CAACC,GAAG,CAACL,GAAG,CAAC,IAAI,CACvB;UACL,CAAC,MAAM;YACH,IAAI,CAACrE,UAAU,CAACuE,QAAQ,CACpB,IAAI,CAACxE,WAAW,CAACyE,aAAa,EAC9B,YAAY,EACZ,IAAI,CACP;UACL;QACJ;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IACF,IAAI,CAACX,iBAAiB,CAACc,OAAO,CAAC,IAAI,CAAC7E,SAAS,CAAC8E,eAAe,EAAE;MAC3DC,UAAU,EAAE,IAAI;MAChBC,eAAe,EAAE,CAAC,OAAO;KAC5B,CAAC;IAEFzB,UAAU,CAAC,MAAK;MACZ;MACA,IAAI,CAAC,IAAI,CAAC0B,oBAAoB,EAAE;QAC5B;MACJ;MAEA;MACA;MACA,IACI,CAAC,IAAI,CAACA,oBAAoB,CAACP,aAAa,CAAC7C,SAAS,CAACyC,QAAQ,CACvD,IAAI,CACP,EACH;QACE;QACA,MAAMY,UAAU,GACZ,IAAI,CAACD,oBAAoB,CAACP,aAAa,CAACS,aAAa,CACjD,uCAAuC,CAC1C;QAEL;QACA,IAAID,UAAU,EAAE;UACZA,UAAU,CAACE,cAAc,EAAE;QAC/B;MACJ;MACA;MAAA,KACK;QACD;QACA,IAAI,CAACnD,wBAAwB,CAACM,OAAO,CAChCC,sBAAsB,IAAI;UACvB;UACA,IAAI,CAACA,sBAAsB,CAAC6C,SAAS,EAAE,EAAE;YACrC;UACJ;UAEA;UACA7C,sBAAsB,CAAC8C,eAAe,CAClC,uCAAuC,EACvC,CAAC,GAAG,EACJ,IAAI,CACP;QACL,CAAC,CACJ;MACL;IACJ,CAAC,CAAC;EACN;EAEA;;;EAGAC,WAAWA,CAAA;IACP;IACA,IAAI,CAACxB,iBAAiB,CAACyB,UAAU,EAAE;IAEnC;IACA,IAAI,CAAC5D,KAAK,EAAE;IACZ,IAAI,CAACF,UAAU,EAAE;IAEjB;IACA,IAAI,CAACrB,sBAAsB,CAACoF,mBAAmB,CAAC,IAAI,CAAC1H,IAAI,CAAC;IAE1D;IACA,IAAI,CAACyD,eAAe,CAACuB,IAAI,CAAC,IAAI,CAAC;IAC/B,IAAI,CAACvB,eAAe,CAACkE,QAAQ,EAAE;EACnC;EAEA;EACA;EACA;EAEA;;;EAGAC,OAAOA,CAAA;IACH;IACA,IAAI,CAAC5F,kBAAkB,CAACyD,YAAY,EAAE;IAEtC;IACA,IAAI,CAACrC,WAAW,CAAC4B,IAAI,CAAC,IAAI,CAAC;EAC/B;EAEA;;;EAGA6C,IAAIA,CAAA;IACA;IACA,IAAI,IAAI,CAACjF,MAAM,EAAE;MACb;IACJ;IAEA;IACA,IAAI,CAAC8C,aAAa,CAAC,IAAI,CAAC;EAC5B;EAEA;;;EAGA7B,KAAKA,CAAA;IACD;IACA,IAAI,CAAC,IAAI,CAACjB,MAAM,EAAE;MACd;IACJ;IAEA;IACA,IAAI,CAACe,UAAU,EAAE;IAEjB;IACA,IAAI,CAAC+B,aAAa,CAAC,KAAK,CAAC;EAC7B;EAEA;;;EAGAoC,MAAMA,CAAA;IACF;IACA,IAAI,IAAI,CAAClF,MAAM,EAAE;MACb,IAAI,CAACiB,KAAK,EAAE;IAChB,CAAC,MAAM;MACH,IAAI,CAACgE,IAAI,EAAE;IACf;EACJ;EAEA;;;;;EAKAE,SAASA,CAACC,IAAwB;IAC9B;IACA,IAAIA,IAAI,CAACC,QAAQ,IAAI,CAACD,IAAI,CAAC9G,EAAE,EAAE;MAC3B;IACJ;IAEA;IACA,IAAI,CAACjB,iBAAiB,GAAG+H,IAAI,CAAC9G,EAAE;IAEhC;IACA,IAAI,CAACgH,iBAAiB,EAAE;IAExB;IACA,IAAI,CAAClG,kBAAkB,CAACyD,YAAY,EAAE;EAC1C;EAEA;;;EAGA9B,UAAUA,CAAA;IACN;IACA,IAAI,CAAC1D,iBAAiB,GAAG,IAAI;IAE7B;IACA,IAAI,CAACkI,iBAAiB,EAAE;IAExB;IACA,IAAI,CAACnG,kBAAkB,CAACyD,YAAY,EAAE;EAC1C;EAEA;;;;;EAKA5F,WAAWA,CAACmI,IAAwB;IAChC;IACA,IAAI,IAAI,CAAC/H,iBAAiB,KAAK+H,IAAI,CAAC9G,EAAE,EAAE;MACpC,IAAI,CAACyC,UAAU,EAAE;IACrB,CAAC,MAAM;MACH,IAAI,CAACoE,SAAS,CAACC,IAAI,CAAC;IACxB;EACJ;EAEA;;;;;;EAMAzG,SAASA,CAAC6G,KAAa,EAAEJ,IAAS;IAC9B,OAAOA,IAAI,CAAC9G,EAAE,IAAIkH,KAAK;EAC3B;EAEA;EACA;EACA;EAEA;;;;;EAKQxD,iBAAiBA,CAAA;IACrB;IACA,IAAI,IAAI,CAACvB,kBAAkB,EAAE;MACzB;IACJ;IAEA;IACA,IAAI,CAACA,kBAAkB,GAAG,IAAI;EAClC;EAEA;;;;;EAKQgC,kBAAkBA,CAAA;IACtB;IACA,IAAI,CAAC,IAAI,CAAChC,kBAAkB,EAAE;MAC1B;IACJ;IAEA;IACA,IAAI,CAACA,kBAAkB,GAAG,KAAK;EACnC;EAEA;;;;;EAKQkC,YAAYA,CAAA;IAChB;IACA,IAAI,IAAI,CAAC8C,aAAa,EAAE;MACpB;IACJ;IAEA;IACA,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACnG,UAAU,CAACoG,aAAa,CAAC,KAAK,CAAC;IAEpD;IACA,IAAI,CAACD,QAAQ,CAACxE,SAAS,CAAC0E,GAAG,CAAC,kCAAkC,CAAC;IAE/D;IACA,IAAI,IAAI,CAAC3F,kBAAkB,EAAE;MACzB,IAAI,CAACyF,QAAQ,CAACxE,SAAS,CAAC0E,GAAG,CACvB,8CAA8C,CACjD;IACL;IAEA;IACA,IAAI,CAACrG,UAAU,CAACsG,WAAW,CACvB,IAAI,CAACvG,WAAW,CAACyE,aAAa,CAAC+B,aAAa,EAC5C,IAAI,CAACJ,QAAQ,CAChB;IAED;IACA,IAAI,CAAC/E,eAAe,CAACoF,MAAM,EAAE;IAE7B;IACA,IAAI,CAACC,OAAO,GAAG,IAAI,CAAC7G,iBAAiB,CAChC8G,KAAK,CAAC,CACHxL,OAAO,CACH,wCAAwC,EACxCE,KAAK,CAAC;MAAEuL,OAAO,EAAE;IAAC,CAAE,CAAC,CACxB,CACJ,CAAC,CACDC,MAAM,CAAC,IAAI,CAACT,QAAQ,CAAC;IAE1B;IACA,IAAI,CAACM,OAAO,CAACI,IAAI,EAAE;IAEnB;IACA,IAAI,CAACV,QAAQ,CAACW,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACrF,mBAAmB,CAAC;EACrE;EAEA;;;;;EAKQ0B,YAAYA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACgD,QAAQ,EAAE;MAChB;IACJ;IAEA;IACA,IAAI,CAACM,OAAO,GAAG,IAAI,CAAC7G,iBAAiB,CAChC8G,KAAK,CAAC,CACHxL,OAAO,CACH,wCAAwC,EACxCE,KAAK,CAAC;MAAEuL,OAAO,EAAE;IAAC,CAAE,CAAC,CACxB,CACJ,CAAC,CACDC,MAAM,CAAC,IAAI,CAACT,QAAQ,CAAC;IAE1B;IACA,IAAI,CAACM,OAAO,CAACI,IAAI,EAAE;IAEnB;IACA,IAAI,CAACJ,OAAO,CAACM,MAAM,CAAC,MAAK;MACrB;MACA,IAAI,IAAI,CAACZ,QAAQ,EAAE;QACf;QACA,IAAI,CAACA,QAAQ,CAACa,mBAAmB,CAC7B,OAAO,EACP,IAAI,CAACvF,mBAAmB,CAC3B;QAED;QACA,IAAI,CAAC0E,QAAQ,CAACc,UAAU,CAACC,WAAW,CAAC,IAAI,CAACf,QAAQ,CAAC;QACnD,IAAI,CAACA,QAAQ,GAAG,IAAI;MACxB;MAEA;MACA,IAAI,CAAC/E,eAAe,CAAC+F,OAAO,EAAE;IAClC,CAAC,CAAC;EACN;EAEA;;;;;EAKQpB,iBAAiBA,CAAA;IACrB;IACA,IAAI,IAAI,CAACG,aAAa,EAAE;MACpB;IACJ;IAEA;IACA,IAAI,CAACA,aAAa,GAAG,IAAI,CAAClG,UAAU,CAACoG,aAAa,CAAC,KAAK,CAAC;IAEzD;IACA,IAAI,CAACF,aAAa,CAACvE,SAAS,CAAC0E,GAAG,CAC5B,wCAAwC,CAC3C;IAED;IACA,IAAI,CAACrG,UAAU,CAACsG,WAAW,CACvB,IAAI,CAACvG,WAAW,CAACyE,aAAa,CAAC+B,aAAa,EAC5C,IAAI,CAACL,aAAa,CACrB;IAED;IACA,IAAI,CAACO,OAAO,GAAG,IAAI,CAAC7G,iBAAiB,CAChC8G,KAAK,CAAC,CACHxL,OAAO,CACH,wCAAwC,EACxCE,KAAK,CAAC;MAAEuL,OAAO,EAAE;IAAC,CAAE,CAAC,CACxB,CACJ,CAAC,CACDC,MAAM,CAAC,IAAI,CAACV,aAAa,CAAC;IAE/B;IACA,IAAI,CAACO,OAAO,CAACI,IAAI,EAAE;IAEnB;IACA,IAAI,CAACX,aAAa,CAACY,gBAAgB,CAC/B,OAAO,EACP,IAAI,CAACvF,wBAAwB,CAChC;EACL;EAEA;;;;;EAKQyE,iBAAiBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACE,aAAa,EAAE;MACrB;IACJ;IAEA;IACA,IAAI,CAACO,OAAO,GAAG,IAAI,CAAC7G,iBAAiB,CAChC8G,KAAK,CAAC,CACHxL,OAAO,CACH,wCAAwC,EACxCE,KAAK,CAAC;MAAEuL,OAAO,EAAE;IAAC,CAAE,CAAC,CACxB,CACJ,CAAC,CACDC,MAAM,CAAC,IAAI,CAACV,aAAa,CAAC;IAE/B;IACA,IAAI,CAACO,OAAO,CAACI,IAAI,EAAE;IAEnB;IACA,IAAI,CAACJ,OAAO,CAACM,MAAM,CAAC,MAAK;MACrB;MACA,IAAI,IAAI,CAACb,aAAa,EAAE;QACpB;QACA,IAAI,CAACA,aAAa,CAACc,mBAAmB,CAClC,OAAO,EACP,IAAI,CAACzF,wBAAwB,CAChC;QAED;QACA,IAAI,CAAC2E,aAAa,CAACe,UAAU,CAACC,WAAW,CAAC,IAAI,CAAChB,aAAa,CAAC;QAC7D,IAAI,CAACA,aAAa,GAAG,IAAI;MAC7B;IACJ,CAAC,CAAC;EACN;EAEA;;;;;;EAMQ3C,aAAaA,CAACmC,IAAa;IAC/B;IACA,IAAI,CAACjF,MAAM,GAAGiF,IAAI;IAElB;IACA,IAAI,CAACjD,iBAAiB,EAAE;IAExB;IACA;IACA,IAAI,IAAI,CAAClC,IAAI,KAAK,MAAM,EAAE;MACtB,IAAI,IAAI,CAACE,MAAM,EAAE;QACb,IAAI,CAAC2C,YAAY,EAAE;MACvB,CAAC,MAAM;QACH,IAAI,CAACD,YAAY,EAAE;MACvB;IACJ;IAEA;IACA,IAAI,CAACtC,aAAa,CAACgC,IAAI,CAAC6C,IAAI,CAAC;EACjC;EAAC,QAAA0B,CAAA,G;qCAhvBQ1H,+BAA+B;EAAA;EAAA,QAAA2H,EAAA,G;UAA/B3H,+BAA+B;IAAA4H,SAAA;IAAAC,SAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;uBA6G1BlL,sBAAsB;;;;;;;;;;;QA7G3BQ,EAAA,CAAAE,UAAA,wBAAA0K,8DAAA;UAAA,OAAAD,GAAA,CAAAlF,aAAA,EAAe;QAAA,EAAgB,wBAAAoF,8DAAA;UAAA,OAA/BF,GAAA,CAAAhF,aAAA,EAAe;QAAA,EAAgB;;;QAA/B3F,EAAA,CAAA8K,UAAA,CAAAH,GAAA,CAAA9F,SAAA,CAA+B;QAA/B7E,EAAA,CAAA+K,UAAA,CAAAJ,GAAA,CAAA/F,SAAA,CAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QC1ExC5E,EAFJ,CAAAC,cAAA,aAA8C,aAEG;QACzCD,EAAA,CAAAgL,YAAA,GAAiE;QACrEhL,EAAA,CAAAY,YAAA,EAAM;QAaFZ,EAVJ,CAAAC,cAAA,gBAQC,aAEwD;QACjDD,EAAA,CAAAgL,YAAA,MAEc;QAClBhL,EAAA,CAAAY,YAAA,EAAM;QAGNZ,EAAA,CAAAkC,gBAAA,IAAA+I,8CAAA,oBAAAN,GAAA,CAAAtI,SAAA,OAyDC;QAGDrC,EAAA,CAAAC,cAAA,aAAqD;QACjDD,EAAA,CAAAgL,YAAA,OAEc;QAEtBhL,EADI,CAAAY,YAAA,EAAM,EACJ;QAGNZ,EAAA,CAAAC,cAAA,cAA6C;QACzCD,EAAA,CAAAgL,YAAA,OAAiE;QAEzEhL,EADI,CAAAY,YAAA,EAAM,EACJ;QAGNZ,EAAA,CAAAkB,mBAAA,KAAAgK,uDAAA,iBAAyB;;;QAxFjBlL,EAAA,CAAA2B,SAAA,GAGE;QAHF3B,EAAA,CAAAa,UAAA,yBAAAb,EAAA,CAAAmL,eAAA,IAAAC,GAAA,EAAAT,GAAA,CAAApH,KAAA,EAGE;QAWFvD,EAAA,CAAA2B,SAAA,GAyDC;QAzDD3B,EAAA,CAAAyC,UAAA,CAAAkI,GAAA,CAAAjI,UAAA,CAyDC;QAiBT1C,EAAA,CAAA2B,SAAA,GA4BC;QA5BD3B,EAAA,CAAAyB,aAAA,CAAAkJ,GAAA,CAAA5J,iBAAA,WA4BC;;;mBD3DOvB,sBAAsB,EACtBN,wCAAwC,EACxCC,wCAAwC,EACxCC,8CAA8C,EAC9CC,0CAA0C,EAC1CC,wCAAwC,EACxCC,yCAAyC;IAAA8L,MAAA;IAAAC,aAAA;IAAAC,IAAA;MAAAC,SAAA,EAXjCxM;IAAc;IAAAyM,eAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}