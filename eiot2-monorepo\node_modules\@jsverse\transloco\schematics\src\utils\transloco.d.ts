import { PathFragment } from '@angular-devkit/core';
import { DirEntry, Tree } from '@angular-devkit/schematics';
import { TranslocoGlobalConfig } from '@jsverse/transloco-utils';
import { SchemaOptions } from '../join/schema';
export declare function createConfig(host: Tree, langs: string[], rootTranslationsPath?: string): void;
export declare function updateConfig(host: Tree, config: TranslocoGlobalConfig): void;
export declare function getJsonFileContent(fileName: PathFragment, dir: DirEntry, parser?: (text: string, reviver?: (this: any, key: string, value: any) => any) => any): any;
export declare function setFileContent(host: Tree, dirPath: string, fileName: PathFragment, content: any): void;
export declare function hasSubdirs(dir: DirEntry): number;
export declare function hasFiles(dir: DirEntry): number;
export declare function getTranslationKey(prefix: string, key: any): any;
export declare function getTranslationsRoot(host: Tree, options: {
    project?: string;
    translationPath?: string;
}): string;
export declare function getTranslationFiles(host: Tree, root: string, parser?: any): {
    lang: string;
    translation: Record<string, unknown>;
}[];
export declare function getTranslationEntryPaths(host: Tree, rootDirPath: string): {
    scope: string;
    path: string;
}[];
export declare function getDefaultLang(options: SchemaOptions): string;
