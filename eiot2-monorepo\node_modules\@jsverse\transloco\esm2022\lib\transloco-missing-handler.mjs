import { Injectable, InjectionToken } from '@angular/core';
import * as i0 from "@angular/core";
export const TRANSLOCO_MISSING_HANDLER = new InjectionToken(ngDevMode ? 'TRANSLOCO_MISSING_HANDLER' : '');
export class DefaultMissingHandler {
    handle(key, config) {
        if (config.missingHandler.logMissingKey && !config.prodMode) {
            const msg = `Missing translation for '${key}'`;
            console.warn(`%c ${msg}`, 'font-size: 12px; color: red');
        }
        return key;
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "18.2.9", ngImport: i0, type: Default<PERSON>issing<PERSON>andler, deps: [], target: i0.ɵɵFactoryTarget.Injectable });
    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: "12.0.0", version: "18.2.9", ngImport: i0, type: DefaultMissing<PERSON><PERSON><PERSON> });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "18.2.9", ngImport: i0, type: Default<PERSON>issing<PERSON>and<PERSON>, decorators: [{
            type: Injectable
        }] });
//# sourceMappingURL=data:application/json;base64,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