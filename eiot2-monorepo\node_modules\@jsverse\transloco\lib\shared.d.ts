import { OperatorFunction } from 'rxjs';
import { TranslocoService } from './transloco.service';
import { InlineLoader, LoadedEvent, TranslocoScope } from './types';
export declare function getScopeFromLang(lang: string): string;
export declare function getLangFromScope(lang: string): string;
/**
 * @example
 *
 * getPipeValue('todos|scoped', 'scoped') [true, 'todos']
 * getPipeValue('en|static', 'static') [true, 'en']
 * getPipeValue('en', 'static') [false, 'en']
 */
export declare function getPipeValue(str: string | undefined, value: string, char?: string): [boolean, string];
export declare function shouldListenToLangChanges(service: TranslocoService, lang?: string): boolean;
export declare function listenOrNotOperator<T>(listenToLangChange?: boolean): OperatorFunction<T, T>;
export declare function resolveInlineLoader(providerScope: TranslocoScope | null, scope?: string): InlineLoader | undefined;
export declare function getEventPayload(lang: string): LoadedEvent['payload'];
