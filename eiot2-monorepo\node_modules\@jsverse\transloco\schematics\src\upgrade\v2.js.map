{"version": 3, "file": "v2.js", "sourceRoot": "", "sources": ["../../../../../../libs/transloco-schematics/src/upgrade/v2.ts"], "names": [], "mappings": ";;AAKA,kBAiGC;AAtGD,+BAA+B;AAC/B,8BAA8B;AAE9B,+BAAwC;AAExC,SAAgB,GAAG,CAAC,IAAI;IACtB,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,kCAAkC,CAAC,CAAC;IACpE,MAAM,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;IACrC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IAClC,MAAM,SAAS,GAAG,IAAA,WAAQ,EAAC,GAAG,IAAI,OAAO,CAAC,CAAC;IAC3C,MAAM,aAAa,GAAG,uDAAuD,CAAC;IAC9E,MAAM,eAAe,GACnB,qFAAqF,CAAC;IACxF,MAAM,YAAY,GAAG,CAAC,OAAO,EAAE,EAAE,CAC/B,aAAa,OAAO,2EAA2E,CAAC;IAClG,MAAM,UAAU,GAAG,CAAC,OAAO,EAAE,EAAE,CAC7B,IAAI,MAAM,CAAC,YAAY,YAAY,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC1D,MAAM,WAAW,GAAG,CAAC,OAAO,EAAE,EAAE,CAC9B,IAAI,MAAM,CAAC,SAAS,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IACtD,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE,CAAC;QAC7B,IAAI,GAAG,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACjD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,CAAC;YAAE,SAAS;QACzC,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;YACtD,IAAI,eAAe,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACpC,OAAO,eAAe,EAAE,CAAC;gBACvB,MAAM,CAAC,UAAU,CAAC,GAAG,eAAe,CAAC;gBACrC,MAAM,EAAE,OAAO,EAAE,GACf,KAAK,KAAK,CAAC;oBACT,CAAC,CAAC,eAAe,CAAC,MAAM;oBACxB,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,MAAM,CAAC;gBACrD,IAAI,gBAAgB,GAAG,UAAU,CAAC;gBAClC,IAAI,UAAU,CAAC;gBACf,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;oBAC/D,IAAI,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBAC1C,UAAU,GAAG,UAAU,IAAI,CAAC,CAAC,SAAS,CAAC;oBACvC,OAAO,SAAS,EAAE,CAAC;wBACjB,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC;wBAC3C,uEAAuE;wBACvE,wCAAwC;wBACxC,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,GAAG,MAAM;6BACzB,IAAI,EAAE;6BACN,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;6BACnB,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;6BACzB,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,EAAE,CAAC;6BAC1B,KAAK,CAAC,GAAG,CAAC,CAAC;wBACd,IAAI,OAAO,GAAG,GAAG,CAAC;wBAClB,MAAM,KAAK,GAAG,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;wBAChD,MAAM,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CACzC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CACjC,CAAC;wBACF,IAAI,UAAU,EAAE,CAAC;4BACf,MAAM,UAAU,GAAG,UAAU,CAAC,SAAS,CACrC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,EACvB,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAChC,CAAC;4BACF,IAAI,UAAU,EAAE,CAAC;gCACf,OAAO,GAAG,KAAK,UAAU,GAAG,CAAC;4BAC/B,CAAC;wBACH,CAAC;wBACD,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;4BACjB,GAAG,GAAG,GAAG,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;wBACpC,CAAC;wBACD,gBAAgB,GAAG,UAAU;4BAC3B,CAAC,CAAC,gBAAgB,CAAC,OAAO,CACtB,GAAG,MAAM,GAAG,KAAK,EAAE,EACnB,GAAG,OAAO,KAAK,GAAG,IAAI,OAAO,IAAI,KAAK;iCACnC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;iCACnD,IAAI,CAAC,GAAG,CAAC,EAAE,CACf;4BACH,CAAC,CAAC,gBAAgB,CAAC,OAAO,CACtB,MAAM,EACN,GAAG,OAAO,KAAK,GAAG,IAAI,OAAO,EAAE,CAChC,CAAC;wBACN,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBACxC,CAAC;gBACH,CAAC,CAAC,CAAC;gBACH,IAAI,UAAU,EAAE,CAAC;oBACf,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;gBAClD,CAAC;gBACD,eAAe,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAClC,CAAC;QACH,CAAC,CAAC,CAAC;QACH,EAAE,CAAC,aAAa,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAC;IACpD,CAAC;IACD,MAAM,OAAO,GAAG,IAAA,WAAQ,EAAC,GAAG,IAAI,YAAY,CAAC,CAAC;IAC9C,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE,CAAC;QAC3B,IAAI,GAAG,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACjD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,oBAAoB,CAAC;YAAE,SAAS;QAClD,8CAA8C;QAC9C,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,oBAAoB,EAAE,sBAAsB,CAAC,CAAC;QAChE,2BAA2B;QAC3B,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,uBAAuB,EAAE,EAAE,CAAC,CAAC;QAC/C,yBAAyB;QACzB,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;YACpC,GAAG,GAAG,GAAG,CAAC,OAAO,CACf,2BAA2B,EAC3B,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,oBAAoB,EAAE,CAAC,IAAI,EAAE,IAAI,CACjE,CAAC;QACJ,CAAC;QACD,EAAE,CAAC,aAAa,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAC;IACpD,CAAC;IACD,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;AAClD,CAAC"}