import { makeEnvironmentProviders, } from '@angular/core';
import { TRANSLOCO_LOADER } from './transloco.loader';
import { TRANSLOCO_CONFIG, translocoConfig, } from './transloco.config';
import { TRANSLOCO_SCOPE } from './transloco-scope';
import { DefaultTranspiler, TRANSLOCO_TRANSPILER, } from './transloco.transpiler';
import { DefaultMissingHandler, TRANSLOCO_MISSING_HANDLER, } from './transloco-missing-handler';
import { DefaultInterceptor, TRANSLOCO_INTERCEPTOR, } from './transloco.interceptor';
import { DefaultFallbackStrategy, TRANSLOCO_FALLBACK_STRATEGY, } from './transloco-fallback-strategy';
import { TRANSLOCO_LOADING_TEMPLATE } from './transloco-loading-template';
import { TRANSLOCO_LANG } from './transloco-lang';
export function provideTransloco(options) {
    const providers = [
        provideTranslocoTranspiler(DefaultTranspiler),
        provideTranslocoMissingHandler(DefaultMissingHandler),
        provideTranslocoInterceptor(DefaultInterceptor),
        provideTranslocoFallbackStrategy(DefaultFallbackStrategy),
    ];
    if (options.config) {
        providers.push(provideTranslocoConfig(options.config));
    }
    if (options.loader) {
        providers.push(provideTranslocoLoader(options.loader));
    }
    return providers;
}
export function provideTranslocoConfig(config) {
    return makeEnvironmentProviders([
        {
            provide: TRANSLOCO_CONFIG,
            useValue: translocoConfig(config),
        },
    ]);
}
export function provideTranslocoLoader(loader) {
    return makeEnvironmentProviders([
        { provide: TRANSLOCO_LOADER, useClass: loader },
    ]);
}
export function provideTranslocoScope(...scopes) {
    return scopes.map((scope) => ({
        provide: TRANSLOCO_SCOPE,
        useValue: scope,
        multi: true,
    }));
}
export function provideTranslocoLoadingTpl(content) {
    return {
        provide: TRANSLOCO_LOADING_TEMPLATE,
        useValue: content,
    };
}
export function provideTranslocoTranspiler(transpiler) {
    return makeEnvironmentProviders([
        {
            provide: TRANSLOCO_TRANSPILER,
            useClass: transpiler,
            deps: [TRANSLOCO_CONFIG],
        },
    ]);
}
export function provideTranslocoFallbackStrategy(strategy) {
    return makeEnvironmentProviders([
        {
            provide: TRANSLOCO_FALLBACK_STRATEGY,
            useClass: strategy,
            deps: [TRANSLOCO_CONFIG],
        },
    ]);
}
export function provideTranslocoMissingHandler(handler) {
    return makeEnvironmentProviders([
        {
            provide: TRANSLOCO_MISSING_HANDLER,
            useClass: handler,
        },
    ]);
}
export function provideTranslocoInterceptor(interceptor) {
    return makeEnvironmentProviders([
        {
            provide: TRANSLOCO_INTERCEPTOR,
            useClass: interceptor,
        },
    ]);
}
export function provideTranslocoLang(lang) {
    return {
        provide: TRANSLOCO_LANG,
        useValue: lang,
    };
}
//# sourceMappingURL=data:application/json;base64,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