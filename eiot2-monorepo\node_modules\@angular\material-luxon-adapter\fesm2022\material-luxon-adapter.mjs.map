{"version": 3, "file": "material-luxon-adapter.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material-luxon-adapter/adapter/luxon-date-adapter.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material-luxon-adapter/adapter/luxon-date-formats.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material-luxon-adapter/adapter/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Injectable, InjectionToken, inject} from '@angular/core';\nimport {DateAdapter, MAT_DATE_LOCALE} from '@angular/material/core';\nimport {\n  DateTime as LuxonDateTime,\n  Info as LuxonInfo,\n  DateTimeOptions as LuxonDateTimeOptions,\n  CalendarSystem as LuxonCalendarSystem,\n} from 'luxon';\n\n/** Configurable options for the `LuxonDateAdapter`. */\nexport interface MatLuxonDateAdapterOptions {\n  /**\n   * Turns the use of utc dates on or off.\n   * Changing this will change how Angular Material components like DatePicker output dates.\n   */\n  useUtc: boolean;\n\n  /**\n   * Sets the first day of week.\n   * Changing this will change how Angular Material components like DatePicker shows start of week.\n   */\n  firstDayOfWeek?: number;\n\n  /**\n   * Sets the output Calendar.\n   * Changing this will change how Angular Material components like DatePicker output dates.\n   */\n  defaultOutputCalendar: LuxonCalendarSystem;\n}\n\n/** InjectionToken for LuxonDateAdapter to configure options. */\nexport const MAT_LUXON_DATE_ADAPTER_OPTIONS = new InjectionToken<MatLuxonDateAdapterOptions>(\n  'MAT_LUXON_DATE_ADAPTER_OPTIONS',\n  {\n    providedIn: 'root',\n    factory: MAT_LUXON_DATE_ADAPTER_OPTIONS_FACTORY,\n  },\n);\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport function MAT_LUXON_DATE_ADAPTER_OPTIONS_FACTORY(): MatLuxonDateAdapterOptions {\n  return {\n    useUtc: false,\n    defaultOutputCalendar: 'gregory',\n  };\n}\n\n/** Creates an array and fills it with values. */\nfunction range<T>(length: number, valueFunction: (index: number) => T): T[] {\n  const valuesArray = Array(length);\n  for (let i = 0; i < length; i++) {\n    valuesArray[i] = valueFunction(i);\n  }\n  return valuesArray;\n}\n\n/** Adapts Luxon Dates for use with Angular Material. */\n@Injectable()\nexport class LuxonDateAdapter extends DateAdapter<LuxonDateTime> {\n  private _useUTC: boolean;\n  private _firstDayOfWeek: number | undefined;\n  private _defaultOutputCalendar: LuxonCalendarSystem;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super();\n\n    const dateLocale = inject(MAT_DATE_LOCALE, {optional: true});\n    const options = inject<MatLuxonDateAdapterOptions>(MAT_LUXON_DATE_ADAPTER_OPTIONS, {\n      optional: true,\n    });\n\n    this._useUTC = !!options?.useUtc;\n    this._firstDayOfWeek = options?.firstDayOfWeek;\n    this._defaultOutputCalendar = options?.defaultOutputCalendar || 'gregory';\n    this.setLocale(dateLocale || LuxonDateTime.local().locale);\n  }\n\n  getYear(date: LuxonDateTime): number {\n    return date.year;\n  }\n\n  getMonth(date: LuxonDateTime): number {\n    // Luxon works with 1-indexed months whereas our code expects 0-indexed.\n    return date.month - 1;\n  }\n\n  getDate(date: LuxonDateTime): number {\n    return date.day;\n  }\n\n  getDayOfWeek(date: LuxonDateTime): number {\n    return date.weekday;\n  }\n\n  getMonthNames(style: 'long' | 'short' | 'narrow'): string[] {\n    // Adding outputCalendar option, because LuxonInfo doesn't get effected by LuxonSettings\n    return LuxonInfo.months(style, {\n      locale: this.locale,\n      outputCalendar: this._defaultOutputCalendar,\n    });\n  }\n\n  getDateNames(): string[] {\n    // At the time of writing, Luxon doesn't offer similar\n    // functionality so we have to fall back to the Intl API.\n    const dtf = new Intl.DateTimeFormat(this.locale, {day: 'numeric', timeZone: 'utc'});\n\n    // Format a UTC date in order to avoid DST issues.\n    return range(31, i => dtf.format(LuxonDateTime.utc(2017, 1, i + 1).toJSDate()));\n  }\n\n  getDayOfWeekNames(style: 'long' | 'short' | 'narrow'): string[] {\n    // Note that we shift the array once, because Luxon returns Monday as the\n    // first day of the week, whereas our logic assumes that it's Sunday. See:\n    // https://moment.github.io/luxon/api-docs/index.html#infoweekdays\n    const days = LuxonInfo.weekdays(style, {locale: this.locale});\n    days.unshift(days.pop()!);\n    return days;\n  }\n\n  getYearName(date: LuxonDateTime): string {\n    return date.toFormat('yyyy', this._getOptions());\n  }\n\n  getFirstDayOfWeek(): number {\n    return this._firstDayOfWeek ?? LuxonInfo.getStartOfWeek({locale: this.locale});\n  }\n\n  getNumDaysInMonth(date: LuxonDateTime): number {\n    return date.daysInMonth!;\n  }\n\n  clone(date: LuxonDateTime): LuxonDateTime {\n    return LuxonDateTime.fromObject(date.toObject(), this._getOptions());\n  }\n\n  createDate(year: number, month: number, date: number): LuxonDateTime {\n    const options = this._getOptions();\n\n    if (month < 0 || month > 11) {\n      throw Error(`Invalid month index \"${month}\". Month index has to be between 0 and 11.`);\n    }\n\n    if (date < 1) {\n      throw Error(`Invalid date \"${date}\". Date has to be greater than 0.`);\n    }\n\n    // Luxon uses 1-indexed months so we need to add one to the month.\n    const result = this._useUTC\n      ? LuxonDateTime.utc(year, month + 1, date, options)\n      : LuxonDateTime.local(year, month + 1, date, options);\n\n    if (!this.isValid(result)) {\n      throw Error(`Invalid date \"${date}\". Reason: \"${result.invalidReason}\".`);\n    }\n\n    return result;\n  }\n\n  today(): LuxonDateTime {\n    const options = this._getOptions();\n\n    return this._useUTC ? LuxonDateTime.utc(options) : LuxonDateTime.local(options);\n  }\n\n  parse(value: unknown, parseFormat: string | string[]): LuxonDateTime | null {\n    const options: LuxonDateTimeOptions = this._getOptions();\n\n    if (typeof value == 'string' && value.length > 0) {\n      const iso8601Date = LuxonDateTime.fromISO(value, options);\n\n      if (this.isValid(iso8601Date)) {\n        return iso8601Date;\n      }\n\n      const formats = Array.isArray(parseFormat) ? parseFormat : [parseFormat];\n\n      if (!parseFormat.length) {\n        throw Error('Formats array must not be empty.');\n      }\n\n      for (const format of formats) {\n        const fromFormat = LuxonDateTime.fromFormat(value, format, options);\n\n        if (this.isValid(fromFormat)) {\n          return fromFormat;\n        }\n      }\n\n      return this.invalid();\n    } else if (typeof value === 'number') {\n      return LuxonDateTime.fromMillis(value, options);\n    } else if (value instanceof Date) {\n      return LuxonDateTime.fromJSDate(value, options);\n    } else if (value instanceof LuxonDateTime) {\n      return LuxonDateTime.fromMillis(value.toMillis(), options);\n    }\n\n    return null;\n  }\n\n  format(date: LuxonDateTime, displayFormat: string): string {\n    if (!this.isValid(date)) {\n      throw Error('LuxonDateAdapter: Cannot format invalid date.');\n    }\n    if (this._useUTC) {\n      return date.setLocale(this.locale).setZone('utc').toFormat(displayFormat);\n    } else {\n      return date.setLocale(this.locale).toFormat(displayFormat);\n    }\n  }\n\n  addCalendarYears(date: LuxonDateTime, years: number): LuxonDateTime {\n    return date.reconfigure(this._getOptions()).plus({years});\n  }\n\n  addCalendarMonths(date: LuxonDateTime, months: number): LuxonDateTime {\n    return date.reconfigure(this._getOptions()).plus({months});\n  }\n\n  addCalendarDays(date: LuxonDateTime, days: number): LuxonDateTime {\n    return date.reconfigure(this._getOptions()).plus({days});\n  }\n\n  toIso8601(date: LuxonDateTime): string {\n    return date.toISO()!;\n  }\n\n  /**\n   * Returns the given value if given a valid Luxon or null. Deserializes valid ISO 8601 strings\n   * (https://www.ietf.org/rfc/rfc3339.txt) and valid Date objects into valid DateTime and empty\n   * string into null. Returns an invalid date for all other values.\n   */\n  override deserialize(value: unknown): LuxonDateTime | null {\n    const options = this._getOptions();\n    let date: LuxonDateTime | undefined;\n    if (value instanceof Date) {\n      date = LuxonDateTime.fromJSDate(value, options);\n    }\n    if (typeof value === 'string') {\n      if (!value) {\n        return null;\n      }\n      date = LuxonDateTime.fromISO(value, options);\n    }\n    if (date && this.isValid(date)) {\n      return date;\n    }\n    return super.deserialize(value);\n  }\n\n  isDateInstance(obj: unknown): obj is LuxonDateTime {\n    return obj instanceof LuxonDateTime;\n  }\n\n  isValid(date: LuxonDateTime): boolean {\n    return date.isValid;\n  }\n\n  invalid(): LuxonDateTime {\n    return LuxonDateTime.invalid('Invalid Luxon DateTime object.');\n  }\n\n  override setTime(\n    target: LuxonDateTime,\n    hours: number,\n    minutes: number,\n    seconds: number,\n  ): LuxonDateTime {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (hours < 0 || hours > 23) {\n        throw Error(`Invalid hours \"${hours}\". Hours value must be between 0 and 23.`);\n      }\n\n      if (minutes < 0 || minutes > 59) {\n        throw Error(`Invalid minutes \"${minutes}\". Minutes value must be between 0 and 59.`);\n      }\n\n      if (seconds < 0 || seconds > 59) {\n        throw Error(`Invalid seconds \"${seconds}\". Seconds value must be between 0 and 59.`);\n      }\n    }\n\n    return this.clone(target).set({\n      hour: hours,\n      minute: minutes,\n      second: seconds,\n      millisecond: 0,\n    });\n  }\n\n  override getHours(date: LuxonDateTime): number {\n    return date.hour;\n  }\n\n  override getMinutes(date: LuxonDateTime): number {\n    return date.minute;\n  }\n\n  override getSeconds(date: LuxonDateTime): number {\n    return date.second;\n  }\n\n  override parseTime(value: unknown, parseFormat: string | string[]): LuxonDateTime | null {\n    const result = this.parse(value, parseFormat);\n\n    if ((!result || !this.isValid(result)) && typeof value === 'string') {\n      // It seems like Luxon doesn't work well cross-browser for strings that have\n      // additional characters around the time. Try parsing without those characters.\n      return this.parse(value.replace(/[^0-9:(AM|PM)]/gi, ''), parseFormat) || result;\n    }\n\n    return result;\n  }\n\n  override addSeconds(date: LuxonDateTime, amount: number): LuxonDateTime {\n    return date.reconfigure(this._getOptions()).plus({seconds: amount});\n  }\n\n  /** Gets the options that should be used when constructing a new `DateTime` object. */\n  private _getOptions(): LuxonDateTimeOptions {\n    return {\n      zone: this._useUTC ? 'utc' : undefined,\n      locale: this.locale,\n      outputCalendar: this._defaultOutputCalendar,\n    };\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {MatDateFormats} from '@angular/material/core';\n\nexport const MAT_LUXON_DATE_FORMATS: MatDateFormats = {\n  parse: {\n    dateInput: 'D',\n    timeInput: 't',\n  },\n  display: {\n    dateInput: 'D',\n    timeInput: 't',\n    monthYearLabel: 'LLL yyyy',\n    dateA11yLabel: 'DD',\n    monthYearA11yLabel: 'LLLL yyyy',\n    timeOptionLabel: 't',\n  },\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule, Provider} from '@angular/core';\nimport {\n  DateAdapter,\n  MAT_DATE_FORMATS,\n  MAT_DATE_LOCALE,\n  MatDateFormats,\n} from '@angular/material/core';\nimport {MAT_LUXON_DATE_ADAPTER_OPTIONS, LuxonDateAdapter} from './luxon-date-adapter';\nimport {MAT_LUXON_DATE_FORMATS} from './luxon-date-formats';\n\nexport * from './luxon-date-adapter';\nexport * from './luxon-date-formats';\n\n@NgModule({\n  providers: [\n    {\n      provide: DateAdapter,\n      useClass: LuxonDateAdapter,\n      deps: [MAT_DATE_LOCALE, MAT_LUXON_DATE_ADAPTER_OPTIONS],\n    },\n  ],\n})\nexport class LuxonDateModule {}\n\n@NgModule({\n  providers: [provideLuxonDateAdapter()],\n})\nexport class MatLuxonDateModule {}\n\nexport function provideLuxonDateAdapter(\n  formats: MatDateFormats = MAT_LUXON_DATE_FORMATS,\n): Provider[] {\n  return [\n    {\n      provide: DateAdapter,\n      useClass: LuxonDateAdapter,\n      deps: [MAT_DATE_LOCALE, MAT_LUXON_DATE_ADAPTER_OPTIONS],\n    },\n    {provide: MAT_DATE_FORMATS, useValue: formats},\n  ];\n}\n"], "names": ["LuxonDateTime", "LuxonInfo"], "mappings": ";;;;;AAsCA;MACa,8BAA8B,GAAG,IAAI,cAAc,CAC9D,gCAAgC,EAChC;AACE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,sCAAsC;AAChD,CAAA;AAGH;;;;AAIG;SACa,sCAAsC,GAAA;IACpD,OAAO;AACL,QAAA,MAAM,EAAE,KAAK;AACb,QAAA,qBAAqB,EAAE,SAAS;KACjC;AACH;AAEA;AACA,SAAS,KAAK,CAAI,MAAc,EAAE,aAAmC,EAAA;AACnE,IAAA,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC;AACjC,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;QAC/B,WAAW,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;;AAEnC,IAAA,OAAO,WAAW;AACpB;AAEA;AAEM,MAAO,gBAAiB,SAAQ,WAA0B,CAAA;AACtD,IAAA,OAAO;AACP,IAAA,eAAe;AACf,IAAA,sBAAsB;AAI9B,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;AAEP,QAAA,MAAM,UAAU,GAAG,MAAM,CAAC,eAAe,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAC5D,QAAA,MAAM,OAAO,GAAG,MAAM,CAA6B,8BAA8B,EAAE;AACjF,YAAA,QAAQ,EAAE,IAAI;AACf,SAAA,CAAC;QAEF,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,MAAM;AAChC,QAAA,IAAI,CAAC,eAAe,GAAG,OAAO,EAAE,cAAc;QAC9C,IAAI,CAAC,sBAAsB,GAAG,OAAO,EAAE,qBAAqB,IAAI,SAAS;AACzE,QAAA,IAAI,CAAC,SAAS,CAAC,UAAU,IAAIA,QAAa,CAAC,KAAK,EAAE,CAAC,MAAM,CAAC;;AAG5D,IAAA,OAAO,CAAC,IAAmB,EAAA;QACzB,OAAO,IAAI,CAAC,IAAI;;AAGlB,IAAA,QAAQ,CAAC,IAAmB,EAAA;;AAE1B,QAAA,OAAO,IAAI,CAAC,KAAK,GAAG,CAAC;;AAGvB,IAAA,OAAO,CAAC,IAAmB,EAAA;QACzB,OAAO,IAAI,CAAC,GAAG;;AAGjB,IAAA,YAAY,CAAC,IAAmB,EAAA;QAC9B,OAAO,IAAI,CAAC,OAAO;;AAGrB,IAAA,aAAa,CAAC,KAAkC,EAAA;;AAE9C,QAAA,OAAOC,IAAS,CAAC,MAAM,CAAC,KAAK,EAAE;YAC7B,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,cAAc,EAAE,IAAI,CAAC,sBAAsB;AAC5C,SAAA,CAAC;;IAGJ,YAAY,GAAA;;;QAGV,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,EAAC,GAAG,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAC,CAAC;;AAGnF,QAAA,OAAO,KAAK,CAAC,EAAE,EAAE,CAAC,IAAI,GAAG,CAAC,MAAM,CAACD,QAAa,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;;AAGjF,IAAA,iBAAiB,CAAC,KAAkC,EAAA;;;;AAIlD,QAAA,MAAM,IAAI,GAAGC,IAAS,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAC,CAAC;QAC7D,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAG,CAAC;AACzB,QAAA,OAAO,IAAI;;AAGb,IAAA,WAAW,CAAC,IAAmB,EAAA;QAC7B,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC;;IAGlD,iBAAiB,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,eAAe,IAAIA,IAAS,CAAC,cAAc,CAAC,EAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAC,CAAC;;AAGhF,IAAA,iBAAiB,CAAC,IAAmB,EAAA;QACnC,OAAO,IAAI,CAAC,WAAY;;AAG1B,IAAA,KAAK,CAAC,IAAmB,EAAA;AACvB,QAAA,OAAOD,QAAa,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC;;AAGtE,IAAA,UAAU,CAAC,IAAY,EAAE,KAAa,EAAE,IAAY,EAAA;AAClD,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE;QAElC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,EAAE,EAAE;AAC3B,YAAA,MAAM,KAAK,CAAC,CAAA,qBAAA,EAAwB,KAAK,CAAA,0CAAA,CAA4C,CAAC;;AAGxF,QAAA,IAAI,IAAI,GAAG,CAAC,EAAE;AACZ,YAAA,MAAM,KAAK,CAAC,CAAA,cAAA,EAAiB,IAAI,CAAA,iCAAA,CAAmC,CAAC;;;AAIvE,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC;AAClB,cAAEA,QAAa,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,GAAG,CAAC,EAAE,IAAI,EAAE,OAAO;AAClD,cAAEA,QAAa,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,GAAG,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC;QAEvD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACzB,MAAM,KAAK,CAAC,CAAA,cAAA,EAAiB,IAAI,CAAA,YAAA,EAAe,MAAM,CAAC,aAAa,CAAI,EAAA,CAAA,CAAC;;AAG3E,QAAA,OAAO,MAAM;;IAGf,KAAK,GAAA;AACH,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE;QAElC,OAAO,IAAI,CAAC,OAAO,GAAGA,QAAa,CAAC,GAAG,CAAC,OAAO,CAAC,GAAGA,QAAa,CAAC,KAAK,CAAC,OAAO,CAAC;;IAGjF,KAAK,CAAC,KAAc,EAAE,WAA8B,EAAA;AAClD,QAAA,MAAM,OAAO,GAAyB,IAAI,CAAC,WAAW,EAAE;QAExD,IAAI,OAAO,KAAK,IAAI,QAAQ,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YAChD,MAAM,WAAW,GAAGA,QAAa,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC;AAEzD,YAAA,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;AAC7B,gBAAA,OAAO,WAAW;;AAGpB,YAAA,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,WAAW,GAAG,CAAC,WAAW,CAAC;AAExE,YAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;AACvB,gBAAA,MAAM,KAAK,CAAC,kCAAkC,CAAC;;AAGjD,YAAA,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;AAC5B,gBAAA,MAAM,UAAU,GAAGA,QAAa,CAAC,UAAU,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC;AAEnE,gBAAA,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;AAC5B,oBAAA,OAAO,UAAU;;;AAIrB,YAAA,OAAO,IAAI,CAAC,OAAO,EAAE;;AAChB,aAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YACpC,OAAOA,QAAa,CAAC,UAAU,CAAC,KAAK,EAAE,OAAO,CAAC;;AAC1C,aAAA,IAAI,KAAK,YAAY,IAAI,EAAE;YAChC,OAAOA,QAAa,CAAC,UAAU,CAAC,KAAK,EAAE,OAAO,CAAC;;AAC1C,aAAA,IAAI,KAAK,YAAYA,QAAa,EAAE;YACzC,OAAOA,QAAa,CAAC,UAAU,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,OAAO,CAAC;;AAG5D,QAAA,OAAO,IAAI;;IAGb,MAAM,CAAC,IAAmB,EAAE,aAAqB,EAAA;QAC/C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AACvB,YAAA,MAAM,KAAK,CAAC,+CAA+C,CAAC;;AAE9D,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC;;aACpE;AACL,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC;;;IAI9D,gBAAgB,CAAC,IAAmB,EAAE,KAAa,EAAA;AACjD,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,EAAC,KAAK,EAAC,CAAC;;IAG3D,iBAAiB,CAAC,IAAmB,EAAE,MAAc,EAAA;AACnD,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,EAAC,MAAM,EAAC,CAAC;;IAG5D,eAAe,CAAC,IAAmB,EAAE,IAAY,EAAA;AAC/C,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,EAAC,IAAI,EAAC,CAAC;;AAG1D,IAAA,SAAS,CAAC,IAAmB,EAAA;AAC3B,QAAA,OAAO,IAAI,CAAC,KAAK,EAAG;;AAGtB;;;;AAIG;AACM,IAAA,WAAW,CAAC,KAAc,EAAA;AACjC,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE;AAClC,QAAA,IAAI,IAA+B;AACnC,QAAA,IAAI,KAAK,YAAY,IAAI,EAAE;YACzB,IAAI,GAAGA,QAAa,CAAC,UAAU,CAAC,KAAK,EAAE,OAAO,CAAC;;AAEjD,QAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,IAAI,CAAC,KAAK,EAAE;AACV,gBAAA,OAAO,IAAI;;YAEb,IAAI,GAAGA,QAAa,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC;;QAE9C,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AAC9B,YAAA,OAAO,IAAI;;AAEb,QAAA,OAAO,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC;;AAGjC,IAAA,cAAc,CAAC,GAAY,EAAA;QACzB,OAAO,GAAG,YAAYA,QAAa;;AAGrC,IAAA,OAAO,CAAC,IAAmB,EAAA;QACzB,OAAO,IAAI,CAAC,OAAO;;IAGrB,OAAO,GAAA;AACL,QAAA,OAAOA,QAAa,CAAC,OAAO,CAAC,gCAAgC,CAAC;;AAGvD,IAAA,OAAO,CACd,MAAqB,EACrB,KAAa,EACb,OAAe,EACf,OAAe,EAAA;AAEf,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;YACjD,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,EAAE,EAAE;AAC3B,gBAAA,MAAM,KAAK,CAAC,CAAA,eAAA,EAAkB,KAAK,CAAA,wCAAA,CAA0C,CAAC;;YAGhF,IAAI,OAAO,GAAG,CAAC,IAAI,OAAO,GAAG,EAAE,EAAE;AAC/B,gBAAA,MAAM,KAAK,CAAC,CAAA,iBAAA,EAAoB,OAAO,CAAA,0CAAA,CAA4C,CAAC;;YAGtF,IAAI,OAAO,GAAG,CAAC,IAAI,OAAO,GAAG,EAAE,EAAE;AAC/B,gBAAA,MAAM,KAAK,CAAC,CAAA,iBAAA,EAAoB,OAAO,CAAA,0CAAA,CAA4C,CAAC;;;QAIxF,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC;AAC5B,YAAA,IAAI,EAAE,KAAK;AACX,YAAA,MAAM,EAAE,OAAO;AACf,YAAA,MAAM,EAAE,OAAO;AACf,YAAA,WAAW,EAAE,CAAC;AACf,SAAA,CAAC;;AAGK,IAAA,QAAQ,CAAC,IAAmB,EAAA;QACnC,OAAO,IAAI,CAAC,IAAI;;AAGT,IAAA,UAAU,CAAC,IAAmB,EAAA;QACrC,OAAO,IAAI,CAAC,MAAM;;AAGX,IAAA,UAAU,CAAC,IAAmB,EAAA;QACrC,OAAO,IAAI,CAAC,MAAM;;IAGX,SAAS,CAAC,KAAc,EAAE,WAA8B,EAAA;QAC/D,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,WAAW,CAAC;AAE7C,QAAA,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,OAAO,KAAK,KAAK,QAAQ,EAAE;;;AAGnE,YAAA,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,EAAE,WAAW,CAAC,IAAI,MAAM;;AAGjF,QAAA,OAAO,MAAM;;IAGN,UAAU,CAAC,IAAmB,EAAE,MAAc,EAAA;AACrD,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,IAAI,CAAC,EAAC,OAAO,EAAE,MAAM,EAAC,CAAC;;;IAI7D,WAAW,GAAA;QACjB,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,OAAO,GAAG,KAAK,GAAG,SAAS;YACtC,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,cAAc,EAAE,IAAI,CAAC,sBAAsB;SAC5C;;uGA7QQ,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;2GAAhB,gBAAgB,EAAA,CAAA;;2FAAhB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAD5B;;;AC3DY,MAAA,sBAAsB,GAAmB;AACpD,IAAA,KAAK,EAAE;AACL,QAAA,SAAS,EAAE,GAAG;AACd,QAAA,SAAS,EAAE,GAAG;AACf,KAAA;AACD,IAAA,OAAO,EAAE;AACP,QAAA,SAAS,EAAE,GAAG;AACd,QAAA,SAAS,EAAE,GAAG;AACd,QAAA,cAAc,EAAE,UAAU;AAC1B,QAAA,aAAa,EAAE,IAAI;AACnB,QAAA,kBAAkB,EAAE,WAAW;AAC/B,QAAA,eAAe,EAAE,GAAG;AACrB,KAAA;;;MCQU,eAAe,CAAA;uGAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAf,eAAe,EAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,EARf,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,WAAW;AACpB,gBAAA,QAAQ,EAAE,gBAAgB;AAC1B,gBAAA,IAAI,EAAE,CAAC,eAAe,EAAE,8BAA8B,CAAC;AACxD,aAAA;AACF,SAAA,EAAA,CAAA;;2FAEU,eAAe,EAAA,UAAA,EAAA,CAAA;kBAT3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,SAAS,EAAE;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,WAAW;AACpB,4BAAA,QAAQ,EAAE,gBAAgB;AAC1B,4BAAA,IAAI,EAAE,CAAC,eAAe,EAAE,8BAA8B,CAAC;AACxD,yBAAA;AACF,qBAAA;AACF,iBAAA;;MAMY,kBAAkB,CAAA;uGAAlB,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAlB,kBAAkB,EAAA,CAAA;AAAlB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,kBAAkB,EAFlB,SAAA,EAAA,CAAC,uBAAuB,EAAE,CAAC,EAAA,CAAA;;2FAE3B,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAH9B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,SAAS,EAAE,CAAC,uBAAuB,EAAE,CAAC;AACvC,iBAAA;;AAGe,SAAA,uBAAuB,CACrC,OAAA,GAA0B,sBAAsB,EAAA;IAEhD,OAAO;AACL,QAAA;AACE,YAAA,OAAO,EAAE,WAAW;AACpB,YAAA,QAAQ,EAAE,gBAAgB;AAC1B,YAAA,IAAI,EAAE,CAAC,eAAe,EAAE,8BAA8B,CAAC;AACxD,SAAA;AACD,QAAA,EAAC,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,OAAO,EAAC;KAC/C;AACH;;;;"}