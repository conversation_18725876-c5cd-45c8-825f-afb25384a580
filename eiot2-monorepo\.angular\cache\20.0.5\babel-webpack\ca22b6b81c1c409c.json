{"ast": null, "code": "import { ChangeDetectorRef, inject } from '@angular/core';\nimport { fuseAnimations } from '@fuse/animations';\nimport { FuseNavigationService } from '@fuse/components/navigation/navigation.service';\nimport { FuseUtilsService } from '@fuse/services/utils/utils.service';\nimport { ReplaySubject, Subject } from 'rxjs';\nimport { FuseHorizontalNavigationBasicItemComponent } from './components/basic/basic.component';\nimport { FuseHorizontalNavigationBranchItemComponent } from './components/branch/branch.component';\nimport { FuseHorizontalNavigationSpacerItemComponent } from './components/spacer/spacer.component';\nimport * as i0 from \"@angular/core\";\nfunction FuseHorizontalNavigationComponent_For_2_Conditional_0_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"fuse-horizontal-navigation-basic-item\", 1);\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"item\", item_r1)(\"name\", ctx_r1.name);\n  }\n}\nfunction FuseHorizontalNavigationComponent_For_2_Conditional_0_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"fuse-horizontal-navigation-branch-item\", 1);\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"item\", item_r1)(\"name\", ctx_r1.name);\n  }\n}\nfunction FuseHorizontalNavigationComponent_For_2_Conditional_0_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"fuse-horizontal-navigation-spacer-item\", 1);\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"item\", item_r1)(\"name\", ctx_r1.name);\n  }\n}\nfunction FuseHorizontalNavigationComponent_For_2_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵconditionalCreate(0, FuseHorizontalNavigationComponent_For_2_Conditional_0_Conditional_0_Template, 1, 2, \"fuse-horizontal-navigation-basic-item\", 1);\n    i0.ɵɵconditionalCreate(1, FuseHorizontalNavigationComponent_For_2_Conditional_0_Conditional_1_Template, 1, 2, \"fuse-horizontal-navigation-branch-item\", 1);\n    i0.ɵɵconditionalCreate(2, FuseHorizontalNavigationComponent_For_2_Conditional_0_Conditional_2_Template, 1, 2, \"fuse-horizontal-navigation-spacer-item\", 1);\n  }\n  if (rf & 2) {\n    const item_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵconditional(item_r1.type === \"basic\" ? 0 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(item_r1.type === \"aside\" || item_r1.type === \"collapsable\" || item_r1.type === \"group\" ? 1 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(item_r1.type === \"spacer\" ? 2 : -1);\n  }\n}\nfunction FuseHorizontalNavigationComponent_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵconditionalCreate(0, FuseHorizontalNavigationComponent_For_2_Conditional_0_Template, 3, 3);\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    i0.ɵɵconditional(item_r1.hidden && !item_r1.hidden(item_r1) || !item_r1.hidden ? 0 : -1);\n  }\n}\nexport class FuseHorizontalNavigationComponent {\n  constructor() {\n    this._changeDetectorRef = inject(ChangeDetectorRef);\n    this._fuseNavigationService = inject(FuseNavigationService);\n    this._fuseUtilsService = inject(FuseUtilsService);\n    this.name = this._fuseUtilsService.randomId();\n    this.onRefreshed = new ReplaySubject(1);\n    this._unsubscribeAll = new Subject();\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Lifecycle hooks\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * On changes\n   *\n   * @param changes\n   */\n  ngOnChanges(changes) {\n    // Navigation\n    if ('navigation' in changes) {\n      // Mark for check\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /**\n   * On init\n   */\n  ngOnInit() {\n    // Make sure the name input is not an empty string\n    if (this.name === '') {\n      this.name = this._fuseUtilsService.randomId();\n    }\n    // Register the navigation component\n    this._fuseNavigationService.registerComponent(this.name, this);\n  }\n  /**\n   * On destroy\n   */\n  ngOnDestroy() {\n    // Deregister the navigation component from the registry\n    this._fuseNavigationService.deregisterComponent(this.name);\n    // Unsubscribe from all subscriptions\n    this._unsubscribeAll.next(null);\n    this._unsubscribeAll.complete();\n  }\n  // -----------------------------------------------------------------------------------------------------\n  // @ Public methods\n  // -----------------------------------------------------------------------------------------------------\n  /**\n   * Refresh the component to apply the changes\n   */\n  refresh() {\n    // Mark for check\n    this._changeDetectorRef.markForCheck();\n    // Execute the observable\n    this.onRefreshed.next(true);\n  }\n  /**\n   * Track by function for ngFor loops\n   *\n   * @param index\n   * @param item\n   */\n  trackByFn(index, item) {\n    return item.id || index;\n  }\n  static #_ = this.ɵfac = function FuseHorizontalNavigationComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FuseHorizontalNavigationComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: FuseHorizontalNavigationComponent,\n    selectors: [[\"fuse-horizontal-navigation\"]],\n    inputs: {\n      name: \"name\",\n      navigation: \"navigation\"\n    },\n    exportAs: [\"fuseHorizontalNavigation\"],\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 3,\n    vars: 0,\n    consts: [[1, \"fuse-horizontal-navigation-wrapper\"], [1, \"fuse-horizontal-navigation-menu-item\", 3, \"item\", \"name\"]],\n    template: function FuseHorizontalNavigationComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵrepeaterCreate(1, FuseHorizontalNavigationComponent_For_2_Template, 1, 1, null, null, ctx.trackByFn, true);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵrepeater(ctx.navigation);\n      }\n    },\n    dependencies: [FuseHorizontalNavigationBasicItemComponent, FuseHorizontalNavigationBranchItemComponent, FuseHorizontalNavigationSpacerItemComponent],\n    styles: [\"/* Root navigation specific */\\nfuse-horizontal-navigation .fuse-horizontal-navigation-wrapper {\\n  display: flex;\\n  align-items: center;\\n  /* Basic, Branch */\\n  /* Basic - When item active (current link) */\\n  /* Branch - When menu open */\\n  /* Spacer */\\n}\\n@media (min-width: 600px) {\\n  fuse-horizontal-navigation .fuse-horizontal-navigation-wrapper fuse-horizontal-navigation-basic-item:hover .fuse-horizontal-navigation-item-wrapper,\\n  fuse-horizontal-navigation .fuse-horizontal-navigation-wrapper fuse-horizontal-navigation-branch-item:hover .fuse-horizontal-navigation-item-wrapper {\\n    background-color: var(--fuse-bg-hover);\\n  }\\n}\\nfuse-horizontal-navigation .fuse-horizontal-navigation-wrapper fuse-horizontal-navigation-basic-item .fuse-horizontal-navigation-item-wrapper,\\nfuse-horizontal-navigation .fuse-horizontal-navigation-wrapper fuse-horizontal-navigation-branch-item .fuse-horizontal-navigation-item-wrapper {\\n  border-radius: 4px;\\n  overflow: hidden;\\n}\\nfuse-horizontal-navigation .fuse-horizontal-navigation-wrapper fuse-horizontal-navigation-basic-item .fuse-horizontal-navigation-item-wrapper .fuse-horizontal-navigation-item,\\nfuse-horizontal-navigation .fuse-horizontal-navigation-wrapper fuse-horizontal-navigation-branch-item .fuse-horizontal-navigation-item-wrapper .fuse-horizontal-navigation-item {\\n  padding: 0 16px;\\n  cursor: pointer;\\n  -webkit-user-select: none;\\n          user-select: none;\\n}\\nfuse-horizontal-navigation .fuse-horizontal-navigation-wrapper fuse-horizontal-navigation-basic-item .fuse-horizontal-navigation-item-wrapper .fuse-horizontal-navigation-item .fuse-horizontal-navigation-item-icon,\\nfuse-horizontal-navigation .fuse-horizontal-navigation-wrapper fuse-horizontal-navigation-branch-item .fuse-horizontal-navigation-item-wrapper .fuse-horizontal-navigation-item .fuse-horizontal-navigation-item-icon {\\n  margin-right: 12px;\\n}\\nfuse-horizontal-navigation .fuse-horizontal-navigation-wrapper fuse-horizontal-navigation-basic-item .fuse-horizontal-navigation-item-active .fuse-horizontal-navigation-item-title,\\nfuse-horizontal-navigation .fuse-horizontal-navigation-wrapper fuse-horizontal-navigation-basic-item .fuse-horizontal-navigation-item-active-forced .fuse-horizontal-navigation-item-title {\\n  --tw-text-opacity: 1 !important;\\n  color: rgba(var(--fuse-primary-rgb), var(--tw-text-opacity)) !important;\\n}\\nfuse-horizontal-navigation .fuse-horizontal-navigation-wrapper fuse-horizontal-navigation-basic-item .fuse-horizontal-navigation-item-active .fuse-horizontal-navigation-item-subtitle,\\nfuse-horizontal-navigation .fuse-horizontal-navigation-wrapper fuse-horizontal-navigation-basic-item .fuse-horizontal-navigation-item-active-forced .fuse-horizontal-navigation-item-subtitle {\\n  --tw-text-opacity: 1 !important;\\n  color: rgba(var(--fuse-primary-400-rgb), var(--tw-text-opacity)) !important;\\n}\\n.dark fuse-horizontal-navigation .fuse-horizontal-navigation-wrapper fuse-horizontal-navigation-basic-item .fuse-horizontal-navigation-item-active .fuse-horizontal-navigation-item-subtitle,\\n.dark fuse-horizontal-navigation .fuse-horizontal-navigation-wrapper fuse-horizontal-navigation-basic-item .fuse-horizontal-navigation-item-active-forced .fuse-horizontal-navigation-item-subtitle {\\n  --tw-text-opacity: 1 !important;\\n  color: rgba(var(--fuse-primary-600-rgb), var(--tw-text-opacity)) !important;\\n}\\nfuse-horizontal-navigation .fuse-horizontal-navigation-wrapper fuse-horizontal-navigation-basic-item .fuse-horizontal-navigation-item-active .fuse-horizontal-navigation-item-icon,\\nfuse-horizontal-navigation .fuse-horizontal-navigation-wrapper fuse-horizontal-navigation-basic-item .fuse-horizontal-navigation-item-active-forced .fuse-horizontal-navigation-item-icon {\\n  --tw-text-opacity: 1 !important;\\n  color: rgba(var(--fuse-primary-rgb), var(--tw-text-opacity)) !important;\\n}\\nfuse-horizontal-navigation .fuse-horizontal-navigation-wrapper fuse-horizontal-navigation-branch-item .fuse-horizontal-navigation-menu-active .fuse-horizontal-navigation-item-wrapper,\\nfuse-horizontal-navigation .fuse-horizontal-navigation-wrapper fuse-horizontal-navigation-branch-item .fuse-horizontal-navigation-menu-active-forced .fuse-horizontal-navigation-item-wrapper {\\n  background-color: var(--fuse-bg-hover);\\n}\\nfuse-horizontal-navigation .fuse-horizontal-navigation-wrapper fuse-horizontal-navigation-spacer-item {\\n  margin: 12px 0;\\n}\\n\\n/* Menu panel specific */\\n.fuse-horizontal-navigation-menu-panel .fuse-horizontal-navigation-menu-item {\\n  height: auto;\\n  min-height: 0;\\n  line-height: normal;\\n  white-space: normal;\\n  /* Basic, Branch */\\n  /* Divider */\\n}\\n.fuse-horizontal-navigation-menu-panel .fuse-horizontal-navigation-menu-item fuse-horizontal-navigation-basic-item,\\n.fuse-horizontal-navigation-menu-panel .fuse-horizontal-navigation-menu-item fuse-horizontal-navigation-branch-item,\\n.fuse-horizontal-navigation-menu-panel .fuse-horizontal-navigation-menu-item fuse-horizontal-navigation-divider-item {\\n  display: flex;\\n  flex: 1 1 auto;\\n}\\n.fuse-horizontal-navigation-menu-panel .fuse-horizontal-navigation-menu-item fuse-horizontal-navigation-divider-item {\\n  margin: 8px -16px;\\n}\\n.fuse-horizontal-navigation-menu-panel .fuse-horizontal-navigation-menu-item fuse-horizontal-navigation-divider-item .fuse-horizontal-navigation-item-wrapper {\\n  height: 1px;\\n  box-shadow: 0 1px 0 0;\\n}\\n\\n/* Navigation menu item common */\\n.fuse-horizontal-navigation-menu-item {\\n  /* Basic - When item active (current link) */\\n}\\n.fuse-horizontal-navigation-menu-item fuse-horizontal-navigation-basic-item .fuse-horizontal-navigation-item-active .fuse-horizontal-navigation-item-title,\\n.fuse-horizontal-navigation-menu-item fuse-horizontal-navigation-basic-item .fuse-horizontal-navigation-item-active-forced .fuse-horizontal-navigation-item-title {\\n  --tw-text-opacity: 1 !important;\\n  color: rgba(var(--fuse-primary-rgb), var(--tw-text-opacity)) !important;\\n}\\n.fuse-horizontal-navigation-menu-item fuse-horizontal-navigation-basic-item .fuse-horizontal-navigation-item-active .fuse-horizontal-navigation-item-subtitle,\\n.fuse-horizontal-navigation-menu-item fuse-horizontal-navigation-basic-item .fuse-horizontal-navigation-item-active-forced .fuse-horizontal-navigation-item-subtitle {\\n  --tw-text-opacity: 1 !important;\\n  color: rgba(var(--fuse-primary-400-rgb), var(--tw-text-opacity)) !important;\\n}\\n.dark .fuse-horizontal-navigation-menu-item fuse-horizontal-navigation-basic-item .fuse-horizontal-navigation-item-active .fuse-horizontal-navigation-item-subtitle,\\n.dark .fuse-horizontal-navigation-menu-item fuse-horizontal-navigation-basic-item .fuse-horizontal-navigation-item-active-forced .fuse-horizontal-navigation-item-subtitle {\\n  --tw-text-opacity: 1 !important;\\n  color: rgba(var(--fuse-primary-600-rgb), var(--tw-text-opacity)) !important;\\n}\\n.fuse-horizontal-navigation-menu-item fuse-horizontal-navigation-basic-item .fuse-horizontal-navigation-item-active .fuse-horizontal-navigation-item-icon,\\n.fuse-horizontal-navigation-menu-item fuse-horizontal-navigation-basic-item .fuse-horizontal-navigation-item-active-forced .fuse-horizontal-navigation-item-icon {\\n  --tw-text-opacity: 1 !important;\\n  color: rgba(var(--fuse-primary-rgb), var(--tw-text-opacity)) !important;\\n}\\n.fuse-horizontal-navigation-menu-item .fuse-horizontal-navigation-item-wrapper {\\n  width: 100%;\\n}\\n.fuse-horizontal-navigation-menu-item .fuse-horizontal-navigation-item-wrapper.fuse-horizontal-navigation-item-has-subtitle .fuse-horizontal-navigation-item {\\n  min-height: 56px;\\n}\\n.fuse-horizontal-navigation-menu-item .fuse-horizontal-navigation-item-wrapper .fuse-horizontal-navigation-item {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-start;\\n  min-height: 48px;\\n  width: 100%;\\n  font-size: 13px;\\n  font-weight: 500;\\n  text-decoration: none;\\n}\\n.fuse-horizontal-navigation-menu-item .fuse-horizontal-navigation-item-wrapper .fuse-horizontal-navigation-item .fuse-horizontal-navigation-item-title-wrapper .fuse-horizontal-navigation-item-subtitle {\\n  font-size: 12px;\\n}\\n.fuse-horizontal-navigation-menu-item .fuse-horizontal-navigation-item-wrapper .fuse-horizontal-navigation-item .fuse-horizontal-navigation-item-badge {\\n  margin-left: auto;\\n}\\n.fuse-horizontal-navigation-menu-item .fuse-horizontal-navigation-item-wrapper .fuse-horizontal-navigation-item .fuse-horizontal-navigation-item-badge .fuse-horizontal-navigation-item-badge-content {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 10px;\\n  font-weight: 600;\\n  white-space: nowrap;\\n  height: 20px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n    encapsulation: 2,\n    data: {\n      animation: fuseAnimations\n    },\n    changeDetection: 0\n  });\n}", "map": {"version": 3, "names": ["ChangeDetectorRef", "inject", "fuseAnimations", "FuseNavigationService", "FuseUtilsService", "ReplaySubject", "Subject", "FuseHorizontalNavigationBasicItemComponent", "FuseHorizontalNavigationBranchItemComponent", "FuseHorizontalNavigationSpacerItemComponent", "i0", "ɵɵelement", "ɵɵproperty", "item_r1", "ctx_r1", "name", "ɵɵconditionalCreate", "FuseHorizontalNavigationComponent_For_2_Conditional_0_Conditional_0_Template", "FuseHorizontalNavigationComponent_For_2_Conditional_0_Conditional_1_Template", "FuseHorizontalNavigationComponent_For_2_Conditional_0_Conditional_2_Template", "ɵɵconditional", "type", "ɵɵadvance", "FuseHorizontalNavigationComponent_For_2_Conditional_0_Template", "hidden", "FuseHorizontalNavigationComponent", "constructor", "_changeDetectorRef", "_fuseNavigationService", "_fuseUtilsService", "randomId", "onRefreshed", "_unsubscribeAll", "ngOnChanges", "changes", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "registerComponent", "ngOnDestroy", "deregisterComponent", "next", "complete", "refresh", "trackByFn", "index", "item", "id", "_", "_2", "selectors", "inputs", "navigation", "exportAs", "features", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "FuseHorizontalNavigationComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵrepeaterCreate", "FuseHorizontalNavigationComponent_For_2_Template", "ɵɵelementEnd", "ɵɵrepeater", "styles", "encapsulation", "data", "animation", "changeDetection"], "sources": ["D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\@fuse\\components\\navigation\\horizontal\\horizontal.component.ts", "D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\@fuse\\components\\navigation\\horizontal\\horizontal.component.html"], "sourcesContent": ["import {\n    ChangeDetectionStrategy,\n    ChangeDetectorRef,\n    Component,\n    Input,\n    OnChanges,\n    OnDestroy,\n    OnInit,\n    SimpleChanges,\n    ViewEncapsulation,\n    inject,\n} from '@angular/core';\nimport { fuseAnimations } from '@fuse/animations';\nimport { FuseNavigationService } from '@fuse/components/navigation/navigation.service';\nimport { FuseNavigationItem } from '@fuse/components/navigation/navigation.types';\nimport { FuseUtilsService } from '@fuse/services/utils/utils.service';\nimport { ReplaySubject, Subject } from 'rxjs';\nimport { FuseHorizontalNavigationBasicItemComponent } from './components/basic/basic.component';\nimport { FuseHorizontalNavigationBranchItemComponent } from './components/branch/branch.component';\nimport { FuseHorizontalNavigationSpacerItemComponent } from './components/spacer/spacer.component';\n\n@Component({\n    selector: 'fuse-horizontal-navigation',\n    templateUrl: './horizontal.component.html',\n    styleUrls: ['./horizontal.component.scss'],\n    animations: fuseAnimations,\n    encapsulation: ViewEncapsulation.None,\n    changeDetection: ChangeDetectionStrategy.OnPush,\n    exportAs: 'fuseHorizontalNavigation',\n    imports: [\n        FuseHorizontalNavigationBasicItemComponent,\n        FuseHorizontalNavigationBranchItemComponent,\n        FuseHorizontalNavigationSpacerItemComponent,\n    ],\n})\nexport class FuseHorizontalNavigationComponent\n    implements OnChanges, OnInit, OnDestroy\n{\n    private _changeDetectorRef = inject(ChangeDetectorRef);\n    private _fuseNavigationService = inject(FuseNavigationService);\n    private _fuseUtilsService = inject(FuseUtilsService);\n\n    @Input() name: string = this._fuseUtilsService.randomId();\n    @Input() navigation: FuseNavigationItem[];\n\n    onRefreshed: ReplaySubject<boolean> = new ReplaySubject<boolean>(1);\n    private _unsubscribeAll: Subject<any> = new Subject<any>();\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Lifecycle hooks\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * On changes\n     *\n     * @param changes\n     */\n    ngOnChanges(changes: SimpleChanges): void {\n        // Navigation\n        if ('navigation' in changes) {\n            // Mark for check\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n\n    /**\n     * On init\n     */\n    ngOnInit(): void {\n        // Make sure the name input is not an empty string\n        if (this.name === '') {\n            this.name = this._fuseUtilsService.randomId();\n        }\n\n        // Register the navigation component\n        this._fuseNavigationService.registerComponent(this.name, this);\n    }\n\n    /**\n     * On destroy\n     */\n    ngOnDestroy(): void {\n        // Deregister the navigation component from the registry\n        this._fuseNavigationService.deregisterComponent(this.name);\n\n        // Unsubscribe from all subscriptions\n        this._unsubscribeAll.next(null);\n        this._unsubscribeAll.complete();\n    }\n\n    // -----------------------------------------------------------------------------------------------------\n    // @ Public methods\n    // -----------------------------------------------------------------------------------------------------\n\n    /**\n     * Refresh the component to apply the changes\n     */\n    refresh(): void {\n        // Mark for check\n        this._changeDetectorRef.markForCheck();\n\n        // Execute the observable\n        this.onRefreshed.next(true);\n    }\n\n    /**\n     * Track by function for ngFor loops\n     *\n     * @param index\n     * @param item\n     */\n    trackByFn(index: number, item: any): any {\n        return item.id || index;\n    }\n}\n", "<div class=\"fuse-horizontal-navigation-wrapper\">\n    @for (item of navigation; track trackByFn($index, item)) {\n        <!-- Skip the hidden items -->\n        @if ((item.hidden && !item.hidden(item)) || !item.hidden) {\n            <!-- Basic -->\n            @if (item.type === 'basic') {\n                <fuse-horizontal-navigation-basic-item\n                    class=\"fuse-horizontal-navigation-menu-item\"\n                    [item]=\"item\"\n                    [name]=\"name\"\n                ></fuse-horizontal-navigation-basic-item>\n            }\n\n            <!-- Branch: aside, collapsable, group -->\n            @if (\n                item.type === 'aside' ||\n                item.type === 'collapsable' ||\n                item.type === 'group'\n            ) {\n                <fuse-horizontal-navigation-branch-item\n                    class=\"fuse-horizontal-navigation-menu-item\"\n                    [item]=\"item\"\n                    [name]=\"name\"\n                ></fuse-horizontal-navigation-branch-item>\n            }\n\n            <!-- Spacer -->\n            @if (item.type === 'spacer') {\n                <fuse-horizontal-navigation-spacer-item\n                    class=\"fuse-horizontal-navigation-menu-item\"\n                    [item]=\"item\"\n                    [name]=\"name\"\n                ></fuse-horizontal-navigation-spacer-item>\n            }\n        }\n    }\n</div>\n"], "mappings": "AAAA,SAEIA,iBAAiB,EAQjBC,MAAM,QACH,eAAe;AACtB,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,qBAAqB,QAAQ,gDAAgD;AAEtF,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,aAAa,EAAEC,OAAO,QAAQ,MAAM;AAC7C,SAASC,0CAA0C,QAAQ,oCAAoC;AAC/F,SAASC,2CAA2C,QAAQ,sCAAsC;AAClG,SAASC,2CAA2C,QAAQ,sCAAsC;;;;ICblFC,EAAA,CAAAC,SAAA,+CAIyC;;;;;IADrCD,EADA,CAAAE,UAAA,SAAAC,OAAA,CAAa,SAAAC,MAAA,CAAAC,IAAA,CACA;;;;;IAUjBL,EAAA,CAAAC,SAAA,gDAI0C;;;;;IADtCD,EADA,CAAAE,UAAA,SAAAC,OAAA,CAAa,SAAAC,MAAA,CAAAC,IAAA,CACA;;;;;IAMjBL,EAAA,CAAAC,SAAA,gDAI0C;;;;;IADtCD,EADA,CAAAE,UAAA,SAAAC,OAAA,CAAa,SAAAC,MAAA,CAAAC,IAAA,CACA;;;;;IA1BrBL,EAAA,CAAAM,mBAAA,IAAAC,4EAAA,mDAA6B;IAS7BP,EAAA,CAAAM,mBAAA,IAAAE,4EAAA,oDAIG;IASHR,EAAA,CAAAM,mBAAA,IAAAG,4EAAA,oDAA8B;;;;IAtB9BT,EAAA,CAAAU,aAAA,CAAAP,OAAA,CAAAQ,IAAA,sBAMC;IAGDX,EAAA,CAAAY,SAAA,EAUC;IAVDZ,EAAA,CAAAU,aAAA,CAAAP,OAAA,CAAAQ,IAAA,gBAAAR,OAAA,CAAAQ,IAAA,sBAAAR,OAAA,CAAAQ,IAAA,sBAUC;IAGDX,EAAA,CAAAY,SAAA,EAMC;IANDZ,EAAA,CAAAU,aAAA,CAAAP,OAAA,CAAAQ,IAAA,uBAMC;;;;;IA9BLX,EAAA,CAAAM,mBAAA,IAAAO,8DAAA,OAA2D;;;;IAA3Db,EAAA,CAAAU,aAAA,CAAAP,OAAA,CAAAW,MAAA,KAAAX,OAAA,CAAAW,MAAA,CAAAX,OAAA,MAAAA,OAAA,CAAAW,MAAA,UA+BC;;;ADCT,OAAM,MAAOC,iCAAiC;EAd9CC,YAAA;IAiBY,KAAAC,kBAAkB,GAAG1B,MAAM,CAACD,iBAAiB,CAAC;IAC9C,KAAA4B,sBAAsB,GAAG3B,MAAM,CAACE,qBAAqB,CAAC;IACtD,KAAA0B,iBAAiB,GAAG5B,MAAM,CAACG,gBAAgB,CAAC;IAE3C,KAAAW,IAAI,GAAW,IAAI,CAACc,iBAAiB,CAACC,QAAQ,EAAE;IAGzD,KAAAC,WAAW,GAA2B,IAAI1B,aAAa,CAAU,CAAC,CAAC;IAC3D,KAAA2B,eAAe,GAAiB,IAAI1B,OAAO,EAAO;;EAE1D;EACA;EACA;EAEA;;;;;EAKA2B,WAAWA,CAACC,OAAsB;IAC9B;IACA,IAAI,YAAY,IAAIA,OAAO,EAAE;MACzB;MACA,IAAI,CAACP,kBAAkB,CAACQ,YAAY,EAAE;IAC1C;EACJ;EAEA;;;EAGAC,QAAQA,CAAA;IACJ;IACA,IAAI,IAAI,CAACrB,IAAI,KAAK,EAAE,EAAE;MAClB,IAAI,CAACA,IAAI,GAAG,IAAI,CAACc,iBAAiB,CAACC,QAAQ,EAAE;IACjD;IAEA;IACA,IAAI,CAACF,sBAAsB,CAACS,iBAAiB,CAAC,IAAI,CAACtB,IAAI,EAAE,IAAI,CAAC;EAClE;EAEA;;;EAGAuB,WAAWA,CAAA;IACP;IACA,IAAI,CAACV,sBAAsB,CAACW,mBAAmB,CAAC,IAAI,CAACxB,IAAI,CAAC;IAE1D;IACA,IAAI,CAACiB,eAAe,CAACQ,IAAI,CAAC,IAAI,CAAC;IAC/B,IAAI,CAACR,eAAe,CAACS,QAAQ,EAAE;EACnC;EAEA;EACA;EACA;EAEA;;;EAGAC,OAAOA,CAAA;IACH;IACA,IAAI,CAACf,kBAAkB,CAACQ,YAAY,EAAE;IAEtC;IACA,IAAI,CAACJ,WAAW,CAACS,IAAI,CAAC,IAAI,CAAC;EAC/B;EAEA;;;;;;EAMAG,SAASA,CAACC,KAAa,EAAEC,IAAS;IAC9B,OAAOA,IAAI,CAACC,EAAE,IAAIF,KAAK;EAC3B;EAAC,QAAAG,CAAA,G;qCA9EQtB,iCAAiC;EAAA;EAAA,QAAAuB,EAAA,G;UAAjCvB,iCAAiC;IAAAwB,SAAA;IAAAC,MAAA;MAAAnC,IAAA;MAAAoC,UAAA;IAAA;IAAAC,QAAA;IAAAC,QAAA,GAAA3C,EAAA,CAAA4C,oBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,2CAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCnC9ClD,EAAA,CAAAoD,cAAA,aAAgD;QAC5CpD,EAAA,CAAAqD,gBAAA,IAAAC,gDAAA,oBAAAH,GAAA,CAAAlB,SAAA,OAkCC;QACLjC,EAAA,CAAAuD,YAAA,EAAM;;;QAnCFvD,EAAA,CAAAY,SAAA,EAkCC;QAlCDZ,EAAA,CAAAwD,UAAA,CAAAL,GAAA,CAAAV,UAAA,CAkCC;;;mBDLG5C,0CAA0C,EAC1CC,2CAA2C,EAC3CC,2CAA2C;IAAA0D,MAAA;IAAAC,aAAA;IAAAC,IAAA;MAAAC,SAAA,EAPnCpE;IAAc;IAAAqE,eAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}