{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, Injectable, Injector, Inject, DestroyRef, Optional, Component, Input, TemplateRef, ChangeDetectorRef, ElementRef, ViewContainerRef, Renderer2, Directive, Pipe, NgModule, makeEnvironmentProviders, APP_INITIALIZER, assertInInjectionContext, runInInjectionContext, isSignal, computed } from '@angular/core';\nimport { of, take, from, map, Subject, BehaviorSubject, forkJoin, retry, tap, catchError, shareReplay, switchMap, combineLatest, EMPTY } from 'rxjs';\nimport { takeUntilDestroyed, toObservable, toSignal } from '@angular/core/rxjs-interop';\nclass DefaultLoader {\n  translations;\n  constructor(translations) {\n    this.translations = translations;\n  }\n  getTranslation(lang) {\n    return of(this.translations.get(lang) || {});\n  }\n}\nconst TRANSLOCO_LOADER = new InjectionToken(ngDevMode ? 'TRANSLOCO_LOADER' : '');\nfunction getValue(obj, path) {\n  if (!obj) {\n    return obj;\n  }\n  /* For cases where the key is like: 'general.something.thing' */\n  if (Object.prototype.hasOwnProperty.call(obj, path)) {\n    return obj[path];\n  }\n  return path.split('.').reduce((p, c) => p?.[c], obj);\n}\nfunction setValue(obj, prop, val) {\n  obj = {\n    ...obj\n  };\n  const split = prop.split('.');\n  const lastIndex = split.length - 1;\n  split.reduce((acc, part, index) => {\n    if (index === lastIndex) {\n      acc[part] = val;\n    } else {\n      acc[part] = Array.isArray(acc[part]) ? acc[part].slice() : {\n        ...acc[part]\n      };\n    }\n    return acc && acc[part];\n  }, obj);\n  return obj;\n}\nfunction size(collection) {\n  if (!collection) {\n    return 0;\n  }\n  if (Array.isArray(collection)) {\n    return collection.length;\n  }\n  if (isObject(collection)) {\n    return Object.keys(collection).length;\n  }\n  return collection ? collection.length : 0;\n}\nfunction isEmpty(collection) {\n  return size(collection) === 0;\n}\nfunction isFunction(val) {\n  return typeof val === 'function';\n}\nfunction isString(val) {\n  return typeof val === 'string';\n}\nfunction isNumber(val) {\n  return typeof val === 'number';\n}\nfunction isObject(item) {\n  return !!item && typeof item === 'object' && !Array.isArray(item);\n}\nfunction coerceArray(value) {\n  return Array.isArray(value) ? value : [value];\n}\n/*\n * @example\n *\n * given: path-to-happiness => pathToHappiness\n * given: path_to_happiness => pathToHappiness\n * given: path-to_happiness => pathToHappiness\n *\n */\nfunction toCamelCase(str) {\n  return str.replace(/(?:^\\w|[A-Z]|\\b\\w)/g, (word, index) => index == 0 ? word.toLowerCase() : word.toUpperCase()).replace(/\\s+|_|-|\\//g, '');\n}\nfunction isBrowser() {\n  return typeof window !== 'undefined';\n}\nfunction isNil(value) {\n  return value === null || value === undefined;\n}\nfunction isDefined(value) {\n  return isNil(value) === false;\n}\nfunction toNumber(value) {\n  if (isNumber(value)) return value;\n  if (isString(value) && !isNaN(Number(value) - parseFloat(value))) {\n    return Number(value);\n  }\n  return null;\n}\nfunction isScopeObject(item) {\n  return item && typeof item.scope === 'string';\n}\nfunction hasInlineLoader(item) {\n  return item && isObject(item.loader);\n}\nfunction flatten(obj) {\n  const result = {};\n  function recurse(curr, prop) {\n    if (curr === null) {\n      result[prop] = null;\n    } else if (isObject(curr)) {\n      for (const [key, value] of Object.entries(curr)) {\n        recurse(value, prop ? `${prop}.${key}` : key);\n      }\n    } else {\n      result[prop] = curr;\n    }\n  }\n  recurse(obj, '');\n  return result;\n}\nfunction unflatten(obj) {\n  const result = {};\n  for (const [key, value] of Object.entries(obj)) {\n    const keys = key.split('.');\n    let current = result;\n    keys.forEach((key, i) => {\n      if (i === keys.length - 1) {\n        current[key] = value;\n      } else {\n        current[key] ??= {};\n        current = current[key];\n      }\n    });\n  }\n  return result;\n}\nconst TRANSLOCO_CONFIG = new InjectionToken(ngDevMode ? 'TRANSLOCO_CONFIG' : '', {\n  providedIn: 'root',\n  factory: () => defaultConfig\n});\nconst defaultConfig = {\n  defaultLang: 'en',\n  reRenderOnLangChange: false,\n  prodMode: false,\n  failedRetries: 2,\n  fallbackLang: [],\n  availableLangs: [],\n  missingHandler: {\n    logMissingKey: true,\n    useFallbackTranslation: false,\n    allowEmpty: false\n  },\n  flatten: {\n    aot: false\n  },\n  interpolation: ['{{', '}}'],\n  scopes: {\n    keepCasing: false\n  }\n};\nfunction translocoConfig(config = {}) {\n  return {\n    ...defaultConfig,\n    ...config,\n    missingHandler: {\n      ...defaultConfig.missingHandler,\n      ...config.missingHandler\n    },\n    flatten: {\n      ...defaultConfig.flatten,\n      ...config.flatten\n    },\n    scopes: {\n      ...defaultConfig.scopes,\n      ...config.scopes\n    }\n  };\n}\nconst TRANSLOCO_TRANSPILER = new InjectionToken(ngDevMode ? 'TRANSLOCO_TRANSPILER' : '');\nclass DefaultTranspiler {\n  config = inject(TRANSLOCO_CONFIG, {\n    optional: true\n  }) ?? defaultConfig;\n  get interpolationMatcher() {\n    return resolveMatcher(this.config);\n  }\n  transpile({\n    value,\n    params = {},\n    translation,\n    key\n  }) {\n    if (isString(value)) {\n      let paramMatch;\n      let parsedValue = value;\n      while ((paramMatch = this.interpolationMatcher.exec(parsedValue)) !== null) {\n        const [match, paramValue] = paramMatch;\n        parsedValue = parsedValue.replace(match, () => {\n          const match = paramValue.trim();\n          const param = getValue(params, match);\n          if (isDefined(param)) {\n            return param;\n          }\n          return isDefined(translation[match]) ? this.transpile({\n            params,\n            translation,\n            key,\n            value: translation[match]\n          }) : '';\n        });\n      }\n      return parsedValue;\n    } else if (params) {\n      if (isObject(value)) {\n        value = this.handleObject({\n          value,\n          params,\n          translation,\n          key\n        });\n      } else if (Array.isArray(value)) {\n        value = this.handleArray({\n          value,\n          params,\n          translation,\n          key\n        });\n      }\n    }\n    return value;\n  }\n  /**\n   *\n   * @example\n   *\n   * const en = {\n   *  a: {\n   *    b: {\n   *      c: \"Hello {{ value }}\"\n   *    }\n   *  }\n   * }\n   *\n   * const params =  {\n   *  \"b.c\": { value: \"Transloco \"}\n   * }\n   *\n   * service.selectTranslate('a', params);\n   *\n   * // the first param will be the result of `en.a`.\n   * // the second param will be `params`.\n   * parser.transpile(value, params, {});\n   *\n   *\n   */\n  handleObject({\n    value,\n    params = {},\n    translation,\n    key\n  }) {\n    let result = value;\n    Object.keys(params).forEach(p => {\n      // transpile the value => \"Hello Transloco\"\n      const transpiled = this.transpile({\n        // get the value of \"b.c\" inside \"a\" => \"Hello {{ value }}\"\n        value: getValue(result, p),\n        // get the params of \"b.c\" => { value: \"Transloco\" }\n        params: getValue(params, p),\n        translation,\n        key\n      });\n      // set \"b.c\" to `transpiled`\n      result = setValue(result, p, transpiled);\n    });\n    return result;\n  }\n  handleArray({\n    value,\n    ...rest\n  }) {\n    return value.map(v => this.transpile({\n      value: v,\n      ...rest\n    }));\n  }\n  static ɵfac = function DefaultTranspiler_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DefaultTranspiler)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DefaultTranspiler,\n    factory: DefaultTranspiler.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DefaultTranspiler, [{\n    type: Injectable\n  }], null, null);\n})();\nfunction resolveMatcher(config) {\n  const [start, end] = config.interpolation;\n  return new RegExp(`${start}([^${start}${end}]*?)${end}`, 'g');\n}\nfunction getFunctionArgs(argsString) {\n  const splitted = argsString ? argsString.split(',') : [];\n  const args = [];\n  for (let i = 0; i < splitted.length; i++) {\n    let value = splitted[i].trim();\n    while (value[value.length - 1] === '\\\\') {\n      i++;\n      value = value.replace('\\\\', ',') + splitted[i];\n    }\n    args.push(value);\n  }\n  return args;\n}\nclass FunctionalTranspiler extends DefaultTranspiler {\n  injector = inject(Injector);\n  transpile({\n    value,\n    ...rest\n  }) {\n    let transpiled = value;\n    if (isString(value)) {\n      transpiled = value.replace(/\\[\\[\\s*(\\w+)\\((.*?)\\)\\s*]]/g, (match, functionName, args) => {\n        try {\n          const func = this.injector.get(functionName);\n          return func.transpile(...getFunctionArgs(args));\n        } catch (e) {\n          let message = `There is an error in: '${value}'. \n                          Check that the you used the right syntax in your translation and that the implementation of ${functionName} is correct.`;\n          if (e.message.includes('NullInjectorError')) {\n            message = `You are using the '${functionName}' function in your translation but no provider was found!`;\n          }\n          throw new Error(message);\n        }\n      });\n    }\n    return super.transpile({\n      value: transpiled,\n      ...rest\n    });\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵFunctionalTranspiler_BaseFactory;\n    return function FunctionalTranspiler_Factory(__ngFactoryType__) {\n      return (ɵFunctionalTranspiler_BaseFactory || (ɵFunctionalTranspiler_BaseFactory = i0.ɵɵgetInheritedFactory(FunctionalTranspiler)))(__ngFactoryType__ || FunctionalTranspiler);\n    };\n  })();\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: FunctionalTranspiler,\n    factory: FunctionalTranspiler.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FunctionalTranspiler, [{\n    type: Injectable\n  }], null, null);\n})();\nconst TRANSLOCO_MISSING_HANDLER = new InjectionToken(ngDevMode ? 'TRANSLOCO_MISSING_HANDLER' : '');\nclass DefaultMissingHandler {\n  handle(key, config) {\n    if (config.missingHandler.logMissingKey && !config.prodMode) {\n      const msg = `Missing translation for '${key}'`;\n      console.warn(`%c ${msg}`, 'font-size: 12px; color: red');\n    }\n    return key;\n  }\n  static ɵfac = function DefaultMissingHandler_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DefaultMissingHandler)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DefaultMissingHandler,\n    factory: DefaultMissingHandler.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DefaultMissingHandler, [{\n    type: Injectable\n  }], null, null);\n})();\nconst TRANSLOCO_INTERCEPTOR = new InjectionToken(ngDevMode ? 'TRANSLOCO_INTERCEPTOR' : '');\nclass DefaultInterceptor {\n  preSaveTranslation(translation) {\n    return translation;\n  }\n  preSaveTranslationKey(_, value) {\n    return value;\n  }\n  static ɵfac = function DefaultInterceptor_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DefaultInterceptor)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DefaultInterceptor,\n    factory: DefaultInterceptor.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DefaultInterceptor, [{\n    type: Injectable\n  }], null, null);\n})();\nconst TRANSLOCO_FALLBACK_STRATEGY = new InjectionToken(ngDevMode ? 'TRANSLOCO_FALLBACK_STRATEGY' : '');\nclass DefaultFallbackStrategy {\n  userConfig;\n  constructor(userConfig) {\n    this.userConfig = userConfig;\n  }\n  getNextLangs() {\n    const fallbackLang = this.userConfig.fallbackLang;\n    if (!fallbackLang) {\n      throw new Error('When using the default fallback, a fallback language must be provided in the config!');\n    }\n    return Array.isArray(fallbackLang) ? fallbackLang : [fallbackLang];\n  }\n  static ɵfac = function DefaultFallbackStrategy_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || DefaultFallbackStrategy)(i0.ɵɵinject(TRANSLOCO_CONFIG));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: DefaultFallbackStrategy,\n    factory: DefaultFallbackStrategy.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DefaultFallbackStrategy, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [TRANSLOCO_CONFIG]\n    }]\n  }], null);\n})();\n\n/*\n * @example\n *\n * given: lazy-page/en => lazy-page\n *\n */\nfunction getScopeFromLang(lang) {\n  if (!lang) {\n    return '';\n  }\n  const split = lang.split('/');\n  split.pop();\n  return split.join('/');\n}\n/*\n * @example\n *\n * given: lazy-page/en => en\n *\n */\nfunction getLangFromScope(lang) {\n  if (!lang) {\n    return '';\n  }\n  return lang.split('/').pop();\n}\n/**\n * @example\n *\n * getPipeValue('todos|scoped', 'scoped') [true, 'todos']\n * getPipeValue('en|static', 'static') [true, 'en']\n * getPipeValue('en', 'static') [false, 'en']\n */\nfunction getPipeValue(str, value, char = '|') {\n  if (isString(str)) {\n    const splitted = str.split(char);\n    const lastItem = splitted.pop();\n    return lastItem === value ? [true, splitted.toString()] : [false, lastItem];\n  }\n  return [false, ''];\n}\nfunction shouldListenToLangChanges(service, lang) {\n  const [hasStatic] = getPipeValue(lang, 'static');\n  if (!hasStatic) {\n    // If we didn't get 'lang|static' check if it's set in the global level\n    return !!service.config.reRenderOnLangChange;\n  }\n  // We have 'lang|static' so don't listen to lang changes\n  return false;\n}\nfunction listenOrNotOperator(listenToLangChange) {\n  return listenToLangChange ? source => source : take(1);\n}\nfunction prependScope(inlineLoader, scope) {\n  return Object.keys(inlineLoader).reduce((acc, lang) => {\n    acc[`${scope}/${lang}`] = inlineLoader[lang];\n    return acc;\n  }, {});\n}\nfunction resolveInlineLoader(providerScope, scope) {\n  return hasInlineLoader(providerScope) ? prependScope(providerScope.loader, scope) : undefined;\n}\nfunction getEventPayload(lang) {\n  return {\n    scope: getScopeFromLang(lang) || null,\n    langName: getLangFromScope(lang)\n  };\n}\nfunction resolveLoader(options) {\n  const {\n    path,\n    inlineLoader,\n    mainLoader,\n    data\n  } = options;\n  if (inlineLoader) {\n    const pathLoader = inlineLoader[path];\n    if (isFunction(pathLoader) === false) {\n      throw `You're using an inline loader but didn't provide a loader for ${path}`;\n    }\n    return inlineLoader[path]().then(res => res.default ? res.default : res);\n  }\n  return mainLoader.getTranslation(path, data);\n}\nfunction getFallbacksLoaders({\n  mainLoader,\n  path,\n  data,\n  fallbackPath,\n  inlineLoader\n}) {\n  const paths = fallbackPath ? [path, fallbackPath] : [path];\n  return paths.map(path => {\n    const loader = resolveLoader({\n      path,\n      mainLoader,\n      inlineLoader,\n      data\n    });\n    return from(loader).pipe(map(translation => ({\n      translation,\n      lang: path\n    })));\n  });\n}\nlet service;\nfunction translate(key, params = {}, lang) {\n  return service.translate(key, params, lang);\n}\nfunction translateObject(key, params = {}, lang) {\n  return service.translateObject(key, params, lang);\n}\nclass TranslocoService {\n  loader;\n  parser;\n  missingHandler;\n  interceptor;\n  fallbackStrategy;\n  langChanges$;\n  translations = new Map();\n  cache = new Map();\n  firstFallbackLang;\n  defaultLang = '';\n  availableLangs = [];\n  isResolvedMissingOnce = false;\n  lang;\n  failedLangs = new Set();\n  events = new Subject();\n  events$ = this.events.asObservable();\n  config;\n  destroyRef = inject(DestroyRef);\n  constructor(loader, parser, missingHandler, interceptor, userConfig, fallbackStrategy) {\n    this.loader = loader;\n    this.parser = parser;\n    this.missingHandler = missingHandler;\n    this.interceptor = interceptor;\n    this.fallbackStrategy = fallbackStrategy;\n    if (!this.loader) {\n      this.loader = new DefaultLoader(this.translations);\n    }\n    service = this;\n    this.config = JSON.parse(JSON.stringify(userConfig));\n    this.setAvailableLangs(this.config.availableLangs || []);\n    this.setFallbackLangForMissingTranslation(this.config);\n    this.setDefaultLang(this.config.defaultLang);\n    this.lang = new BehaviorSubject(this.getDefaultLang());\n    // Don't use distinctUntilChanged as we need the ability to update\n    // the value when using setTranslation or setTranslationKeys\n    this.langChanges$ = this.lang.asObservable();\n    /**\n     * When we have a failure, we want to define the next language that succeeded as the active\n     */\n    this.events$.subscribe(e => {\n      if (e.type === 'translationLoadSuccess' && e.wasFailure) {\n        this.setActiveLang(e.payload.langName);\n      }\n    });\n    this.destroyRef.onDestroy(() => {\n      // Complete subjects to release observers if users forget to unsubscribe manually.\n      // This is important in server-side rendering.\n      this.lang.complete();\n      this.events.complete();\n      // As a root provider, this service is destroyed with when the application is destroyed.\n      // Cached values retain `this`, causing circular references that block garbage collection,\n      // leading to memory leaks during server-side rendering.\n      this.cache.clear();\n    });\n  }\n  getDefaultLang() {\n    return this.defaultLang;\n  }\n  setDefaultLang(lang) {\n    this.defaultLang = lang;\n  }\n  getActiveLang() {\n    return this.lang.getValue();\n  }\n  setActiveLang(lang) {\n    this.parser.onLangChanged?.(lang);\n    this.lang.next(lang);\n    this.events.next({\n      type: 'langChanged',\n      payload: getEventPayload(lang)\n    });\n    return this;\n  }\n  setAvailableLangs(langs) {\n    this.availableLangs = langs;\n  }\n  /**\n   * Gets the available languages.\n   *\n   * @returns\n   * An array of the available languages. Can be either a `string[]` or a `{ id: string; label: string }[]`\n   * depending on how the available languages are set in your module.\n   */\n  getAvailableLangs() {\n    return this.availableLangs;\n  }\n  load(path, options = {}) {\n    const cached = this.cache.get(path);\n    if (cached) {\n      return cached;\n    }\n    let loadTranslation;\n    const isScope = this._isLangScoped(path);\n    let scope;\n    if (isScope) {\n      scope = getScopeFromLang(path);\n    }\n    const loadersOptions = {\n      path,\n      mainLoader: this.loader,\n      inlineLoader: options.inlineLoader,\n      data: isScope ? {\n        scope: scope\n      } : undefined\n    };\n    if (this.useFallbackTranslation(path)) {\n      // if the path is scope the fallback should be `scope/fallbackLang`;\n      const fallback = isScope ? `${scope}/${this.firstFallbackLang}` : this.firstFallbackLang;\n      const loaders = getFallbacksLoaders({\n        ...loadersOptions,\n        fallbackPath: fallback\n      });\n      loadTranslation = forkJoin(loaders);\n    } else {\n      const loader = resolveLoader(loadersOptions);\n      loadTranslation = from(loader);\n    }\n    const load$ = loadTranslation.pipe(retry(this.config.failedRetries), tap(translation => {\n      if (Array.isArray(translation)) {\n        translation.forEach(t => {\n          this.handleSuccess(t.lang, t.translation);\n          // Save the fallback in cache so we'll not create a redundant request\n          if (t.lang !== path) {\n            this.cache.set(t.lang, of({}));\n          }\n        });\n        return;\n      }\n      this.handleSuccess(path, translation);\n    }), catchError(error => {\n      if (!this.config.prodMode) {\n        console.error(`Error while trying to load \"${path}\"`, error);\n      }\n      return this.handleFailure(path, options);\n    }), shareReplay(1), takeUntilDestroyed(this.destroyRef));\n    this.cache.set(path, load$);\n    return load$;\n  }\n  /**\n   * Gets the instant translated value of a key\n   *\n   * @example\n   *\n   * translate<string>('hello')\n   * translate('hello', { value: 'value' })\n   * translate<string[]>(['hello', 'key'])\n   * translate('hello', { }, 'en')\n   * translate('scope.someKey', { }, 'en')\n   */\n  translate(key, params = {}, lang = this.getActiveLang()) {\n    if (!key) return key;\n    const {\n      scope,\n      resolveLang\n    } = this.resolveLangAndScope(lang);\n    if (Array.isArray(key)) {\n      return key.map(k => this.translate(scope ? `${scope}.${k}` : k, params, resolveLang));\n    }\n    key = scope ? `${scope}.${key}` : key;\n    const translation = this.getTranslation(resolveLang);\n    const value = translation[key];\n    if (!value) {\n      return this._handleMissingKey(key, value, params);\n    }\n    return this.parser.transpile({\n      value,\n      params,\n      translation,\n      key\n    });\n  }\n  /**\n   * Gets the translated value of a key as observable\n   *\n   * @example\n   *\n   * selectTranslate<string>('hello').subscribe(value => ...)\n   * selectTranslate<string>('hello', {}, 'es').subscribe(value => ...)\n   * selectTranslate<string>('hello', {}, 'todos').subscribe(value => ...)\n   * selectTranslate<string>('hello', {}, { scope: 'todos' }).subscribe(value => ...)\n   *\n   */\n  selectTranslate(key, params, lang, _isObject = false) {\n    let inlineLoader;\n    const load = (lang, options) => this.load(lang, options).pipe(map(() => _isObject ? this.translateObject(key, params, lang) : this.translate(key, params, lang)));\n    if (isNil(lang)) {\n      return this.langChanges$.pipe(switchMap(lang => load(lang)));\n    }\n    lang = Array.isArray(lang) ? lang[0] : lang;\n    if (isScopeObject(lang)) {\n      // it's a scope object.\n      const providerScope = lang;\n      lang = providerScope.scope;\n      inlineLoader = resolveInlineLoader(providerScope, providerScope.scope);\n    }\n    lang = lang;\n    if (this.isLang(lang) || this.isScopeWithLang(lang)) {\n      return load(lang);\n    }\n    // it's a scope\n    const scope = lang;\n    return this.langChanges$.pipe(switchMap(lang => load(`${scope}/${lang}`, {\n      inlineLoader\n    })));\n  }\n  /**\n   * Whether the scope with lang\n   *\n   * @example\n   *\n   * todos/en => true\n   * todos => false\n   */\n  isScopeWithLang(lang) {\n    return this.isLang(getLangFromScope(lang));\n  }\n  translateObject(key, params = {}, lang = this.getActiveLang()) {\n    if (isString(key) || Array.isArray(key)) {\n      const {\n        resolveLang,\n        scope\n      } = this.resolveLangAndScope(lang);\n      if (Array.isArray(key)) {\n        return key.map(k => this.translateObject(scope ? `${scope}.${k}` : k, params, resolveLang));\n      }\n      const translation = this.getTranslation(resolveLang);\n      key = scope ? `${scope}.${key}` : key;\n      const value = unflatten(this.getObjectByKey(translation, key));\n      /* If an empty object was returned we want to try and translate the key as a string and not an object */\n      return isEmpty(value) ? this.translate(key, params, lang) : this.parser.transpile({\n        value,\n        params: params,\n        translation,\n        key\n      });\n    }\n    const translations = [];\n    for (const [_key, _params] of this.getEntries(key)) {\n      translations.push(this.translateObject(_key, _params, lang));\n    }\n    return translations;\n  }\n  selectTranslateObject(key, params, lang) {\n    if (isString(key) || Array.isArray(key)) {\n      return this.selectTranslate(key, params, lang, true);\n    }\n    const [[firstKey, firstParams], ...rest] = this.getEntries(key);\n    /* In order to avoid subscribing multiple times to the load language event by calling selectTranslateObject for each pair,\n     * we listen to when the first key has been translated (the language is loaded) and translate the rest synchronously */\n    return this.selectTranslateObject(firstKey, firstParams, lang).pipe(map(value => {\n      const translations = [value];\n      for (const [_key, _params] of rest) {\n        translations.push(this.translateObject(_key, _params, lang));\n      }\n      return translations;\n    }));\n  }\n  getTranslation(langOrScope) {\n    if (langOrScope) {\n      if (this.isLang(langOrScope)) {\n        return this.translations.get(langOrScope) || {};\n      } else {\n        // This is a scope, build the scope value from the translation object\n        const {\n          scope,\n          resolveLang\n        } = this.resolveLangAndScope(langOrScope);\n        const translation = this.translations.get(resolveLang) || {};\n        return this.getObjectByKey(translation, scope);\n      }\n    }\n    return this.translations;\n  }\n  /**\n   * Gets an object of translations for a given language\n   *\n   * @example\n   *\n   * selectTranslation().subscribe() - will return the current lang translation\n   * selectTranslation('es').subscribe()\n   * selectTranslation('admin-page').subscribe() - will return the current lang scope translation\n   * selectTranslation('admin-page/es').subscribe()\n   */\n  selectTranslation(lang) {\n    let language$ = this.langChanges$;\n    if (lang) {\n      const scopeLangSpecified = getLangFromScope(lang) !== lang;\n      if (this.isLang(lang) || scopeLangSpecified) {\n        language$ = of(lang);\n      } else {\n        language$ = this.langChanges$.pipe(map(currentLang => `${lang}/${currentLang}`));\n      }\n    }\n    return language$.pipe(switchMap(language => this.load(language).pipe(map(() => this.getTranslation(language)))));\n  }\n  /**\n   * Sets or merge a given translation object to current lang\n   *\n   * @example\n   *\n   * setTranslation({ ... })\n   * setTranslation({ ... }, 'en')\n   * setTranslation({ ... }, 'es', { merge: false } )\n   * setTranslation({ ... }, 'todos/en', { merge: false } )\n   */\n  setTranslation(translation, lang = this.getActiveLang(), options = {}) {\n    const defaults = {\n      merge: true,\n      emitChange: true\n    };\n    const mergedOptions = {\n      ...defaults,\n      ...options\n    };\n    const scope = getScopeFromLang(lang);\n    /**\n     * If this isn't a scope we use the whole translation as is\n     * otherwise we need to flat the scope and use it\n     */\n    let flattenScopeOrTranslation = translation;\n    // Merged the scoped language into the active language\n    if (scope) {\n      const key = this.getMappedScope(scope);\n      flattenScopeOrTranslation = flatten({\n        [key]: translation\n      });\n    }\n    const currentLang = scope ? getLangFromScope(lang) : lang;\n    const mergedTranslation = {\n      ...(mergedOptions.merge && this.getTranslation(currentLang)),\n      ...flattenScopeOrTranslation\n    };\n    const flattenTranslation = this.config.flatten.aot ? mergedTranslation : flatten(mergedTranslation);\n    const withHook = this.interceptor.preSaveTranslation(flattenTranslation, currentLang);\n    this.translations.set(currentLang, withHook);\n    mergedOptions.emitChange && this.setActiveLang(this.getActiveLang());\n  }\n  /**\n   * Sets translation key with given value\n   *\n   * @example\n   *\n   * setTranslationKey('key', 'value')\n   * setTranslationKey('key.nested', 'value')\n   * setTranslationKey('key.nested', 'value', 'en')\n   * setTranslationKey('key.nested', 'value', 'en', { emitChange: false } )\n   */\n  setTranslationKey(key, value, options = {}) {\n    const lang = options.lang || this.getActiveLang();\n    const withHook = this.interceptor.preSaveTranslationKey(key, value, lang);\n    const newValue = {\n      [key]: withHook\n    };\n    this.setTranslation(newValue, lang, {\n      ...options,\n      merge: true\n    });\n  }\n  /**\n   * Sets the fallback lang for the currently active language\n   * @param fallbackLang\n   */\n  setFallbackLangForMissingTranslation({\n    fallbackLang\n  }) {\n    const lang = Array.isArray(fallbackLang) ? fallbackLang[0] : fallbackLang;\n    if (fallbackLang && this.useFallbackTranslation(lang)) {\n      this.firstFallbackLang = lang;\n    }\n  }\n  /**\n   * @internal\n   */\n  _handleMissingKey(key, value, params) {\n    if (this.config.missingHandler.allowEmpty && value === '') {\n      return '';\n    }\n    if (!this.isResolvedMissingOnce && this.useFallbackTranslation()) {\n      // We need to set it to true to prevent a loop\n      this.isResolvedMissingOnce = true;\n      const fallbackValue = this.translate(key, params, this.firstFallbackLang);\n      this.isResolvedMissingOnce = false;\n      return fallbackValue;\n    }\n    return this.missingHandler.handle(key, this.getMissingHandlerData(), params);\n  }\n  /**\n   * @internal\n   */\n  _isLangScoped(lang) {\n    return this.getAvailableLangsIds().indexOf(lang) === -1;\n  }\n  /**\n   * Checks if a given string is one of the specified available languages.\n   * @returns\n   * True if the given string is an available language.\n   * False if the given string is not an available language.\n   */\n  isLang(lang) {\n    return this.getAvailableLangsIds().indexOf(lang) !== -1;\n  }\n  /**\n   * @internal\n   *\n   * We always want to make sure the global lang is loaded\n   * before loading the scope since you can access both via the pipe/directive.\n   */\n  _loadDependencies(path, inlineLoader) {\n    const mainLang = getLangFromScope(path);\n    if (this._isLangScoped(path) && !this.isLoadedTranslation(mainLang)) {\n      return combineLatest([this.load(mainLang), this.load(path, {\n        inlineLoader\n      })]);\n    }\n    return this.load(path, {\n      inlineLoader\n    });\n  }\n  /**\n   * @internal\n   */\n  _completeScopeWithLang(langOrScope) {\n    if (this._isLangScoped(langOrScope) && !this.isLang(getLangFromScope(langOrScope))) {\n      return `${langOrScope}/${this.getActiveLang()}`;\n    }\n    return langOrScope;\n  }\n  /**\n   * @internal\n   */\n  _setScopeAlias(scope, alias) {\n    if (!this.config.scopeMapping) {\n      this.config.scopeMapping = {};\n    }\n    this.config.scopeMapping[scope] = alias;\n  }\n  isLoadedTranslation(lang) {\n    return size(this.getTranslation(lang));\n  }\n  getAvailableLangsIds() {\n    const first = this.getAvailableLangs()[0];\n    if (isString(first)) {\n      return this.getAvailableLangs();\n    }\n    return this.getAvailableLangs().map(l => l.id);\n  }\n  getMissingHandlerData() {\n    return {\n      ...this.config,\n      activeLang: this.getActiveLang(),\n      availableLangs: this.availableLangs,\n      defaultLang: this.defaultLang\n    };\n  }\n  /**\n   * Use a fallback translation set for missing keys of the primary language\n   * This is unrelated to the fallback language (which changes the active language)\n   */\n  useFallbackTranslation(lang) {\n    return this.config.missingHandler.useFallbackTranslation && lang !== this.firstFallbackLang;\n  }\n  handleSuccess(lang, translation) {\n    this.setTranslation(translation, lang, {\n      emitChange: false\n    });\n    this.events.next({\n      wasFailure: !!this.failedLangs.size,\n      type: 'translationLoadSuccess',\n      payload: getEventPayload(lang)\n    });\n    this.failedLangs.forEach(l => this.cache.delete(l));\n    this.failedLangs.clear();\n  }\n  handleFailure(lang, loadOptions) {\n    // When starting to load a first choice language, initialize\n    // the failed counter and resolve the fallback langs.\n    if (isNil(loadOptions.failedCounter)) {\n      loadOptions.failedCounter = 0;\n      if (!loadOptions.fallbackLangs) {\n        loadOptions.fallbackLangs = this.fallbackStrategy.getNextLangs(lang);\n      }\n    }\n    const splitted = lang.split('/');\n    const fallbacks = loadOptions.fallbackLangs;\n    const nextLang = fallbacks[loadOptions.failedCounter];\n    this.failedLangs.add(lang);\n    // This handles the case where a loaded fallback language is requested again\n    if (this.cache.has(nextLang)) {\n      this.handleSuccess(nextLang, this.getTranslation(nextLang));\n      return EMPTY;\n    }\n    const isFallbackLang = nextLang === splitted[splitted.length - 1];\n    if (!nextLang || isFallbackLang) {\n      let msg = `Unable to load translation and all the fallback languages`;\n      if (splitted.length > 1) {\n        msg += `, did you misspelled the scope name?`;\n      }\n      throw new Error(msg);\n    }\n    let resolveLang = nextLang;\n    // if it's scoped lang\n    if (splitted.length > 1) {\n      // We need to resolve it to:\n      // todos/langNotExists => todos/nextLang\n      splitted[splitted.length - 1] = nextLang;\n      resolveLang = splitted.join('/');\n    }\n    loadOptions.failedCounter++;\n    this.events.next({\n      type: 'translationLoadFailure',\n      payload: getEventPayload(lang)\n    });\n    return this.load(resolveLang, loadOptions);\n  }\n  getMappedScope(scope) {\n    const {\n      scopeMapping = {},\n      scopes = {\n        keepCasing: false\n      }\n    } = this.config;\n    return scopeMapping[scope] || (scopes.keepCasing ? scope : toCamelCase(scope));\n  }\n  /**\n   * If lang is scope we need to check the following cases:\n   * todos/es => in this case we should take `es` as lang\n   * todos => in this case we should set the active lang as lang\n   */\n  resolveLangAndScope(lang) {\n    let resolveLang = lang;\n    let scope;\n    if (this._isLangScoped(lang)) {\n      // en for example\n      const langFromScope = getLangFromScope(lang);\n      // en is lang\n      const hasLang = this.isLang(langFromScope);\n      // take en\n      resolveLang = hasLang ? langFromScope : this.getActiveLang();\n      // find the scope\n      scope = this.getMappedScope(hasLang ? getScopeFromLang(lang) : lang);\n    }\n    return {\n      scope,\n      resolveLang\n    };\n  }\n  getObjectByKey(translation, key) {\n    const result = {};\n    const prefix = `${key}.`;\n    for (const currentKey in translation) {\n      if (currentKey.startsWith(prefix)) {\n        result[currentKey.replace(prefix, '')] = translation[currentKey];\n      }\n    }\n    return result;\n  }\n  getEntries(key) {\n    return key instanceof Map ? key.entries() : Object.entries(key);\n  }\n  static ɵfac = function TranslocoService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || TranslocoService)(i0.ɵɵinject(TRANSLOCO_LOADER, 8), i0.ɵɵinject(TRANSLOCO_TRANSPILER), i0.ɵɵinject(TRANSLOCO_MISSING_HANDLER), i0.ɵɵinject(TRANSLOCO_INTERCEPTOR), i0.ɵɵinject(TRANSLOCO_CONFIG), i0.ɵɵinject(TRANSLOCO_FALLBACK_STRATEGY));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: TranslocoService,\n    factory: TranslocoService.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslocoService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [TRANSLOCO_LOADER]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [TRANSLOCO_TRANSPILER]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [TRANSLOCO_MISSING_HANDLER]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [TRANSLOCO_INTERCEPTOR]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [TRANSLOCO_CONFIG]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [TRANSLOCO_FALLBACK_STRATEGY]\n    }]\n  }], null);\n})();\nclass TranslocoLoaderComponent {\n  html;\n  static ɵfac = function TranslocoLoaderComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || TranslocoLoaderComponent)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: TranslocoLoaderComponent,\n    selectors: [[\"ng-component\"]],\n    inputs: {\n      html: \"html\"\n    },\n    decls: 1,\n    vars: 1,\n    consts: [[1, \"transloco-loader-template\", 3, \"innerHTML\"]],\n    template: function TranslocoLoaderComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"div\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"innerHTML\", ctx.html, i0.ɵɵsanitizeHtml);\n      }\n    },\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslocoLoaderComponent, [{\n    type: Component,\n    args: [{\n      template: `\n    <div class=\"transloco-loader-template\" [innerHTML]=\"html\"></div>\n  `,\n      standalone: true\n    }]\n  }], null, {\n    html: [{\n      type: Input\n    }]\n  });\n})();\nclass TemplateHandler {\n  view;\n  vcr;\n  constructor(view, vcr) {\n    this.view = view;\n    this.vcr = vcr;\n  }\n  attachView() {\n    if (this.view instanceof TemplateRef) {\n      this.vcr.createEmbeddedView(this.view);\n    } else if (isString(this.view)) {\n      const componentRef = this.vcr.createComponent(TranslocoLoaderComponent);\n      componentRef.instance.html = this.view;\n      componentRef.hostView.detectChanges();\n    } else {\n      this.vcr.createComponent(this.view);\n    }\n  }\n  detachView() {\n    this.vcr.clear();\n  }\n}\nconst TRANSLOCO_LANG = new InjectionToken(ngDevMode ? 'TRANSLOCO_LANG' : '');\nconst TRANSLOCO_LOADING_TEMPLATE = new InjectionToken(ngDevMode ? 'TRANSLOCO_LOADING_TEMPLATE' : '');\nconst TRANSLOCO_SCOPE = new InjectionToken(ngDevMode ? 'TRANSLOCO_SCOPE' : '');\nclass LangResolver {\n  initialized = false;\n  // inline => provider => active\n  resolve({\n    inline,\n    provider,\n    active\n  }) {\n    let lang = active;\n    /**\n     * When the user changes the lang we need to update\n     * the view. Otherwise, the lang will remain the inline/provided lang\n     */\n    if (this.initialized) {\n      lang = active;\n      return lang;\n    }\n    if (provider) {\n      const [, extracted] = getPipeValue(provider, 'static');\n      lang = extracted;\n    }\n    if (inline) {\n      const [, extracted] = getPipeValue(inline, 'static');\n      lang = extracted;\n    }\n    this.initialized = true;\n    return lang;\n  }\n  /**\n   *\n   * Resolve the lang\n   *\n   * @example\n   *\n   * resolveLangBasedOnScope('todos/en') => en\n   * resolveLangBasedOnScope('en') => en\n   *\n   */\n  resolveLangBasedOnScope(lang) {\n    const scope = getScopeFromLang(lang);\n    return scope ? getLangFromScope(lang) : lang;\n  }\n  /**\n   *\n   * Resolve the lang path for loading\n   *\n   * @example\n   *\n   * resolveLangPath('todos', 'en') => todos/en\n   * resolveLangPath('en') => en\n   *\n   */\n  resolveLangPath(lang, scope) {\n    return scope ? `${scope}/${lang}` : lang;\n  }\n}\nclass ScopeResolver {\n  service;\n  constructor(service) {\n    this.service = service;\n  }\n  // inline => provider\n  resolve(params) {\n    const {\n      inline,\n      provider\n    } = params;\n    if (inline) {\n      return inline;\n    }\n    if (provider) {\n      if (isScopeObject(provider)) {\n        const {\n          scope,\n          alias = this.service.config.scopes.keepCasing ? scope : toCamelCase(scope)\n        } = provider;\n        this.service._setScopeAlias(scope, alias);\n        return scope;\n      }\n      return provider;\n    }\n    return undefined;\n  }\n}\nclass TranslocoDirective {\n  destroyRef = inject(DestroyRef);\n  service = inject(TranslocoService);\n  tpl = inject(TemplateRef, {\n    optional: true\n  });\n  providerLang = inject(TRANSLOCO_LANG, {\n    optional: true\n  });\n  providerScope = inject(TRANSLOCO_SCOPE, {\n    optional: true\n  });\n  providedLoadingTpl = inject(TRANSLOCO_LOADING_TEMPLATE, {\n    optional: true\n  });\n  cdr = inject(ChangeDetectorRef);\n  host = inject(ElementRef);\n  vcr = inject(ViewContainerRef);\n  renderer = inject(Renderer2);\n  view;\n  memo = new Map();\n  key;\n  params = {};\n  inlineScope;\n  /** @deprecated use prefix instead, will be removed in Transloco v8 */\n  inlineRead;\n  prefix;\n  inlineLang;\n  inlineTpl;\n  currentLang;\n  loaderTplHandler;\n  // Whether we already rendered the view once\n  initialized = false;\n  path;\n  langResolver = new LangResolver();\n  scopeResolver = new ScopeResolver(this.service);\n  strategy = this.tpl === null ? 'attribute' : 'structural';\n  static ngTemplateContextGuard(dir, ctx) {\n    return true;\n  }\n  ngOnInit() {\n    const listenToLangChange = shouldListenToLangChanges(this.service, this.providerLang || this.inlineLang);\n    this.service.langChanges$.pipe(switchMap(activeLang => {\n      const lang = this.langResolver.resolve({\n        inline: this.inlineLang,\n        provider: this.providerLang,\n        active: activeLang\n      });\n      return Array.isArray(this.providerScope) ? forkJoin(this.providerScope.map(providerScope => this.resolveScope(lang, providerScope))) : this.resolveScope(lang, this.providerScope);\n    }), listenOrNotOperator(listenToLangChange), takeUntilDestroyed(this.destroyRef)).subscribe(() => {\n      this.currentLang = this.langResolver.resolveLangBasedOnScope(this.path);\n      this.strategy === 'attribute' ? this.attributeStrategy() : this.structuralStrategy(this.currentLang, this.prefix || this.inlineRead);\n      this.cdr.markForCheck();\n      this.initialized = true;\n    });\n    if (!this.initialized) {\n      const loadingContent = this.resolveLoadingContent();\n      if (loadingContent) {\n        this.loaderTplHandler = new TemplateHandler(loadingContent, this.vcr);\n        this.loaderTplHandler.attachView();\n      }\n    }\n  }\n  ngOnChanges(changes) {\n    // We need to support dynamic keys/params, so if this is not the first change CD cycle\n    // we need to run the function again in order to update the value\n    if (this.strategy === 'attribute') {\n      const notInit = Object.keys(changes).some(v => !changes[v].firstChange);\n      notInit && this.attributeStrategy();\n    }\n  }\n  attributeStrategy() {\n    this.detachLoader();\n    this.renderer.setProperty(this.host.nativeElement, 'innerText', this.service.translate(this.key, this.params, this.currentLang));\n  }\n  structuralStrategy(lang, prefix) {\n    this.memo.clear();\n    const translateFn = this.getTranslateFn(lang, prefix);\n    if (this.view) {\n      // when the lang changes we need to change the reference so Angular will update the view\n      this.view.context['$implicit'] = translateFn;\n      this.view.context['currentLang'] = this.currentLang;\n    } else {\n      this.detachLoader();\n      this.view = this.vcr.createEmbeddedView(this.tpl, {\n        $implicit: translateFn,\n        currentLang: this.currentLang\n      });\n    }\n  }\n  getTranslateFn(lang, prefix) {\n    return (key, params) => {\n      const withPrefix = prefix ? `${prefix}.${key}` : key;\n      const memoKey = params ? `${withPrefix}${JSON.stringify(params)}` : withPrefix;\n      if (!this.memo.has(memoKey)) {\n        this.memo.set(memoKey, this.service.translate(withPrefix, params, lang));\n      }\n      return this.memo.get(memoKey);\n    };\n  }\n  resolveLoadingContent() {\n    return this.inlineTpl || this.providedLoadingTpl;\n  }\n  ngOnDestroy() {\n    this.memo.clear();\n  }\n  detachLoader() {\n    this.loaderTplHandler?.detachView();\n  }\n  resolveScope(lang, providerScope) {\n    const resolvedScope = this.scopeResolver.resolve({\n      inline: this.inlineScope,\n      provider: providerScope\n    });\n    this.path = this.langResolver.resolveLangPath(lang, resolvedScope);\n    const inlineLoader = resolveInlineLoader(providerScope, resolvedScope);\n    return this.service._loadDependencies(this.path, inlineLoader);\n  }\n  static ɵfac = function TranslocoDirective_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || TranslocoDirective)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: TranslocoDirective,\n    selectors: [[\"\", \"transloco\", \"\"]],\n    inputs: {\n      key: [0, \"transloco\", \"key\"],\n      params: [0, \"translocoParams\", \"params\"],\n      inlineScope: [0, \"translocoScope\", \"inlineScope\"],\n      inlineRead: [0, \"translocoRead\", \"inlineRead\"],\n      prefix: [0, \"translocoPrefix\", \"prefix\"],\n      inlineLang: [0, \"translocoLang\", \"inlineLang\"],\n      inlineTpl: [0, \"translocoLoadingTpl\", \"inlineTpl\"]\n    },\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslocoDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[transloco]',\n      standalone: true\n    }]\n  }], null, {\n    key: [{\n      type: Input,\n      args: ['transloco']\n    }],\n    params: [{\n      type: Input,\n      args: ['translocoParams']\n    }],\n    inlineScope: [{\n      type: Input,\n      args: ['translocoScope']\n    }],\n    inlineRead: [{\n      type: Input,\n      args: ['translocoRead']\n    }],\n    prefix: [{\n      type: Input,\n      args: ['translocoPrefix']\n    }],\n    inlineLang: [{\n      type: Input,\n      args: ['translocoLang']\n    }],\n    inlineTpl: [{\n      type: Input,\n      args: ['translocoLoadingTpl']\n    }]\n  });\n})();\nclass TranslocoPipe {\n  service;\n  providerScope;\n  providerLang;\n  cdr;\n  subscription = null;\n  lastValue = '';\n  lastKey;\n  path;\n  langResolver = new LangResolver();\n  scopeResolver;\n  constructor(service, providerScope, providerLang, cdr) {\n    this.service = service;\n    this.providerScope = providerScope;\n    this.providerLang = providerLang;\n    this.cdr = cdr;\n    this.scopeResolver = new ScopeResolver(this.service);\n  }\n  // null is for handling strict mode + async pipe types https://github.com/jsverse/transloco/issues/311\n  // null is for handling strict mode + optional chaining types https://github.com/jsverse/transloco/issues/488\n  transform(key, params, inlineLang) {\n    if (!key) {\n      return key;\n    }\n    const keyName = params ? `${key}${JSON.stringify(params)}` : key;\n    if (keyName === this.lastKey) {\n      return this.lastValue;\n    }\n    this.lastKey = keyName;\n    this.subscription?.unsubscribe();\n    const listenToLangChange = shouldListenToLangChanges(this.service, this.providerLang || inlineLang);\n    this.subscription = this.service.langChanges$.pipe(switchMap(activeLang => {\n      const lang = this.langResolver.resolve({\n        inline: inlineLang,\n        provider: this.providerLang,\n        active: activeLang\n      });\n      return Array.isArray(this.providerScope) ? forkJoin(this.providerScope.map(providerScope => this.resolveScope(lang, providerScope))) : this.resolveScope(lang, this.providerScope);\n    }), listenOrNotOperator(listenToLangChange)).subscribe(() => this.updateValue(key, params));\n    return this.lastValue;\n  }\n  ngOnDestroy() {\n    this.subscription?.unsubscribe();\n    // Caretaker note: it's important to clean up references to subscriptions since they save the `next`\n    // callback within its `destination` property, preventing classes from being GC'd.\n    this.subscription = null;\n  }\n  updateValue(key, params) {\n    const lang = this.langResolver.resolveLangBasedOnScope(this.path);\n    this.lastValue = this.service.translate(key, params, lang);\n    this.cdr.markForCheck();\n  }\n  resolveScope(lang, providerScope) {\n    const resolvedScope = this.scopeResolver.resolve({\n      inline: undefined,\n      provider: providerScope\n    });\n    this.path = this.langResolver.resolveLangPath(lang, resolvedScope);\n    const inlineLoader = resolveInlineLoader(providerScope, resolvedScope);\n    return this.service._loadDependencies(this.path, inlineLoader);\n  }\n  static ɵfac = function TranslocoPipe_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || TranslocoPipe)(i0.ɵɵdirectiveInject(TranslocoService, 16), i0.ɵɵdirectiveInject(TRANSLOCO_SCOPE, 24), i0.ɵɵdirectiveInject(TRANSLOCO_LANG, 24), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef, 16));\n  };\n  static ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n    name: \"transloco\",\n    type: TranslocoPipe,\n    pure: false\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslocoPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'transloco',\n      pure: false,\n      standalone: true\n    }]\n  }], () => [{\n    type: TranslocoService\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [TRANSLOCO_SCOPE]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [TRANSLOCO_LANG]\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }], null);\n})();\nconst decl = [TranslocoDirective, TranslocoPipe];\nclass TranslocoModule {\n  static ɵfac = function TranslocoModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || TranslocoModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: TranslocoModule,\n    imports: [TranslocoDirective, TranslocoPipe],\n    exports: [TranslocoDirective, TranslocoPipe]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslocoModule, [{\n    type: NgModule,\n    args: [{\n      imports: decl,\n      exports: decl\n    }]\n  }], null, null);\n})();\nfunction provideTransloco(options) {\n  const providers = [provideTranslocoTranspiler(DefaultTranspiler), provideTranslocoMissingHandler(DefaultMissingHandler), provideTranslocoInterceptor(DefaultInterceptor), provideTranslocoFallbackStrategy(DefaultFallbackStrategy)];\n  if (options.config) {\n    providers.push(provideTranslocoConfig(options.config));\n  }\n  if (options.loader) {\n    providers.push(provideTranslocoLoader(options.loader));\n  }\n  return providers;\n}\nfunction provideTranslocoConfig(config) {\n  return makeEnvironmentProviders([{\n    provide: TRANSLOCO_CONFIG,\n    useValue: translocoConfig(config)\n  }]);\n}\nfunction provideTranslocoLoader(loader) {\n  return makeEnvironmentProviders([{\n    provide: TRANSLOCO_LOADER,\n    useClass: loader\n  }]);\n}\nfunction provideTranslocoScope(...scopes) {\n  return scopes.map(scope => ({\n    provide: TRANSLOCO_SCOPE,\n    useValue: scope,\n    multi: true\n  }));\n}\nfunction provideTranslocoLoadingTpl(content) {\n  return {\n    provide: TRANSLOCO_LOADING_TEMPLATE,\n    useValue: content\n  };\n}\nfunction provideTranslocoTranspiler(transpiler) {\n  return makeEnvironmentProviders([{\n    provide: TRANSLOCO_TRANSPILER,\n    useClass: transpiler,\n    deps: [TRANSLOCO_CONFIG]\n  }]);\n}\nfunction provideTranslocoFallbackStrategy(strategy) {\n  return makeEnvironmentProviders([{\n    provide: TRANSLOCO_FALLBACK_STRATEGY,\n    useClass: strategy,\n    deps: [TRANSLOCO_CONFIG]\n  }]);\n}\nfunction provideTranslocoMissingHandler(handler) {\n  return makeEnvironmentProviders([{\n    provide: TRANSLOCO_MISSING_HANDLER,\n    useClass: handler\n  }]);\n}\nfunction provideTranslocoInterceptor(interceptor) {\n  return makeEnvironmentProviders([{\n    provide: TRANSLOCO_INTERCEPTOR,\n    useClass: interceptor\n  }]);\n}\nfunction provideTranslocoLang(lang) {\n  return {\n    provide: TRANSLOCO_LANG,\n    useValue: lang\n  };\n}\nconst TRANSLOCO_TEST_LANGS = new InjectionToken('TRANSLOCO_TEST_LANGS - Available testing languages');\nconst TRANSLOCO_TEST_OPTIONS = new InjectionToken('TRANSLOCO_TEST_OPTIONS - Testing options');\nclass TestingLoader {\n  langs;\n  constructor(langs) {\n    this.langs = langs;\n  }\n  getTranslation(lang) {\n    return of(this.langs[lang]);\n  }\n  static ɵfac = function TestingLoader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || TestingLoader)(i0.ɵɵinject(TRANSLOCO_TEST_LANGS));\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: TestingLoader,\n    factory: TestingLoader.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TestingLoader, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [TRANSLOCO_TEST_LANGS]\n    }]\n  }], null);\n})();\nfunction initTranslocoService(service, langs = {}, options) {\n  const preloadAllLangs = () => options.preloadLangs ? Promise.all(Object.keys(langs).map(lang => service.load(lang).toPromise())) : Promise.resolve();\n  return preloadAllLangs;\n}\nclass TranslocoTestingModule {\n  static forRoot(options) {\n    return {\n      ngModule: TranslocoTestingModule,\n      providers: [provideTransloco({\n        loader: TestingLoader,\n        config: {\n          prodMode: true,\n          missingHandler: {\n            logMissingKey: false\n          },\n          ...options.translocoConfig\n        }\n      }), {\n        provide: TRANSLOCO_TEST_LANGS,\n        useValue: options.langs\n      }, {\n        provide: TRANSLOCO_TEST_OPTIONS,\n        useValue: options\n      }, {\n        provide: APP_INITIALIZER,\n        useFactory: initTranslocoService,\n        deps: [TranslocoService, TRANSLOCO_TEST_LANGS, TRANSLOCO_TEST_OPTIONS],\n        multi: true\n      }]\n    };\n  }\n  static ɵfac = function TranslocoTestingModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || TranslocoTestingModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: TranslocoTestingModule,\n    exports: [TranslocoModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [TranslocoModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TranslocoTestingModule, [{\n    type: NgModule,\n    args: [{\n      exports: [TranslocoModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Returns the language code name from the browser, e.g. \"en\"\n */\nfunction getBrowserLang() {\n  let browserLang = getBrowserCultureLang();\n  if (!browserLang || !isBrowser()) {\n    return undefined;\n  }\n  if (browserLang.indexOf('-') !== -1) {\n    browserLang = browserLang.split('-')[0];\n  }\n  if (browserLang.indexOf('_') !== -1) {\n    browserLang = browserLang.split('_')[0];\n  }\n  return browserLang;\n}\n/**\n * Returns the culture language code name from the browser, e.g. \"en-US\"\n */\nfunction getBrowserCultureLang() {\n  if (!isBrowser()) {\n    return '';\n  }\n  const navigator = window.navigator;\n  return navigator.languages?.[0] ?? navigator.language;\n}\n\n/**\n * Gets the translated value of a key as Signal\n *\n * @example\n * text = translateSignal('hello');\n * textList = translateSignal(['green', 'blue']);\n * textVar = translateSignal('hello', { variable: 'world' });\n * textSpanish = translateSignal('hello', { variable: 'world' }, 'es');\n * textTodosScope = translateSignal('hello', { variable: 'world' }, { scope: 'todos' });\n *\n * @example\n * dynamicKey = signal('hello');\n * dynamicParam = signal({ variable: 'world' });\n * text = translateSignal(this.dynamicKey, this.dynamicParam);\n *\n */\nfunction translateSignal(key, params, lang, injector) {\n  if (!injector) {\n    assertInInjectionContext(translateSignal);\n  }\n  injector ??= inject(Injector);\n  const result = runInInjectionContext(injector, () => {\n    const service = inject(TranslocoService);\n    const scope = resolveScope(lang);\n    return toObservable(computerKeysAndParams(key, params)).pipe(switchMap(dynamic => service.selectTranslate(dynamic.key, dynamic.params, scope)));\n  });\n  return toSignal(result, {\n    initialValue: Array.isArray(key) ? [''] : ''\n  });\n}\n/**\n * Gets the translated object of a key as Signal\n *\n * @example\n * object = translateObjectSignal('nested.object');\n * title = object().title;\n *\n * @example\n * dynamicKey = signal('nested.object');\n * dynamicParam = signal({ variable: 'world' });\n * object = translateObjectSignal(this.dynamicKey, this.dynamicParam);\n */\nfunction translateObjectSignal(key, params, lang, injector) {\n  if (!injector) {\n    assertInInjectionContext(translateObjectSignal);\n  }\n  injector ??= inject(Injector);\n  const result = runInInjectionContext(injector, () => {\n    const service = inject(TranslocoService);\n    const scope = resolveScope(lang);\n    return toObservable(computerKeysAndParams(key, params)).pipe(switchMap(dynamic => service.selectTranslateObject(dynamic.key, dynamic.params, scope)));\n  });\n  return toSignal(result, {\n    initialValue: Array.isArray(key) ? [] : {}\n  });\n}\nfunction computerParams(params) {\n  if (isSignal(params)) {\n    return computed(() => params());\n  }\n  return computed(() => {\n    return Object.entries(params).reduce((acc, [key, value]) => {\n      acc[key] = isSignal(value) ? value() : value;\n      return acc;\n    }, {});\n  });\n}\nfunction computerKeys(keys) {\n  if (Array.isArray(keys)) {\n    return computed(() => keys.map(key => isSignal(key) ? key() : key));\n  }\n  return computed(() => keys());\n}\nfunction isSignalKey(key) {\n  return Array.isArray(key) ? key.some(isSignal) : isSignal(key);\n}\nfunction isSignalParams(params) {\n  return params ? isSignal(params) || Object.values(params).some(isSignal) : false;\n}\nfunction computerKeysAndParams(key, params) {\n  const computedKeys = isSignalKey(key) ? computerKeys(key) : computed(() => key);\n  const computedParams = isSignalParams(params) ? computerParams(params) : computed(() => params);\n  return computed(() => ({\n    key: computedKeys(),\n    params: computedParams()\n  }));\n}\nfunction resolveScope(scope) {\n  if (typeof scope === 'undefined' || scope === '') {\n    const translocoScope = inject(TRANSLOCO_SCOPE, {\n      optional: true\n    });\n    return translocoScope ?? undefined;\n  }\n  return scope;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DefaultFallbackStrategy, DefaultInterceptor, DefaultMissingHandler, DefaultTranspiler, FunctionalTranspiler, TRANSLOCO_CONFIG, TRANSLOCO_FALLBACK_STRATEGY, TRANSLOCO_INTERCEPTOR, TRANSLOCO_LANG, TRANSLOCO_LOADER, TRANSLOCO_LOADING_TEMPLATE, TRANSLOCO_MISSING_HANDLER, TRANSLOCO_SCOPE, TRANSLOCO_TRANSPILER, TestingLoader, TranslocoDirective, TranslocoModule, TranslocoPipe, TranslocoService, TranslocoTestingModule, coerceArray, defaultConfig, flatten, getBrowserCultureLang, getBrowserLang, getFunctionArgs, getLangFromScope, getPipeValue, getScopeFromLang, getValue, hasInlineLoader, isBrowser, isDefined, isEmpty, isFunction, isNil, isNumber, isObject, isScopeObject, isString, provideTransloco, provideTranslocoConfig, provideTranslocoFallbackStrategy, provideTranslocoInterceptor, provideTranslocoLang, provideTranslocoLoader, provideTranslocoLoadingTpl, provideTranslocoMissingHandler, provideTranslocoScope, provideTranslocoTranspiler, setValue, size, toCamelCase, toNumber, translate, translateObject, translateObjectSignal, translateSignal, translocoConfig, unflatten };", "map": {"version": 3, "names": ["i0", "InjectionToken", "inject", "Injectable", "Injector", "Inject", "DestroyRef", "Optional", "Component", "Input", "TemplateRef", "ChangeDetectorRef", "ElementRef", "ViewContainerRef", "Renderer2", "Directive", "<PERSON><PERSON>", "NgModule", "makeEnvironmentProviders", "APP_INITIALIZER", "assertInInjectionContext", "runInInjectionContext", "isSignal", "computed", "of", "take", "from", "map", "Subject", "BehaviorSubject", "fork<PERSON><PERSON>n", "retry", "tap", "catchError", "shareReplay", "switchMap", "combineLatest", "EMPTY", "takeUntilDestroyed", "toObservable", "toSignal", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "translations", "constructor", "getTranslation", "lang", "get", "TRANSLOCO_LOADER", "ngDevMode", "getValue", "obj", "path", "Object", "prototype", "hasOwnProperty", "call", "split", "reduce", "p", "c", "setValue", "prop", "val", "lastIndex", "length", "acc", "part", "index", "Array", "isArray", "slice", "size", "collection", "isObject", "keys", "isEmpty", "isFunction", "isString", "isNumber", "item", "coerce<PERSON><PERSON><PERSON>", "value", "toCamelCase", "str", "replace", "word", "toLowerCase", "toUpperCase", "<PERSON><PERSON><PERSON><PERSON>", "window", "isNil", "undefined", "isDefined", "toNumber", "isNaN", "Number", "parseFloat", "isScopeObject", "scope", "hasInlineLoader", "loader", "flatten", "result", "recurse", "curr", "key", "entries", "unflatten", "current", "for<PERSON>ach", "i", "TRANSLOCO_CONFIG", "providedIn", "factory", "defaultConfig", "defaultLang", "reRenderOnLangChange", "prodMode", "failedRetries", "fallback<PERSON><PERSON>", "availableLangs", "<PERSON><PERSON><PERSON><PERSON>", "logMissingKey", "useFallbackTranslation", "allowEmpty", "aot", "interpolation", "scopes", "keepCasing", "translocoConfig", "config", "TRANSLOCO_TRANSPILER", "DefaultTranspiler", "optional", "interpolationMatcher", "<PERSON><PERSON><PERSON><PERSON>", "transpile", "params", "translation", "paramMatch", "parsedValue", "exec", "match", "paramValue", "trim", "param", "handleObject", "handleArray", "transpiled", "rest", "v", "ɵfac", "DefaultTranspiler_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "ɵsetClassMetadata", "type", "start", "end", "RegExp", "getFunctionArgs", "argsString", "splitted", "args", "push", "FunctionalTranspiler", "injector", "functionName", "func", "e", "message", "includes", "Error", "ɵFunctionalTranspiler_BaseFactory", "FunctionalTranspiler_Factory", "ɵɵgetInheritedFactory", "TRANSLOCO_MISSING_HANDLER", "DefaultMissingHandler", "handle", "msg", "console", "warn", "DefaultMissingHandler_Factory", "TRANSLOCO_INTERCEPTOR", "DefaultInterceptor", "preSaveTranslation", "preSaveTranslationKey", "_", "DefaultInterceptor_Factory", "TRANSLOCO_FALLBACK_STRATEGY", "DefaultFallbackStrategy", "userConfig", "getNextLangs", "DefaultFallbackStrategy_Factory", "ɵɵinject", "decorators", "getScopeFromLang", "pop", "join", "getLangFromScope", "getPipeValue", "char", "lastItem", "toString", "shouldListenToLangChanges", "service", "hasStatic", "listenOrNotOperator", "listenToLangChange", "source", "prependScope", "inline<PERSON><PERSON>der", "resolveInlineLoader", "providerScope", "getEventPayload", "langName", "<PERSON><PERSON><PERSON><PERSON>", "options", "<PERSON><PERSON><PERSON><PERSON>", "data", "<PERSON><PERSON><PERSON><PERSON>", "then", "res", "default", "getFallbacksLoaders", "fallback<PERSON><PERSON>", "paths", "pipe", "translate", "translateObject", "TranslocoService", "parser", "interceptor", "fallbackStrategy", "langChanges$", "Map", "cache", "firstFallbackLang", "isResolvedMissingOnce", "failed<PERSON>ang<PERSON>", "Set", "events", "events$", "asObservable", "destroyRef", "JSON", "parse", "stringify", "setAvailableLangs", "setFallbackLangForMissingTranslation", "setDefaultLang", "getDefaultLang", "subscribe", "wasFailure", "setActiveLang", "payload", "onDestroy", "complete", "clear", "getActiveLang", "onLangChanged", "next", "langs", "getAvailableLangs", "load", "cached", "loadTranslation", "isScope", "_isLangScoped", "loadersOptions", "fallback", "loaders", "load$", "t", "handleSuccess", "set", "error", "handleFailure", "resolveLang", "resolveLangAndScope", "k", "_handleMissingKey", "selectTranslate", "_isObject", "isLang", "isScopeWithLang", "getObjectByKey", "_key", "_params", "getEntries", "selectTranslateObject", "firstKey", "firstParams", "langOrScope", "selectTranslation", "language$", "scopeLangSpecified", "currentLang", "language", "setTranslation", "defaults", "merge", "emitChange", "mergedOptions", "flattenScopeOrTranslation", "getMappedScope", "mergedTranslation", "flattenTranslation", "with<PERSON><PERSON>", "setTranslationKey", "newValue", "fallback<PERSON><PERSON><PERSON>", "getMissingHandlerData", "getAvailableLangsIds", "indexOf", "_loadDependencies", "mainLang", "isLoadedTranslation", "_completeScopeWithLang", "_setScopeAlias", "alias", "scopeMapping", "first", "l", "id", "activeLang", "delete", "loadOptions", "failedCounter", "fallback<PERSON><PERSON>s", "fallbacks", "nextLang", "add", "has", "isFallbackLang", "langFromScope", "hasLang", "prefix", "current<PERSON><PERSON>", "startsWith", "TranslocoService_Factory", "TranslocoLoaderComponent", "html", "TranslocoLoaderComponent_Factory", "ɵcmp", "ɵɵdefineComponent", "selectors", "inputs", "decls", "vars", "consts", "template", "TranslocoLoaderComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵproperty", "ɵɵsanitizeHtml", "encapsulation", "standalone", "Template<PERSON><PERSON><PERSON>", "view", "vcr", "attachView", "createEmbeddedView", "componentRef", "createComponent", "instance", "<PERSON><PERSON><PERSON><PERSON>", "detectChanges", "detach<PERSON>iew", "TRANSLOCO_LANG", "TRANSLOCO_LOADING_TEMPLATE", "TRANSLOCO_SCOPE", "LangResolver", "initialized", "resolve", "inline", "provider", "active", "extracted", "resolveLangBasedOnScope", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ScopeResolver", "TranslocoDirective", "tpl", "providerLang", "providedLoadingTpl", "cdr", "host", "renderer", "memo", "inlineScope", "inlineRead", "inlineLang", "inlineTpl", "loaderTplHandler", "langResolver", "scopeResolver", "strategy", "ngTemplateContextGuard", "dir", "ngOnInit", "resolveScope", "attributeStrategy", "structuralStrategy", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadingContent", "resolveLoadingContent", "ngOnChanges", "changes", "notInit", "some", "firstChange", "deta<PERSON><PERSON><PERSON><PERSON>", "setProperty", "nativeElement", "translateFn", "getTranslateFn", "context", "$implicit", "withPrefix", "memoKey", "ngOnDestroy", "resolvedScope", "TranslocoDirective_Factory", "ɵdir", "ɵɵdefineDirective", "features", "ɵɵNgOnChangesFeature", "selector", "TranslocoPipe", "subscription", "lastValue", "last<PERSON>ey", "transform", "keyName", "unsubscribe", "updateValue", "TranslocoPipe_Factory", "ɵɵdirectiveInject", "ɵpipe", "ɵɵdefinePipe", "name", "pure", "decl", "TranslocoModule", "TranslocoModule_Factory", "ɵmod", "ɵɵdefineNgModule", "imports", "exports", "ɵinj", "ɵɵdefineInjector", "provideTransloco", "providers", "provideTranslocoTranspiler", "provideTranslocoMissingHandler", "provideTranslocoInterceptor", "provideTranslocoFallbackStrategy", "provideTranslocoConfig", "provideTranslocoLoader", "provide", "useValue", "useClass", "provideTranslocoScope", "multi", "provideTranslocoLoadingTpl", "content", "transpiler", "deps", "handler", "provideTranslocoLang", "TRANSLOCO_TEST_LANGS", "TRANSLOCO_TEST_OPTIONS", "TestingLoader", "TestingLoader_Factory", "initTranslocoService", "preloadAllLangs", "preloadLangs", "Promise", "all", "to<PERSON>romise", "TranslocoTestingModule", "forRoot", "ngModule", "useFactory", "TranslocoTestingModule_Factory", "getBrowserLang", "browserLang", "getBrowserCultureLang", "navigator", "languages", "translateSignal", "computerKeysAndParams", "dynamic", "initialValue", "translateObjectSignal", "computerParams", "computerKeys", "isSignalKey", "isSignalParams", "values", "computedKeys", "computedParams", "translocoScope"], "sources": ["D:/EIOT2.SERVER/eiot2-monorepo/node_modules/@jsverse/transloco/fesm2022/jsverse-transloco.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, Injectable, Injector, Inject, DestroyRef, Optional, Component, Input, TemplateRef, ChangeDetectorRef, ElementRef, ViewContainerRef, Renderer2, Directive, Pipe, NgModule, makeEnvironmentProviders, APP_INITIALIZER, assertInInjectionContext, runInInjectionContext, isSignal, computed } from '@angular/core';\nimport { of, take, from, map, Subject, BehaviorSubject, forkJoin, retry, tap, catchError, shareReplay, switchMap, combineLatest, EMPTY } from 'rxjs';\nimport { takeUntilDestroyed, toObservable, toSignal } from '@angular/core/rxjs-interop';\n\nclass DefaultLoader {\n    translations;\n    constructor(translations) {\n        this.translations = translations;\n    }\n    getTranslation(lang) {\n        return of(this.translations.get(lang) || {});\n    }\n}\nconst TRANSLOCO_LOADER = new InjectionToken(ngDevMode ? 'TRANSLOCO_LOADER' : '');\n\nfunction getValue(obj, path) {\n    if (!obj) {\n        return obj;\n    }\n    /* For cases where the key is like: 'general.something.thing' */\n    if (Object.prototype.hasOwnProperty.call(obj, path)) {\n        return obj[path];\n    }\n    return path.split('.').reduce((p, c) => p?.[c], obj);\n}\nfunction setValue(obj, prop, val) {\n    obj = { ...obj };\n    const split = prop.split('.');\n    const lastIndex = split.length - 1;\n    split.reduce((acc, part, index) => {\n        if (index === lastIndex) {\n            acc[part] = val;\n        }\n        else {\n            acc[part] = Array.isArray(acc[part])\n                ? acc[part].slice()\n                : { ...acc[part] };\n        }\n        return acc && acc[part];\n    }, obj);\n    return obj;\n}\nfunction size(collection) {\n    if (!collection) {\n        return 0;\n    }\n    if (Array.isArray(collection)) {\n        return collection.length;\n    }\n    if (isObject(collection)) {\n        return Object.keys(collection).length;\n    }\n    return collection ? collection.length : 0;\n}\nfunction isEmpty(collection) {\n    return size(collection) === 0;\n}\nfunction isFunction(val) {\n    return typeof val === 'function';\n}\nfunction isString(val) {\n    return typeof val === 'string';\n}\nfunction isNumber(val) {\n    return typeof val === 'number';\n}\nfunction isObject(item) {\n    return !!item && typeof item === 'object' && !Array.isArray(item);\n}\nfunction coerceArray(value) {\n    return Array.isArray(value) ? value : [value];\n}\n/*\n * @example\n *\n * given: path-to-happiness => pathToHappiness\n * given: path_to_happiness => pathToHappiness\n * given: path-to_happiness => pathToHappiness\n *\n */\nfunction toCamelCase(str) {\n    return str\n        .replace(/(?:^\\w|[A-Z]|\\b\\w)/g, (word, index) => index == 0 ? word.toLowerCase() : word.toUpperCase())\n        .replace(/\\s+|_|-|\\//g, '');\n}\nfunction isBrowser() {\n    return typeof window !== 'undefined';\n}\nfunction isNil(value) {\n    return value === null || value === undefined;\n}\nfunction isDefined(value) {\n    return isNil(value) === false;\n}\nfunction toNumber(value) {\n    if (isNumber(value))\n        return value;\n    if (isString(value) && !isNaN(Number(value) - parseFloat(value))) {\n        return Number(value);\n    }\n    return null;\n}\nfunction isScopeObject(item) {\n    return item && typeof item.scope === 'string';\n}\nfunction hasInlineLoader(item) {\n    return item && isObject(item.loader);\n}\nfunction flatten(obj) {\n    const result = {};\n    function recurse(curr, prop) {\n        if (curr === null) {\n            result[prop] = null;\n        }\n        else if (isObject(curr)) {\n            for (const [key, value] of Object.entries(curr)) {\n                recurse(value, prop ? `${prop}.${key}` : key);\n            }\n        }\n        else {\n            result[prop] = curr;\n        }\n    }\n    recurse(obj, '');\n    return result;\n}\nfunction unflatten(obj) {\n    const result = {};\n    for (const [key, value] of Object.entries(obj)) {\n        const keys = key.split('.');\n        let current = result;\n        keys.forEach((key, i) => {\n            if (i === keys.length - 1) {\n                current[key] = value;\n            }\n            else {\n                current[key] ??= {};\n                current = current[key];\n            }\n        });\n    }\n    return result;\n}\n\nconst TRANSLOCO_CONFIG = new InjectionToken(ngDevMode ? 'TRANSLOCO_CONFIG' : '', {\n    providedIn: 'root',\n    factory: () => defaultConfig,\n});\nconst defaultConfig = {\n    defaultLang: 'en',\n    reRenderOnLangChange: false,\n    prodMode: false,\n    failedRetries: 2,\n    fallbackLang: [],\n    availableLangs: [],\n    missingHandler: {\n        logMissingKey: true,\n        useFallbackTranslation: false,\n        allowEmpty: false,\n    },\n    flatten: {\n        aot: false,\n    },\n    interpolation: ['{{', '}}'],\n    scopes: {\n        keepCasing: false,\n    },\n};\nfunction translocoConfig(config = {}) {\n    return {\n        ...defaultConfig,\n        ...config,\n        missingHandler: {\n            ...defaultConfig.missingHandler,\n            ...config.missingHandler,\n        },\n        flatten: {\n            ...defaultConfig.flatten,\n            ...config.flatten,\n        },\n        scopes: {\n            ...defaultConfig.scopes,\n            ...config.scopes,\n        },\n    };\n}\n\nconst TRANSLOCO_TRANSPILER = new InjectionToken(ngDevMode ? 'TRANSLOCO_TRANSPILER' : '');\nclass DefaultTranspiler {\n    config = inject(TRANSLOCO_CONFIG, { optional: true }) ?? defaultConfig;\n    get interpolationMatcher() {\n        return resolveMatcher(this.config);\n    }\n    transpile({ value, params = {}, translation, key }) {\n        if (isString(value)) {\n            let paramMatch;\n            let parsedValue = value;\n            while ((paramMatch = this.interpolationMatcher.exec(parsedValue)) !== null) {\n                const [match, paramValue] = paramMatch;\n                parsedValue = parsedValue.replace(match, () => {\n                    const match = paramValue.trim();\n                    const param = getValue(params, match);\n                    if (isDefined(param)) {\n                        return param;\n                    }\n                    return isDefined(translation[match])\n                        ? this.transpile({\n                            params,\n                            translation,\n                            key,\n                            value: translation[match],\n                        })\n                        : '';\n                });\n            }\n            return parsedValue;\n        }\n        else if (params) {\n            if (isObject(value)) {\n                value = this.handleObject({\n                    value,\n                    params,\n                    translation,\n                    key,\n                });\n            }\n            else if (Array.isArray(value)) {\n                value = this.handleArray({ value, params, translation, key });\n            }\n        }\n        return value;\n    }\n    /**\n     *\n     * @example\n     *\n     * const en = {\n     *  a: {\n     *    b: {\n     *      c: \"Hello {{ value }}\"\n     *    }\n     *  }\n     * }\n     *\n     * const params =  {\n     *  \"b.c\": { value: \"Transloco \"}\n     * }\n     *\n     * service.selectTranslate('a', params);\n     *\n     * // the first param will be the result of `en.a`.\n     * // the second param will be `params`.\n     * parser.transpile(value, params, {});\n     *\n     *\n     */\n    handleObject({ value, params = {}, translation, key, }) {\n        let result = value;\n        Object.keys(params).forEach((p) => {\n            // transpile the value => \"Hello Transloco\"\n            const transpiled = this.transpile({\n                // get the value of \"b.c\" inside \"a\" => \"Hello {{ value }}\"\n                value: getValue(result, p),\n                // get the params of \"b.c\" => { value: \"Transloco\" }\n                params: getValue(params, p),\n                translation,\n                key,\n            });\n            // set \"b.c\" to `transpiled`\n            result = setValue(result, p, transpiled);\n        });\n        return result;\n    }\n    handleArray({ value, ...rest }) {\n        return value.map((v) => this.transpile({\n            value: v,\n            ...rest,\n        }));\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: DefaultTranspiler, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: DefaultTranspiler });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: DefaultTranspiler, decorators: [{\n            type: Injectable\n        }] });\nfunction resolveMatcher(config) {\n    const [start, end] = config.interpolation;\n    return new RegExp(`${start}([^${start}${end}]*?)${end}`, 'g');\n}\nfunction getFunctionArgs(argsString) {\n    const splitted = argsString ? argsString.split(',') : [];\n    const args = [];\n    for (let i = 0; i < splitted.length; i++) {\n        let value = splitted[i].trim();\n        while (value[value.length - 1] === '\\\\') {\n            i++;\n            value = value.replace('\\\\', ',') + splitted[i];\n        }\n        args.push(value);\n    }\n    return args;\n}\nclass FunctionalTranspiler extends DefaultTranspiler {\n    injector = inject(Injector);\n    transpile({ value, ...rest }) {\n        let transpiled = value;\n        if (isString(value)) {\n            transpiled = value.replace(/\\[\\[\\s*(\\w+)\\((.*?)\\)\\s*]]/g, (match, functionName, args) => {\n                try {\n                    const func = this.injector.get(functionName);\n                    return func.transpile(...getFunctionArgs(args));\n                }\n                catch (e) {\n                    let message = `There is an error in: '${value}'. \n                          Check that the you used the right syntax in your translation and that the implementation of ${functionName} is correct.`;\n                    if (e.message.includes('NullInjectorError')) {\n                        message = `You are using the '${functionName}' function in your translation but no provider was found!`;\n                    }\n                    throw new Error(message);\n                }\n            });\n        }\n        return super.transpile({ value: transpiled, ...rest });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: FunctionalTranspiler, deps: null, target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: FunctionalTranspiler });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: FunctionalTranspiler, decorators: [{\n            type: Injectable\n        }] });\n\nconst TRANSLOCO_MISSING_HANDLER = new InjectionToken(ngDevMode ? 'TRANSLOCO_MISSING_HANDLER' : '');\nclass DefaultMissingHandler {\n    handle(key, config) {\n        if (config.missingHandler.logMissingKey && !config.prodMode) {\n            const msg = `Missing translation for '${key}'`;\n            console.warn(`%c ${msg}`, 'font-size: 12px; color: red');\n        }\n        return key;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: DefaultMissingHandler, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: DefaultMissingHandler });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: DefaultMissingHandler, decorators: [{\n            type: Injectable\n        }] });\n\nconst TRANSLOCO_INTERCEPTOR = new InjectionToken(ngDevMode ? 'TRANSLOCO_INTERCEPTOR' : '');\nclass DefaultInterceptor {\n    preSaveTranslation(translation) {\n        return translation;\n    }\n    preSaveTranslationKey(_, value) {\n        return value;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: DefaultInterceptor, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: DefaultInterceptor });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: DefaultInterceptor, decorators: [{\n            type: Injectable\n        }] });\n\nconst TRANSLOCO_FALLBACK_STRATEGY = new InjectionToken(ngDevMode ? 'TRANSLOCO_FALLBACK_STRATEGY' : '');\nclass DefaultFallbackStrategy {\n    userConfig;\n    constructor(userConfig) {\n        this.userConfig = userConfig;\n    }\n    getNextLangs() {\n        const fallbackLang = this.userConfig.fallbackLang;\n        if (!fallbackLang) {\n            throw new Error('When using the default fallback, a fallback language must be provided in the config!');\n        }\n        return Array.isArray(fallbackLang) ? fallbackLang : [fallbackLang];\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: DefaultFallbackStrategy, deps: [{ token: TRANSLOCO_CONFIG }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: DefaultFallbackStrategy });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: DefaultFallbackStrategy, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [TRANSLOCO_CONFIG]\n                }] }] });\n\n/*\n * @example\n *\n * given: lazy-page/en => lazy-page\n *\n */\nfunction getScopeFromLang(lang) {\n    if (!lang) {\n        return '';\n    }\n    const split = lang.split('/');\n    split.pop();\n    return split.join('/');\n}\n/*\n * @example\n *\n * given: lazy-page/en => en\n *\n */\nfunction getLangFromScope(lang) {\n    if (!lang) {\n        return '';\n    }\n    return lang.split('/').pop();\n}\n/**\n * @example\n *\n * getPipeValue('todos|scoped', 'scoped') [true, 'todos']\n * getPipeValue('en|static', 'static') [true, 'en']\n * getPipeValue('en', 'static') [false, 'en']\n */\nfunction getPipeValue(str, value, char = '|') {\n    if (isString(str)) {\n        const splitted = str.split(char);\n        const lastItem = splitted.pop();\n        return lastItem === value ? [true, splitted.toString()] : [false, lastItem];\n    }\n    return [false, ''];\n}\nfunction shouldListenToLangChanges(service, lang) {\n    const [hasStatic] = getPipeValue(lang, 'static');\n    if (!hasStatic) {\n        // If we didn't get 'lang|static' check if it's set in the global level\n        return !!service.config.reRenderOnLangChange;\n    }\n    // We have 'lang|static' so don't listen to lang changes\n    return false;\n}\nfunction listenOrNotOperator(listenToLangChange) {\n    return listenToLangChange ? (source) => source : take(1);\n}\nfunction prependScope(inlineLoader, scope) {\n    return Object.keys(inlineLoader).reduce((acc, lang) => {\n        acc[`${scope}/${lang}`] = inlineLoader[lang];\n        return acc;\n    }, {});\n}\nfunction resolveInlineLoader(providerScope, scope) {\n    return hasInlineLoader(providerScope)\n        ? prependScope(providerScope.loader, scope)\n        : undefined;\n}\nfunction getEventPayload(lang) {\n    return {\n        scope: getScopeFromLang(lang) || null,\n        langName: getLangFromScope(lang),\n    };\n}\n\nfunction resolveLoader(options) {\n    const { path, inlineLoader, mainLoader, data } = options;\n    if (inlineLoader) {\n        const pathLoader = inlineLoader[path];\n        if (isFunction(pathLoader) === false) {\n            throw `You're using an inline loader but didn't provide a loader for ${path}`;\n        }\n        return inlineLoader[path]().then((res) => res.default ? res.default : res);\n    }\n    return mainLoader.getTranslation(path, data);\n}\n\nfunction getFallbacksLoaders({ mainLoader, path, data, fallbackPath, inlineLoader, }) {\n    const paths = fallbackPath ? [path, fallbackPath] : [path];\n    return paths.map((path) => {\n        const loader = resolveLoader({ path, mainLoader, inlineLoader, data });\n        return from(loader).pipe(map((translation) => ({\n            translation,\n            lang: path,\n        })));\n    });\n}\n\nlet service;\nfunction translate(key, params = {}, lang) {\n    return service.translate(key, params, lang);\n}\nfunction translateObject(key, params = {}, lang) {\n    return service.translateObject(key, params, lang);\n}\nclass TranslocoService {\n    loader;\n    parser;\n    missingHandler;\n    interceptor;\n    fallbackStrategy;\n    langChanges$;\n    translations = new Map();\n    cache = new Map();\n    firstFallbackLang;\n    defaultLang = '';\n    availableLangs = [];\n    isResolvedMissingOnce = false;\n    lang;\n    failedLangs = new Set();\n    events = new Subject();\n    events$ = this.events.asObservable();\n    config;\n    destroyRef = inject(DestroyRef);\n    constructor(loader, parser, missingHandler, interceptor, userConfig, fallbackStrategy) {\n        this.loader = loader;\n        this.parser = parser;\n        this.missingHandler = missingHandler;\n        this.interceptor = interceptor;\n        this.fallbackStrategy = fallbackStrategy;\n        if (!this.loader) {\n            this.loader = new DefaultLoader(this.translations);\n        }\n        service = this;\n        this.config = JSON.parse(JSON.stringify(userConfig));\n        this.setAvailableLangs(this.config.availableLangs || []);\n        this.setFallbackLangForMissingTranslation(this.config);\n        this.setDefaultLang(this.config.defaultLang);\n        this.lang = new BehaviorSubject(this.getDefaultLang());\n        // Don't use distinctUntilChanged as we need the ability to update\n        // the value when using setTranslation or setTranslationKeys\n        this.langChanges$ = this.lang.asObservable();\n        /**\n         * When we have a failure, we want to define the next language that succeeded as the active\n         */\n        this.events$.subscribe((e) => {\n            if (e.type === 'translationLoadSuccess' && e.wasFailure) {\n                this.setActiveLang(e.payload.langName);\n            }\n        });\n        this.destroyRef.onDestroy(() => {\n            // Complete subjects to release observers if users forget to unsubscribe manually.\n            // This is important in server-side rendering.\n            this.lang.complete();\n            this.events.complete();\n            // As a root provider, this service is destroyed with when the application is destroyed.\n            // Cached values retain `this`, causing circular references that block garbage collection,\n            // leading to memory leaks during server-side rendering.\n            this.cache.clear();\n        });\n    }\n    getDefaultLang() {\n        return this.defaultLang;\n    }\n    setDefaultLang(lang) {\n        this.defaultLang = lang;\n    }\n    getActiveLang() {\n        return this.lang.getValue();\n    }\n    setActiveLang(lang) {\n        this.parser.onLangChanged?.(lang);\n        this.lang.next(lang);\n        this.events.next({\n            type: 'langChanged',\n            payload: getEventPayload(lang),\n        });\n        return this;\n    }\n    setAvailableLangs(langs) {\n        this.availableLangs = langs;\n    }\n    /**\n     * Gets the available languages.\n     *\n     * @returns\n     * An array of the available languages. Can be either a `string[]` or a `{ id: string; label: string }[]`\n     * depending on how the available languages are set in your module.\n     */\n    getAvailableLangs() {\n        return this.availableLangs;\n    }\n    load(path, options = {}) {\n        const cached = this.cache.get(path);\n        if (cached) {\n            return cached;\n        }\n        let loadTranslation;\n        const isScope = this._isLangScoped(path);\n        let scope;\n        if (isScope) {\n            scope = getScopeFromLang(path);\n        }\n        const loadersOptions = {\n            path,\n            mainLoader: this.loader,\n            inlineLoader: options.inlineLoader,\n            data: isScope ? { scope: scope } : undefined,\n        };\n        if (this.useFallbackTranslation(path)) {\n            // if the path is scope the fallback should be `scope/fallbackLang`;\n            const fallback = isScope\n                ? `${scope}/${this.firstFallbackLang}`\n                : this.firstFallbackLang;\n            const loaders = getFallbacksLoaders({\n                ...loadersOptions,\n                fallbackPath: fallback,\n            });\n            loadTranslation = forkJoin(loaders);\n        }\n        else {\n            const loader = resolveLoader(loadersOptions);\n            loadTranslation = from(loader);\n        }\n        const load$ = loadTranslation.pipe(retry(this.config.failedRetries), tap((translation) => {\n            if (Array.isArray(translation)) {\n                translation.forEach((t) => {\n                    this.handleSuccess(t.lang, t.translation);\n                    // Save the fallback in cache so we'll not create a redundant request\n                    if (t.lang !== path) {\n                        this.cache.set(t.lang, of({}));\n                    }\n                });\n                return;\n            }\n            this.handleSuccess(path, translation);\n        }), catchError((error) => {\n            if (!this.config.prodMode) {\n                console.error(`Error while trying to load \"${path}\"`, error);\n            }\n            return this.handleFailure(path, options);\n        }), shareReplay(1), takeUntilDestroyed(this.destroyRef));\n        this.cache.set(path, load$);\n        return load$;\n    }\n    /**\n     * Gets the instant translated value of a key\n     *\n     * @example\n     *\n     * translate<string>('hello')\n     * translate('hello', { value: 'value' })\n     * translate<string[]>(['hello', 'key'])\n     * translate('hello', { }, 'en')\n     * translate('scope.someKey', { }, 'en')\n     */\n    translate(key, params = {}, lang = this.getActiveLang()) {\n        if (!key)\n            return key;\n        const { scope, resolveLang } = this.resolveLangAndScope(lang);\n        if (Array.isArray(key)) {\n            return key.map((k) => this.translate(scope ? `${scope}.${k}` : k, params, resolveLang));\n        }\n        key = scope ? `${scope}.${key}` : key;\n        const translation = this.getTranslation(resolveLang);\n        const value = translation[key];\n        if (!value) {\n            return this._handleMissingKey(key, value, params);\n        }\n        return this.parser.transpile({\n            value,\n            params,\n            translation,\n            key,\n        });\n    }\n    /**\n     * Gets the translated value of a key as observable\n     *\n     * @example\n     *\n     * selectTranslate<string>('hello').subscribe(value => ...)\n     * selectTranslate<string>('hello', {}, 'es').subscribe(value => ...)\n     * selectTranslate<string>('hello', {}, 'todos').subscribe(value => ...)\n     * selectTranslate<string>('hello', {}, { scope: 'todos' }).subscribe(value => ...)\n     *\n     */\n    selectTranslate(key, params, lang, _isObject = false) {\n        let inlineLoader;\n        const load = (lang, options) => this.load(lang, options).pipe(map(() => _isObject\n            ? this.translateObject(key, params, lang)\n            : this.translate(key, params, lang)));\n        if (isNil(lang)) {\n            return this.langChanges$.pipe(switchMap((lang) => load(lang)));\n        }\n        lang = Array.isArray(lang) ? lang[0] : lang;\n        if (isScopeObject(lang)) {\n            // it's a scope object.\n            const providerScope = lang;\n            lang = providerScope.scope;\n            inlineLoader = resolveInlineLoader(providerScope, providerScope.scope);\n        }\n        lang = lang;\n        if (this.isLang(lang) || this.isScopeWithLang(lang)) {\n            return load(lang);\n        }\n        // it's a scope\n        const scope = lang;\n        return this.langChanges$.pipe(switchMap((lang) => load(`${scope}/${lang}`, { inlineLoader })));\n    }\n    /**\n     * Whether the scope with lang\n     *\n     * @example\n     *\n     * todos/en => true\n     * todos => false\n     */\n    isScopeWithLang(lang) {\n        return this.isLang(getLangFromScope(lang));\n    }\n    translateObject(key, params = {}, lang = this.getActiveLang()) {\n        if (isString(key) || Array.isArray(key)) {\n            const { resolveLang, scope } = this.resolveLangAndScope(lang);\n            if (Array.isArray(key)) {\n                return key.map((k) => this.translateObject(scope ? `${scope}.${k}` : k, params, resolveLang));\n            }\n            const translation = this.getTranslation(resolveLang);\n            key = scope ? `${scope}.${key}` : key;\n            const value = unflatten(this.getObjectByKey(translation, key));\n            /* If an empty object was returned we want to try and translate the key as a string and not an object */\n            return isEmpty(value)\n                ? this.translate(key, params, lang)\n                : this.parser.transpile({ value, params: params, translation, key });\n        }\n        const translations = [];\n        for (const [_key, _params] of this.getEntries(key)) {\n            translations.push(this.translateObject(_key, _params, lang));\n        }\n        return translations;\n    }\n    selectTranslateObject(key, params, lang) {\n        if (isString(key) || Array.isArray(key)) {\n            return this.selectTranslate(key, params, lang, true);\n        }\n        const [[firstKey, firstParams], ...rest] = this.getEntries(key);\n        /* In order to avoid subscribing multiple times to the load language event by calling selectTranslateObject for each pair,\n         * we listen to when the first key has been translated (the language is loaded) and translate the rest synchronously */\n        return this.selectTranslateObject(firstKey, firstParams, lang).pipe(map((value) => {\n            const translations = [value];\n            for (const [_key, _params] of rest) {\n                translations.push(this.translateObject(_key, _params, lang));\n            }\n            return translations;\n        }));\n    }\n    getTranslation(langOrScope) {\n        if (langOrScope) {\n            if (this.isLang(langOrScope)) {\n                return this.translations.get(langOrScope) || {};\n            }\n            else {\n                // This is a scope, build the scope value from the translation object\n                const { scope, resolveLang } = this.resolveLangAndScope(langOrScope);\n                const translation = this.translations.get(resolveLang) || {};\n                return this.getObjectByKey(translation, scope);\n            }\n        }\n        return this.translations;\n    }\n    /**\n     * Gets an object of translations for a given language\n     *\n     * @example\n     *\n     * selectTranslation().subscribe() - will return the current lang translation\n     * selectTranslation('es').subscribe()\n     * selectTranslation('admin-page').subscribe() - will return the current lang scope translation\n     * selectTranslation('admin-page/es').subscribe()\n     */\n    selectTranslation(lang) {\n        let language$ = this.langChanges$;\n        if (lang) {\n            const scopeLangSpecified = getLangFromScope(lang) !== lang;\n            if (this.isLang(lang) || scopeLangSpecified) {\n                language$ = of(lang);\n            }\n            else {\n                language$ = this.langChanges$.pipe(map((currentLang) => `${lang}/${currentLang}`));\n            }\n        }\n        return language$.pipe(switchMap((language) => this.load(language).pipe(map(() => this.getTranslation(language)))));\n    }\n    /**\n     * Sets or merge a given translation object to current lang\n     *\n     * @example\n     *\n     * setTranslation({ ... })\n     * setTranslation({ ... }, 'en')\n     * setTranslation({ ... }, 'es', { merge: false } )\n     * setTranslation({ ... }, 'todos/en', { merge: false } )\n     */\n    setTranslation(translation, lang = this.getActiveLang(), options = {}) {\n        const defaults = { merge: true, emitChange: true };\n        const mergedOptions = { ...defaults, ...options };\n        const scope = getScopeFromLang(lang);\n        /**\n         * If this isn't a scope we use the whole translation as is\n         * otherwise we need to flat the scope and use it\n         */\n        let flattenScopeOrTranslation = translation;\n        // Merged the scoped language into the active language\n        if (scope) {\n            const key = this.getMappedScope(scope);\n            flattenScopeOrTranslation = flatten({ [key]: translation });\n        }\n        const currentLang = scope ? getLangFromScope(lang) : lang;\n        const mergedTranslation = {\n            ...(mergedOptions.merge && this.getTranslation(currentLang)),\n            ...flattenScopeOrTranslation,\n        };\n        const flattenTranslation = this.config.flatten.aot\n            ? mergedTranslation\n            : flatten(mergedTranslation);\n        const withHook = this.interceptor.preSaveTranslation(flattenTranslation, currentLang);\n        this.translations.set(currentLang, withHook);\n        mergedOptions.emitChange && this.setActiveLang(this.getActiveLang());\n    }\n    /**\n     * Sets translation key with given value\n     *\n     * @example\n     *\n     * setTranslationKey('key', 'value')\n     * setTranslationKey('key.nested', 'value')\n     * setTranslationKey('key.nested', 'value', 'en')\n     * setTranslationKey('key.nested', 'value', 'en', { emitChange: false } )\n     */\n    setTranslationKey(key, value, options = {}) {\n        const lang = options.lang || this.getActiveLang();\n        const withHook = this.interceptor.preSaveTranslationKey(key, value, lang);\n        const newValue = {\n            [key]: withHook,\n        };\n        this.setTranslation(newValue, lang, { ...options, merge: true });\n    }\n    /**\n     * Sets the fallback lang for the currently active language\n     * @param fallbackLang\n     */\n    setFallbackLangForMissingTranslation({ fallbackLang, }) {\n        const lang = Array.isArray(fallbackLang) ? fallbackLang[0] : fallbackLang;\n        if (fallbackLang && this.useFallbackTranslation(lang)) {\n            this.firstFallbackLang = lang;\n        }\n    }\n    /**\n     * @internal\n     */\n    _handleMissingKey(key, value, params) {\n        if (this.config.missingHandler.allowEmpty && value === '') {\n            return '';\n        }\n        if (!this.isResolvedMissingOnce && this.useFallbackTranslation()) {\n            // We need to set it to true to prevent a loop\n            this.isResolvedMissingOnce = true;\n            const fallbackValue = this.translate(key, params, this.firstFallbackLang);\n            this.isResolvedMissingOnce = false;\n            return fallbackValue;\n        }\n        return this.missingHandler.handle(key, this.getMissingHandlerData(), params);\n    }\n    /**\n     * @internal\n     */\n    _isLangScoped(lang) {\n        return this.getAvailableLangsIds().indexOf(lang) === -1;\n    }\n    /**\n     * Checks if a given string is one of the specified available languages.\n     * @returns\n     * True if the given string is an available language.\n     * False if the given string is not an available language.\n     */\n    isLang(lang) {\n        return this.getAvailableLangsIds().indexOf(lang) !== -1;\n    }\n    /**\n     * @internal\n     *\n     * We always want to make sure the global lang is loaded\n     * before loading the scope since you can access both via the pipe/directive.\n     */\n    _loadDependencies(path, inlineLoader) {\n        const mainLang = getLangFromScope(path);\n        if (this._isLangScoped(path) && !this.isLoadedTranslation(mainLang)) {\n            return combineLatest([\n                this.load(mainLang),\n                this.load(path, { inlineLoader }),\n            ]);\n        }\n        return this.load(path, { inlineLoader });\n    }\n    /**\n     * @internal\n     */\n    _completeScopeWithLang(langOrScope) {\n        if (this._isLangScoped(langOrScope) &&\n            !this.isLang(getLangFromScope(langOrScope))) {\n            return `${langOrScope}/${this.getActiveLang()}`;\n        }\n        return langOrScope;\n    }\n    /**\n     * @internal\n     */\n    _setScopeAlias(scope, alias) {\n        if (!this.config.scopeMapping) {\n            this.config.scopeMapping = {};\n        }\n        this.config.scopeMapping[scope] = alias;\n    }\n    isLoadedTranslation(lang) {\n        return size(this.getTranslation(lang));\n    }\n    getAvailableLangsIds() {\n        const first = this.getAvailableLangs()[0];\n        if (isString(first)) {\n            return this.getAvailableLangs();\n        }\n        return this.getAvailableLangs().map((l) => l.id);\n    }\n    getMissingHandlerData() {\n        return {\n            ...this.config,\n            activeLang: this.getActiveLang(),\n            availableLangs: this.availableLangs,\n            defaultLang: this.defaultLang,\n        };\n    }\n    /**\n     * Use a fallback translation set for missing keys of the primary language\n     * This is unrelated to the fallback language (which changes the active language)\n     */\n    useFallbackTranslation(lang) {\n        return (this.config.missingHandler.useFallbackTranslation &&\n            lang !== this.firstFallbackLang);\n    }\n    handleSuccess(lang, translation) {\n        this.setTranslation(translation, lang, { emitChange: false });\n        this.events.next({\n            wasFailure: !!this.failedLangs.size,\n            type: 'translationLoadSuccess',\n            payload: getEventPayload(lang),\n        });\n        this.failedLangs.forEach((l) => this.cache.delete(l));\n        this.failedLangs.clear();\n    }\n    handleFailure(lang, loadOptions) {\n        // When starting to load a first choice language, initialize\n        // the failed counter and resolve the fallback langs.\n        if (isNil(loadOptions.failedCounter)) {\n            loadOptions.failedCounter = 0;\n            if (!loadOptions.fallbackLangs) {\n                loadOptions.fallbackLangs = this.fallbackStrategy.getNextLangs(lang);\n            }\n        }\n        const splitted = lang.split('/');\n        const fallbacks = loadOptions.fallbackLangs;\n        const nextLang = fallbacks[loadOptions.failedCounter];\n        this.failedLangs.add(lang);\n        // This handles the case where a loaded fallback language is requested again\n        if (this.cache.has(nextLang)) {\n            this.handleSuccess(nextLang, this.getTranslation(nextLang));\n            return EMPTY;\n        }\n        const isFallbackLang = nextLang === splitted[splitted.length - 1];\n        if (!nextLang || isFallbackLang) {\n            let msg = `Unable to load translation and all the fallback languages`;\n            if (splitted.length > 1) {\n                msg += `, did you misspelled the scope name?`;\n            }\n            throw new Error(msg);\n        }\n        let resolveLang = nextLang;\n        // if it's scoped lang\n        if (splitted.length > 1) {\n            // We need to resolve it to:\n            // todos/langNotExists => todos/nextLang\n            splitted[splitted.length - 1] = nextLang;\n            resolveLang = splitted.join('/');\n        }\n        loadOptions.failedCounter++;\n        this.events.next({\n            type: 'translationLoadFailure',\n            payload: getEventPayload(lang),\n        });\n        return this.load(resolveLang, loadOptions);\n    }\n    getMappedScope(scope) {\n        const { scopeMapping = {}, scopes = { keepCasing: false } } = this.config;\n        return (scopeMapping[scope] || (scopes.keepCasing ? scope : toCamelCase(scope)));\n    }\n    /**\n     * If lang is scope we need to check the following cases:\n     * todos/es => in this case we should take `es` as lang\n     * todos => in this case we should set the active lang as lang\n     */\n    resolveLangAndScope(lang) {\n        let resolveLang = lang;\n        let scope;\n        if (this._isLangScoped(lang)) {\n            // en for example\n            const langFromScope = getLangFromScope(lang);\n            // en is lang\n            const hasLang = this.isLang(langFromScope);\n            // take en\n            resolveLang = hasLang ? langFromScope : this.getActiveLang();\n            // find the scope\n            scope = this.getMappedScope(hasLang ? getScopeFromLang(lang) : lang);\n        }\n        return { scope, resolveLang };\n    }\n    getObjectByKey(translation, key) {\n        const result = {};\n        const prefix = `${key}.`;\n        for (const currentKey in translation) {\n            if (currentKey.startsWith(prefix)) {\n                result[currentKey.replace(prefix, '')] = translation[currentKey];\n            }\n        }\n        return result;\n    }\n    getEntries(key) {\n        return key instanceof Map ? key.entries() : Object.entries(key);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: TranslocoService, deps: [{ token: TRANSLOCO_LOADER, optional: true }, { token: TRANSLOCO_TRANSPILER }, { token: TRANSLOCO_MISSING_HANDLER }, { token: TRANSLOCO_INTERCEPTOR }, { token: TRANSLOCO_CONFIG }, { token: TRANSLOCO_FALLBACK_STRATEGY }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: TranslocoService, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: TranslocoService, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [TRANSLOCO_LOADER]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [TRANSLOCO_TRANSPILER]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [TRANSLOCO_MISSING_HANDLER]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [TRANSLOCO_INTERCEPTOR]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [TRANSLOCO_CONFIG]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [TRANSLOCO_FALLBACK_STRATEGY]\n                }] }] });\n\nclass TranslocoLoaderComponent {\n    html;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: TranslocoLoaderComponent, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"18.2.9\", type: TranslocoLoaderComponent, isStandalone: true, selector: \"ng-component\", inputs: { html: \"html\" }, ngImport: i0, template: `\n    <div class=\"transloco-loader-template\" [innerHTML]=\"html\"></div>\n  `, isInline: true });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: TranslocoLoaderComponent, decorators: [{\n            type: Component,\n            args: [{\n                    template: `\n    <div class=\"transloco-loader-template\" [innerHTML]=\"html\"></div>\n  `,\n                    standalone: true,\n                }]\n        }], propDecorators: { html: [{\n                type: Input\n            }] } });\n\nclass TemplateHandler {\n    view;\n    vcr;\n    constructor(view, vcr) {\n        this.view = view;\n        this.vcr = vcr;\n    }\n    attachView() {\n        if (this.view instanceof TemplateRef) {\n            this.vcr.createEmbeddedView(this.view);\n        }\n        else if (isString(this.view)) {\n            const componentRef = this.vcr.createComponent(TranslocoLoaderComponent);\n            componentRef.instance.html = this.view;\n            componentRef.hostView.detectChanges();\n        }\n        else {\n            this.vcr.createComponent(this.view);\n        }\n    }\n    detachView() {\n        this.vcr.clear();\n    }\n}\n\nconst TRANSLOCO_LANG = new InjectionToken(ngDevMode ? 'TRANSLOCO_LANG' : '');\n\nconst TRANSLOCO_LOADING_TEMPLATE = new InjectionToken(ngDevMode ? 'TRANSLOCO_LOADING_TEMPLATE' : '');\n\nconst TRANSLOCO_SCOPE = new InjectionToken(ngDevMode ? 'TRANSLOCO_SCOPE' : '');\n\nclass LangResolver {\n    initialized = false;\n    // inline => provider => active\n    resolve({ inline, provider, active }) {\n        let lang = active;\n        /**\n         * When the user changes the lang we need to update\n         * the view. Otherwise, the lang will remain the inline/provided lang\n         */\n        if (this.initialized) {\n            lang = active;\n            return lang;\n        }\n        if (provider) {\n            const [, extracted] = getPipeValue(provider, 'static');\n            lang = extracted;\n        }\n        if (inline) {\n            const [, extracted] = getPipeValue(inline, 'static');\n            lang = extracted;\n        }\n        this.initialized = true;\n        return lang;\n    }\n    /**\n     *\n     * Resolve the lang\n     *\n     * @example\n     *\n     * resolveLangBasedOnScope('todos/en') => en\n     * resolveLangBasedOnScope('en') => en\n     *\n     */\n    resolveLangBasedOnScope(lang) {\n        const scope = getScopeFromLang(lang);\n        return scope ? getLangFromScope(lang) : lang;\n    }\n    /**\n     *\n     * Resolve the lang path for loading\n     *\n     * @example\n     *\n     * resolveLangPath('todos', 'en') => todos/en\n     * resolveLangPath('en') => en\n     *\n     */\n    resolveLangPath(lang, scope) {\n        return scope ? `${scope}/${lang}` : lang;\n    }\n}\n\nclass ScopeResolver {\n    service;\n    constructor(service) {\n        this.service = service;\n    }\n    // inline => provider\n    resolve(params) {\n        const { inline, provider } = params;\n        if (inline) {\n            return inline;\n        }\n        if (provider) {\n            if (isScopeObject(provider)) {\n                const { scope, alias = this.service.config.scopes.keepCasing\n                    ? scope\n                    : toCamelCase(scope), } = provider;\n                this.service._setScopeAlias(scope, alias);\n                return scope;\n            }\n            return provider;\n        }\n        return undefined;\n    }\n}\n\nclass TranslocoDirective {\n    destroyRef = inject(DestroyRef);\n    service = inject(TranslocoService);\n    tpl = inject(TemplateRef, {\n        optional: true,\n    });\n    providerLang = inject(TRANSLOCO_LANG, { optional: true });\n    providerScope = inject(TRANSLOCO_SCOPE, { optional: true });\n    providedLoadingTpl = inject(TRANSLOCO_LOADING_TEMPLATE, {\n        optional: true,\n    });\n    cdr = inject(ChangeDetectorRef);\n    host = inject(ElementRef);\n    vcr = inject(ViewContainerRef);\n    renderer = inject(Renderer2);\n    view;\n    memo = new Map();\n    key;\n    params = {};\n    inlineScope;\n    /** @deprecated use prefix instead, will be removed in Transloco v8 */\n    inlineRead;\n    prefix;\n    inlineLang;\n    inlineTpl;\n    currentLang;\n    loaderTplHandler;\n    // Whether we already rendered the view once\n    initialized = false;\n    path;\n    langResolver = new LangResolver();\n    scopeResolver = new ScopeResolver(this.service);\n    strategy = this.tpl === null ? 'attribute' : 'structural';\n    static ngTemplateContextGuard(dir, ctx) {\n        return true;\n    }\n    ngOnInit() {\n        const listenToLangChange = shouldListenToLangChanges(this.service, this.providerLang || this.inlineLang);\n        this.service.langChanges$\n            .pipe(switchMap((activeLang) => {\n            const lang = this.langResolver.resolve({\n                inline: this.inlineLang,\n                provider: this.providerLang,\n                active: activeLang,\n            });\n            return Array.isArray(this.providerScope)\n                ? forkJoin(this.providerScope.map((providerScope) => this.resolveScope(lang, providerScope)))\n                : this.resolveScope(lang, this.providerScope);\n        }), listenOrNotOperator(listenToLangChange), takeUntilDestroyed(this.destroyRef))\n            .subscribe(() => {\n            this.currentLang = this.langResolver.resolveLangBasedOnScope(this.path);\n            this.strategy === 'attribute'\n                ? this.attributeStrategy()\n                : this.structuralStrategy(this.currentLang, this.prefix || this.inlineRead);\n            this.cdr.markForCheck();\n            this.initialized = true;\n        });\n        if (!this.initialized) {\n            const loadingContent = this.resolveLoadingContent();\n            if (loadingContent) {\n                this.loaderTplHandler = new TemplateHandler(loadingContent, this.vcr);\n                this.loaderTplHandler.attachView();\n            }\n        }\n    }\n    ngOnChanges(changes) {\n        // We need to support dynamic keys/params, so if this is not the first change CD cycle\n        // we need to run the function again in order to update the value\n        if (this.strategy === 'attribute') {\n            const notInit = Object.keys(changes).some((v) => !changes[v].firstChange);\n            notInit && this.attributeStrategy();\n        }\n    }\n    attributeStrategy() {\n        this.detachLoader();\n        this.renderer.setProperty(this.host.nativeElement, 'innerText', this.service.translate(this.key, this.params, this.currentLang));\n    }\n    structuralStrategy(lang, prefix) {\n        this.memo.clear();\n        const translateFn = this.getTranslateFn(lang, prefix);\n        if (this.view) {\n            // when the lang changes we need to change the reference so Angular will update the view\n            this.view.context['$implicit'] = translateFn;\n            this.view.context['currentLang'] = this.currentLang;\n        }\n        else {\n            this.detachLoader();\n            this.view = this.vcr.createEmbeddedView(this.tpl, {\n                $implicit: translateFn,\n                currentLang: this.currentLang,\n            });\n        }\n    }\n    getTranslateFn(lang, prefix) {\n        return (key, params) => {\n            const withPrefix = prefix ? `${prefix}.${key}` : key;\n            const memoKey = params\n                ? `${withPrefix}${JSON.stringify(params)}`\n                : withPrefix;\n            if (!this.memo.has(memoKey)) {\n                this.memo.set(memoKey, this.service.translate(withPrefix, params, lang));\n            }\n            return this.memo.get(memoKey);\n        };\n    }\n    resolveLoadingContent() {\n        return this.inlineTpl || this.providedLoadingTpl;\n    }\n    ngOnDestroy() {\n        this.memo.clear();\n    }\n    detachLoader() {\n        this.loaderTplHandler?.detachView();\n    }\n    resolveScope(lang, providerScope) {\n        const resolvedScope = this.scopeResolver.resolve({\n            inline: this.inlineScope,\n            provider: providerScope,\n        });\n        this.path = this.langResolver.resolveLangPath(lang, resolvedScope);\n        const inlineLoader = resolveInlineLoader(providerScope, resolvedScope);\n        return this.service._loadDependencies(this.path, inlineLoader);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: TranslocoDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"18.2.9\", type: TranslocoDirective, isStandalone: true, selector: \"[transloco]\", inputs: { key: [\"transloco\", \"key\"], params: [\"translocoParams\", \"params\"], inlineScope: [\"translocoScope\", \"inlineScope\"], inlineRead: [\"translocoRead\", \"inlineRead\"], prefix: [\"translocoPrefix\", \"prefix\"], inlineLang: [\"translocoLang\", \"inlineLang\"], inlineTpl: [\"translocoLoadingTpl\", \"inlineTpl\"] }, usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: TranslocoDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[transloco]',\n                    standalone: true,\n                }]\n        }], propDecorators: { key: [{\n                type: Input,\n                args: ['transloco']\n            }], params: [{\n                type: Input,\n                args: ['translocoParams']\n            }], inlineScope: [{\n                type: Input,\n                args: ['translocoScope']\n            }], inlineRead: [{\n                type: Input,\n                args: ['translocoRead']\n            }], prefix: [{\n                type: Input,\n                args: ['translocoPrefix']\n            }], inlineLang: [{\n                type: Input,\n                args: ['translocoLang']\n            }], inlineTpl: [{\n                type: Input,\n                args: ['translocoLoadingTpl']\n            }] } });\n\nclass TranslocoPipe {\n    service;\n    providerScope;\n    providerLang;\n    cdr;\n    subscription = null;\n    lastValue = '';\n    lastKey;\n    path;\n    langResolver = new LangResolver();\n    scopeResolver;\n    constructor(service, providerScope, providerLang, cdr) {\n        this.service = service;\n        this.providerScope = providerScope;\n        this.providerLang = providerLang;\n        this.cdr = cdr;\n        this.scopeResolver = new ScopeResolver(this.service);\n    }\n    // null is for handling strict mode + async pipe types https://github.com/jsverse/transloco/issues/311\n    // null is for handling strict mode + optional chaining types https://github.com/jsverse/transloco/issues/488\n    transform(key, params, inlineLang) {\n        if (!key) {\n            return key;\n        }\n        const keyName = params ? `${key}${JSON.stringify(params)}` : key;\n        if (keyName === this.lastKey) {\n            return this.lastValue;\n        }\n        this.lastKey = keyName;\n        this.subscription?.unsubscribe();\n        const listenToLangChange = shouldListenToLangChanges(this.service, this.providerLang || inlineLang);\n        this.subscription = this.service.langChanges$\n            .pipe(switchMap((activeLang) => {\n            const lang = this.langResolver.resolve({\n                inline: inlineLang,\n                provider: this.providerLang,\n                active: activeLang,\n            });\n            return Array.isArray(this.providerScope)\n                ? forkJoin(this.providerScope.map((providerScope) => this.resolveScope(lang, providerScope)))\n                : this.resolveScope(lang, this.providerScope);\n        }), listenOrNotOperator(listenToLangChange))\n            .subscribe(() => this.updateValue(key, params));\n        return this.lastValue;\n    }\n    ngOnDestroy() {\n        this.subscription?.unsubscribe();\n        // Caretaker note: it's important to clean up references to subscriptions since they save the `next`\n        // callback within its `destination` property, preventing classes from being GC'd.\n        this.subscription = null;\n    }\n    updateValue(key, params) {\n        const lang = this.langResolver.resolveLangBasedOnScope(this.path);\n        this.lastValue = this.service.translate(key, params, lang);\n        this.cdr.markForCheck();\n    }\n    resolveScope(lang, providerScope) {\n        const resolvedScope = this.scopeResolver.resolve({\n            inline: undefined,\n            provider: providerScope,\n        });\n        this.path = this.langResolver.resolveLangPath(lang, resolvedScope);\n        const inlineLoader = resolveInlineLoader(providerScope, resolvedScope);\n        return this.service._loadDependencies(this.path, inlineLoader);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: TranslocoPipe, deps: [{ token: TranslocoService }, { token: TRANSLOCO_SCOPE, optional: true }, { token: TRANSLOCO_LANG, optional: true }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Pipe });\n    static ɵpipe = i0.ɵɵngDeclarePipe({ minVersion: \"14.0.0\", version: \"18.2.9\", ngImport: i0, type: TranslocoPipe, isStandalone: true, name: \"transloco\", pure: false });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: TranslocoPipe, decorators: [{\n            type: Pipe,\n            args: [{\n                    name: 'transloco',\n                    pure: false,\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: TranslocoService }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [TRANSLOCO_SCOPE]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [TRANSLOCO_LANG]\n                }] }, { type: i0.ChangeDetectorRef }] });\n\nconst decl = [TranslocoDirective, TranslocoPipe];\nclass TranslocoModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: TranslocoModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.2.9\", ngImport: i0, type: TranslocoModule, imports: [TranslocoDirective, TranslocoPipe], exports: [TranslocoDirective, TranslocoPipe] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: TranslocoModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: TranslocoModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: decl,\n                    exports: decl,\n                }]\n        }] });\n\nfunction provideTransloco(options) {\n    const providers = [\n        provideTranslocoTranspiler(DefaultTranspiler),\n        provideTranslocoMissingHandler(DefaultMissingHandler),\n        provideTranslocoInterceptor(DefaultInterceptor),\n        provideTranslocoFallbackStrategy(DefaultFallbackStrategy),\n    ];\n    if (options.config) {\n        providers.push(provideTranslocoConfig(options.config));\n    }\n    if (options.loader) {\n        providers.push(provideTranslocoLoader(options.loader));\n    }\n    return providers;\n}\nfunction provideTranslocoConfig(config) {\n    return makeEnvironmentProviders([\n        {\n            provide: TRANSLOCO_CONFIG,\n            useValue: translocoConfig(config),\n        },\n    ]);\n}\nfunction provideTranslocoLoader(loader) {\n    return makeEnvironmentProviders([\n        { provide: TRANSLOCO_LOADER, useClass: loader },\n    ]);\n}\nfunction provideTranslocoScope(...scopes) {\n    return scopes.map((scope) => ({\n        provide: TRANSLOCO_SCOPE,\n        useValue: scope,\n        multi: true,\n    }));\n}\nfunction provideTranslocoLoadingTpl(content) {\n    return {\n        provide: TRANSLOCO_LOADING_TEMPLATE,\n        useValue: content,\n    };\n}\nfunction provideTranslocoTranspiler(transpiler) {\n    return makeEnvironmentProviders([\n        {\n            provide: TRANSLOCO_TRANSPILER,\n            useClass: transpiler,\n            deps: [TRANSLOCO_CONFIG],\n        },\n    ]);\n}\nfunction provideTranslocoFallbackStrategy(strategy) {\n    return makeEnvironmentProviders([\n        {\n            provide: TRANSLOCO_FALLBACK_STRATEGY,\n            useClass: strategy,\n            deps: [TRANSLOCO_CONFIG],\n        },\n    ]);\n}\nfunction provideTranslocoMissingHandler(handler) {\n    return makeEnvironmentProviders([\n        {\n            provide: TRANSLOCO_MISSING_HANDLER,\n            useClass: handler,\n        },\n    ]);\n}\nfunction provideTranslocoInterceptor(interceptor) {\n    return makeEnvironmentProviders([\n        {\n            provide: TRANSLOCO_INTERCEPTOR,\n            useClass: interceptor,\n        },\n    ]);\n}\nfunction provideTranslocoLang(lang) {\n    return {\n        provide: TRANSLOCO_LANG,\n        useValue: lang,\n    };\n}\n\nconst TRANSLOCO_TEST_LANGS = new InjectionToken('TRANSLOCO_TEST_LANGS - Available testing languages');\nconst TRANSLOCO_TEST_OPTIONS = new InjectionToken('TRANSLOCO_TEST_OPTIONS - Testing options');\nclass TestingLoader {\n    langs;\n    constructor(langs) {\n        this.langs = langs;\n    }\n    getTranslation(lang) {\n        return of(this.langs[lang]);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: TestingLoader, deps: [{ token: TRANSLOCO_TEST_LANGS }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: TestingLoader });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: TestingLoader, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [TRANSLOCO_TEST_LANGS]\n                }] }] });\nfunction initTranslocoService(service, langs = {}, options) {\n    const preloadAllLangs = () => options.preloadLangs\n        ? Promise.all(Object.keys(langs).map((lang) => service.load(lang).toPromise()))\n        : Promise.resolve();\n    return preloadAllLangs;\n}\nclass TranslocoTestingModule {\n    static forRoot(options) {\n        return {\n            ngModule: TranslocoTestingModule,\n            providers: [\n                provideTransloco({\n                    loader: TestingLoader,\n                    config: {\n                        prodMode: true,\n                        missingHandler: { logMissingKey: false },\n                        ...options.translocoConfig,\n                    },\n                }),\n                {\n                    provide: TRANSLOCO_TEST_LANGS,\n                    useValue: options.langs,\n                },\n                {\n                    provide: TRANSLOCO_TEST_OPTIONS,\n                    useValue: options,\n                },\n                {\n                    provide: APP_INITIALIZER,\n                    useFactory: initTranslocoService,\n                    deps: [\n                        TranslocoService,\n                        TRANSLOCO_TEST_LANGS,\n                        TRANSLOCO_TEST_OPTIONS,\n                    ],\n                    multi: true,\n                },\n            ],\n        };\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: TranslocoTestingModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.2.9\", ngImport: i0, type: TranslocoTestingModule, exports: [TranslocoModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: TranslocoTestingModule, imports: [TranslocoModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.9\", ngImport: i0, type: TranslocoTestingModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [TranslocoModule],\n                }]\n        }] });\n\n/**\n * Returns the language code name from the browser, e.g. \"en\"\n */\nfunction getBrowserLang() {\n    let browserLang = getBrowserCultureLang();\n    if (!browserLang || !isBrowser()) {\n        return undefined;\n    }\n    if (browserLang.indexOf('-') !== -1) {\n        browserLang = browserLang.split('-')[0];\n    }\n    if (browserLang.indexOf('_') !== -1) {\n        browserLang = browserLang.split('_')[0];\n    }\n    return browserLang;\n}\n/**\n * Returns the culture language code name from the browser, e.g. \"en-US\"\n */\nfunction getBrowserCultureLang() {\n    if (!isBrowser()) {\n        return '';\n    }\n    const navigator = window.navigator;\n    return navigator.languages?.[0] ?? navigator.language;\n}\n\n/**\n * Gets the translated value of a key as Signal\n *\n * @example\n * text = translateSignal('hello');\n * textList = translateSignal(['green', 'blue']);\n * textVar = translateSignal('hello', { variable: 'world' });\n * textSpanish = translateSignal('hello', { variable: 'world' }, 'es');\n * textTodosScope = translateSignal('hello', { variable: 'world' }, { scope: 'todos' });\n *\n * @example\n * dynamicKey = signal('hello');\n * dynamicParam = signal({ variable: 'world' });\n * text = translateSignal(this.dynamicKey, this.dynamicParam);\n *\n */\nfunction translateSignal(key, params, lang, injector) {\n    if (!injector) {\n        assertInInjectionContext(translateSignal);\n    }\n    injector ??= inject(Injector);\n    const result = runInInjectionContext(injector, () => {\n        const service = inject(TranslocoService);\n        const scope = resolveScope(lang);\n        return toObservable(computerKeysAndParams(key, params)).pipe(switchMap((dynamic) => service.selectTranslate(dynamic.key, dynamic.params, scope)));\n    });\n    return toSignal(result, { initialValue: Array.isArray(key) ? [''] : '' });\n}\n/**\n * Gets the translated object of a key as Signal\n *\n * @example\n * object = translateObjectSignal('nested.object');\n * title = object().title;\n *\n * @example\n * dynamicKey = signal('nested.object');\n * dynamicParam = signal({ variable: 'world' });\n * object = translateObjectSignal(this.dynamicKey, this.dynamicParam);\n */\nfunction translateObjectSignal(key, params, lang, injector) {\n    if (!injector) {\n        assertInInjectionContext(translateObjectSignal);\n    }\n    injector ??= inject(Injector);\n    const result = runInInjectionContext(injector, () => {\n        const service = inject(TranslocoService);\n        const scope = resolveScope(lang);\n        return toObservable(computerKeysAndParams(key, params)).pipe(switchMap((dynamic) => service.selectTranslateObject(dynamic.key, dynamic.params, scope)));\n    });\n    return toSignal(result, { initialValue: Array.isArray(key) ? [] : {} });\n}\nfunction computerParams(params) {\n    if (isSignal(params)) {\n        return computed(() => params());\n    }\n    return computed(() => {\n        return Object.entries(params).reduce((acc, [key, value]) => {\n            acc[key] = isSignal(value) ? value() : value;\n            return acc;\n        }, {});\n    });\n}\nfunction computerKeys(keys) {\n    if (Array.isArray(keys)) {\n        return computed(() => keys.map((key) => (isSignal(key) ? key() : key)));\n    }\n    return computed(() => keys());\n}\nfunction isSignalKey(key) {\n    return Array.isArray(key) ? key.some(isSignal) : isSignal(key);\n}\nfunction isSignalParams(params) {\n    return params\n        ? isSignal(params) || Object.values(params).some(isSignal)\n        : false;\n}\nfunction computerKeysAndParams(key, params) {\n    const computedKeys = isSignalKey(key)\n        ? computerKeys(key)\n        : computed(() => key);\n    const computedParams = isSignalParams(params)\n        ? computerParams(params)\n        : computed(() => params);\n    return computed(() => ({ key: computedKeys(), params: computedParams() }));\n}\nfunction resolveScope(scope) {\n    if (typeof scope === 'undefined' || scope === '') {\n        const translocoScope = inject(TRANSLOCO_SCOPE, { optional: true });\n        return translocoScope ?? undefined;\n    }\n    return scope;\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DefaultFallbackStrategy, DefaultInterceptor, DefaultMissingHandler, DefaultTranspiler, FunctionalTranspiler, TRANSLOCO_CONFIG, TRANSLOCO_FALLBACK_STRATEGY, TRANSLOCO_INTERCEPTOR, TRANSLOCO_LANG, TRANSLOCO_LOADER, TRANSLOCO_LOADING_TEMPLATE, TRANSLOCO_MISSING_HANDLER, TRANSLOCO_SCOPE, TRANSLOCO_TRANSPILER, TestingLoader, TranslocoDirective, TranslocoModule, TranslocoPipe, TranslocoService, TranslocoTestingModule, coerceArray, defaultConfig, flatten, getBrowserCultureLang, getBrowserLang, getFunctionArgs, getLangFromScope, getPipeValue, getScopeFromLang, getValue, hasInlineLoader, isBrowser, isDefined, isEmpty, isFunction, isNil, isNumber, isObject, isScopeObject, isString, provideTransloco, provideTranslocoConfig, provideTranslocoFallbackStrategy, provideTranslocoInterceptor, provideTranslocoLang, provideTranslocoLoader, provideTranslocoLoadingTpl, provideTranslocoMissingHandler, provideTranslocoScope, provideTranslocoTranspiler, setValue, size, toCamelCase, toNumber, translate, translateObject, translateObjectSignal, translateSignal, translocoConfig, unflatten };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,EAAEC,WAAW,EAAEC,iBAAiB,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,SAAS,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,wBAAwB,EAAEC,eAAe,EAAEC,wBAAwB,EAAEC,qBAAqB,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,eAAe;AAChV,SAASC,EAAE,EAAEC,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,OAAO,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,GAAG,EAAEC,UAAU,EAAEC,WAAW,EAAEC,SAAS,EAAEC,aAAa,EAAEC,KAAK,QAAQ,MAAM;AACpJ,SAASC,kBAAkB,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,4BAA4B;AAEvF,MAAMC,aAAa,CAAC;EAChBC,YAAY;EACZC,WAAWA,CAACD,YAAY,EAAE;IACtB,IAAI,CAACA,YAAY,GAAGA,YAAY;EACpC;EACAE,cAAcA,CAACC,IAAI,EAAE;IACjB,OAAOrB,EAAE,CAAC,IAAI,CAACkB,YAAY,CAACI,GAAG,CAACD,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;EAChD;AACJ;AACA,MAAME,gBAAgB,GAAG,IAAI9C,cAAc,CAAC+C,SAAS,GAAG,kBAAkB,GAAG,EAAE,CAAC;AAEhF,SAASC,QAAQA,CAACC,GAAG,EAAEC,IAAI,EAAE;EACzB,IAAI,CAACD,GAAG,EAAE;IACN,OAAOA,GAAG;EACd;EACA;EACA,IAAIE,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACL,GAAG,EAAEC,IAAI,CAAC,EAAE;IACjD,OAAOD,GAAG,CAACC,IAAI,CAAC;EACpB;EACA,OAAOA,IAAI,CAACK,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC,EAAET,GAAG,CAAC;AACxD;AACA,SAASU,QAAQA,CAACV,GAAG,EAAEW,IAAI,EAAEC,GAAG,EAAE;EAC9BZ,GAAG,GAAG;IAAE,GAAGA;EAAI,CAAC;EAChB,MAAMM,KAAK,GAAGK,IAAI,CAACL,KAAK,CAAC,GAAG,CAAC;EAC7B,MAAMO,SAAS,GAAGP,KAAK,CAACQ,MAAM,GAAG,CAAC;EAClCR,KAAK,CAACC,MAAM,CAAC,CAACQ,GAAG,EAAEC,IAAI,EAAEC,KAAK,KAAK;IAC/B,IAAIA,KAAK,KAAKJ,SAAS,EAAE;MACrBE,GAAG,CAACC,IAAI,CAAC,GAAGJ,GAAG;IACnB,CAAC,MACI;MACDG,GAAG,CAACC,IAAI,CAAC,GAAGE,KAAK,CAACC,OAAO,CAACJ,GAAG,CAACC,IAAI,CAAC,CAAC,GAC9BD,GAAG,CAACC,IAAI,CAAC,CAACI,KAAK,CAAC,CAAC,GACjB;QAAE,GAAGL,GAAG,CAACC,IAAI;MAAE,CAAC;IAC1B;IACA,OAAOD,GAAG,IAAIA,GAAG,CAACC,IAAI,CAAC;EAC3B,CAAC,EAAEhB,GAAG,CAAC;EACP,OAAOA,GAAG;AACd;AACA,SAASqB,IAAIA,CAACC,UAAU,EAAE;EACtB,IAAI,CAACA,UAAU,EAAE;IACb,OAAO,CAAC;EACZ;EACA,IAAIJ,KAAK,CAACC,OAAO,CAACG,UAAU,CAAC,EAAE;IAC3B,OAAOA,UAAU,CAACR,MAAM;EAC5B;EACA,IAAIS,QAAQ,CAACD,UAAU,CAAC,EAAE;IACtB,OAAOpB,MAAM,CAACsB,IAAI,CAACF,UAAU,CAAC,CAACR,MAAM;EACzC;EACA,OAAOQ,UAAU,GAAGA,UAAU,CAACR,MAAM,GAAG,CAAC;AAC7C;AACA,SAASW,OAAOA,CAACH,UAAU,EAAE;EACzB,OAAOD,IAAI,CAACC,UAAU,CAAC,KAAK,CAAC;AACjC;AACA,SAASI,UAAUA,CAACd,GAAG,EAAE;EACrB,OAAO,OAAOA,GAAG,KAAK,UAAU;AACpC;AACA,SAASe,QAAQA,CAACf,GAAG,EAAE;EACnB,OAAO,OAAOA,GAAG,KAAK,QAAQ;AAClC;AACA,SAASgB,QAAQA,CAAChB,GAAG,EAAE;EACnB,OAAO,OAAOA,GAAG,KAAK,QAAQ;AAClC;AACA,SAASW,QAAQA,CAACM,IAAI,EAAE;EACpB,OAAO,CAAC,CAACA,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,CAACX,KAAK,CAACC,OAAO,CAACU,IAAI,CAAC;AACrE;AACA,SAASC,WAAWA,CAACC,KAAK,EAAE;EACxB,OAAOb,KAAK,CAACC,OAAO,CAACY,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,GAAG,EAAE;EACtB,OAAOA,GAAG,CACLC,OAAO,CAAC,qBAAqB,EAAE,CAACC,IAAI,EAAElB,KAAK,KAAKA,KAAK,IAAI,CAAC,GAAGkB,IAAI,CAACC,WAAW,CAAC,CAAC,GAAGD,IAAI,CAACE,WAAW,CAAC,CAAC,CAAC,CACrGH,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;AACnC;AACA,SAASI,SAASA,CAAA,EAAG;EACjB,OAAO,OAAOC,MAAM,KAAK,WAAW;AACxC;AACA,SAASC,KAAKA,CAACT,KAAK,EAAE;EAClB,OAAOA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKU,SAAS;AAChD;AACA,SAASC,SAASA,CAACX,KAAK,EAAE;EACtB,OAAOS,KAAK,CAACT,KAAK,CAAC,KAAK,KAAK;AACjC;AACA,SAASY,QAAQA,CAACZ,KAAK,EAAE;EACrB,IAAIH,QAAQ,CAACG,KAAK,CAAC,EACf,OAAOA,KAAK;EAChB,IAAIJ,QAAQ,CAACI,KAAK,CAAC,IAAI,CAACa,KAAK,CAACC,MAAM,CAACd,KAAK,CAAC,GAAGe,UAAU,CAACf,KAAK,CAAC,CAAC,EAAE;IAC9D,OAAOc,MAAM,CAACd,KAAK,CAAC;EACxB;EACA,OAAO,IAAI;AACf;AACA,SAASgB,aAAaA,CAAClB,IAAI,EAAE;EACzB,OAAOA,IAAI,IAAI,OAAOA,IAAI,CAACmB,KAAK,KAAK,QAAQ;AACjD;AACA,SAASC,eAAeA,CAACpB,IAAI,EAAE;EAC3B,OAAOA,IAAI,IAAIN,QAAQ,CAACM,IAAI,CAACqB,MAAM,CAAC;AACxC;AACA,SAASC,OAAOA,CAACnD,GAAG,EAAE;EAClB,MAAMoD,MAAM,GAAG,CAAC,CAAC;EACjB,SAASC,OAAOA,CAACC,IAAI,EAAE3C,IAAI,EAAE;IACzB,IAAI2C,IAAI,KAAK,IAAI,EAAE;MACfF,MAAM,CAACzC,IAAI,CAAC,GAAG,IAAI;IACvB,CAAC,MACI,IAAIY,QAAQ,CAAC+B,IAAI,CAAC,EAAE;MACrB,KAAK,MAAM,CAACC,GAAG,EAAExB,KAAK,CAAC,IAAI7B,MAAM,CAACsD,OAAO,CAACF,IAAI,CAAC,EAAE;QAC7CD,OAAO,CAACtB,KAAK,EAAEpB,IAAI,GAAG,GAAGA,IAAI,IAAI4C,GAAG,EAAE,GAAGA,GAAG,CAAC;MACjD;IACJ,CAAC,MACI;MACDH,MAAM,CAACzC,IAAI,CAAC,GAAG2C,IAAI;IACvB;EACJ;EACAD,OAAO,CAACrD,GAAG,EAAE,EAAE,CAAC;EAChB,OAAOoD,MAAM;AACjB;AACA,SAASK,SAASA,CAACzD,GAAG,EAAE;EACpB,MAAMoD,MAAM,GAAG,CAAC,CAAC;EACjB,KAAK,MAAM,CAACG,GAAG,EAAExB,KAAK,CAAC,IAAI7B,MAAM,CAACsD,OAAO,CAACxD,GAAG,CAAC,EAAE;IAC5C,MAAMwB,IAAI,GAAG+B,GAAG,CAACjD,KAAK,CAAC,GAAG,CAAC;IAC3B,IAAIoD,OAAO,GAAGN,MAAM;IACpB5B,IAAI,CAACmC,OAAO,CAAC,CAACJ,GAAG,EAAEK,CAAC,KAAK;MACrB,IAAIA,CAAC,KAAKpC,IAAI,CAACV,MAAM,GAAG,CAAC,EAAE;QACvB4C,OAAO,CAACH,GAAG,CAAC,GAAGxB,KAAK;MACxB,CAAC,MACI;QACD2B,OAAO,CAACH,GAAG,CAAC,KAAK,CAAC,CAAC;QACnBG,OAAO,GAAGA,OAAO,CAACH,GAAG,CAAC;MAC1B;IACJ,CAAC,CAAC;EACN;EACA,OAAOH,MAAM;AACjB;AAEA,MAAMS,gBAAgB,GAAG,IAAI9G,cAAc,CAAC+C,SAAS,GAAG,kBAAkB,GAAG,EAAE,EAAE;EAC7EgE,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,KAAMC;AACnB,CAAC,CAAC;AACF,MAAMA,aAAa,GAAG;EAClBC,WAAW,EAAE,IAAI;EACjBC,oBAAoB,EAAE,KAAK;EAC3BC,QAAQ,EAAE,KAAK;EACfC,aAAa,EAAE,CAAC;EAChBC,YAAY,EAAE,EAAE;EAChBC,cAAc,EAAE,EAAE;EAClBC,cAAc,EAAE;IACZC,aAAa,EAAE,IAAI;IACnBC,sBAAsB,EAAE,KAAK;IAC7BC,UAAU,EAAE;EAChB,CAAC;EACDvB,OAAO,EAAE;IACLwB,GAAG,EAAE;EACT,CAAC;EACDC,aAAa,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EAC3BC,MAAM,EAAE;IACJC,UAAU,EAAE;EAChB;AACJ,CAAC;AACD,SAASC,eAAeA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAE;EAClC,OAAO;IACH,GAAGhB,aAAa;IAChB,GAAGgB,MAAM;IACTT,cAAc,EAAE;MACZ,GAAGP,aAAa,CAACO,cAAc;MAC/B,GAAGS,MAAM,CAACT;IACd,CAAC;IACDpB,OAAO,EAAE;MACL,GAAGa,aAAa,CAACb,OAAO;MACxB,GAAG6B,MAAM,CAAC7B;IACd,CAAC;IACD0B,MAAM,EAAE;MACJ,GAAGb,aAAa,CAACa,MAAM;MACvB,GAAGG,MAAM,CAACH;IACd;EACJ,CAAC;AACL;AAEA,MAAMI,oBAAoB,GAAG,IAAIlI,cAAc,CAAC+C,SAAS,GAAG,sBAAsB,GAAG,EAAE,CAAC;AACxF,MAAMoF,iBAAiB,CAAC;EACpBF,MAAM,GAAGhI,MAAM,CAAC6G,gBAAgB,EAAE;IAAEsB,QAAQ,EAAE;EAAK,CAAC,CAAC,IAAInB,aAAa;EACtE,IAAIoB,oBAAoBA,CAAA,EAAG;IACvB,OAAOC,cAAc,CAAC,IAAI,CAACL,MAAM,CAAC;EACtC;EACAM,SAASA,CAAC;IAAEvD,KAAK;IAAEwD,MAAM,GAAG,CAAC,CAAC;IAAEC,WAAW;IAAEjC;EAAI,CAAC,EAAE;IAChD,IAAI5B,QAAQ,CAACI,KAAK,CAAC,EAAE;MACjB,IAAI0D,UAAU;MACd,IAAIC,WAAW,GAAG3D,KAAK;MACvB,OAAO,CAAC0D,UAAU,GAAG,IAAI,CAACL,oBAAoB,CAACO,IAAI,CAACD,WAAW,CAAC,MAAM,IAAI,EAAE;QACxE,MAAM,CAACE,KAAK,EAAEC,UAAU,CAAC,GAAGJ,UAAU;QACtCC,WAAW,GAAGA,WAAW,CAACxD,OAAO,CAAC0D,KAAK,EAAE,MAAM;UAC3C,MAAMA,KAAK,GAAGC,UAAU,CAACC,IAAI,CAAC,CAAC;UAC/B,MAAMC,KAAK,GAAGhG,QAAQ,CAACwF,MAAM,EAAEK,KAAK,CAAC;UACrC,IAAIlD,SAAS,CAACqD,KAAK,CAAC,EAAE;YAClB,OAAOA,KAAK;UAChB;UACA,OAAOrD,SAAS,CAAC8C,WAAW,CAACI,KAAK,CAAC,CAAC,GAC9B,IAAI,CAACN,SAAS,CAAC;YACbC,MAAM;YACNC,WAAW;YACXjC,GAAG;YACHxB,KAAK,EAAEyD,WAAW,CAACI,KAAK;UAC5B,CAAC,CAAC,GACA,EAAE;QACZ,CAAC,CAAC;MACN;MACA,OAAOF,WAAW;IACtB,CAAC,MACI,IAAIH,MAAM,EAAE;MACb,IAAIhE,QAAQ,CAACQ,KAAK,CAAC,EAAE;QACjBA,KAAK,GAAG,IAAI,CAACiE,YAAY,CAAC;UACtBjE,KAAK;UACLwD,MAAM;UACNC,WAAW;UACXjC;QACJ,CAAC,CAAC;MACN,CAAC,MACI,IAAIrC,KAAK,CAACC,OAAO,CAACY,KAAK,CAAC,EAAE;QAC3BA,KAAK,GAAG,IAAI,CAACkE,WAAW,CAAC;UAAElE,KAAK;UAAEwD,MAAM;UAAEC,WAAW;UAAEjC;QAAI,CAAC,CAAC;MACjE;IACJ;IACA,OAAOxB,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIiE,YAAYA,CAAC;IAAEjE,KAAK;IAAEwD,MAAM,GAAG,CAAC,CAAC;IAAEC,WAAW;IAAEjC;EAAK,CAAC,EAAE;IACpD,IAAIH,MAAM,GAAGrB,KAAK;IAClB7B,MAAM,CAACsB,IAAI,CAAC+D,MAAM,CAAC,CAAC5B,OAAO,CAAEnD,CAAC,IAAK;MAC/B;MACA,MAAM0F,UAAU,GAAG,IAAI,CAACZ,SAAS,CAAC;QAC9B;QACAvD,KAAK,EAAEhC,QAAQ,CAACqD,MAAM,EAAE5C,CAAC,CAAC;QAC1B;QACA+E,MAAM,EAAExF,QAAQ,CAACwF,MAAM,EAAE/E,CAAC,CAAC;QAC3BgF,WAAW;QACXjC;MACJ,CAAC,CAAC;MACF;MACAH,MAAM,GAAG1C,QAAQ,CAAC0C,MAAM,EAAE5C,CAAC,EAAE0F,UAAU,CAAC;IAC5C,CAAC,CAAC;IACF,OAAO9C,MAAM;EACjB;EACA6C,WAAWA,CAAC;IAAElE,KAAK;IAAE,GAAGoE;EAAK,CAAC,EAAE;IAC5B,OAAOpE,KAAK,CAACtD,GAAG,CAAE2H,CAAC,IAAK,IAAI,CAACd,SAAS,CAAC;MACnCvD,KAAK,EAAEqE,CAAC;MACR,GAAGD;IACP,CAAC,CAAC,CAAC;EACP;EACA,OAAOE,IAAI,YAAAC,0BAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFrB,iBAAiB;EAAA;EACpH,OAAOsB,KAAK,kBAD6E1J,EAAE,CAAA2J,kBAAA;IAAAC,KAAA,EACYxB,iBAAiB;IAAAnB,OAAA,EAAjBmB,iBAAiB,CAAAmB;EAAA;AAC5H;AACA;EAAA,QAAAvG,SAAA,oBAAAA,SAAA,KAH6FhD,EAAE,CAAA6J,iBAAA,CAGJzB,iBAAiB,EAAc,CAAC;IAC/G0B,IAAI,EAAE3J;EACV,CAAC,CAAC;AAAA;AACV,SAASoI,cAAcA,CAACL,MAAM,EAAE;EAC5B,MAAM,CAAC6B,KAAK,EAAEC,GAAG,CAAC,GAAG9B,MAAM,CAACJ,aAAa;EACzC,OAAO,IAAImC,MAAM,CAAC,GAAGF,KAAK,MAAMA,KAAK,GAAGC,GAAG,OAAOA,GAAG,EAAE,EAAE,GAAG,CAAC;AACjE;AACA,SAASE,eAAeA,CAACC,UAAU,EAAE;EACjC,MAAMC,QAAQ,GAAGD,UAAU,GAAGA,UAAU,CAAC3G,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;EACxD,MAAM6G,IAAI,GAAG,EAAE;EACf,KAAK,IAAIvD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsD,QAAQ,CAACpG,MAAM,EAAE8C,CAAC,EAAE,EAAE;IACtC,IAAI7B,KAAK,GAAGmF,QAAQ,CAACtD,CAAC,CAAC,CAACkC,IAAI,CAAC,CAAC;IAC9B,OAAO/D,KAAK,CAACA,KAAK,CAACjB,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;MACrC8C,CAAC,EAAE;MACH7B,KAAK,GAAGA,KAAK,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,GAAGgF,QAAQ,CAACtD,CAAC,CAAC;IAClD;IACAuD,IAAI,CAACC,IAAI,CAACrF,KAAK,CAAC;EACpB;EACA,OAAOoF,IAAI;AACf;AACA,MAAME,oBAAoB,SAASnC,iBAAiB,CAAC;EACjDoC,QAAQ,GAAGtK,MAAM,CAACE,QAAQ,CAAC;EAC3BoI,SAASA,CAAC;IAAEvD,KAAK;IAAE,GAAGoE;EAAK,CAAC,EAAE;IAC1B,IAAID,UAAU,GAAGnE,KAAK;IACtB,IAAIJ,QAAQ,CAACI,KAAK,CAAC,EAAE;MACjBmE,UAAU,GAAGnE,KAAK,CAACG,OAAO,CAAC,6BAA6B,EAAE,CAAC0D,KAAK,EAAE2B,YAAY,EAAEJ,IAAI,KAAK;QACrF,IAAI;UACA,MAAMK,IAAI,GAAG,IAAI,CAACF,QAAQ,CAAC1H,GAAG,CAAC2H,YAAY,CAAC;UAC5C,OAAOC,IAAI,CAAClC,SAAS,CAAC,GAAG0B,eAAe,CAACG,IAAI,CAAC,CAAC;QACnD,CAAC,CACD,OAAOM,CAAC,EAAE;UACN,IAAIC,OAAO,GAAG,0BAA0B3F,KAAK;AACjE,wHAAwHwF,YAAY,cAAc;UAC9H,IAAIE,CAAC,CAACC,OAAO,CAACC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;YACzCD,OAAO,GAAG,sBAAsBH,YAAY,2DAA2D;UAC3G;UACA,MAAM,IAAIK,KAAK,CAACF,OAAO,CAAC;QAC5B;MACJ,CAAC,CAAC;IACN;IACA,OAAO,KAAK,CAACpC,SAAS,CAAC;MAAEvD,KAAK,EAAEmE,UAAU;MAAE,GAAGC;IAAK,CAAC,CAAC;EAC1D;EACA,OAAOE,IAAI;IAAA,IAAAwB,iCAAA;IAAA,gBAAAC,6BAAAvB,iBAAA;MAAA,QAAAsB,iCAAA,KAAAA,iCAAA,GA7C8E/K,EAAE,CAAAiL,qBAAA,CA6CQV,oBAAoB,IAAAd,iBAAA,IAApBc,oBAAoB;IAAA;EAAA;EACvH,OAAOb,KAAK,kBA9C6E1J,EAAE,CAAA2J,kBAAA;IAAAC,KAAA,EA8CYW,oBAAoB;IAAAtD,OAAA,EAApBsD,oBAAoB,CAAAhB;EAAA;AAC/H;AACA;EAAA,QAAAvG,SAAA,oBAAAA,SAAA,KAhD6FhD,EAAE,CAAA6J,iBAAA,CAgDJU,oBAAoB,EAAc,CAAC;IAClHT,IAAI,EAAE3J;EACV,CAAC,CAAC;AAAA;AAEV,MAAM+K,yBAAyB,GAAG,IAAIjL,cAAc,CAAC+C,SAAS,GAAG,2BAA2B,GAAG,EAAE,CAAC;AAClG,MAAMmI,qBAAqB,CAAC;EACxBC,MAAMA,CAAC3E,GAAG,EAAEyB,MAAM,EAAE;IAChB,IAAIA,MAAM,CAACT,cAAc,CAACC,aAAa,IAAI,CAACQ,MAAM,CAACb,QAAQ,EAAE;MACzD,MAAMgE,GAAG,GAAG,4BAA4B5E,GAAG,GAAG;MAC9C6E,OAAO,CAACC,IAAI,CAAC,MAAMF,GAAG,EAAE,EAAE,6BAA6B,CAAC;IAC5D;IACA,OAAO5E,GAAG;EACd;EACA,OAAO8C,IAAI,YAAAiC,8BAAA/B,iBAAA;IAAA,YAAAA,iBAAA,IAAwF0B,qBAAqB;EAAA;EACxH,OAAOzB,KAAK,kBA9D6E1J,EAAE,CAAA2J,kBAAA;IAAAC,KAAA,EA8DYuB,qBAAqB;IAAAlE,OAAA,EAArBkE,qBAAqB,CAAA5B;EAAA;AAChI;AACA;EAAA,QAAAvG,SAAA,oBAAAA,SAAA,KAhE6FhD,EAAE,CAAA6J,iBAAA,CAgEJsB,qBAAqB,EAAc,CAAC;IACnHrB,IAAI,EAAE3J;EACV,CAAC,CAAC;AAAA;AAEV,MAAMsL,qBAAqB,GAAG,IAAIxL,cAAc,CAAC+C,SAAS,GAAG,uBAAuB,GAAG,EAAE,CAAC;AAC1F,MAAM0I,kBAAkB,CAAC;EACrBC,kBAAkBA,CAACjD,WAAW,EAAE;IAC5B,OAAOA,WAAW;EACtB;EACAkD,qBAAqBA,CAACC,CAAC,EAAE5G,KAAK,EAAE;IAC5B,OAAOA,KAAK;EAChB;EACA,OAAOsE,IAAI,YAAAuC,2BAAArC,iBAAA;IAAA,YAAAA,iBAAA,IAAwFiC,kBAAkB;EAAA;EACrH,OAAOhC,KAAK,kBA7E6E1J,EAAE,CAAA2J,kBAAA;IAAAC,KAAA,EA6EY8B,kBAAkB;IAAAzE,OAAA,EAAlByE,kBAAkB,CAAAnC;EAAA;AAC7H;AACA;EAAA,QAAAvG,SAAA,oBAAAA,SAAA,KA/E6FhD,EAAE,CAAA6J,iBAAA,CA+EJ6B,kBAAkB,EAAc,CAAC;IAChH5B,IAAI,EAAE3J;EACV,CAAC,CAAC;AAAA;AAEV,MAAM4L,2BAA2B,GAAG,IAAI9L,cAAc,CAAC+C,SAAS,GAAG,6BAA6B,GAAG,EAAE,CAAC;AACtG,MAAMgJ,uBAAuB,CAAC;EAC1BC,UAAU;EACVtJ,WAAWA,CAACsJ,UAAU,EAAE;IACpB,IAAI,CAACA,UAAU,GAAGA,UAAU;EAChC;EACAC,YAAYA,CAAA,EAAG;IACX,MAAM3E,YAAY,GAAG,IAAI,CAAC0E,UAAU,CAAC1E,YAAY;IACjD,IAAI,CAACA,YAAY,EAAE;MACf,MAAM,IAAIuD,KAAK,CAAC,sFAAsF,CAAC;IAC3G;IACA,OAAO1G,KAAK,CAACC,OAAO,CAACkD,YAAY,CAAC,GAAGA,YAAY,GAAG,CAACA,YAAY,CAAC;EACtE;EACA,OAAOgC,IAAI,YAAA4C,gCAAA1C,iBAAA;IAAA,YAAAA,iBAAA,IAAwFuC,uBAAuB,EAhGjChM,EAAE,CAAAoM,QAAA,CAgGiDrF,gBAAgB;EAAA;EAC5J,OAAO2C,KAAK,kBAjG6E1J,EAAE,CAAA2J,kBAAA;IAAAC,KAAA,EAiGYoC,uBAAuB;IAAA/E,OAAA,EAAvB+E,uBAAuB,CAAAzC;EAAA;AAClI;AACA;EAAA,QAAAvG,SAAA,oBAAAA,SAAA,KAnG6FhD,EAAE,CAAA6J,iBAAA,CAmGJmC,uBAAuB,EAAc,CAAC;IACrHlC,IAAI,EAAE3J;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE2J,IAAI,EAAEnE,SAAS;IAAE0G,UAAU,EAAE,CAAC;MAC/CvC,IAAI,EAAEzJ,MAAM;MACZgK,IAAI,EAAE,CAACtD,gBAAgB;IAC3B,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA,SAASuF,gBAAgBA,CAACzJ,IAAI,EAAE;EAC5B,IAAI,CAACA,IAAI,EAAE;IACP,OAAO,EAAE;EACb;EACA,MAAMW,KAAK,GAAGX,IAAI,CAACW,KAAK,CAAC,GAAG,CAAC;EAC7BA,KAAK,CAAC+I,GAAG,CAAC,CAAC;EACX,OAAO/I,KAAK,CAACgJ,IAAI,CAAC,GAAG,CAAC;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAAC5J,IAAI,EAAE;EAC5B,IAAI,CAACA,IAAI,EAAE;IACP,OAAO,EAAE;EACb;EACA,OAAOA,IAAI,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC+I,GAAG,CAAC,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,YAAYA,CAACvH,GAAG,EAAEF,KAAK,EAAE0H,IAAI,GAAG,GAAG,EAAE;EAC1C,IAAI9H,QAAQ,CAACM,GAAG,CAAC,EAAE;IACf,MAAMiF,QAAQ,GAAGjF,GAAG,CAAC3B,KAAK,CAACmJ,IAAI,CAAC;IAChC,MAAMC,QAAQ,GAAGxC,QAAQ,CAACmC,GAAG,CAAC,CAAC;IAC/B,OAAOK,QAAQ,KAAK3H,KAAK,GAAG,CAAC,IAAI,EAAEmF,QAAQ,CAACyC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,EAAED,QAAQ,CAAC;EAC/E;EACA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;AACtB;AACA,SAASE,yBAAyBA,CAACC,OAAO,EAAElK,IAAI,EAAE;EAC9C,MAAM,CAACmK,SAAS,CAAC,GAAGN,YAAY,CAAC7J,IAAI,EAAE,QAAQ,CAAC;EAChD,IAAI,CAACmK,SAAS,EAAE;IACZ;IACA,OAAO,CAAC,CAACD,OAAO,CAAC7E,MAAM,CAACd,oBAAoB;EAChD;EACA;EACA,OAAO,KAAK;AAChB;AACA,SAAS6F,mBAAmBA,CAACC,kBAAkB,EAAE;EAC7C,OAAOA,kBAAkB,GAAIC,MAAM,IAAKA,MAAM,GAAG1L,IAAI,CAAC,CAAC,CAAC;AAC5D;AACA,SAAS2L,YAAYA,CAACC,YAAY,EAAEnH,KAAK,EAAE;EACvC,OAAO9C,MAAM,CAACsB,IAAI,CAAC2I,YAAY,CAAC,CAAC5J,MAAM,CAAC,CAACQ,GAAG,EAAEpB,IAAI,KAAK;IACnDoB,GAAG,CAAC,GAAGiC,KAAK,IAAIrD,IAAI,EAAE,CAAC,GAAGwK,YAAY,CAACxK,IAAI,CAAC;IAC5C,OAAOoB,GAAG;EACd,CAAC,EAAE,CAAC,CAAC,CAAC;AACV;AACA,SAASqJ,mBAAmBA,CAACC,aAAa,EAAErH,KAAK,EAAE;EAC/C,OAAOC,eAAe,CAACoH,aAAa,CAAC,GAC/BH,YAAY,CAACG,aAAa,CAACnH,MAAM,EAAEF,KAAK,CAAC,GACzCP,SAAS;AACnB;AACA,SAAS6H,eAAeA,CAAC3K,IAAI,EAAE;EAC3B,OAAO;IACHqD,KAAK,EAAEoG,gBAAgB,CAACzJ,IAAI,CAAC,IAAI,IAAI;IACrC4K,QAAQ,EAAEhB,gBAAgB,CAAC5J,IAAI;EACnC,CAAC;AACL;AAEA,SAAS6K,aAAaA,CAACC,OAAO,EAAE;EAC5B,MAAM;IAAExK,IAAI;IAAEkK,YAAY;IAAEO,UAAU;IAAEC;EAAK,CAAC,GAAGF,OAAO;EACxD,IAAIN,YAAY,EAAE;IACd,MAAMS,UAAU,GAAGT,YAAY,CAAClK,IAAI,CAAC;IACrC,IAAIyB,UAAU,CAACkJ,UAAU,CAAC,KAAK,KAAK,EAAE;MAClC,MAAM,iEAAiE3K,IAAI,EAAE;IACjF;IACA,OAAOkK,YAAY,CAAClK,IAAI,CAAC,CAAC,CAAC,CAAC4K,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,OAAO,GAAGD,GAAG,CAACC,OAAO,GAAGD,GAAG,CAAC;EAC9E;EACA,OAAOJ,UAAU,CAAChL,cAAc,CAACO,IAAI,EAAE0K,IAAI,CAAC;AAChD;AAEA,SAASK,mBAAmBA,CAAC;EAAEN,UAAU;EAAEzK,IAAI;EAAE0K,IAAI;EAAEM,YAAY;EAAEd;AAAc,CAAC,EAAE;EAClF,MAAMe,KAAK,GAAGD,YAAY,GAAG,CAAChL,IAAI,EAAEgL,YAAY,CAAC,GAAG,CAAChL,IAAI,CAAC;EAC1D,OAAOiL,KAAK,CAACzM,GAAG,CAAEwB,IAAI,IAAK;IACvB,MAAMiD,MAAM,GAAGsH,aAAa,CAAC;MAAEvK,IAAI;MAAEyK,UAAU;MAAEP,YAAY;MAAEQ;IAAK,CAAC,CAAC;IACtE,OAAOnM,IAAI,CAAC0E,MAAM,CAAC,CAACiI,IAAI,CAAC1M,GAAG,CAAE+G,WAAW,KAAM;MAC3CA,WAAW;MACX7F,IAAI,EAAEM;IACV,CAAC,CAAC,CAAC,CAAC;EACR,CAAC,CAAC;AACN;AAEA,IAAI4J,OAAO;AACX,SAASuB,SAASA,CAAC7H,GAAG,EAAEgC,MAAM,GAAG,CAAC,CAAC,EAAE5F,IAAI,EAAE;EACvC,OAAOkK,OAAO,CAACuB,SAAS,CAAC7H,GAAG,EAAEgC,MAAM,EAAE5F,IAAI,CAAC;AAC/C;AACA,SAAS0L,eAAeA,CAAC9H,GAAG,EAAEgC,MAAM,GAAG,CAAC,CAAC,EAAE5F,IAAI,EAAE;EAC7C,OAAOkK,OAAO,CAACwB,eAAe,CAAC9H,GAAG,EAAEgC,MAAM,EAAE5F,IAAI,CAAC;AACrD;AACA,MAAM2L,gBAAgB,CAAC;EACnBpI,MAAM;EACNqI,MAAM;EACNhH,cAAc;EACdiH,WAAW;EACXC,gBAAgB;EAChBC,YAAY;EACZlM,YAAY,GAAG,IAAImM,GAAG,CAAC,CAAC;EACxBC,KAAK,GAAG,IAAID,GAAG,CAAC,CAAC;EACjBE,iBAAiB;EACjB5H,WAAW,GAAG,EAAE;EAChBK,cAAc,GAAG,EAAE;EACnBwH,qBAAqB,GAAG,KAAK;EAC7BnM,IAAI;EACJoM,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC;EACvBC,MAAM,GAAG,IAAIvN,OAAO,CAAC,CAAC;EACtBwN,OAAO,GAAG,IAAI,CAACD,MAAM,CAACE,YAAY,CAAC,CAAC;EACpCnH,MAAM;EACNoH,UAAU,GAAGpP,MAAM,CAACI,UAAU,CAAC;EAC/BqC,WAAWA,CAACyD,MAAM,EAAEqI,MAAM,EAAEhH,cAAc,EAAEiH,WAAW,EAAEzC,UAAU,EAAE0C,gBAAgB,EAAE;IACnF,IAAI,CAACvI,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACqI,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAChH,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACiH,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAAC,IAAI,CAACvI,MAAM,EAAE;MACd,IAAI,CAACA,MAAM,GAAG,IAAI3D,aAAa,CAAC,IAAI,CAACC,YAAY,CAAC;IACtD;IACAqK,OAAO,GAAG,IAAI;IACd,IAAI,CAAC7E,MAAM,GAAGqH,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACxD,UAAU,CAAC,CAAC;IACpD,IAAI,CAACyD,iBAAiB,CAAC,IAAI,CAACxH,MAAM,CAACV,cAAc,IAAI,EAAE,CAAC;IACxD,IAAI,CAACmI,oCAAoC,CAAC,IAAI,CAACzH,MAAM,CAAC;IACtD,IAAI,CAAC0H,cAAc,CAAC,IAAI,CAAC1H,MAAM,CAACf,WAAW,CAAC;IAC5C,IAAI,CAACtE,IAAI,GAAG,IAAIhB,eAAe,CAAC,IAAI,CAACgO,cAAc,CAAC,CAAC,CAAC;IACtD;IACA;IACA,IAAI,CAACjB,YAAY,GAAG,IAAI,CAAC/L,IAAI,CAACwM,YAAY,CAAC,CAAC;IAC5C;AACR;AACA;IACQ,IAAI,CAACD,OAAO,CAACU,SAAS,CAAEnF,CAAC,IAAK;MAC1B,IAAIA,CAAC,CAACb,IAAI,KAAK,wBAAwB,IAAIa,CAAC,CAACoF,UAAU,EAAE;QACrD,IAAI,CAACC,aAAa,CAACrF,CAAC,CAACsF,OAAO,CAACxC,QAAQ,CAAC;MAC1C;IACJ,CAAC,CAAC;IACF,IAAI,CAAC6B,UAAU,CAACY,SAAS,CAAC,MAAM;MAC5B;MACA;MACA,IAAI,CAACrN,IAAI,CAACsN,QAAQ,CAAC,CAAC;MACpB,IAAI,CAAChB,MAAM,CAACgB,QAAQ,CAAC,CAAC;MACtB;MACA;MACA;MACA,IAAI,CAACrB,KAAK,CAACsB,KAAK,CAAC,CAAC;IACtB,CAAC,CAAC;EACN;EACAP,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC1I,WAAW;EAC3B;EACAyI,cAAcA,CAAC/M,IAAI,EAAE;IACjB,IAAI,CAACsE,WAAW,GAAGtE,IAAI;EAC3B;EACAwN,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACxN,IAAI,CAACI,QAAQ,CAAC,CAAC;EAC/B;EACA+M,aAAaA,CAACnN,IAAI,EAAE;IAChB,IAAI,CAAC4L,MAAM,CAAC6B,aAAa,GAAGzN,IAAI,CAAC;IACjC,IAAI,CAACA,IAAI,CAAC0N,IAAI,CAAC1N,IAAI,CAAC;IACpB,IAAI,CAACsM,MAAM,CAACoB,IAAI,CAAC;MACbzG,IAAI,EAAE,aAAa;MACnBmG,OAAO,EAAEzC,eAAe,CAAC3K,IAAI;IACjC,CAAC,CAAC;IACF,OAAO,IAAI;EACf;EACA6M,iBAAiBA,CAACc,KAAK,EAAE;IACrB,IAAI,CAAChJ,cAAc,GAAGgJ,KAAK;EAC/B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACjJ,cAAc;EAC9B;EACAkJ,IAAIA,CAACvN,IAAI,EAAEwK,OAAO,GAAG,CAAC,CAAC,EAAE;IACrB,MAAMgD,MAAM,GAAG,IAAI,CAAC7B,KAAK,CAAChM,GAAG,CAACK,IAAI,CAAC;IACnC,IAAIwN,MAAM,EAAE;MACR,OAAOA,MAAM;IACjB;IACA,IAAIC,eAAe;IACnB,MAAMC,OAAO,GAAG,IAAI,CAACC,aAAa,CAAC3N,IAAI,CAAC;IACxC,IAAI+C,KAAK;IACT,IAAI2K,OAAO,EAAE;MACT3K,KAAK,GAAGoG,gBAAgB,CAACnJ,IAAI,CAAC;IAClC;IACA,MAAM4N,cAAc,GAAG;MACnB5N,IAAI;MACJyK,UAAU,EAAE,IAAI,CAACxH,MAAM;MACvBiH,YAAY,EAAEM,OAAO,CAACN,YAAY;MAClCQ,IAAI,EAAEgD,OAAO,GAAG;QAAE3K,KAAK,EAAEA;MAAM,CAAC,GAAGP;IACvC,CAAC;IACD,IAAI,IAAI,CAACgC,sBAAsB,CAACxE,IAAI,CAAC,EAAE;MACnC;MACA,MAAM6N,QAAQ,GAAGH,OAAO,GAClB,GAAG3K,KAAK,IAAI,IAAI,CAAC6I,iBAAiB,EAAE,GACpC,IAAI,CAACA,iBAAiB;MAC5B,MAAMkC,OAAO,GAAG/C,mBAAmB,CAAC;QAChC,GAAG6C,cAAc;QACjB5C,YAAY,EAAE6C;MAClB,CAAC,CAAC;MACFJ,eAAe,GAAG9O,QAAQ,CAACmP,OAAO,CAAC;IACvC,CAAC,MACI;MACD,MAAM7K,MAAM,GAAGsH,aAAa,CAACqD,cAAc,CAAC;MAC5CH,eAAe,GAAGlP,IAAI,CAAC0E,MAAM,CAAC;IAClC;IACA,MAAM8K,KAAK,GAAGN,eAAe,CAACvC,IAAI,CAACtM,KAAK,CAAC,IAAI,CAACmG,MAAM,CAACZ,aAAa,CAAC,EAAEtF,GAAG,CAAE0G,WAAW,IAAK;MACtF,IAAItE,KAAK,CAACC,OAAO,CAACqE,WAAW,CAAC,EAAE;QAC5BA,WAAW,CAAC7B,OAAO,CAAEsK,CAAC,IAAK;UACvB,IAAI,CAACC,aAAa,CAACD,CAAC,CAACtO,IAAI,EAAEsO,CAAC,CAACzI,WAAW,CAAC;UACzC;UACA,IAAIyI,CAAC,CAACtO,IAAI,KAAKM,IAAI,EAAE;YACjB,IAAI,CAAC2L,KAAK,CAACuC,GAAG,CAACF,CAAC,CAACtO,IAAI,EAAErB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC;QACJ,CAAC,CAAC;QACF;MACJ;MACA,IAAI,CAAC4P,aAAa,CAACjO,IAAI,EAAEuF,WAAW,CAAC;IACzC,CAAC,CAAC,EAAEzG,UAAU,CAAEqP,KAAK,IAAK;MACtB,IAAI,CAAC,IAAI,CAACpJ,MAAM,CAACb,QAAQ,EAAE;QACvBiE,OAAO,CAACgG,KAAK,CAAC,+BAA+BnO,IAAI,GAAG,EAAEmO,KAAK,CAAC;MAChE;MACA,OAAO,IAAI,CAACC,aAAa,CAACpO,IAAI,EAAEwK,OAAO,CAAC;IAC5C,CAAC,CAAC,EAAEzL,WAAW,CAAC,CAAC,CAAC,EAAEI,kBAAkB,CAAC,IAAI,CAACgN,UAAU,CAAC,CAAC;IACxD,IAAI,CAACR,KAAK,CAACuC,GAAG,CAAClO,IAAI,EAAE+N,KAAK,CAAC;IAC3B,OAAOA,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI5C,SAASA,CAAC7H,GAAG,EAAEgC,MAAM,GAAG,CAAC,CAAC,EAAE5F,IAAI,GAAG,IAAI,CAACwN,aAAa,CAAC,CAAC,EAAE;IACrD,IAAI,CAAC5J,GAAG,EACJ,OAAOA,GAAG;IACd,MAAM;MAAEP,KAAK;MAAEsL;IAAY,CAAC,GAAG,IAAI,CAACC,mBAAmB,CAAC5O,IAAI,CAAC;IAC7D,IAAIuB,KAAK,CAACC,OAAO,CAACoC,GAAG,CAAC,EAAE;MACpB,OAAOA,GAAG,CAAC9E,GAAG,CAAE+P,CAAC,IAAK,IAAI,CAACpD,SAAS,CAACpI,KAAK,GAAG,GAAGA,KAAK,IAAIwL,CAAC,EAAE,GAAGA,CAAC,EAAEjJ,MAAM,EAAE+I,WAAW,CAAC,CAAC;IAC3F;IACA/K,GAAG,GAAGP,KAAK,GAAG,GAAGA,KAAK,IAAIO,GAAG,EAAE,GAAGA,GAAG;IACrC,MAAMiC,WAAW,GAAG,IAAI,CAAC9F,cAAc,CAAC4O,WAAW,CAAC;IACpD,MAAMvM,KAAK,GAAGyD,WAAW,CAACjC,GAAG,CAAC;IAC9B,IAAI,CAACxB,KAAK,EAAE;MACR,OAAO,IAAI,CAAC0M,iBAAiB,CAAClL,GAAG,EAAExB,KAAK,EAAEwD,MAAM,CAAC;IACrD;IACA,OAAO,IAAI,CAACgG,MAAM,CAACjG,SAAS,CAAC;MACzBvD,KAAK;MACLwD,MAAM;MACNC,WAAW;MACXjC;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACImL,eAAeA,CAACnL,GAAG,EAAEgC,MAAM,EAAE5F,IAAI,EAAEgP,SAAS,GAAG,KAAK,EAAE;IAClD,IAAIxE,YAAY;IAChB,MAAMqD,IAAI,GAAGA,CAAC7N,IAAI,EAAE8K,OAAO,KAAK,IAAI,CAAC+C,IAAI,CAAC7N,IAAI,EAAE8K,OAAO,CAAC,CAACU,IAAI,CAAC1M,GAAG,CAAC,MAAMkQ,SAAS,GAC3E,IAAI,CAACtD,eAAe,CAAC9H,GAAG,EAAEgC,MAAM,EAAE5F,IAAI,CAAC,GACvC,IAAI,CAACyL,SAAS,CAAC7H,GAAG,EAAEgC,MAAM,EAAE5F,IAAI,CAAC,CAAC,CAAC;IACzC,IAAI6C,KAAK,CAAC7C,IAAI,CAAC,EAAE;MACb,OAAO,IAAI,CAAC+L,YAAY,CAACP,IAAI,CAAClM,SAAS,CAAEU,IAAI,IAAK6N,IAAI,CAAC7N,IAAI,CAAC,CAAC,CAAC;IAClE;IACAA,IAAI,GAAGuB,KAAK,CAACC,OAAO,CAACxB,IAAI,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI;IAC3C,IAAIoD,aAAa,CAACpD,IAAI,CAAC,EAAE;MACrB;MACA,MAAM0K,aAAa,GAAG1K,IAAI;MAC1BA,IAAI,GAAG0K,aAAa,CAACrH,KAAK;MAC1BmH,YAAY,GAAGC,mBAAmB,CAACC,aAAa,EAAEA,aAAa,CAACrH,KAAK,CAAC;IAC1E;IACArD,IAAI,GAAGA,IAAI;IACX,IAAI,IAAI,CAACiP,MAAM,CAACjP,IAAI,CAAC,IAAI,IAAI,CAACkP,eAAe,CAAClP,IAAI,CAAC,EAAE;MACjD,OAAO6N,IAAI,CAAC7N,IAAI,CAAC;IACrB;IACA;IACA,MAAMqD,KAAK,GAAGrD,IAAI;IAClB,OAAO,IAAI,CAAC+L,YAAY,CAACP,IAAI,CAAClM,SAAS,CAAEU,IAAI,IAAK6N,IAAI,CAAC,GAAGxK,KAAK,IAAIrD,IAAI,EAAE,EAAE;MAAEwK;IAAa,CAAC,CAAC,CAAC,CAAC;EAClG;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI0E,eAAeA,CAAClP,IAAI,EAAE;IAClB,OAAO,IAAI,CAACiP,MAAM,CAACrF,gBAAgB,CAAC5J,IAAI,CAAC,CAAC;EAC9C;EACA0L,eAAeA,CAAC9H,GAAG,EAAEgC,MAAM,GAAG,CAAC,CAAC,EAAE5F,IAAI,GAAG,IAAI,CAACwN,aAAa,CAAC,CAAC,EAAE;IAC3D,IAAIxL,QAAQ,CAAC4B,GAAG,CAAC,IAAIrC,KAAK,CAACC,OAAO,CAACoC,GAAG,CAAC,EAAE;MACrC,MAAM;QAAE+K,WAAW;QAAEtL;MAAM,CAAC,GAAG,IAAI,CAACuL,mBAAmB,CAAC5O,IAAI,CAAC;MAC7D,IAAIuB,KAAK,CAACC,OAAO,CAACoC,GAAG,CAAC,EAAE;QACpB,OAAOA,GAAG,CAAC9E,GAAG,CAAE+P,CAAC,IAAK,IAAI,CAACnD,eAAe,CAACrI,KAAK,GAAG,GAAGA,KAAK,IAAIwL,CAAC,EAAE,GAAGA,CAAC,EAAEjJ,MAAM,EAAE+I,WAAW,CAAC,CAAC;MACjG;MACA,MAAM9I,WAAW,GAAG,IAAI,CAAC9F,cAAc,CAAC4O,WAAW,CAAC;MACpD/K,GAAG,GAAGP,KAAK,GAAG,GAAGA,KAAK,IAAIO,GAAG,EAAE,GAAGA,GAAG;MACrC,MAAMxB,KAAK,GAAG0B,SAAS,CAAC,IAAI,CAACqL,cAAc,CAACtJ,WAAW,EAAEjC,GAAG,CAAC,CAAC;MAC9D;MACA,OAAO9B,OAAO,CAACM,KAAK,CAAC,GACf,IAAI,CAACqJ,SAAS,CAAC7H,GAAG,EAAEgC,MAAM,EAAE5F,IAAI,CAAC,GACjC,IAAI,CAAC4L,MAAM,CAACjG,SAAS,CAAC;QAAEvD,KAAK;QAAEwD,MAAM,EAAEA,MAAM;QAAEC,WAAW;QAAEjC;MAAI,CAAC,CAAC;IAC5E;IACA,MAAM/D,YAAY,GAAG,EAAE;IACvB,KAAK,MAAM,CAACuP,IAAI,EAAEC,OAAO,CAAC,IAAI,IAAI,CAACC,UAAU,CAAC1L,GAAG,CAAC,EAAE;MAChD/D,YAAY,CAAC4H,IAAI,CAAC,IAAI,CAACiE,eAAe,CAAC0D,IAAI,EAAEC,OAAO,EAAErP,IAAI,CAAC,CAAC;IAChE;IACA,OAAOH,YAAY;EACvB;EACA0P,qBAAqBA,CAAC3L,GAAG,EAAEgC,MAAM,EAAE5F,IAAI,EAAE;IACrC,IAAIgC,QAAQ,CAAC4B,GAAG,CAAC,IAAIrC,KAAK,CAACC,OAAO,CAACoC,GAAG,CAAC,EAAE;MACrC,OAAO,IAAI,CAACmL,eAAe,CAACnL,GAAG,EAAEgC,MAAM,EAAE5F,IAAI,EAAE,IAAI,CAAC;IACxD;IACA,MAAM,CAAC,CAACwP,QAAQ,EAAEC,WAAW,CAAC,EAAE,GAAGjJ,IAAI,CAAC,GAAG,IAAI,CAAC8I,UAAU,CAAC1L,GAAG,CAAC;IAC/D;AACR;IACQ,OAAO,IAAI,CAAC2L,qBAAqB,CAACC,QAAQ,EAAEC,WAAW,EAAEzP,IAAI,CAAC,CAACwL,IAAI,CAAC1M,GAAG,CAAEsD,KAAK,IAAK;MAC/E,MAAMvC,YAAY,GAAG,CAACuC,KAAK,CAAC;MAC5B,KAAK,MAAM,CAACgN,IAAI,EAAEC,OAAO,CAAC,IAAI7I,IAAI,EAAE;QAChC3G,YAAY,CAAC4H,IAAI,CAAC,IAAI,CAACiE,eAAe,CAAC0D,IAAI,EAAEC,OAAO,EAAErP,IAAI,CAAC,CAAC;MAChE;MACA,OAAOH,YAAY;IACvB,CAAC,CAAC,CAAC;EACP;EACAE,cAAcA,CAAC2P,WAAW,EAAE;IACxB,IAAIA,WAAW,EAAE;MACb,IAAI,IAAI,CAACT,MAAM,CAACS,WAAW,CAAC,EAAE;QAC1B,OAAO,IAAI,CAAC7P,YAAY,CAACI,GAAG,CAACyP,WAAW,CAAC,IAAI,CAAC,CAAC;MACnD,CAAC,MACI;QACD;QACA,MAAM;UAAErM,KAAK;UAAEsL;QAAY,CAAC,GAAG,IAAI,CAACC,mBAAmB,CAACc,WAAW,CAAC;QACpE,MAAM7J,WAAW,GAAG,IAAI,CAAChG,YAAY,CAACI,GAAG,CAAC0O,WAAW,CAAC,IAAI,CAAC,CAAC;QAC5D,OAAO,IAAI,CAACQ,cAAc,CAACtJ,WAAW,EAAExC,KAAK,CAAC;MAClD;IACJ;IACA,OAAO,IAAI,CAACxD,YAAY;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI8P,iBAAiBA,CAAC3P,IAAI,EAAE;IACpB,IAAI4P,SAAS,GAAG,IAAI,CAAC7D,YAAY;IACjC,IAAI/L,IAAI,EAAE;MACN,MAAM6P,kBAAkB,GAAGjG,gBAAgB,CAAC5J,IAAI,CAAC,KAAKA,IAAI;MAC1D,IAAI,IAAI,CAACiP,MAAM,CAACjP,IAAI,CAAC,IAAI6P,kBAAkB,EAAE;QACzCD,SAAS,GAAGjR,EAAE,CAACqB,IAAI,CAAC;MACxB,CAAC,MACI;QACD4P,SAAS,GAAG,IAAI,CAAC7D,YAAY,CAACP,IAAI,CAAC1M,GAAG,CAAEgR,WAAW,IAAK,GAAG9P,IAAI,IAAI8P,WAAW,EAAE,CAAC,CAAC;MACtF;IACJ;IACA,OAAOF,SAAS,CAACpE,IAAI,CAAClM,SAAS,CAAEyQ,QAAQ,IAAK,IAAI,CAAClC,IAAI,CAACkC,QAAQ,CAAC,CAACvE,IAAI,CAAC1M,GAAG,CAAC,MAAM,IAAI,CAACiB,cAAc,CAACgQ,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACtH;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,cAAcA,CAACnK,WAAW,EAAE7F,IAAI,GAAG,IAAI,CAACwN,aAAa,CAAC,CAAC,EAAE1C,OAAO,GAAG,CAAC,CAAC,EAAE;IACnE,MAAMmF,QAAQ,GAAG;MAAEC,KAAK,EAAE,IAAI;MAAEC,UAAU,EAAE;IAAK,CAAC;IAClD,MAAMC,aAAa,GAAG;MAAE,GAAGH,QAAQ;MAAE,GAAGnF;IAAQ,CAAC;IACjD,MAAMzH,KAAK,GAAGoG,gBAAgB,CAACzJ,IAAI,CAAC;IACpC;AACR;AACA;AACA;IACQ,IAAIqQ,yBAAyB,GAAGxK,WAAW;IAC3C;IACA,IAAIxC,KAAK,EAAE;MACP,MAAMO,GAAG,GAAG,IAAI,CAAC0M,cAAc,CAACjN,KAAK,CAAC;MACtCgN,yBAAyB,GAAG7M,OAAO,CAAC;QAAE,CAACI,GAAG,GAAGiC;MAAY,CAAC,CAAC;IAC/D;IACA,MAAMiK,WAAW,GAAGzM,KAAK,GAAGuG,gBAAgB,CAAC5J,IAAI,CAAC,GAAGA,IAAI;IACzD,MAAMuQ,iBAAiB,GAAG;MACtB,IAAIH,aAAa,CAACF,KAAK,IAAI,IAAI,CAACnQ,cAAc,CAAC+P,WAAW,CAAC,CAAC;MAC5D,GAAGO;IACP,CAAC;IACD,MAAMG,kBAAkB,GAAG,IAAI,CAACnL,MAAM,CAAC7B,OAAO,CAACwB,GAAG,GAC5CuL,iBAAiB,GACjB/M,OAAO,CAAC+M,iBAAiB,CAAC;IAChC,MAAME,QAAQ,GAAG,IAAI,CAAC5E,WAAW,CAAC/C,kBAAkB,CAAC0H,kBAAkB,EAAEV,WAAW,CAAC;IACrF,IAAI,CAACjQ,YAAY,CAAC2O,GAAG,CAACsB,WAAW,EAAEW,QAAQ,CAAC;IAC5CL,aAAa,CAACD,UAAU,IAAI,IAAI,CAAChD,aAAa,CAAC,IAAI,CAACK,aAAa,CAAC,CAAC,CAAC;EACxE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIkD,iBAAiBA,CAAC9M,GAAG,EAAExB,KAAK,EAAE0I,OAAO,GAAG,CAAC,CAAC,EAAE;IACxC,MAAM9K,IAAI,GAAG8K,OAAO,CAAC9K,IAAI,IAAI,IAAI,CAACwN,aAAa,CAAC,CAAC;IACjD,MAAMiD,QAAQ,GAAG,IAAI,CAAC5E,WAAW,CAAC9C,qBAAqB,CAACnF,GAAG,EAAExB,KAAK,EAAEpC,IAAI,CAAC;IACzE,MAAM2Q,QAAQ,GAAG;MACb,CAAC/M,GAAG,GAAG6M;IACX,CAAC;IACD,IAAI,CAACT,cAAc,CAACW,QAAQ,EAAE3Q,IAAI,EAAE;MAAE,GAAG8K,OAAO;MAAEoF,KAAK,EAAE;IAAK,CAAC,CAAC;EACpE;EACA;AACJ;AACA;AACA;EACIpD,oCAAoCA,CAAC;IAAEpI;EAAc,CAAC,EAAE;IACpD,MAAM1E,IAAI,GAAGuB,KAAK,CAACC,OAAO,CAACkD,YAAY,CAAC,GAAGA,YAAY,CAAC,CAAC,CAAC,GAAGA,YAAY;IACzE,IAAIA,YAAY,IAAI,IAAI,CAACI,sBAAsB,CAAC9E,IAAI,CAAC,EAAE;MACnD,IAAI,CAACkM,iBAAiB,GAAGlM,IAAI;IACjC;EACJ;EACA;AACJ;AACA;EACI8O,iBAAiBA,CAAClL,GAAG,EAAExB,KAAK,EAAEwD,MAAM,EAAE;IAClC,IAAI,IAAI,CAACP,MAAM,CAACT,cAAc,CAACG,UAAU,IAAI3C,KAAK,KAAK,EAAE,EAAE;MACvD,OAAO,EAAE;IACb;IACA,IAAI,CAAC,IAAI,CAAC+J,qBAAqB,IAAI,IAAI,CAACrH,sBAAsB,CAAC,CAAC,EAAE;MAC9D;MACA,IAAI,CAACqH,qBAAqB,GAAG,IAAI;MACjC,MAAMyE,aAAa,GAAG,IAAI,CAACnF,SAAS,CAAC7H,GAAG,EAAEgC,MAAM,EAAE,IAAI,CAACsG,iBAAiB,CAAC;MACzE,IAAI,CAACC,qBAAqB,GAAG,KAAK;MAClC,OAAOyE,aAAa;IACxB;IACA,OAAO,IAAI,CAAChM,cAAc,CAAC2D,MAAM,CAAC3E,GAAG,EAAE,IAAI,CAACiN,qBAAqB,CAAC,CAAC,EAAEjL,MAAM,CAAC;EAChF;EACA;AACJ;AACA;EACIqI,aAAaA,CAACjO,IAAI,EAAE;IAChB,OAAO,IAAI,CAAC8Q,oBAAoB,CAAC,CAAC,CAACC,OAAO,CAAC/Q,IAAI,CAAC,KAAK,CAAC,CAAC;EAC3D;EACA;AACJ;AACA;AACA;AACA;AACA;EACIiP,MAAMA,CAACjP,IAAI,EAAE;IACT,OAAO,IAAI,CAAC8Q,oBAAoB,CAAC,CAAC,CAACC,OAAO,CAAC/Q,IAAI,CAAC,KAAK,CAAC,CAAC;EAC3D;EACA;AACJ;AACA;AACA;AACA;AACA;EACIgR,iBAAiBA,CAAC1Q,IAAI,EAAEkK,YAAY,EAAE;IAClC,MAAMyG,QAAQ,GAAGrH,gBAAgB,CAACtJ,IAAI,CAAC;IACvC,IAAI,IAAI,CAAC2N,aAAa,CAAC3N,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC4Q,mBAAmB,CAACD,QAAQ,CAAC,EAAE;MACjE,OAAO1R,aAAa,CAAC,CACjB,IAAI,CAACsO,IAAI,CAACoD,QAAQ,CAAC,EACnB,IAAI,CAACpD,IAAI,CAACvN,IAAI,EAAE;QAAEkK;MAAa,CAAC,CAAC,CACpC,CAAC;IACN;IACA,OAAO,IAAI,CAACqD,IAAI,CAACvN,IAAI,EAAE;MAAEkK;IAAa,CAAC,CAAC;EAC5C;EACA;AACJ;AACA;EACI2G,sBAAsBA,CAACzB,WAAW,EAAE;IAChC,IAAI,IAAI,CAACzB,aAAa,CAACyB,WAAW,CAAC,IAC/B,CAAC,IAAI,CAACT,MAAM,CAACrF,gBAAgB,CAAC8F,WAAW,CAAC,CAAC,EAAE;MAC7C,OAAO,GAAGA,WAAW,IAAI,IAAI,CAAClC,aAAa,CAAC,CAAC,EAAE;IACnD;IACA,OAAOkC,WAAW;EACtB;EACA;AACJ;AACA;EACI0B,cAAcA,CAAC/N,KAAK,EAAEgO,KAAK,EAAE;IACzB,IAAI,CAAC,IAAI,CAAChM,MAAM,CAACiM,YAAY,EAAE;MAC3B,IAAI,CAACjM,MAAM,CAACiM,YAAY,GAAG,CAAC,CAAC;IACjC;IACA,IAAI,CAACjM,MAAM,CAACiM,YAAY,CAACjO,KAAK,CAAC,GAAGgO,KAAK;EAC3C;EACAH,mBAAmBA,CAAClR,IAAI,EAAE;IACtB,OAAO0B,IAAI,CAAC,IAAI,CAAC3B,cAAc,CAACC,IAAI,CAAC,CAAC;EAC1C;EACA8Q,oBAAoBA,CAAA,EAAG;IACnB,MAAMS,KAAK,GAAG,IAAI,CAAC3D,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC,IAAI5L,QAAQ,CAACuP,KAAK,CAAC,EAAE;MACjB,OAAO,IAAI,CAAC3D,iBAAiB,CAAC,CAAC;IACnC;IACA,OAAO,IAAI,CAACA,iBAAiB,CAAC,CAAC,CAAC9O,GAAG,CAAE0S,CAAC,IAAKA,CAAC,CAACC,EAAE,CAAC;EACpD;EACAZ,qBAAqBA,CAAA,EAAG;IACpB,OAAO;MACH,GAAG,IAAI,CAACxL,MAAM;MACdqM,UAAU,EAAE,IAAI,CAAClE,aAAa,CAAC,CAAC;MAChC7I,cAAc,EAAE,IAAI,CAACA,cAAc;MACnCL,WAAW,EAAE,IAAI,CAACA;IACtB,CAAC;EACL;EACA;AACJ;AACA;AACA;EACIQ,sBAAsBA,CAAC9E,IAAI,EAAE;IACzB,OAAQ,IAAI,CAACqF,MAAM,CAACT,cAAc,CAACE,sBAAsB,IACrD9E,IAAI,KAAK,IAAI,CAACkM,iBAAiB;EACvC;EACAqC,aAAaA,CAACvO,IAAI,EAAE6F,WAAW,EAAE;IAC7B,IAAI,CAACmK,cAAc,CAACnK,WAAW,EAAE7F,IAAI,EAAE;MAAEmQ,UAAU,EAAE;IAAM,CAAC,CAAC;IAC7D,IAAI,CAAC7D,MAAM,CAACoB,IAAI,CAAC;MACbR,UAAU,EAAE,CAAC,CAAC,IAAI,CAACd,WAAW,CAAC1K,IAAI;MACnCuF,IAAI,EAAE,wBAAwB;MAC9BmG,OAAO,EAAEzC,eAAe,CAAC3K,IAAI;IACjC,CAAC,CAAC;IACF,IAAI,CAACoM,WAAW,CAACpI,OAAO,CAAEwN,CAAC,IAAK,IAAI,CAACvF,KAAK,CAAC0F,MAAM,CAACH,CAAC,CAAC,CAAC;IACrD,IAAI,CAACpF,WAAW,CAACmB,KAAK,CAAC,CAAC;EAC5B;EACAmB,aAAaA,CAAC1O,IAAI,EAAE4R,WAAW,EAAE;IAC7B;IACA;IACA,IAAI/O,KAAK,CAAC+O,WAAW,CAACC,aAAa,CAAC,EAAE;MAClCD,WAAW,CAACC,aAAa,GAAG,CAAC;MAC7B,IAAI,CAACD,WAAW,CAACE,aAAa,EAAE;QAC5BF,WAAW,CAACE,aAAa,GAAG,IAAI,CAAChG,gBAAgB,CAACzC,YAAY,CAACrJ,IAAI,CAAC;MACxE;IACJ;IACA,MAAMuH,QAAQ,GAAGvH,IAAI,CAACW,KAAK,CAAC,GAAG,CAAC;IAChC,MAAMoR,SAAS,GAAGH,WAAW,CAACE,aAAa;IAC3C,MAAME,QAAQ,GAAGD,SAAS,CAACH,WAAW,CAACC,aAAa,CAAC;IACrD,IAAI,CAACzF,WAAW,CAAC6F,GAAG,CAACjS,IAAI,CAAC;IAC1B;IACA,IAAI,IAAI,CAACiM,KAAK,CAACiG,GAAG,CAACF,QAAQ,CAAC,EAAE;MAC1B,IAAI,CAACzD,aAAa,CAACyD,QAAQ,EAAE,IAAI,CAACjS,cAAc,CAACiS,QAAQ,CAAC,CAAC;MAC3D,OAAOxS,KAAK;IAChB;IACA,MAAM2S,cAAc,GAAGH,QAAQ,KAAKzK,QAAQ,CAACA,QAAQ,CAACpG,MAAM,GAAG,CAAC,CAAC;IACjE,IAAI,CAAC6Q,QAAQ,IAAIG,cAAc,EAAE;MAC7B,IAAI3J,GAAG,GAAG,2DAA2D;MACrE,IAAIjB,QAAQ,CAACpG,MAAM,GAAG,CAAC,EAAE;QACrBqH,GAAG,IAAI,sCAAsC;MACjD;MACA,MAAM,IAAIP,KAAK,CAACO,GAAG,CAAC;IACxB;IACA,IAAImG,WAAW,GAAGqD,QAAQ;IAC1B;IACA,IAAIzK,QAAQ,CAACpG,MAAM,GAAG,CAAC,EAAE;MACrB;MACA;MACAoG,QAAQ,CAACA,QAAQ,CAACpG,MAAM,GAAG,CAAC,CAAC,GAAG6Q,QAAQ;MACxCrD,WAAW,GAAGpH,QAAQ,CAACoC,IAAI,CAAC,GAAG,CAAC;IACpC;IACAiI,WAAW,CAACC,aAAa,EAAE;IAC3B,IAAI,CAACvF,MAAM,CAACoB,IAAI,CAAC;MACbzG,IAAI,EAAE,wBAAwB;MAC9BmG,OAAO,EAAEzC,eAAe,CAAC3K,IAAI;IACjC,CAAC,CAAC;IACF,OAAO,IAAI,CAAC6N,IAAI,CAACc,WAAW,EAAEiD,WAAW,CAAC;EAC9C;EACAtB,cAAcA,CAACjN,KAAK,EAAE;IAClB,MAAM;MAAEiO,YAAY,GAAG,CAAC,CAAC;MAAEpM,MAAM,GAAG;QAAEC,UAAU,EAAE;MAAM;IAAE,CAAC,GAAG,IAAI,CAACE,MAAM;IACzE,OAAQiM,YAAY,CAACjO,KAAK,CAAC,KAAK6B,MAAM,CAACC,UAAU,GAAG9B,KAAK,GAAGhB,WAAW,CAACgB,KAAK,CAAC,CAAC;EACnF;EACA;AACJ;AACA;AACA;AACA;EACIuL,mBAAmBA,CAAC5O,IAAI,EAAE;IACtB,IAAI2O,WAAW,GAAG3O,IAAI;IACtB,IAAIqD,KAAK;IACT,IAAI,IAAI,CAAC4K,aAAa,CAACjO,IAAI,CAAC,EAAE;MAC1B;MACA,MAAMoS,aAAa,GAAGxI,gBAAgB,CAAC5J,IAAI,CAAC;MAC5C;MACA,MAAMqS,OAAO,GAAG,IAAI,CAACpD,MAAM,CAACmD,aAAa,CAAC;MAC1C;MACAzD,WAAW,GAAG0D,OAAO,GAAGD,aAAa,GAAG,IAAI,CAAC5E,aAAa,CAAC,CAAC;MAC5D;MACAnK,KAAK,GAAG,IAAI,CAACiN,cAAc,CAAC+B,OAAO,GAAG5I,gBAAgB,CAACzJ,IAAI,CAAC,GAAGA,IAAI,CAAC;IACxE;IACA,OAAO;MAAEqD,KAAK;MAAEsL;IAAY,CAAC;EACjC;EACAQ,cAAcA,CAACtJ,WAAW,EAAEjC,GAAG,EAAE;IAC7B,MAAMH,MAAM,GAAG,CAAC,CAAC;IACjB,MAAM6O,MAAM,GAAG,GAAG1O,GAAG,GAAG;IACxB,KAAK,MAAM2O,UAAU,IAAI1M,WAAW,EAAE;MAClC,IAAI0M,UAAU,CAACC,UAAU,CAACF,MAAM,CAAC,EAAE;QAC/B7O,MAAM,CAAC8O,UAAU,CAAChQ,OAAO,CAAC+P,MAAM,EAAE,EAAE,CAAC,CAAC,GAAGzM,WAAW,CAAC0M,UAAU,CAAC;MACpE;IACJ;IACA,OAAO9O,MAAM;EACjB;EACA6L,UAAUA,CAAC1L,GAAG,EAAE;IACZ,OAAOA,GAAG,YAAYoI,GAAG,GAAGpI,GAAG,CAACC,OAAO,CAAC,CAAC,GAAGtD,MAAM,CAACsD,OAAO,CAACD,GAAG,CAAC;EACnE;EACA,OAAO8C,IAAI,YAAA+L,yBAAA7L,iBAAA;IAAA,YAAAA,iBAAA,IAAwF+E,gBAAgB,EAnuB1BxO,EAAE,CAAAoM,QAAA,CAmuB0CrJ,gBAAgB,MAnuB5D/C,EAAE,CAAAoM,QAAA,CAmuBuFjE,oBAAoB,GAnuB7GnI,EAAE,CAAAoM,QAAA,CAmuBwHlB,yBAAyB,GAnuBnJlL,EAAE,CAAAoM,QAAA,CAmuB8JX,qBAAqB,GAnuBrLzL,EAAE,CAAAoM,QAAA,CAmuBgMrF,gBAAgB,GAnuBlN/G,EAAE,CAAAoM,QAAA,CAmuB6NL,2BAA2B;EAAA;EACnV,OAAOrC,KAAK,kBApuB6E1J,EAAE,CAAA2J,kBAAA;IAAAC,KAAA,EAouBY4E,gBAAgB;IAAAvH,OAAA,EAAhBuH,gBAAgB,CAAAjF,IAAA;IAAAvC,UAAA,EAAc;EAAM;AAC/I;AACA;EAAA,QAAAhE,SAAA,oBAAAA,SAAA,KAtuB6FhD,EAAE,CAAA6J,iBAAA,CAsuBJ2E,gBAAgB,EAAc,CAAC;IAC9G1E,IAAI,EAAE3J,UAAU;IAChBkK,IAAI,EAAE,CAAC;MAAErD,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE8C,IAAI,EAAEnE,SAAS;IAAE0G,UAAU,EAAE,CAAC;MAC/CvC,IAAI,EAAEvJ;IACV,CAAC,EAAE;MACCuJ,IAAI,EAAEzJ,MAAM;MACZgK,IAAI,EAAE,CAACtH,gBAAgB;IAC3B,CAAC;EAAE,CAAC,EAAE;IAAE+G,IAAI,EAAEnE,SAAS;IAAE0G,UAAU,EAAE,CAAC;MAClCvC,IAAI,EAAEzJ,MAAM;MACZgK,IAAI,EAAE,CAAClC,oBAAoB;IAC/B,CAAC;EAAE,CAAC,EAAE;IAAE2B,IAAI,EAAEnE,SAAS;IAAE0G,UAAU,EAAE,CAAC;MAClCvC,IAAI,EAAEzJ,MAAM;MACZgK,IAAI,EAAE,CAACa,yBAAyB;IACpC,CAAC;EAAE,CAAC,EAAE;IAAEpB,IAAI,EAAEnE,SAAS;IAAE0G,UAAU,EAAE,CAAC;MAClCvC,IAAI,EAAEzJ,MAAM;MACZgK,IAAI,EAAE,CAACoB,qBAAqB;IAChC,CAAC;EAAE,CAAC,EAAE;IAAE3B,IAAI,EAAEnE,SAAS;IAAE0G,UAAU,EAAE,CAAC;MAClCvC,IAAI,EAAEzJ,MAAM;MACZgK,IAAI,EAAE,CAACtD,gBAAgB;IAC3B,CAAC;EAAE,CAAC,EAAE;IAAE+C,IAAI,EAAEnE,SAAS;IAAE0G,UAAU,EAAE,CAAC;MAClCvC,IAAI,EAAEzJ,MAAM;MACZgK,IAAI,EAAE,CAAC0B,2BAA2B;IACtC,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,MAAMwJ,wBAAwB,CAAC;EAC3BC,IAAI;EACJ,OAAOjM,IAAI,YAAAkM,iCAAAhM,iBAAA;IAAA,YAAAA,iBAAA,IAAwF8L,wBAAwB;EAAA;EAC3H,OAAOG,IAAI,kBAlwB8E1V,EAAE,CAAA2V,iBAAA;IAAA7L,IAAA,EAkwBJyL,wBAAwB;IAAAK,SAAA;IAAAC,MAAA;MAAAL,IAAA;IAAA;IAAAM,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAlwBtBnW,EAAE,CAAAqW,SAAA,YAmwB5B,CAAC;MAAA;MAAA,IAAAF,EAAA;QAnwByBnW,EAAE,CAAAsW,UAAA,cAAAF,GAAA,CAAAZ,IAAA,EAAFxV,EAAE,CAAAuW,cAmwBnC,CAAC;MAAA;IAAA;IAAAC,aAAA;EAAA;AAE7D;AACA;EAAA,QAAAxT,SAAA,oBAAAA,SAAA,KAtwB6FhD,EAAE,CAAA6J,iBAAA,CAswBJ0L,wBAAwB,EAAc,CAAC;IACtHzL,IAAI,EAAEtJ,SAAS;IACf6J,IAAI,EAAE,CAAC;MACC4L,QAAQ,EAAE;AAC9B;AACA,GAAG;MACiBQ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEjB,IAAI,EAAE,CAAC;MACrB1L,IAAI,EAAErJ;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMiW,eAAe,CAAC;EAClBC,IAAI;EACJC,GAAG;EACHjU,WAAWA,CAACgU,IAAI,EAAEC,GAAG,EAAE;IACnB,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,GAAG,GAAGA,GAAG;EAClB;EACAC,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACF,IAAI,YAAYjW,WAAW,EAAE;MAClC,IAAI,CAACkW,GAAG,CAACE,kBAAkB,CAAC,IAAI,CAACH,IAAI,CAAC;IAC1C,CAAC,MACI,IAAI9R,QAAQ,CAAC,IAAI,CAAC8R,IAAI,CAAC,EAAE;MAC1B,MAAMI,YAAY,GAAG,IAAI,CAACH,GAAG,CAACI,eAAe,CAACzB,wBAAwB,CAAC;MACvEwB,YAAY,CAACE,QAAQ,CAACzB,IAAI,GAAG,IAAI,CAACmB,IAAI;MACtCI,YAAY,CAACG,QAAQ,CAACC,aAAa,CAAC,CAAC;IACzC,CAAC,MACI;MACD,IAAI,CAACP,GAAG,CAACI,eAAe,CAAC,IAAI,CAACL,IAAI,CAAC;IACvC;EACJ;EACAS,UAAUA,CAAA,EAAG;IACT,IAAI,CAACR,GAAG,CAACxG,KAAK,CAAC,CAAC;EACpB;AACJ;AAEA,MAAMiH,cAAc,GAAG,IAAIpX,cAAc,CAAC+C,SAAS,GAAG,gBAAgB,GAAG,EAAE,CAAC;AAE5E,MAAMsU,0BAA0B,GAAG,IAAIrX,cAAc,CAAC+C,SAAS,GAAG,4BAA4B,GAAG,EAAE,CAAC;AAEpG,MAAMuU,eAAe,GAAG,IAAItX,cAAc,CAAC+C,SAAS,GAAG,iBAAiB,GAAG,EAAE,CAAC;AAE9E,MAAMwU,YAAY,CAAC;EACfC,WAAW,GAAG,KAAK;EACnB;EACAC,OAAOA,CAAC;IAAEC,MAAM;IAAEC,QAAQ;IAAEC;EAAO,CAAC,EAAE;IAClC,IAAIhV,IAAI,GAAGgV,MAAM;IACjB;AACR;AACA;AACA;IACQ,IAAI,IAAI,CAACJ,WAAW,EAAE;MAClB5U,IAAI,GAAGgV,MAAM;MACb,OAAOhV,IAAI;IACf;IACA,IAAI+U,QAAQ,EAAE;MACV,MAAM,GAAGE,SAAS,CAAC,GAAGpL,YAAY,CAACkL,QAAQ,EAAE,QAAQ,CAAC;MACtD/U,IAAI,GAAGiV,SAAS;IACpB;IACA,IAAIH,MAAM,EAAE;MACR,MAAM,GAAGG,SAAS,CAAC,GAAGpL,YAAY,CAACiL,MAAM,EAAE,QAAQ,CAAC;MACpD9U,IAAI,GAAGiV,SAAS;IACpB;IACA,IAAI,CAACL,WAAW,GAAG,IAAI;IACvB,OAAO5U,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIkV,uBAAuBA,CAAClV,IAAI,EAAE;IAC1B,MAAMqD,KAAK,GAAGoG,gBAAgB,CAACzJ,IAAI,CAAC;IACpC,OAAOqD,KAAK,GAAGuG,gBAAgB,CAAC5J,IAAI,CAAC,GAAGA,IAAI;EAChD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACImV,eAAeA,CAACnV,IAAI,EAAEqD,KAAK,EAAE;IACzB,OAAOA,KAAK,GAAG,GAAGA,KAAK,IAAIrD,IAAI,EAAE,GAAGA,IAAI;EAC5C;AACJ;AAEA,MAAMoV,aAAa,CAAC;EAChBlL,OAAO;EACPpK,WAAWA,CAACoK,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B;EACA;EACA2K,OAAOA,CAACjP,MAAM,EAAE;IACZ,MAAM;MAAEkP,MAAM;MAAEC;IAAS,CAAC,GAAGnP,MAAM;IACnC,IAAIkP,MAAM,EAAE;MACR,OAAOA,MAAM;IACjB;IACA,IAAIC,QAAQ,EAAE;MACV,IAAI3R,aAAa,CAAC2R,QAAQ,CAAC,EAAE;QACzB,MAAM;UAAE1R,KAAK;UAAEgO,KAAK,GAAG,IAAI,CAACnH,OAAO,CAAC7E,MAAM,CAACH,MAAM,CAACC,UAAU,GACtD9B,KAAK,GACLhB,WAAW,CAACgB,KAAK;QAAG,CAAC,GAAG0R,QAAQ;QACtC,IAAI,CAAC7K,OAAO,CAACkH,cAAc,CAAC/N,KAAK,EAAEgO,KAAK,CAAC;QACzC,OAAOhO,KAAK;MAChB;MACA,OAAO0R,QAAQ;IACnB;IACA,OAAOjS,SAAS;EACpB;AACJ;AAEA,MAAMuS,kBAAkB,CAAC;EACrB5I,UAAU,GAAGpP,MAAM,CAACI,UAAU,CAAC;EAC/ByM,OAAO,GAAG7M,MAAM,CAACsO,gBAAgB,CAAC;EAClC2J,GAAG,GAAGjY,MAAM,CAACQ,WAAW,EAAE;IACtB2H,QAAQ,EAAE;EACd,CAAC,CAAC;EACF+P,YAAY,GAAGlY,MAAM,CAACmX,cAAc,EAAE;IAAEhP,QAAQ,EAAE;EAAK,CAAC,CAAC;EACzDkF,aAAa,GAAGrN,MAAM,CAACqX,eAAe,EAAE;IAAElP,QAAQ,EAAE;EAAK,CAAC,CAAC;EAC3DgQ,kBAAkB,GAAGnY,MAAM,CAACoX,0BAA0B,EAAE;IACpDjP,QAAQ,EAAE;EACd,CAAC,CAAC;EACFiQ,GAAG,GAAGpY,MAAM,CAACS,iBAAiB,CAAC;EAC/B4X,IAAI,GAAGrY,MAAM,CAACU,UAAU,CAAC;EACzBgW,GAAG,GAAG1W,MAAM,CAACW,gBAAgB,CAAC;EAC9B2X,QAAQ,GAAGtY,MAAM,CAACY,SAAS,CAAC;EAC5B6V,IAAI;EACJ8B,IAAI,GAAG,IAAI5J,GAAG,CAAC,CAAC;EAChBpI,GAAG;EACHgC,MAAM,GAAG,CAAC,CAAC;EACXiQ,WAAW;EACX;EACAC,UAAU;EACVxD,MAAM;EACNyD,UAAU;EACVC,SAAS;EACTlG,WAAW;EACXmG,gBAAgB;EAChB;EACArB,WAAW,GAAG,KAAK;EACnBtU,IAAI;EACJ4V,YAAY,GAAG,IAAIvB,YAAY,CAAC,CAAC;EACjCwB,aAAa,GAAG,IAAIf,aAAa,CAAC,IAAI,CAAClL,OAAO,CAAC;EAC/CkM,QAAQ,GAAG,IAAI,CAACd,GAAG,KAAK,IAAI,GAAG,WAAW,GAAG,YAAY;EACzD,OAAOe,sBAAsBA,CAACC,GAAG,EAAE/C,GAAG,EAAE;IACpC,OAAO,IAAI;EACf;EACAgD,QAAQA,CAAA,EAAG;IACP,MAAMlM,kBAAkB,GAAGJ,yBAAyB,CAAC,IAAI,CAACC,OAAO,EAAE,IAAI,CAACqL,YAAY,IAAI,IAAI,CAACQ,UAAU,CAAC;IACxG,IAAI,CAAC7L,OAAO,CAAC6B,YAAY,CACpBP,IAAI,CAAClM,SAAS,CAAEoS,UAAU,IAAK;MAChC,MAAM1R,IAAI,GAAG,IAAI,CAACkW,YAAY,CAACrB,OAAO,CAAC;QACnCC,MAAM,EAAE,IAAI,CAACiB,UAAU;QACvBhB,QAAQ,EAAE,IAAI,CAACQ,YAAY;QAC3BP,MAAM,EAAEtD;MACZ,CAAC,CAAC;MACF,OAAOnQ,KAAK,CAACC,OAAO,CAAC,IAAI,CAACkJ,aAAa,CAAC,GAClCzL,QAAQ,CAAC,IAAI,CAACyL,aAAa,CAAC5L,GAAG,CAAE4L,aAAa,IAAK,IAAI,CAAC8L,YAAY,CAACxW,IAAI,EAAE0K,aAAa,CAAC,CAAC,CAAC,GAC3F,IAAI,CAAC8L,YAAY,CAACxW,IAAI,EAAE,IAAI,CAAC0K,aAAa,CAAC;IACrD,CAAC,CAAC,EAAEN,mBAAmB,CAACC,kBAAkB,CAAC,EAAE5K,kBAAkB,CAAC,IAAI,CAACgN,UAAU,CAAC,CAAC,CAC5EQ,SAAS,CAAC,MAAM;MACjB,IAAI,CAAC6C,WAAW,GAAG,IAAI,CAACoG,YAAY,CAAChB,uBAAuB,CAAC,IAAI,CAAC5U,IAAI,CAAC;MACvE,IAAI,CAAC8V,QAAQ,KAAK,WAAW,GACvB,IAAI,CAACK,iBAAiB,CAAC,CAAC,GACxB,IAAI,CAACC,kBAAkB,CAAC,IAAI,CAAC5G,WAAW,EAAE,IAAI,CAACwC,MAAM,IAAI,IAAI,CAACwD,UAAU,CAAC;MAC/E,IAAI,CAACL,GAAG,CAACkB,YAAY,CAAC,CAAC;MACvB,IAAI,CAAC/B,WAAW,GAAG,IAAI;IAC3B,CAAC,CAAC;IACF,IAAI,CAAC,IAAI,CAACA,WAAW,EAAE;MACnB,MAAMgC,cAAc,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;MACnD,IAAID,cAAc,EAAE;QAChB,IAAI,CAACX,gBAAgB,GAAG,IAAIpC,eAAe,CAAC+C,cAAc,EAAE,IAAI,CAAC7C,GAAG,CAAC;QACrE,IAAI,CAACkC,gBAAgB,CAACjC,UAAU,CAAC,CAAC;MACtC;IACJ;EACJ;EACA8C,WAAWA,CAACC,OAAO,EAAE;IACjB;IACA;IACA,IAAI,IAAI,CAACX,QAAQ,KAAK,WAAW,EAAE;MAC/B,MAAMY,OAAO,GAAGzW,MAAM,CAACsB,IAAI,CAACkV,OAAO,CAAC,CAACE,IAAI,CAAExQ,CAAC,IAAK,CAACsQ,OAAO,CAACtQ,CAAC,CAAC,CAACyQ,WAAW,CAAC;MACzEF,OAAO,IAAI,IAAI,CAACP,iBAAiB,CAAC,CAAC;IACvC;EACJ;EACAA,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACU,YAAY,CAAC,CAAC;IACnB,IAAI,CAACxB,QAAQ,CAACyB,WAAW,CAAC,IAAI,CAAC1B,IAAI,CAAC2B,aAAa,EAAE,WAAW,EAAE,IAAI,CAACnN,OAAO,CAACuB,SAAS,CAAC,IAAI,CAAC7H,GAAG,EAAE,IAAI,CAACgC,MAAM,EAAE,IAAI,CAACkK,WAAW,CAAC,CAAC;EACpI;EACA4G,kBAAkBA,CAAC1W,IAAI,EAAEsS,MAAM,EAAE;IAC7B,IAAI,CAACsD,IAAI,CAACrI,KAAK,CAAC,CAAC;IACjB,MAAM+J,WAAW,GAAG,IAAI,CAACC,cAAc,CAACvX,IAAI,EAAEsS,MAAM,CAAC;IACrD,IAAI,IAAI,CAACwB,IAAI,EAAE;MACX;MACA,IAAI,CAACA,IAAI,CAAC0D,OAAO,CAAC,WAAW,CAAC,GAAGF,WAAW;MAC5C,IAAI,CAACxD,IAAI,CAAC0D,OAAO,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC1H,WAAW;IACvD,CAAC,MACI;MACD,IAAI,CAACqH,YAAY,CAAC,CAAC;MACnB,IAAI,CAACrD,IAAI,GAAG,IAAI,CAACC,GAAG,CAACE,kBAAkB,CAAC,IAAI,CAACqB,GAAG,EAAE;QAC9CmC,SAAS,EAAEH,WAAW;QACtBxH,WAAW,EAAE,IAAI,CAACA;MACtB,CAAC,CAAC;IACN;EACJ;EACAyH,cAAcA,CAACvX,IAAI,EAAEsS,MAAM,EAAE;IACzB,OAAO,CAAC1O,GAAG,EAAEgC,MAAM,KAAK;MACpB,MAAM8R,UAAU,GAAGpF,MAAM,GAAG,GAAGA,MAAM,IAAI1O,GAAG,EAAE,GAAGA,GAAG;MACpD,MAAM+T,OAAO,GAAG/R,MAAM,GAChB,GAAG8R,UAAU,GAAGhL,IAAI,CAACE,SAAS,CAAChH,MAAM,CAAC,EAAE,GACxC8R,UAAU;MAChB,IAAI,CAAC,IAAI,CAAC9B,IAAI,CAAC1D,GAAG,CAACyF,OAAO,CAAC,EAAE;QACzB,IAAI,CAAC/B,IAAI,CAACpH,GAAG,CAACmJ,OAAO,EAAE,IAAI,CAACzN,OAAO,CAACuB,SAAS,CAACiM,UAAU,EAAE9R,MAAM,EAAE5F,IAAI,CAAC,CAAC;MAC5E;MACA,OAAO,IAAI,CAAC4V,IAAI,CAAC3V,GAAG,CAAC0X,OAAO,CAAC;IACjC,CAAC;EACL;EACAd,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACb,SAAS,IAAI,IAAI,CAACR,kBAAkB;EACpD;EACAoC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAChC,IAAI,CAACrI,KAAK,CAAC,CAAC;EACrB;EACA4J,YAAYA,CAAA,EAAG;IACX,IAAI,CAAClB,gBAAgB,EAAE1B,UAAU,CAAC,CAAC;EACvC;EACAiC,YAAYA,CAACxW,IAAI,EAAE0K,aAAa,EAAE;IAC9B,MAAMmN,aAAa,GAAG,IAAI,CAAC1B,aAAa,CAACtB,OAAO,CAAC;MAC7CC,MAAM,EAAE,IAAI,CAACe,WAAW;MACxBd,QAAQ,EAAErK;IACd,CAAC,CAAC;IACF,IAAI,CAACpK,IAAI,GAAG,IAAI,CAAC4V,YAAY,CAACf,eAAe,CAACnV,IAAI,EAAE6X,aAAa,CAAC;IAClE,MAAMrN,YAAY,GAAGC,mBAAmB,CAACC,aAAa,EAAEmN,aAAa,CAAC;IACtE,OAAO,IAAI,CAAC3N,OAAO,CAAC8G,iBAAiB,CAAC,IAAI,CAAC1Q,IAAI,EAAEkK,YAAY,CAAC;EAClE;EACA,OAAO9D,IAAI,YAAAoR,2BAAAlR,iBAAA;IAAA,YAAAA,iBAAA,IAAwFyO,kBAAkB;EAAA;EACrH,OAAO0C,IAAI,kBA3/B8E5a,EAAE,CAAA6a,iBAAA;IAAA/Q,IAAA,EA2/BJoO,kBAAkB;IAAAtC,SAAA;IAAAC,MAAA;MAAApP,GAAA;MAAAgC,MAAA;MAAAiQ,WAAA;MAAAC,UAAA;MAAAxD,MAAA;MAAAyD,UAAA;MAAAC,SAAA;IAAA;IAAAiC,QAAA,GA3/BhB9a,EAAE,CAAA+a,oBAAA;EAAA;AA4/B/F;AACA;EAAA,QAAA/X,SAAA,oBAAAA,SAAA,KA7/B6FhD,EAAE,CAAA6J,iBAAA,CA6/BJqO,kBAAkB,EAAc,CAAC;IAChHpO,IAAI,EAAE/I,SAAS;IACfsJ,IAAI,EAAE,CAAC;MACC2Q,QAAQ,EAAE,aAAa;MACvBvE,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEhQ,GAAG,EAAE,CAAC;MACpBqD,IAAI,EAAErJ,KAAK;MACX4J,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC,CAAC;IAAE5B,MAAM,EAAE,CAAC;MACTqB,IAAI,EAAErJ,KAAK;MACX4J,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEqO,WAAW,EAAE,CAAC;MACd5O,IAAI,EAAErJ,KAAK;MACX4J,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC,CAAC;IAAEsO,UAAU,EAAE,CAAC;MACb7O,IAAI,EAAErJ,KAAK;MACX4J,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAE8K,MAAM,EAAE,CAAC;MACTrL,IAAI,EAAErJ,KAAK;MACX4J,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEuO,UAAU,EAAE,CAAC;MACb9O,IAAI,EAAErJ,KAAK;MACX4J,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAEwO,SAAS,EAAE,CAAC;MACZ/O,IAAI,EAAErJ,KAAK;MACX4J,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM4Q,aAAa,CAAC;EAChBlO,OAAO;EACPQ,aAAa;EACb6K,YAAY;EACZE,GAAG;EACH4C,YAAY,GAAG,IAAI;EACnBC,SAAS,GAAG,EAAE;EACdC,OAAO;EACPjY,IAAI;EACJ4V,YAAY,GAAG,IAAIvB,YAAY,CAAC,CAAC;EACjCwB,aAAa;EACbrW,WAAWA,CAACoK,OAAO,EAAEQ,aAAa,EAAE6K,YAAY,EAAEE,GAAG,EAAE;IACnD,IAAI,CAACvL,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACQ,aAAa,GAAGA,aAAa;IAClC,IAAI,CAAC6K,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACE,GAAG,GAAGA,GAAG;IACd,IAAI,CAACU,aAAa,GAAG,IAAIf,aAAa,CAAC,IAAI,CAAClL,OAAO,CAAC;EACxD;EACA;EACA;EACAsO,SAASA,CAAC5U,GAAG,EAAEgC,MAAM,EAAEmQ,UAAU,EAAE;IAC/B,IAAI,CAACnS,GAAG,EAAE;MACN,OAAOA,GAAG;IACd;IACA,MAAM6U,OAAO,GAAG7S,MAAM,GAAG,GAAGhC,GAAG,GAAG8I,IAAI,CAACE,SAAS,CAAChH,MAAM,CAAC,EAAE,GAAGhC,GAAG;IAChE,IAAI6U,OAAO,KAAK,IAAI,CAACF,OAAO,EAAE;MAC1B,OAAO,IAAI,CAACD,SAAS;IACzB;IACA,IAAI,CAACC,OAAO,GAAGE,OAAO;IACtB,IAAI,CAACJ,YAAY,EAAEK,WAAW,CAAC,CAAC;IAChC,MAAMrO,kBAAkB,GAAGJ,yBAAyB,CAAC,IAAI,CAACC,OAAO,EAAE,IAAI,CAACqL,YAAY,IAAIQ,UAAU,CAAC;IACnG,IAAI,CAACsC,YAAY,GAAG,IAAI,CAACnO,OAAO,CAAC6B,YAAY,CACxCP,IAAI,CAAClM,SAAS,CAAEoS,UAAU,IAAK;MAChC,MAAM1R,IAAI,GAAG,IAAI,CAACkW,YAAY,CAACrB,OAAO,CAAC;QACnCC,MAAM,EAAEiB,UAAU;QAClBhB,QAAQ,EAAE,IAAI,CAACQ,YAAY;QAC3BP,MAAM,EAAEtD;MACZ,CAAC,CAAC;MACF,OAAOnQ,KAAK,CAACC,OAAO,CAAC,IAAI,CAACkJ,aAAa,CAAC,GAClCzL,QAAQ,CAAC,IAAI,CAACyL,aAAa,CAAC5L,GAAG,CAAE4L,aAAa,IAAK,IAAI,CAAC8L,YAAY,CAACxW,IAAI,EAAE0K,aAAa,CAAC,CAAC,CAAC,GAC3F,IAAI,CAAC8L,YAAY,CAACxW,IAAI,EAAE,IAAI,CAAC0K,aAAa,CAAC;IACrD,CAAC,CAAC,EAAEN,mBAAmB,CAACC,kBAAkB,CAAC,CAAC,CACvC4C,SAAS,CAAC,MAAM,IAAI,CAAC0L,WAAW,CAAC/U,GAAG,EAAEgC,MAAM,CAAC,CAAC;IACnD,OAAO,IAAI,CAAC0S,SAAS;EACzB;EACAV,WAAWA,CAAA,EAAG;IACV,IAAI,CAACS,YAAY,EAAEK,WAAW,CAAC,CAAC;IAChC;IACA;IACA,IAAI,CAACL,YAAY,GAAG,IAAI;EAC5B;EACAM,WAAWA,CAAC/U,GAAG,EAAEgC,MAAM,EAAE;IACrB,MAAM5F,IAAI,GAAG,IAAI,CAACkW,YAAY,CAAChB,uBAAuB,CAAC,IAAI,CAAC5U,IAAI,CAAC;IACjE,IAAI,CAACgY,SAAS,GAAG,IAAI,CAACpO,OAAO,CAACuB,SAAS,CAAC7H,GAAG,EAAEgC,MAAM,EAAE5F,IAAI,CAAC;IAC1D,IAAI,CAACyV,GAAG,CAACkB,YAAY,CAAC,CAAC;EAC3B;EACAH,YAAYA,CAACxW,IAAI,EAAE0K,aAAa,EAAE;IAC9B,MAAMmN,aAAa,GAAG,IAAI,CAAC1B,aAAa,CAACtB,OAAO,CAAC;MAC7CC,MAAM,EAAEhS,SAAS;MACjBiS,QAAQ,EAAErK;IACd,CAAC,CAAC;IACF,IAAI,CAACpK,IAAI,GAAG,IAAI,CAAC4V,YAAY,CAACf,eAAe,CAACnV,IAAI,EAAE6X,aAAa,CAAC;IAClE,MAAMrN,YAAY,GAAGC,mBAAmB,CAACC,aAAa,EAAEmN,aAAa,CAAC;IACtE,OAAO,IAAI,CAAC3N,OAAO,CAAC8G,iBAAiB,CAAC,IAAI,CAAC1Q,IAAI,EAAEkK,YAAY,CAAC;EAClE;EACA,OAAO9D,IAAI,YAAAkS,sBAAAhS,iBAAA;IAAA,YAAAA,iBAAA,IAAwFwR,aAAa,EA3lCvBjb,EAAE,CAAA0b,iBAAA,CA2lCuClN,gBAAgB,OA3lCzDxO,EAAE,CAAA0b,iBAAA,CA2lCoEnE,eAAe,OA3lCrFvX,EAAE,CAAA0b,iBAAA,CA2lCgHrE,cAAc,OA3lChIrX,EAAE,CAAA0b,iBAAA,CA2lC2J1b,EAAE,CAACW,iBAAiB;EAAA;EAC1Q,OAAOgb,KAAK,kBA5lC6E3b,EAAE,CAAA4b,YAAA;IAAAC,IAAA;IAAA/R,IAAA,EA4lCMmR,aAAa;IAAAa,IAAA;EAAA;AAClH;AACA;EAAA,QAAA9Y,SAAA,oBAAAA,SAAA,KA9lC6FhD,EAAE,CAAA6J,iBAAA,CA8lCJoR,aAAa,EAAc,CAAC;IAC3GnR,IAAI,EAAE9I,IAAI;IACVqJ,IAAI,EAAE,CAAC;MACCwR,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,KAAK;MACXrF,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE3M,IAAI,EAAE0E;EAAiB,CAAC,EAAE;IAAE1E,IAAI,EAAEnE,SAAS;IAAE0G,UAAU,EAAE,CAAC;MAC3EvC,IAAI,EAAEvJ;IACV,CAAC,EAAE;MACCuJ,IAAI,EAAEzJ,MAAM;MACZgK,IAAI,EAAE,CAACkN,eAAe;IAC1B,CAAC;EAAE,CAAC,EAAE;IAAEzN,IAAI,EAAEnE,SAAS;IAAE0G,UAAU,EAAE,CAAC;MAClCvC,IAAI,EAAEvJ;IACV,CAAC,EAAE;MACCuJ,IAAI,EAAEzJ,MAAM;MACZgK,IAAI,EAAE,CAACgN,cAAc;IACzB,CAAC;EAAE,CAAC,EAAE;IAAEvN,IAAI,EAAE9J,EAAE,CAACW;EAAkB,CAAC,CAAC;AAAA;AAErD,MAAMob,IAAI,GAAG,CAAC7D,kBAAkB,EAAE+C,aAAa,CAAC;AAChD,MAAMe,eAAe,CAAC;EAClB,OAAOzS,IAAI,YAAA0S,wBAAAxS,iBAAA;IAAA,YAAAA,iBAAA,IAAwFuS,eAAe;EAAA;EAClH,OAAOE,IAAI,kBApnC8Elc,EAAE,CAAAmc,gBAAA;IAAArS,IAAA,EAonCSkS,eAAe;IAAAI,OAAA,GAAYlE,kBAAkB,EAAE+C,aAAa;IAAAoB,OAAA,GAAanE,kBAAkB,EAAE+C,aAAa;EAAA;EAC9M,OAAOqB,IAAI,kBArnC8Etc,EAAE,CAAAuc,gBAAA;AAsnC/F;AACA;EAAA,QAAAvZ,SAAA,oBAAAA,SAAA,KAvnC6FhD,EAAE,CAAA6J,iBAAA,CAunCJmS,eAAe,EAAc,CAAC;IAC7GlS,IAAI,EAAE7I,QAAQ;IACdoJ,IAAI,EAAE,CAAC;MACC+R,OAAO,EAAEL,IAAI;MACbM,OAAO,EAAEN;IACb,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASS,gBAAgBA,CAAC7O,OAAO,EAAE;EAC/B,MAAM8O,SAAS,GAAG,CACdC,0BAA0B,CAACtU,iBAAiB,CAAC,EAC7CuU,8BAA8B,CAACxR,qBAAqB,CAAC,EACrDyR,2BAA2B,CAAClR,kBAAkB,CAAC,EAC/CmR,gCAAgC,CAAC7Q,uBAAuB,CAAC,CAC5D;EACD,IAAI2B,OAAO,CAACzF,MAAM,EAAE;IAChBuU,SAAS,CAACnS,IAAI,CAACwS,sBAAsB,CAACnP,OAAO,CAACzF,MAAM,CAAC,CAAC;EAC1D;EACA,IAAIyF,OAAO,CAACvH,MAAM,EAAE;IAChBqW,SAAS,CAACnS,IAAI,CAACyS,sBAAsB,CAACpP,OAAO,CAACvH,MAAM,CAAC,CAAC;EAC1D;EACA,OAAOqW,SAAS;AACpB;AACA,SAASK,sBAAsBA,CAAC5U,MAAM,EAAE;EACpC,OAAOhH,wBAAwB,CAAC,CAC5B;IACI8b,OAAO,EAAEjW,gBAAgB;IACzBkW,QAAQ,EAAEhV,eAAe,CAACC,MAAM;EACpC,CAAC,CACJ,CAAC;AACN;AACA,SAAS6U,sBAAsBA,CAAC3W,MAAM,EAAE;EACpC,OAAOlF,wBAAwB,CAAC,CAC5B;IAAE8b,OAAO,EAAEja,gBAAgB;IAAEma,QAAQ,EAAE9W;EAAO,CAAC,CAClD,CAAC;AACN;AACA,SAAS+W,qBAAqBA,CAAC,GAAGpV,MAAM,EAAE;EACtC,OAAOA,MAAM,CAACpG,GAAG,CAAEuE,KAAK,KAAM;IAC1B8W,OAAO,EAAEzF,eAAe;IACxB0F,QAAQ,EAAE/W,KAAK;IACfkX,KAAK,EAAE;EACX,CAAC,CAAC,CAAC;AACP;AACA,SAASC,0BAA0BA,CAACC,OAAO,EAAE;EACzC,OAAO;IACHN,OAAO,EAAE1F,0BAA0B;IACnC2F,QAAQ,EAAEK;EACd,CAAC;AACL;AACA,SAASZ,0BAA0BA,CAACa,UAAU,EAAE;EAC5C,OAAOrc,wBAAwB,CAAC,CAC5B;IACI8b,OAAO,EAAE7U,oBAAoB;IAC7B+U,QAAQ,EAAEK,UAAU;IACpBC,IAAI,EAAE,CAACzW,gBAAgB;EAC3B,CAAC,CACJ,CAAC;AACN;AACA,SAAS8V,gCAAgCA,CAAC5D,QAAQ,EAAE;EAChD,OAAO/X,wBAAwB,CAAC,CAC5B;IACI8b,OAAO,EAAEjR,2BAA2B;IACpCmR,QAAQ,EAAEjE,QAAQ;IAClBuE,IAAI,EAAE,CAACzW,gBAAgB;EAC3B,CAAC,CACJ,CAAC;AACN;AACA,SAAS4V,8BAA8BA,CAACc,OAAO,EAAE;EAC7C,OAAOvc,wBAAwB,CAAC,CAC5B;IACI8b,OAAO,EAAE9R,yBAAyB;IAClCgS,QAAQ,EAAEO;EACd,CAAC,CACJ,CAAC;AACN;AACA,SAASb,2BAA2BA,CAAClO,WAAW,EAAE;EAC9C,OAAOxN,wBAAwB,CAAC,CAC5B;IACI8b,OAAO,EAAEvR,qBAAqB;IAC9ByR,QAAQ,EAAExO;EACd,CAAC,CACJ,CAAC;AACN;AACA,SAASgP,oBAAoBA,CAAC7a,IAAI,EAAE;EAChC,OAAO;IACHma,OAAO,EAAE3F,cAAc;IACvB4F,QAAQ,EAAEpa;EACd,CAAC;AACL;AAEA,MAAM8a,oBAAoB,GAAG,IAAI1d,cAAc,CAAC,oDAAoD,CAAC;AACrG,MAAM2d,sBAAsB,GAAG,IAAI3d,cAAc,CAAC,0CAA0C,CAAC;AAC7F,MAAM4d,aAAa,CAAC;EAChBrN,KAAK;EACL7N,WAAWA,CAAC6N,KAAK,EAAE;IACf,IAAI,CAACA,KAAK,GAAGA,KAAK;EACtB;EACA5N,cAAcA,CAACC,IAAI,EAAE;IACjB,OAAOrB,EAAE,CAAC,IAAI,CAACgP,KAAK,CAAC3N,IAAI,CAAC,CAAC;EAC/B;EACA,OAAO0G,IAAI,YAAAuU,sBAAArU,iBAAA;IAAA,YAAAA,iBAAA,IAAwFoU,aAAa,EA3tCvB7d,EAAE,CAAAoM,QAAA,CA2tCuCuR,oBAAoB;EAAA;EACtJ,OAAOjU,KAAK,kBA5tC6E1J,EAAE,CAAA2J,kBAAA;IAAAC,KAAA,EA4tCYiU,aAAa;IAAA5W,OAAA,EAAb4W,aAAa,CAAAtU;EAAA;AACxH;AACA;EAAA,QAAAvG,SAAA,oBAAAA,SAAA,KA9tC6FhD,EAAE,CAAA6J,iBAAA,CA8tCJgU,aAAa,EAAc,CAAC;IAC3G/T,IAAI,EAAE3J;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE2J,IAAI,EAAEnE,SAAS;IAAE0G,UAAU,EAAE,CAAC;MAC/CvC,IAAI,EAAEzJ,MAAM;MACZgK,IAAI,EAAE,CAACsT,oBAAoB;IAC/B,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB,SAASI,oBAAoBA,CAAChR,OAAO,EAAEyD,KAAK,GAAG,CAAC,CAAC,EAAE7C,OAAO,EAAE;EACxD,MAAMqQ,eAAe,GAAGA,CAAA,KAAMrQ,OAAO,CAACsQ,YAAY,GAC5CC,OAAO,CAACC,GAAG,CAAC/a,MAAM,CAACsB,IAAI,CAAC8L,KAAK,CAAC,CAAC7O,GAAG,CAAEkB,IAAI,IAAKkK,OAAO,CAAC2D,IAAI,CAAC7N,IAAI,CAAC,CAACub,SAAS,CAAC,CAAC,CAAC,CAAC,GAC7EF,OAAO,CAACxG,OAAO,CAAC,CAAC;EACvB,OAAOsG,eAAe;AAC1B;AACA,MAAMK,sBAAsB,CAAC;EACzB,OAAOC,OAAOA,CAAC3Q,OAAO,EAAE;IACpB,OAAO;MACH4Q,QAAQ,EAAEF,sBAAsB;MAChC5B,SAAS,EAAE,CACPD,gBAAgB,CAAC;QACbpW,MAAM,EAAEyX,aAAa;QACrB3V,MAAM,EAAE;UACJb,QAAQ,EAAE,IAAI;UACdI,cAAc,EAAE;YAAEC,aAAa,EAAE;UAAM,CAAC;UACxC,GAAGiG,OAAO,CAAC1F;QACf;MACJ,CAAC,CAAC,EACF;QACI+U,OAAO,EAAEW,oBAAoB;QAC7BV,QAAQ,EAAEtP,OAAO,CAAC6C;MACtB,CAAC,EACD;QACIwM,OAAO,EAAEY,sBAAsB;QAC/BX,QAAQ,EAAEtP;MACd,CAAC,EACD;QACIqP,OAAO,EAAE7b,eAAe;QACxBqd,UAAU,EAAET,oBAAoB;QAChCP,IAAI,EAAE,CACFhP,gBAAgB,EAChBmP,oBAAoB,EACpBC,sBAAsB,CACzB;QACDR,KAAK,EAAE;MACX,CAAC;IAET,CAAC;EACL;EACA,OAAO7T,IAAI,YAAAkV,+BAAAhV,iBAAA;IAAA,YAAAA,iBAAA,IAAwF4U,sBAAsB;EAAA;EACzH,OAAOnC,IAAI,kBA7wC8Elc,EAAE,CAAAmc,gBAAA;IAAArS,IAAA,EA6wCSuU,sBAAsB;IAAAhC,OAAA,GAAYL,eAAe;EAAA;EACrJ,OAAOM,IAAI,kBA9wC8Etc,EAAE,CAAAuc,gBAAA;IAAAH,OAAA,GA8wC2CJ,eAAe;EAAA;AACzJ;AACA;EAAA,QAAAhZ,SAAA,oBAAAA,SAAA,KAhxC6FhD,EAAE,CAAA6J,iBAAA,CAgxCJwU,sBAAsB,EAAc,CAAC;IACpHvU,IAAI,EAAE7I,QAAQ;IACdoJ,IAAI,EAAE,CAAC;MACCgS,OAAO,EAAE,CAACL,eAAe;IAC7B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA,SAAS0C,cAAcA,CAAA,EAAG;EACtB,IAAIC,WAAW,GAAGC,qBAAqB,CAAC,CAAC;EACzC,IAAI,CAACD,WAAW,IAAI,CAACnZ,SAAS,CAAC,CAAC,EAAE;IAC9B,OAAOG,SAAS;EACpB;EACA,IAAIgZ,WAAW,CAAC/K,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;IACjC+K,WAAW,GAAGA,WAAW,CAACnb,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC3C;EACA,IAAImb,WAAW,CAAC/K,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;IACjC+K,WAAW,GAAGA,WAAW,CAACnb,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC3C;EACA,OAAOmb,WAAW;AACtB;AACA;AACA;AACA;AACA,SAASC,qBAAqBA,CAAA,EAAG;EAC7B,IAAI,CAACpZ,SAAS,CAAC,CAAC,EAAE;IACd,OAAO,EAAE;EACb;EACA,MAAMqZ,SAAS,GAAGpZ,MAAM,CAACoZ,SAAS;EAClC,OAAOA,SAAS,CAACC,SAAS,GAAG,CAAC,CAAC,IAAID,SAAS,CAACjM,QAAQ;AACzD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmM,eAAeA,CAACtY,GAAG,EAAEgC,MAAM,EAAE5F,IAAI,EAAE2H,QAAQ,EAAE;EAClD,IAAI,CAACA,QAAQ,EAAE;IACXpJ,wBAAwB,CAAC2d,eAAe,CAAC;EAC7C;EACAvU,QAAQ,KAAKtK,MAAM,CAACE,QAAQ,CAAC;EAC7B,MAAMkG,MAAM,GAAGjF,qBAAqB,CAACmJ,QAAQ,EAAE,MAAM;IACjD,MAAMuC,OAAO,GAAG7M,MAAM,CAACsO,gBAAgB,CAAC;IACxC,MAAMtI,KAAK,GAAGmT,YAAY,CAACxW,IAAI,CAAC;IAChC,OAAON,YAAY,CAACyc,qBAAqB,CAACvY,GAAG,EAAEgC,MAAM,CAAC,CAAC,CAAC4F,IAAI,CAAClM,SAAS,CAAE8c,OAAO,IAAKlS,OAAO,CAAC6E,eAAe,CAACqN,OAAO,CAACxY,GAAG,EAAEwY,OAAO,CAACxW,MAAM,EAAEvC,KAAK,CAAC,CAAC,CAAC;EACrJ,CAAC,CAAC;EACF,OAAO1D,QAAQ,CAAC8D,MAAM,EAAE;IAAE4Y,YAAY,EAAE9a,KAAK,CAACC,OAAO,CAACoC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG;EAAG,CAAC,CAAC;AAC7E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0Y,qBAAqBA,CAAC1Y,GAAG,EAAEgC,MAAM,EAAE5F,IAAI,EAAE2H,QAAQ,EAAE;EACxD,IAAI,CAACA,QAAQ,EAAE;IACXpJ,wBAAwB,CAAC+d,qBAAqB,CAAC;EACnD;EACA3U,QAAQ,KAAKtK,MAAM,CAACE,QAAQ,CAAC;EAC7B,MAAMkG,MAAM,GAAGjF,qBAAqB,CAACmJ,QAAQ,EAAE,MAAM;IACjD,MAAMuC,OAAO,GAAG7M,MAAM,CAACsO,gBAAgB,CAAC;IACxC,MAAMtI,KAAK,GAAGmT,YAAY,CAACxW,IAAI,CAAC;IAChC,OAAON,YAAY,CAACyc,qBAAqB,CAACvY,GAAG,EAAEgC,MAAM,CAAC,CAAC,CAAC4F,IAAI,CAAClM,SAAS,CAAE8c,OAAO,IAAKlS,OAAO,CAACqF,qBAAqB,CAAC6M,OAAO,CAACxY,GAAG,EAAEwY,OAAO,CAACxW,MAAM,EAAEvC,KAAK,CAAC,CAAC,CAAC;EAC3J,CAAC,CAAC;EACF,OAAO1D,QAAQ,CAAC8D,MAAM,EAAE;IAAE4Y,YAAY,EAAE9a,KAAK,CAACC,OAAO,CAACoC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;EAAE,CAAC,CAAC;AAC3E;AACA,SAAS2Y,cAAcA,CAAC3W,MAAM,EAAE;EAC5B,IAAInH,QAAQ,CAACmH,MAAM,CAAC,EAAE;IAClB,OAAOlH,QAAQ,CAAC,MAAMkH,MAAM,CAAC,CAAC,CAAC;EACnC;EACA,OAAOlH,QAAQ,CAAC,MAAM;IAClB,OAAO6B,MAAM,CAACsD,OAAO,CAAC+B,MAAM,CAAC,CAAChF,MAAM,CAAC,CAACQ,GAAG,EAAE,CAACwC,GAAG,EAAExB,KAAK,CAAC,KAAK;MACxDhB,GAAG,CAACwC,GAAG,CAAC,GAAGnF,QAAQ,CAAC2D,KAAK,CAAC,GAAGA,KAAK,CAAC,CAAC,GAAGA,KAAK;MAC5C,OAAOhB,GAAG;IACd,CAAC,EAAE,CAAC,CAAC,CAAC;EACV,CAAC,CAAC;AACN;AACA,SAASob,YAAYA,CAAC3a,IAAI,EAAE;EACxB,IAAIN,KAAK,CAACC,OAAO,CAACK,IAAI,CAAC,EAAE;IACrB,OAAOnD,QAAQ,CAAC,MAAMmD,IAAI,CAAC/C,GAAG,CAAE8E,GAAG,IAAMnF,QAAQ,CAACmF,GAAG,CAAC,GAAGA,GAAG,CAAC,CAAC,GAAGA,GAAI,CAAC,CAAC;EAC3E;EACA,OAAOlF,QAAQ,CAAC,MAAMmD,IAAI,CAAC,CAAC,CAAC;AACjC;AACA,SAAS4a,WAAWA,CAAC7Y,GAAG,EAAE;EACtB,OAAOrC,KAAK,CAACC,OAAO,CAACoC,GAAG,CAAC,GAAGA,GAAG,CAACqT,IAAI,CAACxY,QAAQ,CAAC,GAAGA,QAAQ,CAACmF,GAAG,CAAC;AAClE;AACA,SAAS8Y,cAAcA,CAAC9W,MAAM,EAAE;EAC5B,OAAOA,MAAM,GACPnH,QAAQ,CAACmH,MAAM,CAAC,IAAIrF,MAAM,CAACoc,MAAM,CAAC/W,MAAM,CAAC,CAACqR,IAAI,CAACxY,QAAQ,CAAC,GACxD,KAAK;AACf;AACA,SAAS0d,qBAAqBA,CAACvY,GAAG,EAAEgC,MAAM,EAAE;EACxC,MAAMgX,YAAY,GAAGH,WAAW,CAAC7Y,GAAG,CAAC,GAC/B4Y,YAAY,CAAC5Y,GAAG,CAAC,GACjBlF,QAAQ,CAAC,MAAMkF,GAAG,CAAC;EACzB,MAAMiZ,cAAc,GAAGH,cAAc,CAAC9W,MAAM,CAAC,GACvC2W,cAAc,CAAC3W,MAAM,CAAC,GACtBlH,QAAQ,CAAC,MAAMkH,MAAM,CAAC;EAC5B,OAAOlH,QAAQ,CAAC,OAAO;IAAEkF,GAAG,EAAEgZ,YAAY,CAAC,CAAC;IAAEhX,MAAM,EAAEiX,cAAc,CAAC;EAAE,CAAC,CAAC,CAAC;AAC9E;AACA,SAASrG,YAAYA,CAACnT,KAAK,EAAE;EACzB,IAAI,OAAOA,KAAK,KAAK,WAAW,IAAIA,KAAK,KAAK,EAAE,EAAE;IAC9C,MAAMyZ,cAAc,GAAGzf,MAAM,CAACqX,eAAe,EAAE;MAAElP,QAAQ,EAAE;IAAK,CAAC,CAAC;IAClE,OAAOsX,cAAc,IAAIha,SAAS;EACtC;EACA,OAAOO,KAAK;AAChB;;AAEA;AACA;AACA;;AAEA,SAAS8F,uBAAuB,EAAEN,kBAAkB,EAAEP,qBAAqB,EAAE/C,iBAAiB,EAAEmC,oBAAoB,EAAExD,gBAAgB,EAAEgF,2BAA2B,EAAEN,qBAAqB,EAAE4L,cAAc,EAAEtU,gBAAgB,EAAEuU,0BAA0B,EAAEpM,yBAAyB,EAAEqM,eAAe,EAAEpP,oBAAoB,EAAE0V,aAAa,EAAE3F,kBAAkB,EAAE8D,eAAe,EAAEf,aAAa,EAAEzM,gBAAgB,EAAE6P,sBAAsB,EAAErZ,WAAW,EAAEkC,aAAa,EAAEb,OAAO,EAAEuY,qBAAqB,EAAEF,cAAc,EAAExU,eAAe,EAAEuC,gBAAgB,EAAEC,YAAY,EAAEJ,gBAAgB,EAAErJ,QAAQ,EAAEkD,eAAe,EAAEX,SAAS,EAAEI,SAAS,EAAEjB,OAAO,EAAEC,UAAU,EAAEc,KAAK,EAAEZ,QAAQ,EAAEL,QAAQ,EAAEwB,aAAa,EAAEpB,QAAQ,EAAE2X,gBAAgB,EAAEM,sBAAsB,EAAED,gCAAgC,EAAED,2BAA2B,EAAEc,oBAAoB,EAAEX,sBAAsB,EAAEM,0BAA0B,EAAEV,8BAA8B,EAAEQ,qBAAqB,EAAET,0BAA0B,EAAE9Y,QAAQ,EAAEW,IAAI,EAAEW,WAAW,EAAEW,QAAQ,EAAEyI,SAAS,EAAEC,eAAe,EAAE4Q,qBAAqB,EAAEJ,eAAe,EAAE9W,eAAe,EAAEtB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}