import { Injectable, InjectionToken } from '@angular/core';
import * as i0 from "@angular/core";
export const TRANSLOCO_INTERCEPTOR = new InjectionToken(ngDevMode ? 'TRANSLOCO_INTERCEPTOR' : '');
export class DefaultInterceptor {
    preSaveTranslation(translation) {
        return translation;
    }
    preSaveTranslationKey(_, value) {
        return value;
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "18.2.9", ngImport: i0, type: DefaultInterceptor, deps: [], target: i0.ɵɵFactoryTarget.Injectable });
    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: "12.0.0", version: "18.2.9", ngImport: i0, type: DefaultInterceptor });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "18.2.9", ngImport: i0, type: DefaultInterceptor, decorators: [{
            type: Injectable
        }] });
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidHJhbnNsb2NvLmludGVyY2VwdG9yLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vbGlicy90cmFuc2xvY28vc3JjL2xpYi90cmFuc2xvY28uaW50ZXJjZXB0b3IudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsT0FBTyxFQUFFLFVBQVUsRUFBRSxjQUFjLEVBQUUsTUFBTSxlQUFlLENBQUM7O0FBSTNELE1BQU0sQ0FBQyxNQUFNLHFCQUFxQixHQUFHLElBQUksY0FBYyxDQUNyRCxTQUFTLENBQUMsQ0FBQyxDQUFDLHVCQUF1QixDQUFDLENBQUMsQ0FBQyxFQUFFLENBQ3pDLENBQUM7QUFTRixNQUFNLE9BQU8sa0JBQWtCO0lBQzdCLGtCQUFrQixDQUFDLFdBQXdCO1FBQ3pDLE9BQU8sV0FBVyxDQUFDO0lBQ3JCLENBQUM7SUFFRCxxQkFBcUIsQ0FBQyxDQUFTLEVBQUUsS0FBYTtRQUM1QyxPQUFPLEtBQUssQ0FBQztJQUNmLENBQUM7dUdBUFUsa0JBQWtCOzJHQUFsQixrQkFBa0I7OzJGQUFsQixrQkFBa0I7a0JBRDlCLFVBQVUiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBJbmplY3RhYmxlLCBJbmplY3Rpb25Ub2tlbiB9IGZyb20gJ0Bhbmd1bGFyL2NvcmUnO1xuXG5pbXBvcnQgeyBUcmFuc2xhdGlvbiB9IGZyb20gJy4vdHlwZXMnO1xuXG5leHBvcnQgY29uc3QgVFJBTlNMT0NPX0lOVEVSQ0VQVE9SID0gbmV3IEluamVjdGlvblRva2VuPFRyYW5zbG9jb0ludGVyY2VwdG9yPihcbiAgbmdEZXZNb2RlID8gJ1RSQU5TTE9DT19JTlRFUkNFUFRPUicgOiAnJyxcbik7XG5cbmV4cG9ydCBpbnRlcmZhY2UgVHJhbnNsb2NvSW50ZXJjZXB0b3Ige1xuICBwcmVTYXZlVHJhbnNsYXRpb24odHJhbnNsYXRpb246IFRyYW5zbGF0aW9uLCBsYW5nOiBzdHJpbmcpOiBUcmFuc2xhdGlvbjtcblxuICBwcmVTYXZlVHJhbnNsYXRpb25LZXkoa2V5OiBzdHJpbmcsIHZhbHVlOiBzdHJpbmcsIGxhbmc6IHN0cmluZyk6IHN0cmluZztcbn1cblxuQEluamVjdGFibGUoKVxuZXhwb3J0IGNsYXNzIERlZmF1bHRJbnRlcmNlcHRvciBpbXBsZW1lbnRzIFRyYW5zbG9jb0ludGVyY2VwdG9yIHtcbiAgcHJlU2F2ZVRyYW5zbGF0aW9uKHRyYW5zbGF0aW9uOiBUcmFuc2xhdGlvbik6IFRyYW5zbGF0aW9uIHtcbiAgICByZXR1cm4gdHJhbnNsYXRpb247XG4gIH1cblxuICBwcmVTYXZlVHJhbnNsYXRpb25LZXkoXzogc3RyaW5nLCB2YWx1ZTogc3RyaW5nKTogc3RyaW5nIHtcbiAgICByZXR1cm4gdmFsdWU7XG4gIH1cbn1cbiJdfQ==