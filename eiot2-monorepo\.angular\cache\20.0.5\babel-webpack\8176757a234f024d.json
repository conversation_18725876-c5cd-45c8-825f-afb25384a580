{"ast": null, "code": "import('./bootstrap').catch(err => console.error(err));", "map": {"version": 3, "names": ["catch", "err", "console", "error"], "sources": ["D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\main.ts"], "sourcesContent": ["import('./bootstrap').catch((err) => console.error(err));\n"], "mappings": "AAAA,MAAM,CAAC,aAAa,CAAC,CAACA,KAAK,CAAEC,GAAG,IAAKC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}