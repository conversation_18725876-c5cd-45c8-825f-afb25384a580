{"version": "6.0", "nxVersion": "21.2.2", "pathMappings": {"@eiot2-monorepo/ui": ["libs/shared/ui/src/index.ts"]}, "nxJsonPlugins": [{"name": "@nx/playwright/plugin", "options": {"targetName": "e2e"}}, {"name": "@nx/eslint/plugin", "options": {"targetName": "lint"}}], "fileMap": {"nonProjectFiles": [{"file": "tailwind.config.js", "hash": "3332805694195277209"}, {"file": "eslint.config.mjs", "hash": "10770449096712831357"}, {"file": "package.json", "hash": "3884075383479241736"}, {"file": "tsconfig.base.json", "hash": "15882919660781123598"}, {"file": ".vscode/extensions.json", "hash": "108759356477840841"}, {"file": ".prettieri<PERSON>re", "hash": "5407418692668764289"}, {"file": ".prettier<PERSON>", "hash": "16267754514737964994"}, {"file": "nx.json", "hash": "2693832969276615563"}, {"file": "jest.preset.js", "hash": "9430166341120122740"}, {"file": ".editorconfig", "hash": "5443105041930014821"}, {"file": "postcss.config.js", "hash": "8801564480749162926"}, {"file": ".giti<PERSON>re", "hash": "17559216753634855455"}, {"file": "package-lock.json", "hash": "15576681209560439659"}, {"file": "jest.config.ts", "hash": "6870352021923392442"}], "projectFileMap": {"eiot-stub": [{"file": "apps/eiot-stub/eslint.config.mjs", "hash": "15052851186193004871", "deps": ["npm:@nx/eslint-plugin"]}, {"file": "apps/eiot-stub/jest.config.ts", "hash": "12443077723975770363"}, {"file": "apps/eiot-stub/project.json", "hash": "8303888222138371508"}, {"file": "apps/eiot-stub/public/favicon.ico", "hash": "9303420814833116677"}, {"file": "apps/eiot-stub/src/app/app.config.ts", "hash": "4515289115773032281", "deps": ["npm:@angular/core", "npm:@angular/router"]}, {"file": "apps/eiot-stub/src/app/app.html", "hash": "15726301981022621358"}, {"file": "apps/eiot-stub/src/app/app.routes.ts", "hash": "7396331776768897651", "deps": ["npm:@angular/router"]}, {"file": "apps/eiot-stub/src/app/app.scss", "hash": "12249006352210165923"}, {"file": "apps/eiot-stub/src/app/app.spec.ts", "hash": "12164898256445777378", "deps": ["npm:@angular/core"]}, {"file": "apps/eiot-stub/src/app/app.ts", "hash": "17921717743883278136", "deps": ["npm:@angular/core", "npm:@angular/router"]}, {"file": "apps/eiot-stub/src/app/home/<USER>", "hash": "15221810485335934639", "deps": ["npm:@angular/core"]}, {"file": "apps/eiot-stub/src/app/nx-welcome.ts", "hash": "9853477230199189745", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "apps/eiot-stub/src/bootstrap.ts", "hash": "3314327030169821486", "deps": ["npm:@angular/platform-browser"]}, {"file": "apps/eiot-stub/src/index.html", "hash": "6118171675619996744"}, {"file": "apps/eiot-stub/src/main.ts", "hash": "14701344671644104227"}, {"file": "apps/eiot-stub/src/styles.scss", "hash": "5195668842064076916"}, {"file": "apps/eiot-stub/src/test-setup.ts", "hash": "1917999429567095215", "deps": ["npm:jest-preset-angular"]}, {"file": "apps/eiot-stub/tsconfig.app.json", "hash": "10974451356376189872"}, {"file": "apps/eiot-stub/tsconfig.json", "hash": "4663124578817442910"}, {"file": "apps/eiot-stub/tsconfig.spec.json", "hash": "1029128818174064697"}, {"file": "apps/eiot-stub/webpack.config.js", "hash": "8293132989619118667", "deps": ["npm:@angular-architects/module-federation"]}], "@eiot2-monorepo/fuse": [{"file": "libs/fuse/README.md", "hash": "13920635378947267060"}, {"file": "libs/fuse/package.json", "hash": "9837857210571633489", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/router"]}, {"file": "libs/fuse/src/index.ts", "hash": "3003547998098062606", "deps": ["ui"]}], "eiot-admin": [{"file": "apps/eiot-admin/.editorconfig", "hash": "6729982560532353059"}, {"file": "apps/eiot-admin/.gitignore", "hash": "17884450377372542257"}, {"file": "apps/eiot-admin/.npmrc", "hash": "18085261541358865259"}, {"file": "apps/eiot-admin/.nvmrc", "hash": "14743000810602433389"}, {"file": "apps/eiot-admin/.prettierrc", "hash": "18063749794141746840"}, {"file": "apps/eiot-admin/CREDITS", "hash": "15816759832009630992"}, {"file": "apps/eiot-admin/LICENSE.md", "hash": "2061571274526334392"}, {"file": "apps/eiot-admin/README.md", "hash": "8399711428531508716"}, {"file": "apps/eiot-admin/angular.json", "hash": "13414988530184075413"}, {"file": "apps/eiot-admin/eslint.config.mjs", "hash": "15052851186193004871", "deps": ["npm:@nx/eslint-plugin"]}, {"file": "apps/eiot-admin/jest.config.ts", "hash": "8500380658683770831"}, {"file": "apps/eiot-admin/package-lock.json", "hash": "7326557617423673886"}, {"file": "apps/eiot-admin/package.json", "hash": "241342318818086627", "deps": ["npm:@angular-devkit/build-angular", "npm:@angular/cli", "npm:@angular/compiler-cli", "npm:@types/lodash", "npm:@types/lodash-es", "npm:autoprefixer", "npm:lodash", "npm:postcss", "npm:prettier", "npm:tailwindcss", "npm:typescript", "npm:@angular/animations", "npm:@angular/cdk", "npm:@angular/common", "npm:@angular/compiler", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:@angular/platform-browser", "npm:@angular/platform-browser-dynamic", "npm:@angular/router", "npm:lodash-es", "npm:luxon", "npm:perfect-scrollbar", "npm:rxjs", "npm:tslib", "npm:zone.js"]}, {"file": "apps/eiot-admin/project.json", "hash": "3402935990819231638"}, {"file": "apps/eiot-admin/public/.gitkeep", "hash": "3244421341483603138"}, {"file": "apps/eiot-admin/public/favicon-16x16.png", "hash": "1036624592215189517"}, {"file": "apps/eiot-admin/public/favicon-32x32.png", "hash": "8461103129062566301"}, {"file": "apps/eiot-admin/public/favicon.ico", "hash": "9303420814833116677"}, {"file": "apps/eiot-admin/public/fonts/inter/Inter-italic.var.woff2", "hash": "2283011398089021421"}, {"file": "apps/eiot-admin/public/fonts/inter/Inter-roman.var.woff2", "hash": "15746750612725855840"}, {"file": "apps/eiot-admin/public/i18n/en.json", "hash": "15712220666280478307"}, {"file": "apps/eiot-admin/public/i18n/tr.json", "hash": "18263242878832834513"}, {"file": "apps/eiot-admin/public/icons/feather.svg", "hash": "7535188576898634125"}, {"file": "apps/eiot-admin/public/icons/heroicons-mini.svg", "hash": "4490825136675843054"}, {"file": "apps/eiot-admin/public/icons/heroicons-outline.svg", "hash": "17335524454696621209"}, {"file": "apps/eiot-admin/public/icons/heroicons-solid.svg", "hash": "4523901164501953759"}, {"file": "apps/eiot-admin/public/icons/material-outline.svg", "hash": "8733277527709835906"}, {"file": "apps/eiot-admin/public/icons/material-solid.svg", "hash": "2567969963546594757"}, {"file": "apps/eiot-admin/public/icons/material-twotone.svg", "hash": "24036692367393956"}, {"file": "apps/eiot-admin/public/images/apps/contacts/flags.png", "hash": "12931064074289390101"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-01-01.jpg", "hash": "15821627931377632708"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-01-02.jpg", "hash": "16124769973213180769"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-01-03.jpg", "hash": "7259325983651399565"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-01-thumb.jpg", "hash": "7146750741556020233"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-02-01.jpg", "hash": "8384318941588098404"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-02-02.jpg", "hash": "8459655993168232337"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-02-03.jpg", "hash": "1185583291596761224"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-02-thumb.jpg", "hash": "16535464510219086329"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-03-01.jpg", "hash": "7807948740482063669"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-03-02.jpg", "hash": "3487298713617733362"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-03-03.jpg", "hash": "4807977466988211202"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-03-thumb.jpg", "hash": "12248679632654810073"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-04-01.jpg", "hash": "16461330209182342933"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-04-02.jpg", "hash": "13433342500314377262"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-04-03.jpg", "hash": "17170396183214858558"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-04-thumb.jpg", "hash": "16777320830239880158"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-05-01.jpg", "hash": "2345457300798108527"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-05-02.jpg", "hash": "3514517838869193617"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-05-03.jpg", "hash": "7279285661335515601"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-05-thumb.jpg", "hash": "17657977887440083906"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-06-01.jpg", "hash": "11561482590255948256"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-06-02.jpg", "hash": "13027729089527630397"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-06-03.jpg", "hash": "17378322318981159661"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-06-thumb.jpg", "hash": "520424184920743374"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-07-01.jpg", "hash": "15922816637225117364"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-07-02.jpg", "hash": "16158332489519023763"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-07-03.jpg", "hash": "16732876296764429280"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-07-thumb.jpg", "hash": "3276143295141697434"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-08-01.jpg", "hash": "6380672066074257082"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-08-02.jpg", "hash": "1401660697982422927"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-08-03.jpg", "hash": "17302191982752256431"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-08-thumb.jpg", "hash": "17417909187912120429"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-09-01.jpg", "hash": "16012386952580569005"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-09-02.jpg", "hash": "5042542549452193147"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-09-03.jpg", "hash": "17392482433969914282"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-09-thumb.jpg", "hash": "3315964174719910364"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-10-01.jpg", "hash": "1207073022487216998"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-10-02.jpg", "hash": "5157083012750252855"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-10-03.jpg", "hash": "16454380960425738998"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-10-thumb.jpg", "hash": "5794946346282510520"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-11-01.jpg", "hash": "10805773330039896816"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-11-02.jpg", "hash": "14482123351891220302"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-11-03.jpg", "hash": "14303087227998579758"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-11-thumb.jpg", "hash": "10553131026583174692"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-12-01.jpg", "hash": "9090876048690242443"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-12-02.jpg", "hash": "762144122657217484"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-12-03.jpg", "hash": "14567007534436435688"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-12-thumb.jpg", "hash": "1656736603539342525"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-13-01.jpg", "hash": "7229367710482791687"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-13-02.jpg", "hash": "9107657839282199107"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-13-03.jpg", "hash": "836909337210368040"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-13-thumb.jpg", "hash": "17083448325011524893"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-14-01.jpg", "hash": "14862508241806831012"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-14-02.jpg", "hash": "17284393578625844754"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-14-03.jpg", "hash": "3629878076667982260"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-14-thumb.jpg", "hash": "10083201222664927778"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-15-01.jpg", "hash": "7212861252292766958"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-15-02.jpg", "hash": "3406959523151641163"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-15-03.jpg", "hash": "17546207427110501548"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-15-thumb.jpg", "hash": "1026222938359056767"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-16-01.jpg", "hash": "4224146643446997730"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-16-02.jpg", "hash": "6163845813261036249"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-16-03.jpg", "hash": "9986832371443857952"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-16-thumb.jpg", "hash": "5870486112417227195"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-17-01.jpg", "hash": "13640340362744672629"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-17-02.jpg", "hash": "1118904457744374423"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-17-03.jpg", "hash": "9068997166581668607"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-17-thumb.jpg", "hash": "18092282868857852187"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-18-01.jpg", "hash": "15199449187020810303"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-18-02.jpg", "hash": "2268044109721265306"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-18-03.jpg", "hash": "12119588244536590133"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-18-thumb.jpg", "hash": "431704188114176495"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-19-01.jpg", "hash": "12180287461312077187"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-19-02.jpg", "hash": "8904196836035147962"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-19-03.jpg", "hash": "10775927165001410938"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-19-thumb.jpg", "hash": "3117856353852535065"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-20-01.jpg", "hash": "2113224263106877740"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-20-02.jpg", "hash": "6604130213982097493"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-20-03.jpg", "hash": "17441351420886578255"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-20-thumb.jpg", "hash": "18202637346100353019"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-21-01.jpg", "hash": "7811306422896732078"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-21-02.jpg", "hash": "684207081225219081"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-21-03.jpg", "hash": "4008026477598153604"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-21-thumb.jpg", "hash": "3443896916794520582"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-22-01.jpg", "hash": "10333583356677359763"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-22-02.jpg", "hash": "18254978957528330607"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-22-03.jpg", "hash": "11594280676263417311"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-22-thumb.jpg", "hash": "12377086253695167322"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-23-01.jpg", "hash": "13623565717791263344"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-23-02.jpg", "hash": "5640430423534096532"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-23-03.jpg", "hash": "3849243014136302073"}, {"file": "apps/eiot-admin/public/images/apps/ecommerce/products/watch-23-thumb.jpg", "hash": "4933300132969492890"}, {"file": "apps/eiot-admin/public/images/apps/mailbox/birds-eye-sydney_preview.jpg", "hash": "1427342911077009016"}, {"file": "apps/eiot-admin/public/images/apps/mailbox/lake-of-carrezza_preview.png", "hash": "18088688540386014722"}, {"file": "apps/eiot-admin/public/images/apps/mailbox/mystery-forest_preview.jpg", "hash": "6451911902231135075"}, {"file": "apps/eiot-admin/public/images/apps/mailbox/yosemite-national-park_preview.png", "hash": "13818661068703420293"}, {"file": "apps/eiot-admin/public/images/avatars/brian-hughes.jpg", "hash": "13937613583704513345"}, {"file": "apps/eiot-admin/public/images/avatars/female-01.jpg", "hash": "8586549865935331348"}, {"file": "apps/eiot-admin/public/images/avatars/female-02.jpg", "hash": "8468656640665950916"}, {"file": "apps/eiot-admin/public/images/avatars/female-03.jpg", "hash": "10815203119151814684"}, {"file": "apps/eiot-admin/public/images/avatars/female-04.jpg", "hash": "14410245044740612639"}, {"file": "apps/eiot-admin/public/images/avatars/female-05.jpg", "hash": "4166044825629701968"}, {"file": "apps/eiot-admin/public/images/avatars/female-06.jpg", "hash": "9011788355660318094"}, {"file": "apps/eiot-admin/public/images/avatars/female-07.jpg", "hash": "189674534090388264"}, {"file": "apps/eiot-admin/public/images/avatars/female-08.jpg", "hash": "3232723534471436299"}, {"file": "apps/eiot-admin/public/images/avatars/female-09.jpg", "hash": "3554008083837229383"}, {"file": "apps/eiot-admin/public/images/avatars/female-10.jpg", "hash": "1750801511741552471"}, {"file": "apps/eiot-admin/public/images/avatars/female-11.jpg", "hash": "6502649379399214373"}, {"file": "apps/eiot-admin/public/images/avatars/female-12.jpg", "hash": "11919481688176293825"}, {"file": "apps/eiot-admin/public/images/avatars/female-13.jpg", "hash": "15258935544676196264"}, {"file": "apps/eiot-admin/public/images/avatars/female-14.jpg", "hash": "7910528046139609890"}, {"file": "apps/eiot-admin/public/images/avatars/female-15.jpg", "hash": "670502627418682449"}, {"file": "apps/eiot-admin/public/images/avatars/female-16.jpg", "hash": "15601015359708434110"}, {"file": "apps/eiot-admin/public/images/avatars/female-17.jpg", "hash": "11824709012757370044"}, {"file": "apps/eiot-admin/public/images/avatars/female-18.jpg", "hash": "7352187020063296970"}, {"file": "apps/eiot-admin/public/images/avatars/female-19.jpg", "hash": "2575891191376865559"}, {"file": "apps/eiot-admin/public/images/avatars/female-20.jpg", "hash": "15782982823302951023"}, {"file": "apps/eiot-admin/public/images/avatars/male-01.jpg", "hash": "4240758940380011584"}, {"file": "apps/eiot-admin/public/images/avatars/male-02.jpg", "hash": "11611290591264458361"}, {"file": "apps/eiot-admin/public/images/avatars/male-03.jpg", "hash": "16190605272004688996"}, {"file": "apps/eiot-admin/public/images/avatars/male-04.jpg", "hash": "13937613583704513345"}, {"file": "apps/eiot-admin/public/images/avatars/male-05.jpg", "hash": "5448544201045835310"}, {"file": "apps/eiot-admin/public/images/avatars/male-06.jpg", "hash": "3225370260940283656"}, {"file": "apps/eiot-admin/public/images/avatars/male-07.jpg", "hash": "8022878812170720282"}, {"file": "apps/eiot-admin/public/images/avatars/male-08.jpg", "hash": "12187933696361167708"}, {"file": "apps/eiot-admin/public/images/avatars/male-09.jpg", "hash": "11577706816526814561"}, {"file": "apps/eiot-admin/public/images/avatars/male-10.jpg", "hash": "17071245728290368935"}, {"file": "apps/eiot-admin/public/images/avatars/male-11.jpg", "hash": "6496050421733782086"}, {"file": "apps/eiot-admin/public/images/avatars/male-12.jpg", "hash": "6875787727363448458"}, {"file": "apps/eiot-admin/public/images/avatars/male-13.jpg", "hash": "6052865309401505818"}, {"file": "apps/eiot-admin/public/images/avatars/male-14.jpg", "hash": "7029590298251665420"}, {"file": "apps/eiot-admin/public/images/avatars/male-15.jpg", "hash": "3770726829645225550"}, {"file": "apps/eiot-admin/public/images/avatars/male-16.jpg", "hash": "7972518743473855344"}, {"file": "apps/eiot-admin/public/images/avatars/male-17.jpg", "hash": "10309859072344948225"}, {"file": "apps/eiot-admin/public/images/avatars/male-18.jpg", "hash": "5351120127688616710"}, {"file": "apps/eiot-admin/public/images/avatars/male-19.jpg", "hash": "1054935309633952207"}, {"file": "apps/eiot-admin/public/images/avatars/male-20.jpg", "hash": "12304948321445020375"}, {"file": "apps/eiot-admin/public/images/cards/01-320x200.jpg", "hash": "10586999134741351821"}, {"file": "apps/eiot-admin/public/images/cards/02-320x200.jpg", "hash": "6998458187084767944"}, {"file": "apps/eiot-admin/public/images/cards/03-320x200.jpg", "hash": "5615477378142829759"}, {"file": "apps/eiot-admin/public/images/cards/04-320x200.jpg", "hash": "10468389948952944599"}, {"file": "apps/eiot-admin/public/images/cards/05-320x200.jpg", "hash": "9910348632189693999"}, {"file": "apps/eiot-admin/public/images/cards/06-320x200.jpg", "hash": "18080014414469893298"}, {"file": "apps/eiot-admin/public/images/cards/07-320x200.jpg", "hash": "7960128927943218609"}, {"file": "apps/eiot-admin/public/images/cards/08-320x200.jpg", "hash": "9855162117169213106"}, {"file": "apps/eiot-admin/public/images/cards/09-320x200.jpg", "hash": "7662432182091857042"}, {"file": "apps/eiot-admin/public/images/cards/10-320x200.jpg", "hash": "13333051799892669061"}, {"file": "apps/eiot-admin/public/images/cards/11-512x512.jpg", "hash": "12203288261256568840"}, {"file": "apps/eiot-admin/public/images/cards/12-512x512.jpg", "hash": "15488639658130155754"}, {"file": "apps/eiot-admin/public/images/cards/13-160x160.jpg", "hash": "15784938804369538966"}, {"file": "apps/eiot-admin/public/images/cards/14-640x480.jpg", "hash": "3599949684496365360"}, {"file": "apps/eiot-admin/public/images/cards/15-640x480.jpg", "hash": "16958344088358492395"}, {"file": "apps/eiot-admin/public/images/cards/16-640x480.jpg", "hash": "10758226513512693761"}, {"file": "apps/eiot-admin/public/images/cards/17-640x480.jpg", "hash": "1529235831340158404"}, {"file": "apps/eiot-admin/public/images/cards/18-640x480.jpg", "hash": "13800516430325810023"}, {"file": "apps/eiot-admin/public/images/cards/19-640x480.jpg", "hash": "594918782700076020"}, {"file": "apps/eiot-admin/public/images/cards/20-640x480.jpg", "hash": "3870952109912780177"}, {"file": "apps/eiot-admin/public/images/cards/21-640x480.jpg", "hash": "6805145911276525879"}, {"file": "apps/eiot-admin/public/images/cards/22-640x480.jpg", "hash": "3227548859825871403"}, {"file": "apps/eiot-admin/public/images/cards/23-640x480.jpg", "hash": "10831177667801372403"}, {"file": "apps/eiot-admin/public/images/cards/24-640x480.jpg", "hash": "11462169375009173788"}, {"file": "apps/eiot-admin/public/images/cards/25-640x480.jpg", "hash": "16943151411863153209"}, {"file": "apps/eiot-admin/public/images/cards/26-640x480.jpg", "hash": "5255101183509419112"}, {"file": "apps/eiot-admin/public/images/cards/27-640x480.jpg", "hash": "15706763019757146591"}, {"file": "apps/eiot-admin/public/images/cards/28-640x480.jpg", "hash": "1222706250654119997"}, {"file": "apps/eiot-admin/public/images/cards/29-640x480.jpg", "hash": "1937512318176272615"}, {"file": "apps/eiot-admin/public/images/cards/30-640x480.jpg", "hash": "1316697886473624736"}, {"file": "apps/eiot-admin/public/images/cards/31-640x480.jpg", "hash": "11361549014991957670"}, {"file": "apps/eiot-admin/public/images/cards/32-640x480.jpg", "hash": "3453216424172596092"}, {"file": "apps/eiot-admin/public/images/cards/33-640x480.jpg", "hash": "11610848512148071434"}, {"file": "apps/eiot-admin/public/images/cards/34-640x480.jpg", "hash": "7368817414550140775"}, {"file": "apps/eiot-admin/public/images/cards/35-640x480.jpg", "hash": "17697714615568770627"}, {"file": "apps/eiot-admin/public/images/cards/36-640x480.jpg", "hash": "9205870543814491716"}, {"file": "apps/eiot-admin/public/images/cards/avatar-400x400.jpg", "hash": "7876920063915980370"}, {"file": "apps/eiot-admin/public/images/cards/coffee-shop-01-320x200.jpg", "hash": "18209515858254583940"}, {"file": "apps/eiot-admin/public/images/cards/coffee-shop-02-512x512.jpg", "hash": "12286524463814876712"}, {"file": "apps/eiot-admin/public/images/cards/coffee-shop-03-320x320.jpg", "hash": "18438012284908272139"}, {"file": "apps/eiot-admin/public/images/cards/mansion-01-320x200.jpg", "hash": "16638182910314819503"}, {"file": "apps/eiot-admin/public/images/cards/product-01-224x256.jpg", "hash": "9806597535872757188"}, {"file": "apps/eiot-admin/public/images/cards/sneakers-01-320x200.jpg", "hash": "2968113095848963054"}, {"file": "apps/eiot-admin/public/images/cards/sneakers-02-448x560.jpg", "hash": "15382883164769044270"}, {"file": "apps/eiot-admin/public/images/cards/sneakers-03-448x560.jpg", "hash": "3660373062885789297"}, {"file": "apps/eiot-admin/public/images/flags/TR.svg", "hash": "949733221512496308"}, {"file": "apps/eiot-admin/public/images/flags/US.svg", "hash": "16467969063938751139"}, {"file": "apps/eiot-admin/public/images/flags/where-to-find-other-flags.txt", "hash": "14816262607826986621"}, {"file": "apps/eiot-admin/public/images/logo/logo-text-on-dark.svg", "hash": "*******************"}, {"file": "apps/eiot-admin/public/images/logo/logo-text.svg", "hash": "14975107125096330206"}, {"file": "apps/eiot-admin/public/images/logo/logo.svg", "hash": "1440226741534041088"}, {"file": "apps/eiot-admin/public/images/pages/help-center/image-1.jpg", "hash": "*******************"}, {"file": "apps/eiot-admin/public/images/pages/profile/cover.jpg", "hash": "3964822742190263234"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/autocomplete.scene.png", "hash": "3082886449268293692"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/badge.scene.png", "hash": "12505888861314946018"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/bottom-sheet.scene.png", "hash": "15349707543777131452"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/button-toggle.scene.png", "hash": "1850718572047312243"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/button.scene.png", "hash": "10252496421837564479"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/card.scene.png", "hash": "8576503231112843489"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/checkbox.scene.png", "hash": "12868018946461001994"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/chips.scene.png", "hash": "6103140229507298143"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/core.scene.png", "hash": "5973720554972223457"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/datepicker.scene.png", "hash": "18410626099368602909"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/dialog.scene.png", "hash": "17802359673271818086"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/divider.scene.png", "hash": "14618915521944306096"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/expansion.scene.png", "hash": "13892453746287890656"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/form-field.scene.png", "hash": "11489945002083249764"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/grid-list.scene.png", "hash": "14277955464128685248"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/icon.scene.png", "hash": "18150848660743389202"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/input.scene.png", "hash": "787875456615948683"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/list.scene.png", "hash": "8226073645468228938"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/menu.scene.png", "hash": "13658454798311282873"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/paginator.scene.png", "hash": "8115077954256692369"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/progress-bar.scene.png", "hash": "17669226824411892456"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/progress-spinner.scene.png", "hash": "12535132717602182277"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/radio.scene.png", "hash": "9381244797317477484"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/ripple.scene.png", "hash": "1767251688447424664"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/select.scene.png", "hash": "12855889226601325851"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/sidenav.scene.png", "hash": "3396249682565863742"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/slide-toggle.scene.png", "hash": "11061222868368757843"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/slider.scene.png", "hash": "5230254340674589560"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/snack-bar.scene.png", "hash": "4345207932467504480"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/sort.scene.png", "hash": "3000302804240571415"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/stepper.scene.png", "hash": "5863502010993475079"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/table.scene.png", "hash": "12350055270865986426"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/tabs.scene.png", "hash": "14897619669058068080"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/toolbar.scene.png", "hash": "2926828702807698274"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/tooltip.scene.png", "hash": "12368326747124581888"}, {"file": "apps/eiot-admin/public/images/ui/angular-material/scenes/tree.scene.png", "hash": "15557500635714716146"}, {"file": "apps/eiot-admin/shell/tsconfig.app.json", "hash": "3244421341483603138"}, {"file": "apps/eiot-admin/src/@fuse/animations/defaults.ts", "hash": "15294815884258272562"}, {"file": "apps/eiot-admin/src/@fuse/animations/expand-collapse.ts", "hash": "3956691543296502421", "deps": ["npm:@angular/animations"]}, {"file": "apps/eiot-admin/src/@fuse/animations/fade.ts", "hash": "1573455729823139080", "deps": ["npm:@angular/animations"]}, {"file": "apps/eiot-admin/src/@fuse/animations/index.ts", "hash": "249972317308509343"}, {"file": "apps/eiot-admin/src/@fuse/animations/public-api.ts", "hash": "7665910543974359097"}, {"file": "apps/eiot-admin/src/@fuse/animations/shake.ts", "hash": "11863831505194264665", "deps": ["npm:@angular/animations"]}, {"file": "apps/eiot-admin/src/@fuse/animations/slide.ts", "hash": "8871130008323108315", "deps": ["npm:@angular/animations"]}, {"file": "apps/eiot-admin/src/@fuse/animations/zoom.ts", "hash": "18199996693375608020", "deps": ["npm:@angular/animations"]}, {"file": "apps/eiot-admin/src/@fuse/components/alert/alert.component.html", "hash": "14938362786261809303"}, {"file": "apps/eiot-admin/src/@fuse/components/alert/alert.component.scss", "hash": "11766720136351808344"}, {"file": "apps/eiot-admin/src/@fuse/components/alert/alert.component.ts", "hash": "16885668217265917286", "deps": ["npm:@angular/cdk", "npm:@angular/core", "npm:@angular/material", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/@fuse/components/alert/alert.service.ts", "hash": "9583207898740421382", "deps": ["npm:@angular/core", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/@fuse/components/alert/alert.types.ts", "hash": "10840620728811833068"}, {"file": "apps/eiot-admin/src/@fuse/components/alert/index.ts", "hash": "9643241796905756734"}, {"file": "apps/eiot-admin/src/@fuse/components/alert/public-api.ts", "hash": "11553072028952109526"}, {"file": "apps/eiot-admin/src/@fuse/components/card/card.component.html", "hash": "11321818701193300727"}, {"file": "apps/eiot-admin/src/@fuse/components/card/card.component.scss", "hash": "6667954301206883651"}, {"file": "apps/eiot-admin/src/@fuse/components/card/card.component.ts", "hash": "8521074844792002510", "deps": ["npm:@angular/cdk", "npm:@angular/core"]}, {"file": "apps/eiot-admin/src/@fuse/components/card/card.types.ts", "hash": "14598758744979151260"}, {"file": "apps/eiot-admin/src/@fuse/components/card/index.ts", "hash": "*******************"}, {"file": "apps/eiot-admin/src/@fuse/components/card/public-api.ts", "hash": "18118095442321375993"}, {"file": "apps/eiot-admin/src/@fuse/components/drawer/drawer.component.html", "hash": "5324540217999113425"}, {"file": "apps/eiot-admin/src/@fuse/components/drawer/drawer.component.scss", "hash": "7963324510033783697"}, {"file": "apps/eiot-admin/src/@fuse/components/drawer/drawer.component.ts", "hash": "15479808369286254657", "deps": ["npm:@angular/animations", "npm:@angular/cdk", "npm:@angular/core"]}, {"file": "apps/eiot-admin/src/@fuse/components/drawer/drawer.service.ts", "hash": "10809116692727941499", "deps": ["npm:@angular/core"]}, {"file": "apps/eiot-admin/src/@fuse/components/drawer/drawer.types.ts", "hash": "16272349317906096941"}, {"file": "apps/eiot-admin/src/@fuse/components/drawer/index.ts", "hash": "12376250808539749459"}, {"file": "apps/eiot-admin/src/@fuse/components/drawer/public-api.ts", "hash": "18158476698171220068"}, {"file": "apps/eiot-admin/src/@fuse/components/fullscreen/fullscreen.component.html", "hash": "4488245815964903865"}, {"file": "apps/eiot-admin/src/@fuse/components/fullscreen/fullscreen.component.ts", "hash": "11846882107256502012", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/material"]}, {"file": "apps/eiot-admin/src/@fuse/components/fullscreen/index.ts", "hash": "15439082091470835798"}, {"file": "apps/eiot-admin/src/@fuse/components/fullscreen/public-api.ts", "hash": "15754077304704295110"}, {"file": "apps/eiot-admin/src/@fuse/components/highlight/highlight.component.html", "hash": "9871551760197404798"}, {"file": "apps/eiot-admin/src/@fuse/components/highlight/highlight.component.scss", "hash": "12329089659202747931"}, {"file": "apps/eiot-admin/src/@fuse/components/highlight/highlight.component.ts", "hash": "7778979821589875352", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/platform-browser"]}, {"file": "apps/eiot-admin/src/@fuse/components/highlight/highlight.service.ts", "hash": "9065470856979451030", "deps": ["npm:@angular/core"]}, {"file": "apps/eiot-admin/src/@fuse/components/highlight/index.ts", "hash": "13900990071675437096"}, {"file": "apps/eiot-admin/src/@fuse/components/highlight/public-api.ts", "hash": "7159336497718345753"}, {"file": "apps/eiot-admin/src/@fuse/components/loading-bar/index.ts", "hash": "12802424307058727799"}, {"file": "apps/eiot-admin/src/@fuse/components/loading-bar/loading-bar.component.html", "hash": "7305396547016060951"}, {"file": "apps/eiot-admin/src/@fuse/components/loading-bar/loading-bar.component.scss", "hash": "10505207679953100266"}, {"file": "apps/eiot-admin/src/@fuse/components/loading-bar/loading-bar.component.ts", "hash": "6920539418300903106", "deps": ["npm:@angular/cdk", "npm:@angular/core", "npm:@angular/material", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/@fuse/components/loading-bar/public-api.ts", "hash": "11814447778896146160"}, {"file": "apps/eiot-admin/src/@fuse/components/masonry/index.ts", "hash": "3300968124842788052"}, {"file": "apps/eiot-admin/src/@fuse/components/masonry/masonry.component.html", "hash": "13722727025070316567"}, {"file": "apps/eiot-admin/src/@fuse/components/masonry/masonry.component.ts", "hash": "3409355146840161693", "deps": ["npm:@angular/common", "npm:@angular/core"]}, {"file": "apps/eiot-admin/src/@fuse/components/masonry/public-api.ts", "hash": "8653346882576344210"}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/horizontal/components/basic/basic.component.html", "hash": "2033519625555641090"}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/horizontal/components/basic/basic.component.ts", "hash": "16229494750546184855", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/material", "npm:@angular/router", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/horizontal/components/branch/branch.component.html", "hash": "15987297175252954182"}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/horizontal/components/branch/branch.component.ts", "hash": "2086573802665172594", "deps": ["npm:@angular/cdk", "npm:@angular/common", "npm:@angular/core", "npm:@angular/material", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/horizontal/components/divider/divider.component.html", "hash": "4477670387423744961"}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/horizontal/components/divider/divider.component.ts", "hash": "1133744354378933454", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/horizontal/components/spacer/spacer.component.html", "hash": "10809296409364222948"}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/horizontal/components/spacer/spacer.component.ts", "hash": "7712043650508853298", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/horizontal/horizontal.component.html", "hash": "1808033591275332105"}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/horizontal/horizontal.component.scss", "hash": "5603949256441783332"}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/horizontal/horizontal.component.ts", "hash": "135375593420295594", "deps": ["npm:@angular/core", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/index.ts", "hash": "14895563194875030815"}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/navigation.service.ts", "hash": "214550740646749366", "deps": ["npm:@angular/core"]}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/navigation.types.ts", "hash": "13148425763145788", "deps": ["npm:@angular/router"]}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/public-api.ts", "hash": "1785887341020517252"}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/vertical/components/aside/aside.component.html", "hash": "17132084692456953234"}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/vertical/components/aside/aside.component.ts", "hash": "6860039371247409151", "deps": ["npm:@angular/cdk", "npm:@angular/common", "npm:@angular/core", "npm:@angular/material", "npm:@angular/router", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/vertical/components/basic/basic.component.html", "hash": "16434197587361583100"}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/vertical/components/basic/basic.component.ts", "hash": "3370622311477474931", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/material", "npm:@angular/router", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/vertical/components/collapsable/collapsable.component.html", "hash": "14735772890349011760"}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/vertical/components/collapsable/collapsable.component.ts", "hash": "17736369865733805522", "deps": ["npm:@angular/cdk", "npm:@angular/common", "npm:@angular/core", "npm:@angular/material", "npm:@angular/router", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/vertical/components/divider/divider.component.html", "hash": "1789227838367047034"}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/vertical/components/divider/divider.component.ts", "hash": "17452101720505015565", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/vertical/components/group/group.component.html", "hash": "14376232046224610851"}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/vertical/components/group/group.component.ts", "hash": "11038145031135014871", "deps": ["npm:@angular/cdk", "npm:@angular/common", "npm:@angular/core", "npm:@angular/material", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/vertical/components/spacer/spacer.component.html", "hash": "15697756490322310313"}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/vertical/components/spacer/spacer.component.ts", "hash": "151858341004749634", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/vertical/styles/appearances/compact.scss", "hash": "14973134694819930386"}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/vertical/styles/appearances/default.scss", "hash": "11713143583533866996"}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/vertical/styles/appearances/dense.scss", "hash": "10411871427193663379"}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/vertical/styles/appearances/thin.scss", "hash": "6592128114363199632"}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/vertical/vertical.component.html", "hash": "12976612575032894599"}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/vertical/vertical.component.scss", "hash": "8203433230470624840"}, {"file": "apps/eiot-admin/src/@fuse/components/navigation/vertical/vertical.component.ts", "hash": "2484414403407751360", "deps": ["npm:@angular/animations", "npm:@angular/cdk", "npm:@angular/common", "npm:@angular/core", "npm:@angular/router", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/@fuse/directives/scroll-reset/index.ts", "hash": "14338229507679220164"}, {"file": "apps/eiot-admin/src/@fuse/directives/scroll-reset/public-api.ts", "hash": "7252746230300477734"}, {"file": "apps/eiot-admin/src/@fuse/directives/scroll-reset/scroll-reset.directive.ts", "hash": "8174771125880489924", "deps": ["npm:@angular/core", "npm:@angular/router", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/@fuse/directives/scrollbar/index.ts", "hash": "5937759299572495819"}, {"file": "apps/eiot-admin/src/@fuse/directives/scrollbar/public-api.ts", "hash": "3691538241287482241"}, {"file": "apps/eiot-admin/src/@fuse/directives/scrollbar/scrollbar.directive.ts", "hash": "5774163582906272608", "deps": ["npm:@angular/cdk", "npm:@angular/core", "npm:lodash-es", "npm:perfect-scrollbar", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/@fuse/directives/scrollbar/scrollbar.types.ts", "hash": "6893978441126548556"}, {"file": "apps/eiot-admin/src/@fuse/fuse.provider.ts", "hash": "8352162942878579331", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/material"]}, {"file": "apps/eiot-admin/src/@fuse/index.ts", "hash": "7719632877505588165"}, {"file": "apps/eiot-admin/src/@fuse/lib/mock-api/index.ts", "hash": "5262689304868450722"}, {"file": "apps/eiot-admin/src/@fuse/lib/mock-api/mock-api.constants.ts", "hash": "8493110394646198210", "deps": ["npm:@angular/core"]}, {"file": "apps/eiot-admin/src/@fuse/lib/mock-api/mock-api.interceptor.ts", "hash": "12958608854180660849", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/@fuse/lib/mock-api/mock-api.request-handler.ts", "hash": "7017926506217871127", "deps": ["npm:@angular/common", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/@fuse/lib/mock-api/mock-api.service.ts", "hash": "12980723210541526295", "deps": ["npm:@angular/core", "npm:lodash-es"]}, {"file": "apps/eiot-admin/src/@fuse/lib/mock-api/mock-api.types.ts", "hash": "15693337554415290001", "deps": ["npm:@angular/common", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/@fuse/lib/mock-api/mock-api.utils.ts", "hash": "1804916503590667717"}, {"file": "apps/eiot-admin/src/@fuse/lib/mock-api/public-api.ts", "hash": "3920028660959316781"}, {"file": "apps/eiot-admin/src/@fuse/pipes/find-by-key/find-by-key.pipe.ts", "hash": "18016329265937769861", "deps": ["npm:@angular/core"]}, {"file": "apps/eiot-admin/src/@fuse/pipes/find-by-key/index.ts", "hash": "8028144741999596047"}, {"file": "apps/eiot-admin/src/@fuse/pipes/find-by-key/public-api.ts", "hash": "4631485034725976211"}, {"file": "apps/eiot-admin/src/@fuse/services/config/config.constants.ts", "hash": "12850450129237075415", "deps": ["npm:@angular/core"]}, {"file": "apps/eiot-admin/src/@fuse/services/config/config.service.ts", "hash": "14006870737366874776", "deps": ["npm:@angular/core", "npm:lodash-es", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/@fuse/services/config/config.types.ts", "hash": "122763955740537783"}, {"file": "apps/eiot-admin/src/@fuse/services/config/index.ts", "hash": "18298392554804514997"}, {"file": "apps/eiot-admin/src/@fuse/services/config/public-api.ts", "hash": "4108501699500337715"}, {"file": "apps/eiot-admin/src/@fuse/services/confirmation/confirmation.service.ts", "hash": "1697249019834862347", "deps": ["npm:@angular/core", "npm:@angular/material", "npm:lodash-es"]}, {"file": "apps/eiot-admin/src/@fuse/services/confirmation/confirmation.types.ts", "hash": "7822337976937236091"}, {"file": "apps/eiot-admin/src/@fuse/services/confirmation/dialog/dialog.component.html", "hash": "15537172590917877429"}, {"file": "apps/eiot-admin/src/@fuse/services/confirmation/dialog/dialog.component.ts", "hash": "13962360976134697262", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/material"]}, {"file": "apps/eiot-admin/src/@fuse/services/confirmation/index.ts", "hash": "8327068781330591863"}, {"file": "apps/eiot-admin/src/@fuse/services/confirmation/public-api.ts", "hash": "4815675321494223997"}, {"file": "apps/eiot-admin/src/@fuse/services/loading/index.ts", "hash": "5329717474434174164"}, {"file": "apps/eiot-admin/src/@fuse/services/loading/loading.interceptor.ts", "hash": "7145510115328328143", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/@fuse/services/loading/loading.service.ts", "hash": "8426398310801506554", "deps": ["npm:@angular/core", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/@fuse/services/loading/public-api.ts", "hash": "17350846712149784510"}, {"file": "apps/eiot-admin/src/@fuse/services/media-watcher/index.ts", "hash": "14460946595848280763"}, {"file": "apps/eiot-admin/src/@fuse/services/media-watcher/media-watcher.service.ts", "hash": "340172484972103938", "deps": ["npm:@angular/cdk", "npm:@angular/core", "npm:lodash-es", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/@fuse/services/media-watcher/public-api.ts", "hash": "2316354370896676848"}, {"file": "apps/eiot-admin/src/@fuse/services/platform/index.ts", "hash": "7021005960686365672"}, {"file": "apps/eiot-admin/src/@fuse/services/platform/platform.service.ts", "hash": "1696650210056543217", "deps": ["npm:@angular/cdk", "npm:@angular/core"]}, {"file": "apps/eiot-admin/src/@fuse/services/platform/public-api.ts", "hash": "13076180091617022659"}, {"file": "apps/eiot-admin/src/@fuse/services/splash-screen/index.ts", "hash": "7825275835830862417"}, {"file": "apps/eiot-admin/src/@fuse/services/splash-screen/public-api.ts", "hash": "411777169198694069"}, {"file": "apps/eiot-admin/src/@fuse/services/splash-screen/splash-screen.service.ts", "hash": "867492811251929004", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/router", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/@fuse/services/utils/index.ts", "hash": "10592195664652477185"}, {"file": "apps/eiot-admin/src/@fuse/services/utils/public-api.ts", "hash": "12716625436491092363"}, {"file": "apps/eiot-admin/src/@fuse/services/utils/utils.service.ts", "hash": "15509946581435336000", "deps": ["npm:@angular/core", "npm:@angular/router"]}, {"file": "apps/eiot-admin/src/@fuse/styles/components/example-viewer.scss", "hash": "246907616408480852"}, {"file": "apps/eiot-admin/src/@fuse/styles/components/input.scss", "hash": "7836476226721769778"}, {"file": "apps/eiot-admin/src/@fuse/styles/main.scss", "hash": "1360815772155484557"}, {"file": "apps/eiot-admin/src/@fuse/styles/overrides/angular-material.scss", "hash": "15748426059771262738"}, {"file": "apps/eiot-admin/src/@fuse/styles/overrides/highlightjs.scss", "hash": "13598369389701093180"}, {"file": "apps/eiot-admin/src/@fuse/styles/overrides/perfect-scrollbar.scss", "hash": "18087209731572783054"}, {"file": "apps/eiot-admin/src/@fuse/styles/overrides/quill.scss", "hash": "6212823297281346728"}, {"file": "apps/eiot-admin/src/@fuse/styles/tailwind.scss", "hash": "8051241344248683002"}, {"file": "apps/eiot-admin/src/@fuse/styles/themes.scss", "hash": "15825700370208127331"}, {"file": "apps/eiot-admin/src/@fuse/styles/user-themes.scss", "hash": "12464307214039680057"}, {"file": "apps/eiot-admin/src/@fuse/tailwind/plugins/icon-size.js", "hash": "6077409942023801483", "deps": ["npm:tailwindcss"]}, {"file": "apps/eiot-admin/src/@fuse/tailwind/plugins/theming.js", "hash": "2887071976169000997", "deps": ["npm:lodash", "npm:tailwindcss"]}, {"file": "apps/eiot-admin/src/@fuse/tailwind/plugins/utilities.js", "hash": "16489843019131236064", "deps": ["npm:tailwindcss"]}, {"file": "apps/eiot-admin/src/@fuse/tailwind/utils/generate-contrasts.js", "hash": "2336036585276760068", "deps": ["npm:lodash"]}, {"file": "apps/eiot-admin/src/@fuse/tailwind/utils/generate-palette.js", "hash": "15235132658431501963", "deps": ["npm:lodash"]}, {"file": "apps/eiot-admin/src/@fuse/tailwind/utils/json-to-sass-map.js", "hash": "9470807863589971874", "deps": ["npm:lodash"]}, {"file": "apps/eiot-admin/src/@fuse/validators/index.ts", "hash": "9061438514407658016"}, {"file": "apps/eiot-admin/src/@fuse/validators/public-api.ts", "hash": "11670583125032599972"}, {"file": "apps/eiot-admin/src/@fuse/validators/validators.ts", "hash": "179049185996112976", "deps": ["npm:@angular/forms"]}, {"file": "apps/eiot-admin/src/@fuse/version/fuse-version.ts", "hash": "8580288450203924428"}, {"file": "apps/eiot-admin/src/@fuse/version/index.ts", "hash": "17350501503626117538"}, {"file": "apps/eiot-admin/src/@fuse/version/public-api.ts", "hash": "12174018565402443459"}, {"file": "apps/eiot-admin/src/@fuse/version/version.ts", "hash": "10320151491621573492"}, {"file": "apps/eiot-admin/src/_redirects", "hash": "3183170139218197972"}, {"file": "apps/eiot-admin/src/app/app.component.html", "hash": "17816966185630879015"}, {"file": "apps/eiot-admin/src/app/app.component.scss", "hash": "16404522680701464459"}, {"file": "apps/eiot-admin/src/app/app.component.ts", "hash": "17071017722363017443", "deps": ["npm:@angular/core", "npm:@angular/router"]}, {"file": "apps/eiot-admin/src/app/app.config.ts", "hash": "12901518514184290645", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/material-luxon-adapter", "npm:@angular/material", "npm:@angular/platform-browser", "npm:@angular/router", "npm:@jsverse/transloco", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/app.html", "hash": "17816966185630879015"}, {"file": "apps/eiot-admin/src/app/app.resolvers.ts", "hash": "2302607453418558182", "deps": ["npm:@angular/core", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/app.routes.ts", "hash": "11949331230133492311", "deps": ["npm:@angular/router"]}, {"file": "apps/eiot-admin/src/app/app.scss", "hash": "3244421341483603138"}, {"file": "apps/eiot-admin/src/app/app.spec.ts", "hash": "16088666089856769534", "deps": ["npm:@angular/core"]}, {"file": "apps/eiot-admin/src/app/app.ts", "hash": "5653762290970332429", "deps": ["npm:@angular/core", "npm:@angular/router"]}, {"file": "apps/eiot-admin/src/app/core/auth/auth.interceptor.ts", "hash": "14034559537343947789", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/core/auth/auth.provider.ts", "hash": "7400547247109228821", "deps": ["npm:@angular/common", "npm:@angular/core"]}, {"file": "apps/eiot-admin/src/app/core/auth/auth.service.ts", "hash": "9312437827731921528", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/core/auth/auth.utils.ts", "hash": "12773914461764936130"}, {"file": "apps/eiot-admin/src/app/core/auth/guards/auth.guard.ts", "hash": "4568171295806449313", "deps": ["npm:@angular/core", "npm:@angular/router", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/core/auth/guards/noAuth.guard.ts", "hash": "17416168292250570086", "deps": ["npm:@angular/core", "npm:@angular/router", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/core/icons/icons.provider.ts", "hash": "5069930939217481742", "deps": ["npm:@angular/core"]}, {"file": "apps/eiot-admin/src/app/core/icons/icons.service.ts", "hash": "12914715287907322843", "deps": ["npm:@angular/core", "npm:@angular/material", "npm:@angular/platform-browser"]}, {"file": "apps/eiot-admin/src/app/core/navigation/navigation.service.ts", "hash": "15448527130430646524", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/core/navigation/navigation.types.ts", "hash": "2051719659316526896"}, {"file": "apps/eiot-admin/src/app/core/transloco/transloco.http-loader.ts", "hash": "4068661415770226788", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/core/user/user.service.ts", "hash": "17001034501786890970", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/core/user/user.types.ts", "hash": "13570481824304366158"}, {"file": "apps/eiot-admin/src/app/fuse/README.md", "hash": "2749413361631485095"}, {"file": "apps/eiot-admin/src/app/fuse/index.ts", "hash": "16837658330560179965"}, {"file": "apps/eiot-admin/src/app/fuse/services/config.service.ts", "hash": "1896607916547068459", "deps": ["npm:@angular/core", "npm:rxjs", "npm:lodash-es"]}, {"file": "apps/eiot-admin/src/app/fuse/services/loading.service.ts", "hash": "15429619426429435178", "deps": ["npm:@angular/core", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/fuse/services/navigation.service.ts", "hash": "17034065029838449319", "deps": ["npm:@angular/core", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/fuse/styles/_fuse.scss", "hash": "13718369218112367925"}, {"file": "apps/eiot-admin/src/app/layout/common/languages/languages.component.html", "hash": "14875266565246671819"}, {"file": "apps/eiot-admin/src/app/layout/common/languages/languages.component.ts", "hash": "3061501480750797901", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/material", "npm:@jsverse/transloco", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/layout/common/messages/messages.component.html", "hash": "12883786089597577686"}, {"file": "apps/eiot-admin/src/app/layout/common/messages/messages.component.ts", "hash": "2467656456158967551", "deps": ["npm:@angular/cdk", "npm:@angular/common", "npm:@angular/core", "npm:@angular/material", "npm:@angular/router", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/layout/common/messages/messages.service.ts", "hash": "2052167013872510364", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/layout/common/messages/messages.types.ts", "hash": "15557090453277207380"}, {"file": "apps/eiot-admin/src/app/layout/common/notifications/notifications.component.html", "hash": "5227066746777384216"}, {"file": "apps/eiot-admin/src/app/layout/common/notifications/notifications.component.ts", "hash": "10411260044590597265", "deps": ["npm:@angular/cdk", "npm:@angular/common", "npm:@angular/core", "npm:@angular/material", "npm:@angular/router", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/layout/common/notifications/notifications.service.ts", "hash": "17056426897342565338", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/layout/common/notifications/notifications.types.ts", "hash": "17933186769955560767"}, {"file": "apps/eiot-admin/src/app/layout/common/quick-chat/quick-chat.component.html", "hash": "16113278948951821916"}, {"file": "apps/eiot-admin/src/app/layout/common/quick-chat/quick-chat.component.scss", "hash": "160110004802087989"}, {"file": "apps/eiot-admin/src/app/layout/common/quick-chat/quick-chat.component.ts", "hash": "6858152924146412585", "deps": ["npm:@angular/cdk", "npm:@angular/common", "npm:@angular/core", "npm:@angular/material", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/layout/common/quick-chat/quick-chat.service.ts", "hash": "14273060402090358968", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/layout/common/quick-chat/quick-chat.types.ts", "hash": "4060666980650071497"}, {"file": "apps/eiot-admin/src/app/layout/common/search/search.component.html", "hash": "15114386501455884897"}, {"file": "apps/eiot-admin/src/app/layout/common/search/search.component.ts", "hash": "9019420549009927769", "deps": ["npm:@angular/cdk", "npm:@angular/common", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:@angular/router", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/layout/common/settings/settings.component.html", "hash": "10095927387386646941"}, {"file": "apps/eiot-admin/src/app/layout/common/settings/settings.component.ts", "hash": "910877528683929967", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/material", "npm:@angular/router", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/layout/common/shortcuts/shortcuts.component.html", "hash": "17624216371368974916"}, {"file": "apps/eiot-admin/src/app/layout/common/shortcuts/shortcuts.component.ts", "hash": "15165956921323912489", "deps": ["npm:@angular/cdk", "npm:@angular/common", "npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:@angular/router", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/layout/common/shortcuts/shortcuts.service.ts", "hash": "7036020365636910112", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/layout/common/shortcuts/shortcuts.types.ts", "hash": "6935225398450542777"}, {"file": "apps/eiot-admin/src/app/layout/common/user/user.component.html", "hash": "16077610728944108530"}, {"file": "apps/eiot-admin/src/app/layout/common/user/user.component.ts", "hash": "15080399380275672462", "deps": ["npm:@angular/cdk", "npm:@angular/common", "npm:@angular/core", "npm:@angular/material", "npm:@angular/router", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/layout/layout.component.html", "hash": "11189885059154361028"}, {"file": "apps/eiot-admin/src/app/layout/layout.component.scss", "hash": "4684939455617624941"}, {"file": "apps/eiot-admin/src/app/layout/layout.component.ts", "hash": "6284276981422023072", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/router", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/layout/layouts/empty/empty.component.html", "hash": "2901045226561179615"}, {"file": "apps/eiot-admin/src/app/layout/layouts/empty/empty.component.ts", "hash": "4732857051082498486", "deps": ["npm:@angular/core", "npm:@angular/router", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/layout/layouts/horizontal/centered/centered.component.html", "hash": "8507005445176718957"}, {"file": "apps/eiot-admin/src/app/layout/layouts/horizontal/centered/centered.component.ts", "hash": "2466603954423177701", "deps": ["npm:@angular/core", "npm:@angular/material", "npm:@angular/router", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/layout/layouts/horizontal/enterprise/enterprise.component.html", "hash": "9984973625847697682"}, {"file": "apps/eiot-admin/src/app/layout/layouts/horizontal/enterprise/enterprise.component.ts", "hash": "5736154441413551492", "deps": ["npm:@angular/core", "npm:@angular/material", "npm:@angular/router", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/layout/layouts/horizontal/material/material.component.html", "hash": "14265799344716485301"}, {"file": "apps/eiot-admin/src/app/layout/layouts/horizontal/material/material.component.ts", "hash": "5503365778947488092", "deps": ["npm:@angular/core", "npm:@angular/material", "npm:@angular/router", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/layout/layouts/horizontal/modern/modern.component.html", "hash": "4783578547320075505"}, {"file": "apps/eiot-admin/src/app/layout/layouts/horizontal/modern/modern.component.ts", "hash": "8949842035997548629", "deps": ["npm:@angular/core", "npm:@angular/material", "npm:@angular/router", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/layout/layouts/vertical/classic/classic.component.html", "hash": "4597659572212920763"}, {"file": "apps/eiot-admin/src/app/layout/layouts/vertical/classic/classic.component.ts", "hash": "16797852228197709894", "deps": ["npm:@angular/core", "npm:@angular/material", "npm:@angular/router", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/layout/layouts/vertical/classy/classy.component.html", "hash": "4300485342665588474"}, {"file": "apps/eiot-admin/src/app/layout/layouts/vertical/classy/classy.component.ts", "hash": "3646056367389923528", "deps": ["npm:@angular/core", "npm:@angular/material", "npm:@angular/router", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/layout/layouts/vertical/compact/compact.component.html", "hash": "5487090223438645287"}, {"file": "apps/eiot-admin/src/app/layout/layouts/vertical/compact/compact.component.ts", "hash": "11556086372107581565", "deps": ["npm:@angular/core", "npm:@angular/material", "npm:@angular/router", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/layout/layouts/vertical/dense/dense.component.html", "hash": "6718393222371977934"}, {"file": "apps/eiot-admin/src/app/layout/layouts/vertical/dense/dense.component.ts", "hash": "9592948709811451861", "deps": ["npm:@angular/core", "npm:@angular/material", "npm:@angular/router", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/layout/layouts/vertical/futuristic/futuristic.component.html", "hash": "7889434071503793731"}, {"file": "apps/eiot-admin/src/app/layout/layouts/vertical/futuristic/futuristic.component.ts", "hash": "11058393231302728049", "deps": ["npm:@angular/core", "npm:@angular/material", "npm:@angular/router", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/layout/layouts/vertical/thin/thin.component.html", "hash": "13904568667115718726"}, {"file": "apps/eiot-admin/src/app/layout/layouts/vertical/thin/thin.component.ts", "hash": "11278767104937006551", "deps": ["npm:@angular/core", "npm:@angular/material", "npm:@angular/router", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/mock-api/apps/academy/api.ts", "hash": "8828069597398575027", "deps": ["npm:@angular/core", "npm:lodash-es"]}, {"file": "apps/eiot-admin/src/app/mock-api/apps/academy/data.ts", "hash": "6093877619337346449"}, {"file": "apps/eiot-admin/src/app/mock-api/apps/chat/api.ts", "hash": "11281962632605917103", "deps": ["npm:@angular/core", "npm:lodash-es"]}, {"file": "apps/eiot-admin/src/app/mock-api/apps/chat/data.ts", "hash": "12010366250140913984", "deps": ["npm:luxon"]}, {"file": "apps/eiot-admin/src/app/mock-api/apps/contacts/api.ts", "hash": "13748414457928589188", "deps": ["npm:@angular/core", "npm:lodash-es", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/mock-api/apps/contacts/data.ts", "hash": "16230450701900715148"}, {"file": "apps/eiot-admin/src/app/mock-api/apps/ecommerce/inventory/api.ts", "hash": "17945126669697925270", "deps": ["npm:@angular/core", "npm:lodash-es"]}, {"file": "apps/eiot-admin/src/app/mock-api/apps/ecommerce/inventory/data.ts", "hash": "2886185054908248771"}, {"file": "apps/eiot-admin/src/app/mock-api/apps/file-manager/api.ts", "hash": "6284753126017141132", "deps": ["npm:@angular/core", "npm:lodash-es"]}, {"file": "apps/eiot-admin/src/app/mock-api/apps/file-manager/data.ts", "hash": "5121346945068454793"}, {"file": "apps/eiot-admin/src/app/mock-api/apps/help-center/api.ts", "hash": "5037478210769358573", "deps": ["npm:@angular/core", "npm:lodash-es"]}, {"file": "apps/eiot-admin/src/app/mock-api/apps/help-center/data.ts", "hash": "15455934213046204640"}, {"file": "apps/eiot-admin/src/app/mock-api/apps/mailbox/api.ts", "hash": "11593480211779912716", "deps": ["npm:@angular/core", "npm:lodash-es"]}, {"file": "apps/eiot-admin/src/app/mock-api/apps/mailbox/data.ts", "hash": "13074689060090058826", "deps": ["npm:luxon"]}, {"file": "apps/eiot-admin/src/app/mock-api/apps/notes/api.ts", "hash": "16462802412998363900", "deps": ["npm:@angular/core", "npm:lodash-es"]}, {"file": "apps/eiot-admin/src/app/mock-api/apps/notes/data.ts", "hash": "13919110082030214548", "deps": ["npm:luxon"]}, {"file": "apps/eiot-admin/src/app/mock-api/apps/scrumboard/api.ts", "hash": "2439689284148391348", "deps": ["npm:@angular/core", "npm:lodash-es"]}, {"file": "apps/eiot-admin/src/app/mock-api/apps/scrumboard/data.ts", "hash": "8097233177876507314", "deps": ["npm:luxon"]}, {"file": "apps/eiot-admin/src/app/mock-api/apps/tasks/api.ts", "hash": "1833030223045885188", "deps": ["npm:@angular/core", "npm:lodash-es"]}, {"file": "apps/eiot-admin/src/app/mock-api/apps/tasks/data.ts", "hash": "15817633453957025414"}, {"file": "apps/eiot-admin/src/app/mock-api/common/auth/api.ts", "hash": "8265847562994252985", "deps": ["npm:@angular/core", "npm:lodash-es"]}, {"file": "apps/eiot-admin/src/app/mock-api/common/messages/api.ts", "hash": "11276809839782437772", "deps": ["npm:@angular/core", "npm:lodash-es"]}, {"file": "apps/eiot-admin/src/app/mock-api/common/messages/data.ts", "hash": "7723927325316866845", "deps": ["npm:luxon"]}, {"file": "apps/eiot-admin/src/app/mock-api/common/navigation/api.ts", "hash": "13812130885193314066", "deps": ["npm:@angular/core", "npm:lodash-es"]}, {"file": "apps/eiot-admin/src/app/mock-api/common/navigation/data.ts", "hash": "18010175436515929186"}, {"file": "apps/eiot-admin/src/app/mock-api/common/notifications/api.ts", "hash": "15780567715836167500", "deps": ["npm:@angular/core", "npm:lodash-es"]}, {"file": "apps/eiot-admin/src/app/mock-api/common/notifications/data.ts", "hash": "5874836452531734629", "deps": ["npm:luxon"]}, {"file": "apps/eiot-admin/src/app/mock-api/common/search/api.ts", "hash": "9714149252312005055", "deps": ["npm:@angular/core", "npm:lodash-es"]}, {"file": "apps/eiot-admin/src/app/mock-api/common/shortcuts/api.ts", "hash": "1268707438680695396", "deps": ["npm:@angular/core", "npm:lodash-es"]}, {"file": "apps/eiot-admin/src/app/mock-api/common/shortcuts/data.ts", "hash": "14888750237783262400"}, {"file": "apps/eiot-admin/src/app/mock-api/common/user/api.ts", "hash": "12891170688140018039", "deps": ["npm:@angular/core", "npm:lodash-es"]}, {"file": "apps/eiot-admin/src/app/mock-api/common/user/data.ts", "hash": "14730320080661409691"}, {"file": "apps/eiot-admin/src/app/mock-api/dashboards/analytics/api.ts", "hash": "16512718163499887642", "deps": ["npm:@angular/core", "npm:lodash-es"]}, {"file": "apps/eiot-admin/src/app/mock-api/dashboards/analytics/data.ts", "hash": "15653641033846456733", "deps": ["npm:luxon"]}, {"file": "apps/eiot-admin/src/app/mock-api/dashboards/crypto/api.ts", "hash": "17839822716513683316", "deps": ["npm:@angular/core", "npm:lodash-es"]}, {"file": "apps/eiot-admin/src/app/mock-api/dashboards/crypto/data.ts", "hash": "16648707748130800636", "deps": ["npm:luxon"]}, {"file": "apps/eiot-admin/src/app/mock-api/dashboards/finance/api.ts", "hash": "9406137932735444677", "deps": ["npm:@angular/core", "npm:lodash-es"]}, {"file": "apps/eiot-admin/src/app/mock-api/dashboards/finance/data.ts", "hash": "17771267672610367614", "deps": ["npm:luxon"]}, {"file": "apps/eiot-admin/src/app/mock-api/dashboards/project/api.ts", "hash": "11502319421485316645", "deps": ["npm:@angular/core", "npm:lodash-es"]}, {"file": "apps/eiot-admin/src/app/mock-api/dashboards/project/data.ts", "hash": "7482583809190422045", "deps": ["npm:luxon"]}, {"file": "apps/eiot-admin/src/app/mock-api/index.ts", "hash": "18328260310110655374", "deps": ["npm:@angular/core"]}, {"file": "apps/eiot-admin/src/app/mock-api/pages/activities/api.ts", "hash": "5172227909974150899", "deps": ["npm:@angular/core", "npm:lodash-es"]}, {"file": "apps/eiot-admin/src/app/mock-api/pages/activities/data.ts", "hash": "3505597800632976345", "deps": ["npm:luxon"]}, {"file": "apps/eiot-admin/src/app/mock-api/ui/icons/api.ts", "hash": "6238989625380810403", "deps": ["npm:@angular/core", "npm:lodash-es"]}, {"file": "apps/eiot-admin/src/app/mock-api/ui/icons/data.ts", "hash": "15804127236068015107"}, {"file": "apps/eiot-admin/src/app/modules/admin/example/example.component.html", "hash": "15047519432923522080"}, {"file": "apps/eiot-admin/src/app/modules/admin/example/example.component.ts", "hash": "2720153598335356363", "deps": ["npm:@angular/core"]}, {"file": "apps/eiot-admin/src/app/modules/admin/example/example.routes.ts", "hash": "10088324710311264972", "deps": ["npm:@angular/router"]}, {"file": "apps/eiot-admin/src/app/modules/auth/confirmation-required/confirmation-required.component.html", "hash": "533500027256616778"}, {"file": "apps/eiot-admin/src/app/modules/auth/confirmation-required/confirmation-required.component.ts", "hash": "10634788465553973241", "deps": ["npm:@angular/core", "npm:@angular/router"]}, {"file": "apps/eiot-admin/src/app/modules/auth/confirmation-required/confirmation-required.routes.ts", "hash": "9796633500338774360", "deps": ["npm:@angular/router"]}, {"file": "apps/eiot-admin/src/app/modules/auth/forgot-password/forgot-password.component.html", "hash": "17627512790844330407"}, {"file": "apps/eiot-admin/src/app/modules/auth/forgot-password/forgot-password.component.ts", "hash": "4843902226394698001", "deps": ["npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:@angular/router", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/modules/auth/forgot-password/forgot-password.routes.ts", "hash": "9590887862982143342", "deps": ["npm:@angular/router"]}, {"file": "apps/eiot-admin/src/app/modules/auth/reset-password/reset-password.component.html", "hash": "1602690044072869351"}, {"file": "apps/eiot-admin/src/app/modules/auth/reset-password/reset-password.component.ts", "hash": "13349585081252023838", "deps": ["npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:@angular/router", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/modules/auth/reset-password/reset-password.routes.ts", "hash": "6029565890329707998", "deps": ["npm:@angular/router"]}, {"file": "apps/eiot-admin/src/app/modules/auth/sign-in/sign-in.component.html", "hash": "13279909005627297648"}, {"file": "apps/eiot-admin/src/app/modules/auth/sign-in/sign-in.component.ts", "hash": "4772531825502279930", "deps": ["npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:@angular/router"]}, {"file": "apps/eiot-admin/src/app/modules/auth/sign-in/sign-in.routes.ts", "hash": "2046840918733787138", "deps": ["npm:@angular/router"]}, {"file": "apps/eiot-admin/src/app/modules/auth/sign-out/sign-out.component.html", "hash": "10610928505425673801"}, {"file": "apps/eiot-admin/src/app/modules/auth/sign-out/sign-out.component.ts", "hash": "18152003132358224986", "deps": ["npm:@angular/common", "npm:@angular/core", "npm:@angular/router", "npm:rxjs"]}, {"file": "apps/eiot-admin/src/app/modules/auth/sign-out/sign-out.routes.ts", "hash": "16376359297282358626", "deps": ["npm:@angular/router"]}, {"file": "apps/eiot-admin/src/app/modules/auth/sign-up/sign-up.component.html", "hash": "7785008728640415995"}, {"file": "apps/eiot-admin/src/app/modules/auth/sign-up/sign-up.component.ts", "hash": "15292422215318009457", "deps": ["npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:@angular/router"]}, {"file": "apps/eiot-admin/src/app/modules/auth/sign-up/sign-up.routes.ts", "hash": "1153548317650694960", "deps": ["npm:@angular/router"]}, {"file": "apps/eiot-admin/src/app/modules/auth/unlock-session/unlock-session.component.html", "hash": "14416438935287202362"}, {"file": "apps/eiot-admin/src/app/modules/auth/unlock-session/unlock-session.component.ts", "hash": "13008551026992446879", "deps": ["npm:@angular/core", "npm:@angular/forms", "npm:@angular/material", "npm:@angular/router"]}, {"file": "apps/eiot-admin/src/app/modules/auth/unlock-session/unlock-session.routes.ts", "hash": "6316413038377329558", "deps": ["npm:@angular/router"]}, {"file": "apps/eiot-admin/src/app/modules/landing/home/<USER>", "hash": "5745472255285456769"}, {"file": "apps/eiot-admin/src/app/modules/landing/home/<USER>", "hash": "1021999698022357776", "deps": ["npm:@angular/core", "npm:@angular/material", "npm:@angular/router"]}, {"file": "apps/eiot-admin/src/app/modules/landing/home/<USER>", "hash": "11356721016041834390", "deps": ["npm:@angular/router"]}, {"file": "apps/eiot-admin/src/bootstrap.ts", "hash": "3314327030169821486", "deps": ["npm:@angular/platform-browser"]}, {"file": "apps/eiot-admin/src/index.html", "hash": "15730974159099078801"}, {"file": "apps/eiot-admin/src/main.ts", "hash": "14701344671644104227"}, {"file": "apps/eiot-admin/src/styles.scss", "hash": "9191114568156452374"}, {"file": "apps/eiot-admin/src/styles/inter.css", "hash": "12412062678978887695"}, {"file": "apps/eiot-admin/src/styles/splash-screen.css", "hash": "15092981912493149700"}, {"file": "apps/eiot-admin/src/styles/styles.scss", "hash": "15028770534667302898"}, {"file": "apps/eiot-admin/src/styles/tailwind.scss", "hash": "17862918790312563780"}, {"file": "apps/eiot-admin/src/styles/vendors.scss", "hash": "8236747794974243889"}, {"file": "apps/eiot-admin/src/test-setup.ts", "hash": "1917999429567095215", "deps": ["npm:jest-preset-angular"]}, {"file": "apps/eiot-admin/tailwind.config.js", "hash": "16315989419192639", "deps": ["npm:tailwindcss"]}, {"file": "apps/eiot-admin/transloco.config.js", "hash": "12116767730746865476"}, {"file": "apps/eiot-admin/tsconfig.app.json", "hash": "3799585672836042095"}, {"file": "apps/eiot-admin/tsconfig.json", "hash": "10155254885564763297"}, {"file": "apps/eiot-admin/tsconfig.spec.json", "hash": "10685812502644337696"}, {"file": "apps/eiot-admin/webpack.config.js", "hash": "14622127217075485295", "deps": ["npm:@angular-architects/module-federation"]}], "shell": [{"file": "apps/shell/eslint.config.mjs", "hash": "15052851186193004871", "deps": ["npm:@nx/eslint-plugin"]}, {"file": "apps/shell/jest.config.ts", "hash": "12443077723975770363"}, {"file": "apps/shell/project.json", "hash": "6242300655441116452"}, {"file": "apps/shell/public/favicon.ico", "hash": "9303420814833116677"}, {"file": "apps/shell/src/app/app.config.ts", "hash": "4515289115773032281", "deps": ["npm:@angular/core", "npm:@angular/router"]}, {"file": "apps/shell/src/app/app.html", "hash": "9850744556795935017"}, {"file": "apps/shell/src/app/app.routes.ts", "hash": "1529541345129123053", "deps": ["npm:@angular/router"]}, {"file": "apps/shell/src/app/app.scss", "hash": "8791014054705639832"}, {"file": "apps/shell/src/app/app.spec.ts", "hash": "12164898256445777378", "deps": ["npm:@angular/core"]}, {"file": "apps/shell/src/app/app.ts", "hash": "17633606002413962476", "deps": ["npm:@angular/core", "npm:@angular/router"]}, {"file": "apps/shell/src/app/home.component.ts", "hash": "4830021448046677040", "deps": ["npm:@angular/core", "npm:@angular/router"]}, {"file": "apps/shell/src/app/nx-welcome.ts", "hash": "9853477230199189745", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "apps/shell/src/bootstrap.ts", "hash": "3314327030169821486", "deps": ["npm:@angular/platform-browser"]}, {"file": "apps/shell/src/index.html", "hash": "6118171675619996744"}, {"file": "apps/shell/src/main.ts", "hash": "14701344671644104227"}, {"file": "apps/shell/src/styles.scss", "hash": "17788899824995975898"}, {"file": "apps/shell/src/test-setup.ts", "hash": "1917999429567095215", "deps": ["npm:jest-preset-angular"]}, {"file": "apps/shell/tsconfig.app.json", "hash": "10974451356376189872"}, {"file": "apps/shell/tsconfig.json", "hash": "4663124578817442910"}, {"file": "apps/shell/tsconfig.spec.json", "hash": "1029128818174064697"}, {"file": "apps/shell/webpack.config.js", "hash": "8083439723322575626", "deps": ["npm:@angular-architects/module-federation"]}], "ui": [{"file": "libs/shared/ui/README.md", "hash": "12870269544986837831"}, {"file": "libs/shared/ui/eslint.config.mjs", "hash": "11726021660447952413", "deps": ["npm:@nx/eslint-plugin", ["npm:jsonc-eslint-parser", "dynamic"]]}, {"file": "libs/shared/ui/ng-package.json", "hash": "2565180101704297589"}, {"file": "libs/shared/ui/package.json", "hash": "5747239654275993357", "deps": ["npm:@angular/common", "npm:@angular/core"]}, {"file": "libs/shared/ui/project.json", "hash": "18348103709581287787"}, {"file": "libs/shared/ui/src/index.ts", "hash": "2664278880031917718"}, {"file": "libs/shared/ui/src/lib/ui/ui.css", "hash": "3244421341483603138"}, {"file": "libs/shared/ui/src/lib/ui/ui.html", "hash": "11089220566535924882"}, {"file": "libs/shared/ui/src/lib/ui/ui.ts", "hash": "7110857949133656523", "deps": ["npm:@angular/core", "npm:@angular/common"]}, {"file": "libs/shared/ui/tsconfig.json", "hash": "9866232282412725319"}, {"file": "libs/shared/ui/tsconfig.lib.json", "hash": "14882717054991120248"}, {"file": "libs/shared/ui/tsconfig.lib.prod.json", "hash": "10809217796916261822"}]}}}