import { InjectionToken } from '@angular/core';
export const TRANSLOCO_CONFIG = new InjectionToken(ngDevMode ? 'TRANSLOCO_CONFIG' : '', {
    providedIn: 'root',
    factory: () => defaultConfig,
});
export const defaultConfig = {
    defaultLang: 'en',
    reRenderOnLangChange: false,
    prodMode: false,
    failedRetries: 2,
    fallbackLang: [],
    availableLangs: [],
    missingHandler: {
        logMissingKey: true,
        useFallbackTranslation: false,
        allowEmpty: false,
    },
    flatten: {
        aot: false,
    },
    interpolation: ['{{', '}}'],
    scopes: {
        keepCasing: false,
    },
};
export function translocoConfig(config = {}) {
    return {
        ...defaultConfig,
        ...config,
        missingHandler: {
            ...defaultConfig.missingHandler,
            ...config.missingHandler,
        },
        flatten: {
            ...defaultConfig.flatten,
            ...config.flatten,
        },
        scopes: {
            ...defaultConfig.scopes,
            ...config.scopes,
        },
    };
}
//# sourceMappingURL=data:application/json;base64,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