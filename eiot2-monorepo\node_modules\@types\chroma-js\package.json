{"name": "@types/chroma-js", "version": "3.1.1", "description": "TypeScript definitions for chroma-js", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/chroma-js", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "invliD", "url": "https://github.com/invliD"}, {"name": "<PERSON><PERSON>", "githubUsername": "mpacholec", "url": "https://github.com/mpacholec"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON>o"}], "type": "module", "main": "", "types": "index.d.ts", "exports": {".": {"import": "./index.d.ts", "require": "./index.d.cts"}, "./light": {"import": "./index.d.ts", "require": "./index.d.cts"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/chroma-js"}, "scripts": {}, "dependencies": {}, "peerDependencies": {}, "typesPublisherContentHash": "ec627601e0f6214c4520cc0feb3c57c4d7507550f5dfec801eb6a14a3c26812c", "typeScriptVersion": "5.0"}