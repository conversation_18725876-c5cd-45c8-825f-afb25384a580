{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class App {\n  constructor() {\n    this.title = 'eiot-admin';\n  }\n  static #_ = this.ɵfac = function App_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || App)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: App,\n    selectors: [[\"app-root\"]],\n    decls: 1,\n    vars: 0,\n    template: function App_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"router-outlet\");\n      }\n    },\n    dependencies: [RouterModule, i1.RouterOutlet],\n    styles: [\"var[_ngcontent-%COMP%]   resource[_ngcontent-%COMP%];\\n\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\tvar __webpack_modules__ = ({\\n\\n\\n 44:\\n\\n\\n\\n\\n\\n (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/postcss-loader/dist/cjs.js):\\\\nError: Cannot find module 'chroma-js'\\\\nRequire stack:\\\\n- D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\apps\\\\\\\\eiot-admin\\\\\\\\src\\\\\\\\@fuse\\\\\\\\tailwind\\\\\\\\utils\\\\\\\\generate-palette.js\\\\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1212:15)\\\\n    at Function.resolve (node:internal/modules/helpers:193:19)\\\\n    at _resolve (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:246378)\\\\n    at jiti (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:249092)\\\\n    at D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\apps\\\\\\\\eiot-admin\\\\\\\\src\\\\\\\\@fuse\\\\\\\\tailwind\\\\\\\\utils\\\\\\\\generate-palette.js:1:91\\\\n    at evalModule (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:251913)\\\\n    at jiti (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:249841)\\\\n    at D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\apps\\\\\\\\eiot-admin\\\\\\\\tailwind.config.js:4:25\\\\n    at evalModule (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:251913)\\\\n    at jiti (D:\\\\\\\\EIOT2.SERVER\\\\\\\\eiot2-monorepo\\\\\\\\node_modules\\\\\\\\jiti\\\\\\\\dist\\\\\\\\jiti.js:1:249841)\\\");\\n\\n\\n })\\n\\n\\n \\t});\\n\\n\\n\\n \\t\\n\\n \\t// startup\\n\\n \\t// Load entry module and return exports\\n\\n \\t// This entry module doesn't tell about it's top-level declarations so it can't be inlined\\n\\n \\tvar __webpack_exports__ = {};\\n\\n \\t__webpack_modules__[44]();\\n\\n \\tresource = __webpack_exports__;\\n\\n \\t\\n\\n })()\\n;\"]\n  });\n}", "map": {"version": 3, "names": ["RouterModule", "App", "constructor", "title", "_", "_2", "selectors", "decls", "vars", "template", "App_Template", "rf", "ctx", "i0", "ɵɵelement", "i1", "RouterOutlet", "styles"], "sources": ["D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\app\\app.ts", "D:\\EIOT2.SERVER\\eiot2-monorepo\\apps\\eiot-admin\\src\\app\\app.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { RouterModule } from '@angular/router';\n\n@Component({\n  imports: [RouterModule],\n  selector: 'app-root',\n  templateUrl: './app.html',\n  styleUrl: './app.scss',\n})\nexport class App {\n  protected title = 'eiot-admin';\n}\n", "<router-outlet></router-outlet>\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;;AAQ9C,OAAM,MAAOC,GAAG;EANhBC,YAAA;IAOY,KAAAC,KAAK,GAAG,YAAY;;EAC/B,QAAAC,CAAA,G;qCAFYH,GAAG;EAAA;EAAA,QAAAI,EAAA,G;UAAHJ,GAAG;IAAAK,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,aAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCThBE,EAAA,CAAAC,SAAA,oBAA+B;;;mBDInBd,YAAY,EAAAe,EAAA,CAAAC,YAAA;IAAAC,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}