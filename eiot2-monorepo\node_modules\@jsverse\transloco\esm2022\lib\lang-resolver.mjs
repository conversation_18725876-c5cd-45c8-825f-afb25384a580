import { getLangFromScope, getPipeValue, getScopeFromLang } from './shared';
export class LangResolver {
    initialized = false;
    // inline => provider => active
    resolve({ inline, provider, active }) {
        let lang = active;
        /**
         * When the user changes the lang we need to update
         * the view. Otherwise, the lang will remain the inline/provided lang
         */
        if (this.initialized) {
            lang = active;
            return lang;
        }
        if (provider) {
            const [, extracted] = getPipeValue(provider, 'static');
            lang = extracted;
        }
        if (inline) {
            const [, extracted] = getPipeValue(inline, 'static');
            lang = extracted;
        }
        this.initialized = true;
        return lang;
    }
    /**
     *
     * Resolve the lang
     *
     * @example
     *
     * resolveLangBasedOnScope('todos/en') => en
     * resolveLangBasedOnScope('en') => en
     *
     */
    resolveLangBasedOnScope(lang) {
        const scope = getScopeFromLang(lang);
        return scope ? getLangFromScope(lang) : lang;
    }
    /**
     *
     * Resolve the lang path for loading
     *
     * @example
     *
     * resolveLangPath('todos', 'en') => todos/en
     * resolveLangPath('en') => en
     *
     */
    resolveLangPath(lang, scope) {
        return scope ? `${scope}/${lang}` : lang;
    }
}
//# sourceMappingURL=data:application/json;base64,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