import { NgModule } from '@angular/core';
import { TranslocoDirective } from './transloco.directive';
import { TranslocoPipe } from './transloco.pipe';
import * as i0 from "@angular/core";
const decl = [TranslocoDirective, TranslocoPipe];
export class TranslocoModule {
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "18.2.9", ngImport: i0, type: TranslocoModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });
    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: "14.0.0", version: "18.2.9", ngImport: i0, type: TranslocoModule, imports: [TranslocoDirective, TranslocoPipe], exports: [TranslocoDirective, TranslocoPipe] });
    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: "12.0.0", version: "18.2.9", ngImport: i0, type: TranslocoModule });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "18.2.9", ngImport: i0, type: TranslocoModule, decorators: [{
            type: NgModule,
            args: [{
                    imports: decl,
                    exports: decl,
                }]
        }] });
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoidHJhbnNsb2NvLm1vZHVsZS5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uL2xpYnMvdHJhbnNsb2NvL3NyYy9saWIvdHJhbnNsb2NvLm1vZHVsZS50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSxPQUFPLEVBQUUsUUFBUSxFQUFFLE1BQU0sZUFBZSxDQUFDO0FBRXpDLE9BQU8sRUFBRSxrQkFBa0IsRUFBRSxNQUFNLHVCQUF1QixDQUFDO0FBQzNELE9BQU8sRUFBRSxhQUFhLEVBQUUsTUFBTSxrQkFBa0IsQ0FBQzs7QUFFakQsTUFBTSxJQUFJLEdBQUcsQ0FBQyxrQkFBa0IsRUFBRSxhQUFhLENBQUMsQ0FBQztBQU1qRCxNQUFNLE9BQU8sZUFBZTt1R0FBZixlQUFlO3dHQUFmLGVBQWUsWUFOZCxrQkFBa0IsRUFBRSxhQUFhLGFBQWpDLGtCQUFrQixFQUFFLGFBQWE7d0dBTWxDLGVBQWU7OzJGQUFmLGVBQWU7a0JBSjNCLFFBQVE7bUJBQUM7b0JBQ1IsT0FBTyxFQUFFLElBQUk7b0JBQ2IsT0FBTyxFQUFFLElBQUk7aUJBQ2QiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZ01vZHVsZSB9IGZyb20gJ0Bhbmd1bGFyL2NvcmUnO1xuXG5pbXBvcnQgeyBUcmFuc2xvY29EaXJlY3RpdmUgfSBmcm9tICcuL3RyYW5zbG9jby5kaXJlY3RpdmUnO1xuaW1wb3J0IHsgVHJhbnNsb2NvUGlwZSB9IGZyb20gJy4vdHJhbnNsb2NvLnBpcGUnO1xuXG5jb25zdCBkZWNsID0gW1RyYW5zbG9jb0RpcmVjdGl2ZSwgVHJhbnNsb2NvUGlwZV07XG5cbkBOZ01vZHVsZSh7XG4gIGltcG9ydHM6IGRlY2wsXG4gIGV4cG9ydHM6IGRlY2wsXG59KVxuZXhwb3J0IGNsYXNzIFRyYW5zbG9jb01vZHVsZSB7fVxuIl19