{"version": 3, "file": "transloco.js", "sourceRoot": "", "sources": ["../../../../../../libs/transloco-schematics/src/utils/transloco.ts"], "names": [], "mappings": ";;AAYA,oCAeC;AAED,oCASC;AAED,gDAMC;AAED,wCAUC;AAED,gCAEC;AAED,4BAEC;AAED,8CAEC;AAED,kDAcC;AAED,kDAUC;AAED,4DAqBC;AAED,wCAEC;AA7HD,+BAA+B;AAO/B,0DAAsE;AAEtE,yCAAwC;AACxC,qCAAqC;AAErC,SAAgB,YAAY,CAC1B,IAAU,EACV,KAAe,EACf,oBAAoB,GAAG,cAAc;IAErC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,8BAAW,CAAC,EAAE,CAAC;QAC3B,IAAI,CAAC,MAAM,CACT,8BAAW,EACX,IAAA,qCAAkB,EAAC;YACjB,oBAAoB,EAAE,oBAAoB;YAC1C,KAAK;YACL,WAAW,EAAE,EAAE;SAChB,CAAC,CACH,CAAC;IACJ,CAAC;AACH,CAAC;AAED,SAAgB,YAAY,CAAC,IAAU,EAAE,MAA6B;IACpE,MAAM,cAAc,GAAG,IAAA,kBAAS,GAAE,CAAC;IACnC,IAAI,CAAC,cAAc,IAAI,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAChE,OAAO,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,oBAAoB,CAAC,CAAC;IACvE,CAAC;IACD,IAAI,CAAC,SAAS,CACZ,8BAAW,EACX,IAAA,qCAAkB,EAAC,EAAE,GAAG,MAAM,EAAE,GAAG,cAAc,EAAE,CAAC,CACrD,CAAC;AACJ,CAAC;AAED,SAAgB,kBAAkB,CAChC,QAAsB,EACtB,GAAa,EACb,MAAM,GAAG,IAAI,CAAC,KAAK;IAEnB,OAAO,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;AAC9D,CAAC;AAED,SAAgB,cAAc,CAC5B,IAAU,EACV,OAAe,EACf,QAAsB,EACtB,OAAO;IAEP,OAAO,IAAI,CAAC,SAAS,CACnB,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,EACzB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CACjC,CAAC;AACJ,CAAC;AAED,SAAgB,UAAU,CAAC,GAAa;IACtC,OAAO,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC;AAC3C,CAAC;AAED,SAAgB,QAAQ,CAAC,GAAa;IACpC,OAAO,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC;AAC7C,CAAC;AAED,SAAgB,iBAAiB,CAAC,MAAM,GAAG,EAAE,EAAE,GAAG;IAChD,OAAO,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;AAC3C,CAAC;AAED,SAAgB,mBAAmB,CACjC,IAAU,EACV,OAAuD;IAEvD,MAAM,eAAe,GAAG,IAAA,kBAAS,GAAE,CAAC;IACpC,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;QAC5B,OAAO,OAAO,CAAC,eAAe,CAAC;IACjC,CAAC;SAAM,IAAI,eAAe,IAAI,eAAe,CAAC,oBAAoB,EAAE,CAAC;QACnE,OAAO,eAAe,CAAC,oBAAoB,CAAC;IAC9C,CAAC;SAAM,CAAC;QACN,MAAM,OAAO,GAAG,IAAA,qBAAU,EAAC,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;QAClD,MAAM,QAAQ,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC;QAC1D,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;IAC5C,CAAC;AACH,CAAC;AAED,SAAgB,mBAAmB,CACjC,IAAU,EACV,IAAY,EACZ,MAAO;IAEP,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAClC,OAAO,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QACzC,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC5B,WAAW,EAAE,kBAAkB,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC;KAC3D,CAAC,CAAC,CAAC;AACN,CAAC;AAED,SAAgB,wBAAwB,CACtC,IAAU,EACV,WAAmB;IAEnB,MAAM,eAAe,GAAG,IAAA,kBAAS,GAAE,CAAC;IACpC,IACE,eAAe,CAAC,YAAY;QAC5B,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,MAAM,EAChD,CAAC;QACD,OAAO,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,GAAG,CACrD,CAAC,CAAC,KAAK,EAAE,IAAI,CAAmB,EAAE,EAAE,CAAC,CAAC;YACpC,KAAK;YACL,IAAI;SACL,CAAC,CACH,CAAC;IACJ,CAAC;IACD,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;IACzC,OAAO,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QACtC,KAAK,EAAE,MAAM;QACb,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC;KAClC,CAAC,CAAC,CAAC;AACN,CAAC;AAED,SAAgB,cAAc,CAAC,OAAsB;IACnD,OAAO,OAAO,CAAC,WAAW,IAAI,IAAA,kBAAS,GAAE,CAAC,WAAW,CAAC;AACxD,CAAC"}