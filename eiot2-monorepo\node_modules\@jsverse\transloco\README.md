> [!IMPORTANT]  
> The Transloco packages are now published under the **@jsverse** scope, update your dependencies to get the latest features 🚀

<br />
<p align="center">
 <img width="50%" height="50%" src="./logo.svg">
</p>

<p align="center">
  The internationalization (i18n) library for Angular
</p>

<h3></h3>

![npm](https://img.shields.io/npm/v/@jsverse/transloco)
![Bundle Size](https://img.shields.io/bundlephobia/min/@jsverse/transloco)
![Downloads](https://img.shields.io/npm/dm/@jsverse/transloco)
[![Build Status](https://github.com/jsverse/transloco/actions/workflows/ci.yml/badge.svg)]()
[![PRs](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square)](https://github.com/jsverse/transloco/blob/master/CONTRIBUTING.md)

Transloco enables you to define translations for your content in multiple languages and seamlessly switch between them at runtime. With its robust API, managing translations becomes efficient and maintainable. Additionally, Transloco offers an array of plugins designed to enhance your development experience. Here's a quick look at its powerful features:

✅ &nbsp;Signal-based API  
✅ &nbsp;Fully supports standalone components  
✅ &nbsp;Clean and DRY templates  
✅ &nbsp;Lazy Loading support  
✅ &nbsp;Handle multiple languages simultaneously  
✅ &nbsp;Flexible fallbacks for missing translations  
✅ &nbsp;Comprehensive testing support  
✅ &nbsp;Server-Side Rendering (SSR) compatibility  
✅ &nbsp;Localization (L10N) support  
✅ &nbsp;A variety of rich plugins  
✅ &nbsp;Highly customizable and hackable  
✅ &nbsp;Schematics

Transloco is your all-in-one solution for internationalizing Angular applications with ease and flexibility.

<hr />

- 🤓 &nbsp;Explore the (brand new ✨) [documentation](https://jsverse.gitbook.io/transloco) to learn more.
- 🚀 &nbsp;Check out the [Sandbox & Examples](https://jsverse.gitbook.io/transloco) to see Transloco in action.
- 😎 &nbsp;Leverage [Schematics](https://jsverse.gitbook.io/transloco/schematics) for streamlined setup and configuration.
- 📖 &nbsp;Dive into insightful [blog posts](https://jsverse.gitbook.io/transloco/blog-posts).
- ❓ &nbsp;Find answers to common questions in our [FAQs](https://jsverse.gitbook.io/transloco/faqs).

## Contributors ✨

Thank goes to all these wonderful [people who contributed](https://github.com/jsverse/transloco/graphs/contributors) ❤️
