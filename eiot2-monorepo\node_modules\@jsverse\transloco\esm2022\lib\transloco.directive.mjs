import { ChangeDetectorRef, DestroyRef, Directive, ElementRef, inject, Input, Renderer2, TemplateRef, ViewContainerRef, } from '@angular/core';
import { forkJoin, switchMap } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Template<PERSON>andler } from './template-handler';
import { TRANSLOCO_LANG } from './transloco-lang';
import { TRANSLOCO_LOADING_TEMPLATE } from './transloco-loading-template';
import { TRANSLOCO_SCOPE } from './transloco-scope';
import { TranslocoService } from './transloco.service';
import { listenOrNotOperator, resolveInlineLoader, shouldListenToLangChanges, } from './shared';
import { LangResolver } from './lang-resolver';
import { ScopeResolver } from './scope-resolver';
import * as i0 from "@angular/core";
export class TranslocoDirective {
    destroyRef = inject(DestroyRef);
    service = inject(TranslocoService);
    tpl = inject(TemplateRef, {
        optional: true,
    });
    providerLang = inject(TRANSLOCO_LANG, { optional: true });
    providerScope = inject(TRANSLOCO_SCOPE, { optional: true });
    providedLoadingTpl = inject(TRANSLOCO_LOADING_TEMPLATE, {
        optional: true,
    });
    cdr = inject(ChangeDetectorRef);
    host = inject(ElementRef);
    vcr = inject(ViewContainerRef);
    renderer = inject(Renderer2);
    view;
    memo = new Map();
    key;
    params = {};
    inlineScope;
    /** @deprecated use prefix instead, will be removed in Transloco v8 */
    inlineRead;
    prefix;
    inlineLang;
    inlineTpl;
    currentLang;
    loaderTplHandler;
    // Whether we already rendered the view once
    initialized = false;
    path;
    langResolver = new LangResolver();
    scopeResolver = new ScopeResolver(this.service);
    strategy = this.tpl === null ? 'attribute' : 'structural';
    static ngTemplateContextGuard(dir, ctx) {
        return true;
    }
    ngOnInit() {
        const listenToLangChange = shouldListenToLangChanges(this.service, this.providerLang || this.inlineLang);
        this.service.langChanges$
            .pipe(switchMap((activeLang) => {
            const lang = this.langResolver.resolve({
                inline: this.inlineLang,
                provider: this.providerLang,
                active: activeLang,
            });
            return Array.isArray(this.providerScope)
                ? forkJoin(this.providerScope.map((providerScope) => this.resolveScope(lang, providerScope)))
                : this.resolveScope(lang, this.providerScope);
        }), listenOrNotOperator(listenToLangChange), takeUntilDestroyed(this.destroyRef))
            .subscribe(() => {
            this.currentLang = this.langResolver.resolveLangBasedOnScope(this.path);
            this.strategy === 'attribute'
                ? this.attributeStrategy()
                : this.structuralStrategy(this.currentLang, this.prefix || this.inlineRead);
            this.cdr.markForCheck();
            this.initialized = true;
        });
        if (!this.initialized) {
            const loadingContent = this.resolveLoadingContent();
            if (loadingContent) {
                this.loaderTplHandler = new TemplateHandler(loadingContent, this.vcr);
                this.loaderTplHandler.attachView();
            }
        }
    }
    ngOnChanges(changes) {
        // We need to support dynamic keys/params, so if this is not the first change CD cycle
        // we need to run the function again in order to update the value
        if (this.strategy === 'attribute') {
            const notInit = Object.keys(changes).some((v) => !changes[v].firstChange);
            notInit && this.attributeStrategy();
        }
    }
    attributeStrategy() {
        this.detachLoader();
        this.renderer.setProperty(this.host.nativeElement, 'innerText', this.service.translate(this.key, this.params, this.currentLang));
    }
    structuralStrategy(lang, prefix) {
        this.memo.clear();
        const translateFn = this.getTranslateFn(lang, prefix);
        if (this.view) {
            // when the lang changes we need to change the reference so Angular will update the view
            this.view.context['$implicit'] = translateFn;
            this.view.context['currentLang'] = this.currentLang;
        }
        else {
            this.detachLoader();
            this.view = this.vcr.createEmbeddedView(this.tpl, {
                $implicit: translateFn,
                currentLang: this.currentLang,
            });
        }
    }
    getTranslateFn(lang, prefix) {
        return (key, params) => {
            const withPrefix = prefix ? `${prefix}.${key}` : key;
            const memoKey = params
                ? `${withPrefix}${JSON.stringify(params)}`
                : withPrefix;
            if (!this.memo.has(memoKey)) {
                this.memo.set(memoKey, this.service.translate(withPrefix, params, lang));
            }
            return this.memo.get(memoKey);
        };
    }
    resolveLoadingContent() {
        return this.inlineTpl || this.providedLoadingTpl;
    }
    ngOnDestroy() {
        this.memo.clear();
    }
    detachLoader() {
        this.loaderTplHandler?.detachView();
    }
    resolveScope(lang, providerScope) {
        const resolvedScope = this.scopeResolver.resolve({
            inline: this.inlineScope,
            provider: providerScope,
        });
        this.path = this.langResolver.resolveLangPath(lang, resolvedScope);
        const inlineLoader = resolveInlineLoader(providerScope, resolvedScope);
        return this.service._loadDependencies(this.path, inlineLoader);
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "18.2.9", ngImport: i0, type: TranslocoDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive });
    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: "14.0.0", version: "18.2.9", type: TranslocoDirective, isStandalone: true, selector: "[transloco]", inputs: { key: ["transloco", "key"], params: ["translocoParams", "params"], inlineScope: ["translocoScope", "inlineScope"], inlineRead: ["translocoRead", "inlineRead"], prefix: ["translocoPrefix", "prefix"], inlineLang: ["translocoLang", "inlineLang"], inlineTpl: ["translocoLoadingTpl", "inlineTpl"] }, usesOnChanges: true, ngImport: i0 });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "18.2.9", ngImport: i0, type: TranslocoDirective, decorators: [{
            type: Directive,
            args: [{
                    selector: '[transloco]',
                    standalone: true,
                }]
        }], propDecorators: { key: [{
                type: Input,
                args: ['transloco']
            }], params: [{
                type: Input,
                args: ['translocoParams']
            }], inlineScope: [{
                type: Input,
                args: ['translocoScope']
            }], inlineRead: [{
                type: Input,
                args: ['translocoRead']
            }], prefix: [{
                type: Input,
                args: ['translocoPrefix']
            }], inlineLang: [{
                type: Input,
                args: ['translocoLang']
            }], inlineTpl: [{
                type: Input,
                args: ['translocoLoadingTpl']
            }] } });
//# sourceMappingURL=data:application/json;base64,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