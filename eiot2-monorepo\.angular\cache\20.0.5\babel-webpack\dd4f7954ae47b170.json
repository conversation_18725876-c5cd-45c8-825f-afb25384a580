{"ast": null, "code": "/**\n * @license Angular v20.0.6\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { Observable, ReplaySubject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { assertInInjectionContext, inject, DestroyRef, RuntimeError, Injector, assertNotInReactiveContext, signal, PendingTasks } from './root_effect_scheduler.mjs';\nimport { getOutputDestroyRef, effect, untracked, computed, resource, encapsulateResourceError } from './resource.mjs';\nimport './primitives/di.mjs';\nimport './signal.mjs';\nimport '@angular/core/primitives/di';\nimport '@angular/core/primitives/signals';\nimport './untracked.mjs';\n\n/**\n * Operator which completes the Observable when the calling context (component, directive, service,\n * etc) is destroyed.\n *\n * @param destroyRef optionally, the `DestroyRef` representing the current context. This can be\n *     passed explicitly to use `takeUntilDestroyed` outside of an [injection\n * context](guide/di/dependency-injection-context). Otherwise, the current `DestroyRef` is injected.\n *\n * @publicApi 19.0\n */\nfunction takeUntilDestroyed(destroyRef) {\n  if (!destroyRef) {\n    ngDevMode && assertInInjectionContext(takeUntilDestroyed);\n    destroyRef = inject(DestroyRef);\n  }\n  const destroyed$ = new Observable(subscriber => {\n    if (destroyRef.destroyed) {\n      subscriber.next();\n      return;\n    }\n    const unregisterFn = destroyRef.onDestroy(subscriber.next.bind(subscriber));\n    return unregisterFn;\n  });\n  return source => {\n    return source.pipe(takeUntil(destroyed$));\n  };\n}\n\n/**\n * Implementation of `OutputRef` that emits values from\n * an RxJS observable source.\n *\n * @internal\n */\nclass OutputFromObservableRef {\n  source;\n  destroyed = false;\n  destroyRef = inject(DestroyRef);\n  constructor(source) {\n    this.source = source;\n    this.destroyRef.onDestroy(() => {\n      this.destroyed = true;\n    });\n  }\n  subscribe(callbackFn) {\n    if (this.destroyed) {\n      throw new RuntimeError(953 /* ɵRuntimeErrorCode.OUTPUT_REF_DESTROYED */, ngDevMode && 'Unexpected subscription to destroyed `OutputRef`. ' + 'The owning directive/component is destroyed.');\n    }\n    // Stop yielding more values when the directive/component is already destroyed.\n    const subscription = this.source.pipe(takeUntilDestroyed(this.destroyRef)).subscribe({\n      next: value => callbackFn(value)\n    });\n    return {\n      unsubscribe: () => subscription.unsubscribe()\n    };\n  }\n}\n/**\n * Declares an Angular output that is using an RxJS observable as a source\n * for events dispatched to parent subscribers.\n *\n * The behavior for an observable as source is defined as followed:\n *    1. New values are forwarded to the Angular output (next notifications).\n *    2. Errors notifications are not handled by Angular. You need to handle these manually.\n *       For example by using `catchError`.\n *    3. Completion notifications stop the output from emitting new values.\n *\n * @usageNotes\n * Initialize an output in your directive by declaring a\n * class field and initializing it with the `outputFromObservable()` function.\n *\n * ```ts\n * @Directive({..})\n * export class MyDir {\n *   nameChange$ = <some-observable>;\n *   nameChange = outputFromObservable(this.nameChange$);\n * }\n * ```\n *\n * @publicApi 19.0\n */\nfunction outputFromObservable(observable, opts) {\n  ngDevMode && assertInInjectionContext(outputFromObservable);\n  return new OutputFromObservableRef(observable);\n}\n\n/**\n * Converts an Angular output declared via `output()` or `outputFromObservable()`\n * to an observable.\n *\n * You can subscribe to the output via `Observable.subscribe` then.\n *\n * @publicApi 19.0\n */\nfunction outputToObservable(ref) {\n  const destroyRef = getOutputDestroyRef(ref);\n  return new Observable(observer => {\n    // Complete the observable upon directive/component destroy.\n    // Note: May be `undefined` if an `EventEmitter` is declared outside\n    // of an injection context.\n    const unregisterOnDestroy = destroyRef?.onDestroy(() => observer.complete());\n    const subscription = ref.subscribe(v => observer.next(v));\n    return () => {\n      subscription.unsubscribe();\n      unregisterOnDestroy?.();\n    };\n  });\n}\n\n/**\n * Exposes the value of an Angular `Signal` as an RxJS `Observable`.\n *\n * The signal's value will be propagated into the `Observable`'s subscribers using an `effect`.\n *\n * `toObservable` must be called in an injection context unless an injector is provided via options.\n *\n * @publicApi 20.0\n */\nfunction toObservable(source, options) {\n  if (ngDevMode && !options?.injector) {\n    assertInInjectionContext(toObservable);\n  }\n  const injector = options?.injector ?? inject(Injector);\n  const subject = new ReplaySubject(1);\n  const watcher = effect(() => {\n    let value;\n    try {\n      value = source();\n    } catch (err) {\n      untracked(() => subject.error(err));\n      return;\n    }\n    untracked(() => subject.next(value));\n  }, {\n    injector,\n    manualCleanup: true\n  });\n  injector.get(DestroyRef).onDestroy(() => {\n    watcher.destroy();\n    subject.complete();\n  });\n  return subject.asObservable();\n}\n\n/**\n * Get the current value of an `Observable` as a reactive `Signal`.\n *\n * `toSignal` returns a `Signal` which provides synchronous reactive access to values produced\n * by the given `Observable`, by subscribing to that `Observable`. The returned `Signal` will always\n * have the most recent value emitted by the subscription, and will throw an error if the\n * `Observable` errors.\n *\n * With `requireSync` set to `true`, `toSignal` will assert that the `Observable` produces a value\n * immediately upon subscription. No `initialValue` is needed in this case, and the returned signal\n * does not include an `undefined` type.\n *\n * By default, the subscription will be automatically cleaned up when the current [injection\n * context](guide/di/dependency-injection-context) is destroyed. For example, when `toSignal` is\n * called during the construction of a component, the subscription will be cleaned up when the\n * component is destroyed. If an injection context is not available, an explicit `Injector` can be\n * passed instead.\n *\n * If the subscription should persist until the `Observable` itself completes, the `manualCleanup`\n * option can be specified instead, which disables the automatic subscription teardown. No injection\n * context is needed in this configuration as well.\n */\nfunction toSignal(source, options) {\n  typeof ngDevMode !== 'undefined' && ngDevMode && assertNotInReactiveContext(toSignal, 'Invoking `toSignal` causes new subscriptions every time. ' + 'Consider moving `toSignal` outside of the reactive context and read the signal value where needed.');\n  const requiresCleanup = !options?.manualCleanup;\n  if (ngDevMode && requiresCleanup && !options?.injector) {\n    assertInInjectionContext(toSignal);\n  }\n  const cleanupRef = requiresCleanup ? options?.injector?.get(DestroyRef) ?? inject(DestroyRef) : null;\n  const equal = makeToSignalEqual(options?.equal);\n  // Note: T is the Observable value type, and U is the initial value type. They don't have to be\n  // the same - the returned signal gives values of type `T`.\n  let state;\n  if (options?.requireSync) {\n    // Initially the signal is in a `NoValue` state.\n    state = signal({\n      kind: 0 /* StateKind.NoValue */\n    }, {\n      equal\n    });\n  } else {\n    // If an initial value was passed, use it. Otherwise, use `undefined` as the initial value.\n    state = signal({\n      kind: 1 /* StateKind.Value */,\n      value: options?.initialValue\n    }, {\n      equal\n    });\n  }\n  let destroyUnregisterFn;\n  // Note: This code cannot run inside a reactive context (see assertion above). If we'd support\n  // this, we would subscribe to the observable outside of the current reactive context, avoiding\n  // that side-effect signal reads/writes are attribute to the current consumer. The current\n  // consumer only needs to be notified when the `state` signal changes through the observable\n  // subscription. Additional context (related to async pipe):\n  // https://github.com/angular/angular/pull/50522.\n  const sub = source.subscribe({\n    next: value => state.set({\n      kind: 1 /* StateKind.Value */,\n      value\n    }),\n    error: error => {\n      state.set({\n        kind: 2 /* StateKind.Error */,\n        error\n      });\n      destroyUnregisterFn?.();\n    },\n    complete: () => {\n      destroyUnregisterFn?.();\n    }\n    // Completion of the Observable is meaningless to the signal. Signals don't have a concept of\n    // \"complete\".\n  });\n  if (options?.requireSync && state().kind === 0 /* StateKind.NoValue */) {\n    throw new RuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, (typeof ngDevMode === 'undefined' || ngDevMode) && '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n  }\n  // Unsubscribe when the current context is destroyed, if requested.\n  destroyUnregisterFn = cleanupRef?.onDestroy(sub.unsubscribe.bind(sub));\n  // The actual returned signal is a `computed` of the `State` signal, which maps the various states\n  // to either values or errors.\n  return computed(() => {\n    const current = state();\n    switch (current.kind) {\n      case 1 /* StateKind.Value */:\n        return current.value;\n      case 2 /* StateKind.Error */:\n        throw current.error;\n      case 0 /* StateKind.NoValue */:\n        // This shouldn't really happen because the error is thrown on creation.\n        throw new RuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, (typeof ngDevMode === 'undefined' || ngDevMode) && '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n    }\n  }, {\n    equal: options?.equal\n  });\n}\nfunction makeToSignalEqual(userEquality = Object.is) {\n  return (a, b) => a.kind === 1 /* StateKind.Value */ && b.kind === 1 /* StateKind.Value */ && userEquality(a.value, b.value);\n}\n\n/**\n * Operator which makes the application unstable until the observable emits, completes, errors, or is unsubscribed.\n *\n * Use this operator in observables whose subscriptions are important for rendering and should be included in SSR serialization.\n *\n * @param injector The `Injector` to use during creation. If this is not provided, the current injection context will be used instead (via `inject`).\n *\n * @developerPreview 20.0\n */\nfunction pendingUntilEvent(injector) {\n  if (injector === undefined) {\n    ngDevMode && assertInInjectionContext(pendingUntilEvent);\n    injector = inject(Injector);\n  }\n  const taskService = injector.get(PendingTasks);\n  return sourceObservable => {\n    return new Observable(originalSubscriber => {\n      // create a new task on subscription\n      const removeTask = taskService.add();\n      let cleanedUp = false;\n      function cleanupTask() {\n        if (cleanedUp) {\n          return;\n        }\n        removeTask();\n        cleanedUp = true;\n      }\n      const innerSubscription = sourceObservable.subscribe({\n        next: v => {\n          originalSubscriber.next(v);\n          cleanupTask();\n        },\n        complete: () => {\n          originalSubscriber.complete();\n          cleanupTask();\n        },\n        error: e => {\n          originalSubscriber.error(e);\n          cleanupTask();\n        }\n      });\n      innerSubscription.add(() => {\n        originalSubscriber.unsubscribe();\n        cleanupTask();\n      });\n      return innerSubscription;\n    });\n  };\n}\nfunction rxResource(opts) {\n  if (ngDevMode && !opts?.injector) {\n    assertInInjectionContext(rxResource);\n  }\n  return resource({\n    ...opts,\n    loader: undefined,\n    stream: params => {\n      let sub;\n      // Track the abort listener so it can be removed if the Observable completes (as a memory\n      // optimization).\n      const onAbort = () => sub.unsubscribe();\n      params.abortSignal.addEventListener('abort', onAbort);\n      // Start off stream as undefined.\n      const stream = signal({\n        value: undefined\n      });\n      let resolve;\n      const promise = new Promise(r => resolve = r);\n      function send(value) {\n        stream.set(value);\n        resolve?.(stream);\n        resolve = undefined;\n      }\n      // TODO(alxhub): remove after g3 updated to rename loader -> stream\n      const streamFn = opts.stream ?? opts.loader;\n      if (streamFn === undefined) {\n        throw new RuntimeError(990 /* ɵRuntimeErrorCode.MUST_PROVIDE_STREAM_OPTION */, ngDevMode && `Must provide \\`stream\\` option.`);\n      }\n      sub = streamFn(params).subscribe({\n        next: value => send({\n          value\n        }),\n        error: error => {\n          send({\n            error: encapsulateResourceError(error)\n          });\n          params.abortSignal.removeEventListener('abort', onAbort);\n        },\n        complete: () => {\n          if (resolve) {\n            send({\n              error: new RuntimeError(991 /* ɵRuntimeErrorCode.RESOURCE_COMPLETED_BEFORE_PRODUCING_VALUE */, ngDevMode && 'Resource completed before producing a value')\n            });\n          }\n          params.abortSignal.removeEventListener('abort', onAbort);\n        }\n      });\n      return promise;\n    }\n  });\n}\nexport { outputFromObservable, outputToObservable, pendingUntilEvent, rxResource, takeUntilDestroyed, toObservable, toSignal };", "map": {"version": 3, "names": ["Observable", "ReplaySubject", "takeUntil", "assertInInjectionContext", "inject", "DestroyRef", "RuntimeError", "Injector", "assertNotInReactiveContext", "signal", "PendingTasks", "getOutputDestroyRef", "effect", "untracked", "computed", "resource", "encapsulateResourceError", "takeUntilDestroyed", "destroyRef", "ngDevMode", "destroyed$", "subscriber", "destroyed", "next", "unregisterFn", "onDestroy", "bind", "source", "pipe", "OutputFromObservableRef", "constructor", "subscribe", "callbackFn", "subscription", "value", "unsubscribe", "outputFromObservable", "observable", "opts", "outputToObservable", "ref", "observer", "unregisterOnDestroy", "complete", "v", "toObservable", "options", "injector", "subject", "watcher", "err", "error", "manualCleanup", "get", "destroy", "asObservable", "toSignal", "requiresCleanup", "cleanupRef", "equal", "makeToSignalEqual", "state", "requireSync", "kind", "initialValue", "destroyUnregisterFn", "sub", "set", "current", "userEquality", "Object", "is", "a", "b", "pendingUntilEvent", "undefined", "taskService", "sourceObservable", "originalSubscriber", "removeTask", "add", "cleanedUp", "cleanupTask", "innerSubscription", "e", "rxResource", "loader", "stream", "params", "onAbort", "abortSignal", "addEventListener", "resolve", "promise", "Promise", "r", "send", "streamFn", "removeEventListener"], "sources": ["D:/EIOT2.SERVER/eiot2-monorepo/node_modules/@angular/core/fesm2022/rxjs-interop.mjs"], "sourcesContent": ["/**\n * @license Angular v20.0.6\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { Observable, ReplaySubject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { assertInInjectionContext, inject, DestroyRef, RuntimeError, Injector, assertNotInReactiveContext, signal, PendingTasks } from './root_effect_scheduler.mjs';\nimport { getOutputDestroyRef, effect, untracked, computed, resource, encapsulateResourceError } from './resource.mjs';\nimport './primitives/di.mjs';\nimport './signal.mjs';\nimport '@angular/core/primitives/di';\nimport '@angular/core/primitives/signals';\nimport './untracked.mjs';\n\n/**\n * Operator which completes the Observable when the calling context (component, directive, service,\n * etc) is destroyed.\n *\n * @param destroyRef optionally, the `DestroyRef` representing the current context. This can be\n *     passed explicitly to use `takeUntilDestroyed` outside of an [injection\n * context](guide/di/dependency-injection-context). Otherwise, the current `DestroyRef` is injected.\n *\n * @publicApi 19.0\n */\nfunction takeUntilDestroyed(destroyRef) {\n    if (!destroyRef) {\n        ngDevMode && assertInInjectionContext(takeUntilDestroyed);\n        destroyRef = inject(DestroyRef);\n    }\n    const destroyed$ = new Observable((subscriber) => {\n        if (destroyRef.destroyed) {\n            subscriber.next();\n            return;\n        }\n        const unregisterFn = destroyRef.onDestroy(subscriber.next.bind(subscriber));\n        return unregisterFn;\n    });\n    return (source) => {\n        return source.pipe(takeUntil(destroyed$));\n    };\n}\n\n/**\n * Implementation of `OutputRef` that emits values from\n * an RxJS observable source.\n *\n * @internal\n */\nclass OutputFromObservableRef {\n    source;\n    destroyed = false;\n    destroyRef = inject(DestroyRef);\n    constructor(source) {\n        this.source = source;\n        this.destroyRef.onDestroy(() => {\n            this.destroyed = true;\n        });\n    }\n    subscribe(callbackFn) {\n        if (this.destroyed) {\n            throw new RuntimeError(953 /* ɵRuntimeErrorCode.OUTPUT_REF_DESTROYED */, ngDevMode &&\n                'Unexpected subscription to destroyed `OutputRef`. ' +\n                    'The owning directive/component is destroyed.');\n        }\n        // Stop yielding more values when the directive/component is already destroyed.\n        const subscription = this.source.pipe(takeUntilDestroyed(this.destroyRef)).subscribe({\n            next: (value) => callbackFn(value),\n        });\n        return {\n            unsubscribe: () => subscription.unsubscribe(),\n        };\n    }\n}\n/**\n * Declares an Angular output that is using an RxJS observable as a source\n * for events dispatched to parent subscribers.\n *\n * The behavior for an observable as source is defined as followed:\n *    1. New values are forwarded to the Angular output (next notifications).\n *    2. Errors notifications are not handled by Angular. You need to handle these manually.\n *       For example by using `catchError`.\n *    3. Completion notifications stop the output from emitting new values.\n *\n * @usageNotes\n * Initialize an output in your directive by declaring a\n * class field and initializing it with the `outputFromObservable()` function.\n *\n * ```ts\n * @Directive({..})\n * export class MyDir {\n *   nameChange$ = <some-observable>;\n *   nameChange = outputFromObservable(this.nameChange$);\n * }\n * ```\n *\n * @publicApi 19.0\n */\nfunction outputFromObservable(observable, opts) {\n    ngDevMode && assertInInjectionContext(outputFromObservable);\n    return new OutputFromObservableRef(observable);\n}\n\n/**\n * Converts an Angular output declared via `output()` or `outputFromObservable()`\n * to an observable.\n *\n * You can subscribe to the output via `Observable.subscribe` then.\n *\n * @publicApi 19.0\n */\nfunction outputToObservable(ref) {\n    const destroyRef = getOutputDestroyRef(ref);\n    return new Observable((observer) => {\n        // Complete the observable upon directive/component destroy.\n        // Note: May be `undefined` if an `EventEmitter` is declared outside\n        // of an injection context.\n        const unregisterOnDestroy = destroyRef?.onDestroy(() => observer.complete());\n        const subscription = ref.subscribe((v) => observer.next(v));\n        return () => {\n            subscription.unsubscribe();\n            unregisterOnDestroy?.();\n        };\n    });\n}\n\n/**\n * Exposes the value of an Angular `Signal` as an RxJS `Observable`.\n *\n * The signal's value will be propagated into the `Observable`'s subscribers using an `effect`.\n *\n * `toObservable` must be called in an injection context unless an injector is provided via options.\n *\n * @publicApi 20.0\n */\nfunction toObservable(source, options) {\n    if (ngDevMode && !options?.injector) {\n        assertInInjectionContext(toObservable);\n    }\n    const injector = options?.injector ?? inject(Injector);\n    const subject = new ReplaySubject(1);\n    const watcher = effect(() => {\n        let value;\n        try {\n            value = source();\n        }\n        catch (err) {\n            untracked(() => subject.error(err));\n            return;\n        }\n        untracked(() => subject.next(value));\n    }, { injector, manualCleanup: true });\n    injector.get(DestroyRef).onDestroy(() => {\n        watcher.destroy();\n        subject.complete();\n    });\n    return subject.asObservable();\n}\n\n/**\n * Get the current value of an `Observable` as a reactive `Signal`.\n *\n * `toSignal` returns a `Signal` which provides synchronous reactive access to values produced\n * by the given `Observable`, by subscribing to that `Observable`. The returned `Signal` will always\n * have the most recent value emitted by the subscription, and will throw an error if the\n * `Observable` errors.\n *\n * With `requireSync` set to `true`, `toSignal` will assert that the `Observable` produces a value\n * immediately upon subscription. No `initialValue` is needed in this case, and the returned signal\n * does not include an `undefined` type.\n *\n * By default, the subscription will be automatically cleaned up when the current [injection\n * context](guide/di/dependency-injection-context) is destroyed. For example, when `toSignal` is\n * called during the construction of a component, the subscription will be cleaned up when the\n * component is destroyed. If an injection context is not available, an explicit `Injector` can be\n * passed instead.\n *\n * If the subscription should persist until the `Observable` itself completes, the `manualCleanup`\n * option can be specified instead, which disables the automatic subscription teardown. No injection\n * context is needed in this configuration as well.\n */\nfunction toSignal(source, options) {\n    typeof ngDevMode !== 'undefined' &&\n        ngDevMode &&\n        assertNotInReactiveContext(toSignal, 'Invoking `toSignal` causes new subscriptions every time. ' +\n            'Consider moving `toSignal` outside of the reactive context and read the signal value where needed.');\n    const requiresCleanup = !options?.manualCleanup;\n    if (ngDevMode && requiresCleanup && !options?.injector) {\n        assertInInjectionContext(toSignal);\n    }\n    const cleanupRef = requiresCleanup\n        ? (options?.injector?.get(DestroyRef) ?? inject(DestroyRef))\n        : null;\n    const equal = makeToSignalEqual(options?.equal);\n    // Note: T is the Observable value type, and U is the initial value type. They don't have to be\n    // the same - the returned signal gives values of type `T`.\n    let state;\n    if (options?.requireSync) {\n        // Initially the signal is in a `NoValue` state.\n        state = signal({ kind: 0 /* StateKind.NoValue */ }, { equal });\n    }\n    else {\n        // If an initial value was passed, use it. Otherwise, use `undefined` as the initial value.\n        state = signal({ kind: 1 /* StateKind.Value */, value: options?.initialValue }, { equal });\n    }\n    let destroyUnregisterFn;\n    // Note: This code cannot run inside a reactive context (see assertion above). If we'd support\n    // this, we would subscribe to the observable outside of the current reactive context, avoiding\n    // that side-effect signal reads/writes are attribute to the current consumer. The current\n    // consumer only needs to be notified when the `state` signal changes through the observable\n    // subscription. Additional context (related to async pipe):\n    // https://github.com/angular/angular/pull/50522.\n    const sub = source.subscribe({\n        next: (value) => state.set({ kind: 1 /* StateKind.Value */, value }),\n        error: (error) => {\n            state.set({ kind: 2 /* StateKind.Error */, error });\n            destroyUnregisterFn?.();\n        },\n        complete: () => {\n            destroyUnregisterFn?.();\n        },\n        // Completion of the Observable is meaningless to the signal. Signals don't have a concept of\n        // \"complete\".\n    });\n    if (options?.requireSync && state().kind === 0 /* StateKind.NoValue */) {\n        throw new RuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n            '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n    }\n    // Unsubscribe when the current context is destroyed, if requested.\n    destroyUnregisterFn = cleanupRef?.onDestroy(sub.unsubscribe.bind(sub));\n    // The actual returned signal is a `computed` of the `State` signal, which maps the various states\n    // to either values or errors.\n    return computed(() => {\n        const current = state();\n        switch (current.kind) {\n            case 1 /* StateKind.Value */:\n                return current.value;\n            case 2 /* StateKind.Error */:\n                throw current.error;\n            case 0 /* StateKind.NoValue */:\n                // This shouldn't really happen because the error is thrown on creation.\n                throw new RuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                    '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');\n        }\n    }, { equal: options?.equal });\n}\nfunction makeToSignalEqual(userEquality = Object.is) {\n    return (a, b) => a.kind === 1 /* StateKind.Value */ && b.kind === 1 /* StateKind.Value */ && userEquality(a.value, b.value);\n}\n\n/**\n * Operator which makes the application unstable until the observable emits, completes, errors, or is unsubscribed.\n *\n * Use this operator in observables whose subscriptions are important for rendering and should be included in SSR serialization.\n *\n * @param injector The `Injector` to use during creation. If this is not provided, the current injection context will be used instead (via `inject`).\n *\n * @developerPreview 20.0\n */\nfunction pendingUntilEvent(injector) {\n    if (injector === undefined) {\n        ngDevMode && assertInInjectionContext(pendingUntilEvent);\n        injector = inject(Injector);\n    }\n    const taskService = injector.get(PendingTasks);\n    return (sourceObservable) => {\n        return new Observable((originalSubscriber) => {\n            // create a new task on subscription\n            const removeTask = taskService.add();\n            let cleanedUp = false;\n            function cleanupTask() {\n                if (cleanedUp) {\n                    return;\n                }\n                removeTask();\n                cleanedUp = true;\n            }\n            const innerSubscription = sourceObservable.subscribe({\n                next: (v) => {\n                    originalSubscriber.next(v);\n                    cleanupTask();\n                },\n                complete: () => {\n                    originalSubscriber.complete();\n                    cleanupTask();\n                },\n                error: (e) => {\n                    originalSubscriber.error(e);\n                    cleanupTask();\n                },\n            });\n            innerSubscription.add(() => {\n                originalSubscriber.unsubscribe();\n                cleanupTask();\n            });\n            return innerSubscription;\n        });\n    };\n}\n\nfunction rxResource(opts) {\n    if (ngDevMode && !opts?.injector) {\n        assertInInjectionContext(rxResource);\n    }\n    return resource({\n        ...opts,\n        loader: undefined,\n        stream: (params) => {\n            let sub;\n            // Track the abort listener so it can be removed if the Observable completes (as a memory\n            // optimization).\n            const onAbort = () => sub.unsubscribe();\n            params.abortSignal.addEventListener('abort', onAbort);\n            // Start off stream as undefined.\n            const stream = signal({ value: undefined });\n            let resolve;\n            const promise = new Promise((r) => (resolve = r));\n            function send(value) {\n                stream.set(value);\n                resolve?.(stream);\n                resolve = undefined;\n            }\n            // TODO(alxhub): remove after g3 updated to rename loader -> stream\n            const streamFn = opts.stream ?? opts.loader;\n            if (streamFn === undefined) {\n                throw new RuntimeError(990 /* ɵRuntimeErrorCode.MUST_PROVIDE_STREAM_OPTION */, ngDevMode && `Must provide \\`stream\\` option.`);\n            }\n            sub = streamFn(params).subscribe({\n                next: (value) => send({ value }),\n                error: (error) => {\n                    send({ error: encapsulateResourceError(error) });\n                    params.abortSignal.removeEventListener('abort', onAbort);\n                },\n                complete: () => {\n                    if (resolve) {\n                        send({\n                            error: new RuntimeError(991 /* ɵRuntimeErrorCode.RESOURCE_COMPLETED_BEFORE_PRODUCING_VALUE */, ngDevMode && 'Resource completed before producing a value'),\n                        });\n                    }\n                    params.abortSignal.removeEventListener('abort', onAbort);\n                },\n            });\n            return promise;\n        },\n    });\n}\n\nexport { outputFromObservable, outputToObservable, pendingUntilEvent, rxResource, takeUntilDestroyed, toObservable, toSignal };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,UAAU,EAAEC,aAAa,QAAQ,MAAM;AAChD,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,wBAAwB,EAAEC,MAAM,EAAEC,UAAU,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,0BAA0B,EAAEC,MAAM,EAAEC,YAAY,QAAQ,6BAA6B;AACpK,SAASC,mBAAmB,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,wBAAwB,QAAQ,gBAAgB;AACrH,OAAO,qBAAqB;AAC5B,OAAO,cAAc;AACrB,OAAO,6BAA6B;AACpC,OAAO,kCAAkC;AACzC,OAAO,iBAAiB;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACC,UAAU,EAAE;EACpC,IAAI,CAACA,UAAU,EAAE;IACbC,SAAS,IAAIhB,wBAAwB,CAACc,kBAAkB,CAAC;IACzDC,UAAU,GAAGd,MAAM,CAACC,UAAU,CAAC;EACnC;EACA,MAAMe,UAAU,GAAG,IAAIpB,UAAU,CAAEqB,UAAU,IAAK;IAC9C,IAAIH,UAAU,CAACI,SAAS,EAAE;MACtBD,UAAU,CAACE,IAAI,CAAC,CAAC;MACjB;IACJ;IACA,MAAMC,YAAY,GAAGN,UAAU,CAACO,SAAS,CAACJ,UAAU,CAACE,IAAI,CAACG,IAAI,CAACL,UAAU,CAAC,CAAC;IAC3E,OAAOG,YAAY;EACvB,CAAC,CAAC;EACF,OAAQG,MAAM,IAAK;IACf,OAAOA,MAAM,CAACC,IAAI,CAAC1B,SAAS,CAACkB,UAAU,CAAC,CAAC;EAC7C,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMS,uBAAuB,CAAC;EAC1BF,MAAM;EACNL,SAAS,GAAG,KAAK;EACjBJ,UAAU,GAAGd,MAAM,CAACC,UAAU,CAAC;EAC/ByB,WAAWA,CAACH,MAAM,EAAE;IAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACT,UAAU,CAACO,SAAS,CAAC,MAAM;MAC5B,IAAI,CAACH,SAAS,GAAG,IAAI;IACzB,CAAC,CAAC;EACN;EACAS,SAASA,CAACC,UAAU,EAAE;IAClB,IAAI,IAAI,CAACV,SAAS,EAAE;MAChB,MAAM,IAAIhB,YAAY,CAAC,GAAG,CAAC,8CAA8Ca,SAAS,IAC9E,oDAAoD,GAChD,8CAA8C,CAAC;IAC3D;IACA;IACA,MAAMc,YAAY,GAAG,IAAI,CAACN,MAAM,CAACC,IAAI,CAACX,kBAAkB,CAAC,IAAI,CAACC,UAAU,CAAC,CAAC,CAACa,SAAS,CAAC;MACjFR,IAAI,EAAGW,KAAK,IAAKF,UAAU,CAACE,KAAK;IACrC,CAAC,CAAC;IACF,OAAO;MACHC,WAAW,EAAEA,CAAA,KAAMF,YAAY,CAACE,WAAW,CAAC;IAChD,CAAC;EACL;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAACC,UAAU,EAAEC,IAAI,EAAE;EAC5CnB,SAAS,IAAIhB,wBAAwB,CAACiC,oBAAoB,CAAC;EAC3D,OAAO,IAAIP,uBAAuB,CAACQ,UAAU,CAAC;AAClD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,kBAAkBA,CAACC,GAAG,EAAE;EAC7B,MAAMtB,UAAU,GAAGP,mBAAmB,CAAC6B,GAAG,CAAC;EAC3C,OAAO,IAAIxC,UAAU,CAAEyC,QAAQ,IAAK;IAChC;IACA;IACA;IACA,MAAMC,mBAAmB,GAAGxB,UAAU,EAAEO,SAAS,CAAC,MAAMgB,QAAQ,CAACE,QAAQ,CAAC,CAAC,CAAC;IAC5E,MAAMV,YAAY,GAAGO,GAAG,CAACT,SAAS,CAAEa,CAAC,IAAKH,QAAQ,CAAClB,IAAI,CAACqB,CAAC,CAAC,CAAC;IAC3D,OAAO,MAAM;MACTX,YAAY,CAACE,WAAW,CAAC,CAAC;MAC1BO,mBAAmB,GAAG,CAAC;IAC3B,CAAC;EACL,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,YAAYA,CAAClB,MAAM,EAAEmB,OAAO,EAAE;EACnC,IAAI3B,SAAS,IAAI,CAAC2B,OAAO,EAAEC,QAAQ,EAAE;IACjC5C,wBAAwB,CAAC0C,YAAY,CAAC;EAC1C;EACA,MAAME,QAAQ,GAAGD,OAAO,EAAEC,QAAQ,IAAI3C,MAAM,CAACG,QAAQ,CAAC;EACtD,MAAMyC,OAAO,GAAG,IAAI/C,aAAa,CAAC,CAAC,CAAC;EACpC,MAAMgD,OAAO,GAAGrC,MAAM,CAAC,MAAM;IACzB,IAAIsB,KAAK;IACT,IAAI;MACAA,KAAK,GAAGP,MAAM,CAAC,CAAC;IACpB,CAAC,CACD,OAAOuB,GAAG,EAAE;MACRrC,SAAS,CAAC,MAAMmC,OAAO,CAACG,KAAK,CAACD,GAAG,CAAC,CAAC;MACnC;IACJ;IACArC,SAAS,CAAC,MAAMmC,OAAO,CAACzB,IAAI,CAACW,KAAK,CAAC,CAAC;EACxC,CAAC,EAAE;IAAEa,QAAQ;IAAEK,aAAa,EAAE;EAAK,CAAC,CAAC;EACrCL,QAAQ,CAACM,GAAG,CAAChD,UAAU,CAAC,CAACoB,SAAS,CAAC,MAAM;IACrCwB,OAAO,CAACK,OAAO,CAAC,CAAC;IACjBN,OAAO,CAACL,QAAQ,CAAC,CAAC;EACtB,CAAC,CAAC;EACF,OAAOK,OAAO,CAACO,YAAY,CAAC,CAAC;AACjC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAAC7B,MAAM,EAAEmB,OAAO,EAAE;EAC/B,OAAO3B,SAAS,KAAK,WAAW,IAC5BA,SAAS,IACTX,0BAA0B,CAACgD,QAAQ,EAAE,2DAA2D,GAC5F,oGAAoG,CAAC;EAC7G,MAAMC,eAAe,GAAG,CAACX,OAAO,EAAEM,aAAa;EAC/C,IAAIjC,SAAS,IAAIsC,eAAe,IAAI,CAACX,OAAO,EAAEC,QAAQ,EAAE;IACpD5C,wBAAwB,CAACqD,QAAQ,CAAC;EACtC;EACA,MAAME,UAAU,GAAGD,eAAe,GAC3BX,OAAO,EAAEC,QAAQ,EAAEM,GAAG,CAAChD,UAAU,CAAC,IAAID,MAAM,CAACC,UAAU,CAAC,GACzD,IAAI;EACV,MAAMsD,KAAK,GAAGC,iBAAiB,CAACd,OAAO,EAAEa,KAAK,CAAC;EAC/C;EACA;EACA,IAAIE,KAAK;EACT,IAAIf,OAAO,EAAEgB,WAAW,EAAE;IACtB;IACAD,KAAK,GAAGpD,MAAM,CAAC;MAAEsD,IAAI,EAAE,CAAC,CAAC;IAAwB,CAAC,EAAE;MAAEJ;IAAM,CAAC,CAAC;EAClE,CAAC,MACI;IACD;IACAE,KAAK,GAAGpD,MAAM,CAAC;MAAEsD,IAAI,EAAE,CAAC,CAAC;MAAuB7B,KAAK,EAAEY,OAAO,EAAEkB;IAAa,CAAC,EAAE;MAAEL;IAAM,CAAC,CAAC;EAC9F;EACA,IAAIM,mBAAmB;EACvB;EACA;EACA;EACA;EACA;EACA;EACA,MAAMC,GAAG,GAAGvC,MAAM,CAACI,SAAS,CAAC;IACzBR,IAAI,EAAGW,KAAK,IAAK2B,KAAK,CAACM,GAAG,CAAC;MAAEJ,IAAI,EAAE,CAAC,CAAC;MAAuB7B;IAAM,CAAC,CAAC;IACpEiB,KAAK,EAAGA,KAAK,IAAK;MACdU,KAAK,CAACM,GAAG,CAAC;QAAEJ,IAAI,EAAE,CAAC,CAAC;QAAuBZ;MAAM,CAAC,CAAC;MACnDc,mBAAmB,GAAG,CAAC;IAC3B,CAAC;IACDtB,QAAQ,EAAEA,CAAA,KAAM;MACZsB,mBAAmB,GAAG,CAAC;IAC3B;IACA;IACA;EACJ,CAAC,CAAC;EACF,IAAInB,OAAO,EAAEgB,WAAW,IAAID,KAAK,CAAC,CAAC,CAACE,IAAI,KAAK,CAAC,CAAC,yBAAyB;IACpE,MAAM,IAAIzD,YAAY,CAAC,GAAG,CAAC,wDAAwD,CAAC,OAAOa,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC7H,qFAAqF,CAAC;EAC9F;EACA;EACA8C,mBAAmB,GAAGP,UAAU,EAAEjC,SAAS,CAACyC,GAAG,CAAC/B,WAAW,CAACT,IAAI,CAACwC,GAAG,CAAC,CAAC;EACtE;EACA;EACA,OAAOpD,QAAQ,CAAC,MAAM;IAClB,MAAMsD,OAAO,GAAGP,KAAK,CAAC,CAAC;IACvB,QAAQO,OAAO,CAACL,IAAI;MAChB,KAAK,CAAC,CAAC;QACH,OAAOK,OAAO,CAAClC,KAAK;MACxB,KAAK,CAAC,CAAC;QACH,MAAMkC,OAAO,CAACjB,KAAK;MACvB,KAAK,CAAC,CAAC;QACH;QACA,MAAM,IAAI7C,YAAY,CAAC,GAAG,CAAC,wDAAwD,CAAC,OAAOa,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC7H,qFAAqF,CAAC;IAClG;EACJ,CAAC,EAAE;IAAEwC,KAAK,EAAEb,OAAO,EAAEa;EAAM,CAAC,CAAC;AACjC;AACA,SAASC,iBAAiBA,CAACS,YAAY,GAAGC,MAAM,CAACC,EAAE,EAAE;EACjD,OAAO,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACT,IAAI,KAAK,CAAC,CAAC,yBAAyBU,CAAC,CAACV,IAAI,KAAK,CAAC,CAAC,yBAAyBM,YAAY,CAACG,CAAC,CAACtC,KAAK,EAAEuC,CAAC,CAACvC,KAAK,CAAC;AAC/H;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwC,iBAAiBA,CAAC3B,QAAQ,EAAE;EACjC,IAAIA,QAAQ,KAAK4B,SAAS,EAAE;IACxBxD,SAAS,IAAIhB,wBAAwB,CAACuE,iBAAiB,CAAC;IACxD3B,QAAQ,GAAG3C,MAAM,CAACG,QAAQ,CAAC;EAC/B;EACA,MAAMqE,WAAW,GAAG7B,QAAQ,CAACM,GAAG,CAAC3C,YAAY,CAAC;EAC9C,OAAQmE,gBAAgB,IAAK;IACzB,OAAO,IAAI7E,UAAU,CAAE8E,kBAAkB,IAAK;MAC1C;MACA,MAAMC,UAAU,GAAGH,WAAW,CAACI,GAAG,CAAC,CAAC;MACpC,IAAIC,SAAS,GAAG,KAAK;MACrB,SAASC,WAAWA,CAAA,EAAG;QACnB,IAAID,SAAS,EAAE;UACX;QACJ;QACAF,UAAU,CAAC,CAAC;QACZE,SAAS,GAAG,IAAI;MACpB;MACA,MAAME,iBAAiB,GAAGN,gBAAgB,CAAC9C,SAAS,CAAC;QACjDR,IAAI,EAAGqB,CAAC,IAAK;UACTkC,kBAAkB,CAACvD,IAAI,CAACqB,CAAC,CAAC;UAC1BsC,WAAW,CAAC,CAAC;QACjB,CAAC;QACDvC,QAAQ,EAAEA,CAAA,KAAM;UACZmC,kBAAkB,CAACnC,QAAQ,CAAC,CAAC;UAC7BuC,WAAW,CAAC,CAAC;QACjB,CAAC;QACD/B,KAAK,EAAGiC,CAAC,IAAK;UACVN,kBAAkB,CAAC3B,KAAK,CAACiC,CAAC,CAAC;UAC3BF,WAAW,CAAC,CAAC;QACjB;MACJ,CAAC,CAAC;MACFC,iBAAiB,CAACH,GAAG,CAAC,MAAM;QACxBF,kBAAkB,CAAC3C,WAAW,CAAC,CAAC;QAChC+C,WAAW,CAAC,CAAC;MACjB,CAAC,CAAC;MACF,OAAOC,iBAAiB;IAC5B,CAAC,CAAC;EACN,CAAC;AACL;AAEA,SAASE,UAAUA,CAAC/C,IAAI,EAAE;EACtB,IAAInB,SAAS,IAAI,CAACmB,IAAI,EAAES,QAAQ,EAAE;IAC9B5C,wBAAwB,CAACkF,UAAU,CAAC;EACxC;EACA,OAAOtE,QAAQ,CAAC;IACZ,GAAGuB,IAAI;IACPgD,MAAM,EAAEX,SAAS;IACjBY,MAAM,EAAGC,MAAM,IAAK;MAChB,IAAItB,GAAG;MACP;MACA;MACA,MAAMuB,OAAO,GAAGA,CAAA,KAAMvB,GAAG,CAAC/B,WAAW,CAAC,CAAC;MACvCqD,MAAM,CAACE,WAAW,CAACC,gBAAgB,CAAC,OAAO,EAAEF,OAAO,CAAC;MACrD;MACA,MAAMF,MAAM,GAAG9E,MAAM,CAAC;QAAEyB,KAAK,EAAEyC;MAAU,CAAC,CAAC;MAC3C,IAAIiB,OAAO;MACX,MAAMC,OAAO,GAAG,IAAIC,OAAO,CAAEC,CAAC,IAAMH,OAAO,GAAGG,CAAE,CAAC;MACjD,SAASC,IAAIA,CAAC9D,KAAK,EAAE;QACjBqD,MAAM,CAACpB,GAAG,CAACjC,KAAK,CAAC;QACjB0D,OAAO,GAAGL,MAAM,CAAC;QACjBK,OAAO,GAAGjB,SAAS;MACvB;MACA;MACA,MAAMsB,QAAQ,GAAG3D,IAAI,CAACiD,MAAM,IAAIjD,IAAI,CAACgD,MAAM;MAC3C,IAAIW,QAAQ,KAAKtB,SAAS,EAAE;QACxB,MAAM,IAAIrE,YAAY,CAAC,GAAG,CAAC,oDAAoDa,SAAS,IAAI,iCAAiC,CAAC;MAClI;MACA+C,GAAG,GAAG+B,QAAQ,CAACT,MAAM,CAAC,CAACzD,SAAS,CAAC;QAC7BR,IAAI,EAAGW,KAAK,IAAK8D,IAAI,CAAC;UAAE9D;QAAM,CAAC,CAAC;QAChCiB,KAAK,EAAGA,KAAK,IAAK;UACd6C,IAAI,CAAC;YAAE7C,KAAK,EAAEnC,wBAAwB,CAACmC,KAAK;UAAE,CAAC,CAAC;UAChDqC,MAAM,CAACE,WAAW,CAACQ,mBAAmB,CAAC,OAAO,EAAET,OAAO,CAAC;QAC5D,CAAC;QACD9C,QAAQ,EAAEA,CAAA,KAAM;UACZ,IAAIiD,OAAO,EAAE;YACTI,IAAI,CAAC;cACD7C,KAAK,EAAE,IAAI7C,YAAY,CAAC,GAAG,CAAC,mEAAmEa,SAAS,IAAI,6CAA6C;YAC7J,CAAC,CAAC;UACN;UACAqE,MAAM,CAACE,WAAW,CAACQ,mBAAmB,CAAC,OAAO,EAAET,OAAO,CAAC;QAC5D;MACJ,CAAC,CAAC;MACF,OAAOI,OAAO;IAClB;EACJ,CAAC,CAAC;AACN;AAEA,SAASzD,oBAAoB,EAAEG,kBAAkB,EAAEmC,iBAAiB,EAAEW,UAAU,EAAEpE,kBAAkB,EAAE4B,YAAY,EAAEW,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}