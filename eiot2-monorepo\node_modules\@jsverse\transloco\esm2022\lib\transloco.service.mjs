import { DestroyRef, inject, Inject, Injectable, Optional, } from '@angular/core';
import { BehaviorSubject, catchError, combineLatest, EMPTY, forkJoin, from, map, of, retry, shareReplay, Subject, switchMap, tap, } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DefaultLoader, TRANSLOCO_LOADER, } from './transloco.loader';
import { TRANSLOCO_TRANSPILER, } from './transloco.transpiler';
import { flatten, isEmpty, isNil, isScopeObject, isString, size, toCamelCase, unflatten, } from './helpers';
import { TRANSLOCO_CONFIG } from './transloco.config';
import { TRANSLOCO_MISSING_HANDLER, } from './transloco-missing-handler';
import { TRANSLOCO_INTERCEPTOR, } from './transloco.interceptor';
import { TRANSLOCO_FALLBACK_STRATEGY, } from './transloco-fallback-strategy';
import { getEventPayload, getLangFromScope, getScopeFromLang, resolveInlineLoader, } from './shared';
import { getFallbacksLoaders } from './get-fallbacks-loaders';
import { resolveLoader } from './resolve-loader';
import * as i0 from "@angular/core";
let service;
export function translate(key, params = {}, lang) {
    return service.translate(key, params, lang);
}
export function translateObject(key, params = {}, lang) {
    return service.translateObject(key, params, lang);
}
export class TranslocoService {
    loader;
    parser;
    missingHandler;
    interceptor;
    fallbackStrategy;
    langChanges$;
    translations = new Map();
    cache = new Map();
    firstFallbackLang;
    defaultLang = '';
    availableLangs = [];
    isResolvedMissingOnce = false;
    lang;
    failedLangs = new Set();
    events = new Subject();
    events$ = this.events.asObservable();
    config;
    destroyRef = inject(DestroyRef);
    constructor(loader, parser, missingHandler, interceptor, userConfig, fallbackStrategy) {
        this.loader = loader;
        this.parser = parser;
        this.missingHandler = missingHandler;
        this.interceptor = interceptor;
        this.fallbackStrategy = fallbackStrategy;
        if (!this.loader) {
            this.loader = new DefaultLoader(this.translations);
        }
        service = this;
        this.config = JSON.parse(JSON.stringify(userConfig));
        this.setAvailableLangs(this.config.availableLangs || []);
        this.setFallbackLangForMissingTranslation(this.config);
        this.setDefaultLang(this.config.defaultLang);
        this.lang = new BehaviorSubject(this.getDefaultLang());
        // Don't use distinctUntilChanged as we need the ability to update
        // the value when using setTranslation or setTranslationKeys
        this.langChanges$ = this.lang.asObservable();
        /**
         * When we have a failure, we want to define the next language that succeeded as the active
         */
        this.events$.subscribe((e) => {
            if (e.type === 'translationLoadSuccess' && e.wasFailure) {
                this.setActiveLang(e.payload.langName);
            }
        });
        this.destroyRef.onDestroy(() => {
            // Complete subjects to release observers if users forget to unsubscribe manually.
            // This is important in server-side rendering.
            this.lang.complete();
            this.events.complete();
            // As a root provider, this service is destroyed with when the application is destroyed.
            // Cached values retain `this`, causing circular references that block garbage collection,
            // leading to memory leaks during server-side rendering.
            this.cache.clear();
        });
    }
    getDefaultLang() {
        return this.defaultLang;
    }
    setDefaultLang(lang) {
        this.defaultLang = lang;
    }
    getActiveLang() {
        return this.lang.getValue();
    }
    setActiveLang(lang) {
        this.parser.onLangChanged?.(lang);
        this.lang.next(lang);
        this.events.next({
            type: 'langChanged',
            payload: getEventPayload(lang),
        });
        return this;
    }
    setAvailableLangs(langs) {
        this.availableLangs = langs;
    }
    /**
     * Gets the available languages.
     *
     * @returns
     * An array of the available languages. Can be either a `string[]` or a `{ id: string; label: string }[]`
     * depending on how the available languages are set in your module.
     */
    getAvailableLangs() {
        return this.availableLangs;
    }
    load(path, options = {}) {
        const cached = this.cache.get(path);
        if (cached) {
            return cached;
        }
        let loadTranslation;
        const isScope = this._isLangScoped(path);
        let scope;
        if (isScope) {
            scope = getScopeFromLang(path);
        }
        const loadersOptions = {
            path,
            mainLoader: this.loader,
            inlineLoader: options.inlineLoader,
            data: isScope ? { scope: scope } : undefined,
        };
        if (this.useFallbackTranslation(path)) {
            // if the path is scope the fallback should be `scope/fallbackLang`;
            const fallback = isScope
                ? `${scope}/${this.firstFallbackLang}`
                : this.firstFallbackLang;
            const loaders = getFallbacksLoaders({
                ...loadersOptions,
                fallbackPath: fallback,
            });
            loadTranslation = forkJoin(loaders);
        }
        else {
            const loader = resolveLoader(loadersOptions);
            loadTranslation = from(loader);
        }
        const load$ = loadTranslation.pipe(retry(this.config.failedRetries), tap((translation) => {
            if (Array.isArray(translation)) {
                translation.forEach((t) => {
                    this.handleSuccess(t.lang, t.translation);
                    // Save the fallback in cache so we'll not create a redundant request
                    if (t.lang !== path) {
                        this.cache.set(t.lang, of({}));
                    }
                });
                return;
            }
            this.handleSuccess(path, translation);
        }), catchError((error) => {
            if (!this.config.prodMode) {
                console.error(`Error while trying to load "${path}"`, error);
            }
            return this.handleFailure(path, options);
        }), shareReplay(1), takeUntilDestroyed(this.destroyRef));
        this.cache.set(path, load$);
        return load$;
    }
    /**
     * Gets the instant translated value of a key
     *
     * @example
     *
     * translate<string>('hello')
     * translate('hello', { value: 'value' })
     * translate<string[]>(['hello', 'key'])
     * translate('hello', { }, 'en')
     * translate('scope.someKey', { }, 'en')
     */
    translate(key, params = {}, lang = this.getActiveLang()) {
        if (!key)
            return key;
        const { scope, resolveLang } = this.resolveLangAndScope(lang);
        if (Array.isArray(key)) {
            return key.map((k) => this.translate(scope ? `${scope}.${k}` : k, params, resolveLang));
        }
        key = scope ? `${scope}.${key}` : key;
        const translation = this.getTranslation(resolveLang);
        const value = translation[key];
        if (!value) {
            return this._handleMissingKey(key, value, params);
        }
        return this.parser.transpile({
            value,
            params,
            translation,
            key,
        });
    }
    /**
     * Gets the translated value of a key as observable
     *
     * @example
     *
     * selectTranslate<string>('hello').subscribe(value => ...)
     * selectTranslate<string>('hello', {}, 'es').subscribe(value => ...)
     * selectTranslate<string>('hello', {}, 'todos').subscribe(value => ...)
     * selectTranslate<string>('hello', {}, { scope: 'todos' }).subscribe(value => ...)
     *
     */
    selectTranslate(key, params, lang, _isObject = false) {
        let inlineLoader;
        const load = (lang, options) => this.load(lang, options).pipe(map(() => _isObject
            ? this.translateObject(key, params, lang)
            : this.translate(key, params, lang)));
        if (isNil(lang)) {
            return this.langChanges$.pipe(switchMap((lang) => load(lang)));
        }
        lang = Array.isArray(lang) ? lang[0] : lang;
        if (isScopeObject(lang)) {
            // it's a scope object.
            const providerScope = lang;
            lang = providerScope.scope;
            inlineLoader = resolveInlineLoader(providerScope, providerScope.scope);
        }
        lang = lang;
        if (this.isLang(lang) || this.isScopeWithLang(lang)) {
            return load(lang);
        }
        // it's a scope
        const scope = lang;
        return this.langChanges$.pipe(switchMap((lang) => load(`${scope}/${lang}`, { inlineLoader })));
    }
    /**
     * Whether the scope with lang
     *
     * @example
     *
     * todos/en => true
     * todos => false
     */
    isScopeWithLang(lang) {
        return this.isLang(getLangFromScope(lang));
    }
    translateObject(key, params = {}, lang = this.getActiveLang()) {
        if (isString(key) || Array.isArray(key)) {
            const { resolveLang, scope } = this.resolveLangAndScope(lang);
            if (Array.isArray(key)) {
                return key.map((k) => this.translateObject(scope ? `${scope}.${k}` : k, params, resolveLang));
            }
            const translation = this.getTranslation(resolveLang);
            key = scope ? `${scope}.${key}` : key;
            const value = unflatten(this.getObjectByKey(translation, key));
            /* If an empty object was returned we want to try and translate the key as a string and not an object */
            return isEmpty(value)
                ? this.translate(key, params, lang)
                : this.parser.transpile({ value, params: params, translation, key });
        }
        const translations = [];
        for (const [_key, _params] of this.getEntries(key)) {
            translations.push(this.translateObject(_key, _params, lang));
        }
        return translations;
    }
    selectTranslateObject(key, params, lang) {
        if (isString(key) || Array.isArray(key)) {
            return this.selectTranslate(key, params, lang, true);
        }
        const [[firstKey, firstParams], ...rest] = this.getEntries(key);
        /* In order to avoid subscribing multiple times to the load language event by calling selectTranslateObject for each pair,
         * we listen to when the first key has been translated (the language is loaded) and translate the rest synchronously */
        return this.selectTranslateObject(firstKey, firstParams, lang).pipe(map((value) => {
            const translations = [value];
            for (const [_key, _params] of rest) {
                translations.push(this.translateObject(_key, _params, lang));
            }
            return translations;
        }));
    }
    getTranslation(langOrScope) {
        if (langOrScope) {
            if (this.isLang(langOrScope)) {
                return this.translations.get(langOrScope) || {};
            }
            else {
                // This is a scope, build the scope value from the translation object
                const { scope, resolveLang } = this.resolveLangAndScope(langOrScope);
                const translation = this.translations.get(resolveLang) || {};
                return this.getObjectByKey(translation, scope);
            }
        }
        return this.translations;
    }
    /**
     * Gets an object of translations for a given language
     *
     * @example
     *
     * selectTranslation().subscribe() - will return the current lang translation
     * selectTranslation('es').subscribe()
     * selectTranslation('admin-page').subscribe() - will return the current lang scope translation
     * selectTranslation('admin-page/es').subscribe()
     */
    selectTranslation(lang) {
        let language$ = this.langChanges$;
        if (lang) {
            const scopeLangSpecified = getLangFromScope(lang) !== lang;
            if (this.isLang(lang) || scopeLangSpecified) {
                language$ = of(lang);
            }
            else {
                language$ = this.langChanges$.pipe(map((currentLang) => `${lang}/${currentLang}`));
            }
        }
        return language$.pipe(switchMap((language) => this.load(language).pipe(map(() => this.getTranslation(language)))));
    }
    /**
     * Sets or merge a given translation object to current lang
     *
     * @example
     *
     * setTranslation({ ... })
     * setTranslation({ ... }, 'en')
     * setTranslation({ ... }, 'es', { merge: false } )
     * setTranslation({ ... }, 'todos/en', { merge: false } )
     */
    setTranslation(translation, lang = this.getActiveLang(), options = {}) {
        const defaults = { merge: true, emitChange: true };
        const mergedOptions = { ...defaults, ...options };
        const scope = getScopeFromLang(lang);
        /**
         * If this isn't a scope we use the whole translation as is
         * otherwise we need to flat the scope and use it
         */
        let flattenScopeOrTranslation = translation;
        // Merged the scoped language into the active language
        if (scope) {
            const key = this.getMappedScope(scope);
            flattenScopeOrTranslation = flatten({ [key]: translation });
        }
        const currentLang = scope ? getLangFromScope(lang) : lang;
        const mergedTranslation = {
            ...(mergedOptions.merge && this.getTranslation(currentLang)),
            ...flattenScopeOrTranslation,
        };
        const flattenTranslation = this.config.flatten.aot
            ? mergedTranslation
            : flatten(mergedTranslation);
        const withHook = this.interceptor.preSaveTranslation(flattenTranslation, currentLang);
        this.translations.set(currentLang, withHook);
        mergedOptions.emitChange && this.setActiveLang(this.getActiveLang());
    }
    /**
     * Sets translation key with given value
     *
     * @example
     *
     * setTranslationKey('key', 'value')
     * setTranslationKey('key.nested', 'value')
     * setTranslationKey('key.nested', 'value', 'en')
     * setTranslationKey('key.nested', 'value', 'en', { emitChange: false } )
     */
    setTranslationKey(key, value, options = {}) {
        const lang = options.lang || this.getActiveLang();
        const withHook = this.interceptor.preSaveTranslationKey(key, value, lang);
        const newValue = {
            [key]: withHook,
        };
        this.setTranslation(newValue, lang, { ...options, merge: true });
    }
    /**
     * Sets the fallback lang for the currently active language
     * @param fallbackLang
     */
    setFallbackLangForMissingTranslation({ fallbackLang, }) {
        const lang = Array.isArray(fallbackLang) ? fallbackLang[0] : fallbackLang;
        if (fallbackLang && this.useFallbackTranslation(lang)) {
            this.firstFallbackLang = lang;
        }
    }
    /**
     * @internal
     */
    _handleMissingKey(key, value, params) {
        if (this.config.missingHandler.allowEmpty && value === '') {
            return '';
        }
        if (!this.isResolvedMissingOnce && this.useFallbackTranslation()) {
            // We need to set it to true to prevent a loop
            this.isResolvedMissingOnce = true;
            const fallbackValue = this.translate(key, params, this.firstFallbackLang);
            this.isResolvedMissingOnce = false;
            return fallbackValue;
        }
        return this.missingHandler.handle(key, this.getMissingHandlerData(), params);
    }
    /**
     * @internal
     */
    _isLangScoped(lang) {
        return this.getAvailableLangsIds().indexOf(lang) === -1;
    }
    /**
     * Checks if a given string is one of the specified available languages.
     * @returns
     * True if the given string is an available language.
     * False if the given string is not an available language.
     */
    isLang(lang) {
        return this.getAvailableLangsIds().indexOf(lang) !== -1;
    }
    /**
     * @internal
     *
     * We always want to make sure the global lang is loaded
     * before loading the scope since you can access both via the pipe/directive.
     */
    _loadDependencies(path, inlineLoader) {
        const mainLang = getLangFromScope(path);
        if (this._isLangScoped(path) && !this.isLoadedTranslation(mainLang)) {
            return combineLatest([
                this.load(mainLang),
                this.load(path, { inlineLoader }),
            ]);
        }
        return this.load(path, { inlineLoader });
    }
    /**
     * @internal
     */
    _completeScopeWithLang(langOrScope) {
        if (this._isLangScoped(langOrScope) &&
            !this.isLang(getLangFromScope(langOrScope))) {
            return `${langOrScope}/${this.getActiveLang()}`;
        }
        return langOrScope;
    }
    /**
     * @internal
     */
    _setScopeAlias(scope, alias) {
        if (!this.config.scopeMapping) {
            this.config.scopeMapping = {};
        }
        this.config.scopeMapping[scope] = alias;
    }
    isLoadedTranslation(lang) {
        return size(this.getTranslation(lang));
    }
    getAvailableLangsIds() {
        const first = this.getAvailableLangs()[0];
        if (isString(first)) {
            return this.getAvailableLangs();
        }
        return this.getAvailableLangs().map((l) => l.id);
    }
    getMissingHandlerData() {
        return {
            ...this.config,
            activeLang: this.getActiveLang(),
            availableLangs: this.availableLangs,
            defaultLang: this.defaultLang,
        };
    }
    /**
     * Use a fallback translation set for missing keys of the primary language
     * This is unrelated to the fallback language (which changes the active language)
     */
    useFallbackTranslation(lang) {
        return (this.config.missingHandler.useFallbackTranslation &&
            lang !== this.firstFallbackLang);
    }
    handleSuccess(lang, translation) {
        this.setTranslation(translation, lang, { emitChange: false });
        this.events.next({
            wasFailure: !!this.failedLangs.size,
            type: 'translationLoadSuccess',
            payload: getEventPayload(lang),
        });
        this.failedLangs.forEach((l) => this.cache.delete(l));
        this.failedLangs.clear();
    }
    handleFailure(lang, loadOptions) {
        // When starting to load a first choice language, initialize
        // the failed counter and resolve the fallback langs.
        if (isNil(loadOptions.failedCounter)) {
            loadOptions.failedCounter = 0;
            if (!loadOptions.fallbackLangs) {
                loadOptions.fallbackLangs = this.fallbackStrategy.getNextLangs(lang);
            }
        }
        const splitted = lang.split('/');
        const fallbacks = loadOptions.fallbackLangs;
        const nextLang = fallbacks[loadOptions.failedCounter];
        this.failedLangs.add(lang);
        // This handles the case where a loaded fallback language is requested again
        if (this.cache.has(nextLang)) {
            this.handleSuccess(nextLang, this.getTranslation(nextLang));
            return EMPTY;
        }
        const isFallbackLang = nextLang === splitted[splitted.length - 1];
        if (!nextLang || isFallbackLang) {
            let msg = `Unable to load translation and all the fallback languages`;
            if (splitted.length > 1) {
                msg += `, did you misspelled the scope name?`;
            }
            throw new Error(msg);
        }
        let resolveLang = nextLang;
        // if it's scoped lang
        if (splitted.length > 1) {
            // We need to resolve it to:
            // todos/langNotExists => todos/nextLang
            splitted[splitted.length - 1] = nextLang;
            resolveLang = splitted.join('/');
        }
        loadOptions.failedCounter++;
        this.events.next({
            type: 'translationLoadFailure',
            payload: getEventPayload(lang),
        });
        return this.load(resolveLang, loadOptions);
    }
    getMappedScope(scope) {
        const { scopeMapping = {}, scopes = { keepCasing: false } } = this.config;
        return (scopeMapping[scope] || (scopes.keepCasing ? scope : toCamelCase(scope)));
    }
    /**
     * If lang is scope we need to check the following cases:
     * todos/es => in this case we should take `es` as lang
     * todos => in this case we should set the active lang as lang
     */
    resolveLangAndScope(lang) {
        let resolveLang = lang;
        let scope;
        if (this._isLangScoped(lang)) {
            // en for example
            const langFromScope = getLangFromScope(lang);
            // en is lang
            const hasLang = this.isLang(langFromScope);
            // take en
            resolveLang = hasLang ? langFromScope : this.getActiveLang();
            // find the scope
            scope = this.getMappedScope(hasLang ? getScopeFromLang(lang) : lang);
        }
        return { scope, resolveLang };
    }
    getObjectByKey(translation, key) {
        const result = {};
        const prefix = `${key}.`;
        for (const currentKey in translation) {
            if (currentKey.startsWith(prefix)) {
                result[currentKey.replace(prefix, '')] = translation[currentKey];
            }
        }
        return result;
    }
    getEntries(key) {
        return key instanceof Map ? key.entries() : Object.entries(key);
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "18.2.9", ngImport: i0, type: TranslocoService, deps: [{ token: TRANSLOCO_LOADER, optional: true }, { token: TRANSLOCO_TRANSPILER }, { token: TRANSLOCO_MISSING_HANDLER }, { token: TRANSLOCO_INTERCEPTOR }, { token: TRANSLOCO_CONFIG }, { token: TRANSLOCO_FALLBACK_STRATEGY }], target: i0.ɵɵFactoryTarget.Injectable });
    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: "12.0.0", version: "18.2.9", ngImport: i0, type: TranslocoService, providedIn: 'root' });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "18.2.9", ngImport: i0, type: TranslocoService, decorators: [{
            type: Injectable,
            args: [{ providedIn: 'root' }]
        }], ctorParameters: () => [{ type: undefined, decorators: [{
                    type: Optional
                }, {
                    type: Inject,
                    args: [TRANSLOCO_LOADER]
                }] }, { type: undefined, decorators: [{
                    type: Inject,
                    args: [TRANSLOCO_TRANSPILER]
                }] }, { type: undefined, decorators: [{
                    type: Inject,
                    args: [TRANSLOCO_MISSING_HANDLER]
                }] }, { type: undefined, decorators: [{
                    type: Inject,
                    args: [TRANSLOCO_INTERCEPTOR]
                }] }, { type: undefined, decorators: [{
                    type: Inject,
                    args: [TRANSLOCO_CONFIG]
                }] }, { type: undefined, decorators: [{
                    type: Inject,
                    args: [TRANSLOCO_FALLBACK_STRATEGY]
                }] }] });
//# sourceMappingURL=data:application/json;base64,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