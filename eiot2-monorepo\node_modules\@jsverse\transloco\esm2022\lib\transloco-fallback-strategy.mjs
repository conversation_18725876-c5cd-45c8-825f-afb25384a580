import { Inject, Injectable, InjectionToken } from '@angular/core';
import { TRANSLOCO_CONFIG } from './transloco.config';
import * as i0 from "@angular/core";
export const TRANSLOCO_FALLBACK_STRATEGY = new InjectionToken(ngDevMode ? 'TRANSLOCO_FALLBACK_STRATEGY' : '');
export class DefaultFallbackStrategy {
    userConfig;
    constructor(userConfig) {
        this.userConfig = userConfig;
    }
    getNextLangs() {
        const fallbackLang = this.userConfig.fallbackLang;
        if (!fallbackLang) {
            throw new Error('When using the default fallback, a fallback language must be provided in the config!');
        }
        return Array.isArray(fallbackLang) ? fallbackLang : [fallbackLang];
    }
    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: "12.0.0", version: "18.2.9", ngImport: i0, type: DefaultFallbackStrategy, deps: [{ token: TRANSLOCO_CONFIG }], target: i0.ɵɵFactoryTarget.Injectable });
    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: "12.0.0", version: "18.2.9", ngImport: i0, type: DefaultFallbackStrategy });
}
i0.ɵɵngDeclareClassMetadata({ minVersion: "12.0.0", version: "18.2.9", ngImport: i0, type: DefaultFallbackStrategy, decorators: [{
            type: Injectable
        }], ctorParameters: () => [{ type: undefined, decorators: [{
                    type: Inject,
                    args: [TRANSLOCO_CONFIG]
                }] }] });
//# sourceMappingURL=data:application/json;base64,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