/**
 * chroma.js - JavaScript library for color conversions
 *
 * Copyright (c) 2011-2024, <PERSON>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * 1. Redistributions of source code must retain the above copyright notice, this
 * list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright notice,
 * this list of conditions and the following disclaimer in the documentation
 * and/or other materials provided with the distribution.
 *
 * 3. The name <PERSON> may not be used to endorse or promote products
 * derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL GREGOR AISCH OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, <PERSON>ECIAL, EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIA<PERSON> DAMAGES (INCLUDING,
 * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
 * OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
 * NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 * -------------------------------------------------------
 *
 * chroma.js includes colors from colorbrewer2.org, which are released under
 * the following license:
 *
 * Copyright (c) 2002 Cynthia Brewer, Mark Harrower,
 * and The Pennsylvania State University.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
 * either express or implied. See the License for the specific
 * language governing permissions and limitations under the License.
 *
 * ------------------------------------------------------
 *
 * Named colors are taken from X11 Color Names.
 * http://www.w3.org/TR/css3-color/#svg-color
 *
 * @preserve
 */

!function(t,r){"object"==typeof exports&&"undefined"!=typeof module?module.exports=r():"function"==typeof define&&define.amd?define(r):(t="undefined"!=typeof globalThis?globalThis:t||self).chroma=r()}(this,(function(){"use strict";var t=Math.min,r=Math.max;function n(n,e,o){return void 0===o&&(o=1),t(r(e,n),o)}for(var e={},o=0,a=["Boolean","Number","String","Function","Array","Date","RegExp","Undefined","Null"];o<a.length;o+=1){var i=a[o];e["[object "+i+"]"]=i.toLowerCase()}function u(t){return e[Object.prototype.toString.call(t)]||"object"}function l(t,r){return void 0===r&&(r=null),t.length>=3?Array.prototype.slice.call(t):"object"==u(t[0])&&r?r.split("").filter((function(r){return void 0!==t[0][r]})).map((function(r){return t[0][r]})):t[0].slice(0)}function c(t){if(t.length<2)return null;var r=t.length-1;return"string"==u(t[r])?t[r].toLowerCase():null}var h=Math.PI,s=Math.min,f=Math.max,g=function(t){return Math.round(100*t)/100},p=function(t){return Math.round(100*t)/100},m=h/180,v=180/h,b={format:{},autodetect:[]},d=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];var e=this;if("object"===u(t[0])&&t[0].constructor&&t[0].constructor===this.constructor)return t[0];var o=c(t),a=!1;if(!o){a=!0,b.sorted||(b.autodetect=b.autodetect.sort((function(t,r){return r.p-t.p})),b.sorted=!0);for(var i=0,l=b.autodetect;i<l.length;i+=1){var h=l[i];if(o=h.test.apply(h,t))break}}if(!b.format[o])throw new Error("unknown format: "+t);var s=b.format[o].apply(null,a?t:t.slice(0,-1));e._rgb=function(t){t._clipped=!1,t._unclipped=t.slice(0);for(var r=0;r<=3;r++)r<3?((t[r]<0||t[r]>255)&&(t._clipped=!0),t[r]=n(t[r],0,255)):3===r&&(t[r]=n(t[r],0,1));return t}(s),3===e._rgb.length&&e._rgb.push(1)};d.prototype.toString=function(){return"function"==u(this.hex)?this.hex():"["+this._rgb.join(",")+"]"};var y=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];return new(Function.prototype.bind.apply(d,[null].concat(t)))};y.version="3.1.2";var w=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];var n,e,o=(t=l(t,"rgba"))[0],a=t[1],i=t[2],u=s(o/=255,a/=255,i/=255),c=f(o,a,i),h=(c+u)/2;return c===u?(n=0,e=Number.NaN):n=h<.5?(c-u)/(c+u):(c-u)/(2-c-u),o==c?e=(a-i)/(c-u):a==c?e=2+(i-o)/(c-u):i==c&&(e=4+(o-a)/(c-u)),(e*=60)<0&&(e+=360),t.length>3&&void 0!==t[3]?[e,n,h,t[3]]:[e,n,h]},M={Kn:18,labWhitePoint:"d65",Xn:.95047,Yn:1,Zn:1.08883,t0:.137931034,t1:.206896552,t2:.12841855,t3:.008856452,kE:216/24389,kKE:8,kK:24389/27,RefWhiteRGB:{X:.95047,Y:1,Z:1.08883},MtxRGB2XYZ:{m00:.4124564390896922,m01:.21267285140562253,m02:.0193338955823293,m10:.357576077643909,m11:.715152155287818,m12:.11919202588130297,m20:.18043748326639894,m21:.07217499330655958,m22:.9503040785363679},MtxXYZ2RGB:{m00:3.2404541621141045,m01:-.9692660305051868,m02:.055643430959114726,m10:-1.5371385127977166,m11:1.8760108454466942,m12:-.2040259135167538,m20:-.498531409556016,m21:.041556017530349834,m22:1.0572251882231791},As:.9414285350000001,Bs:1.040417467,Cs:1.089532651,MtxAdaptMa:{m00:.8951,m01:-.7502,m02:.0389,m10:.2664,m11:1.7135,m12:-.0685,m20:-.1614,m21:.0367,m22:1.0296},MtxAdaptMaI:{m00:.9869929054667123,m01:.43230526972339456,m02:-.008528664575177328,m10:-.14705425642099013,m11:.5183602715367776,m12:.04004282165408487,m20:.15996265166373125,m21:.0492912282128556,m22:.9684866957875502}},k=new Map([["a",[1.0985,.35585]],["b",[1.0985,.35585]],["c",[.98074,1.18232]],["d50",[.96422,.82521]],["d55",[.95682,.92149]],["d65",[.95047,1.08883]],["e",[1,1,1]],["f2",[.99186,.67393]],["f7",[.95041,1.08747]],["f11",[1.00962,.6435]],["icc",[.96422,.82521]]]);function x(t){var r=k.get(String(t).toLowerCase());if(!r)throw new Error("unknown Lab illuminant "+t);M.labWhitePoint=t,M.Xn=r[0],M.Zn=r[1]}function j(){return M.labWhitePoint}var _=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];var n=l(t,"rgb"),e=n[0],o=n[1],a=n[2],i=n.slice(3),u=A(e,o,a),c=function(t,r,n){var e=M.Xn,o=M.Yn,a=M.Zn,i=M.kE,u=M.kK,l=t/e,c=r/o,h=n/a,s=l>i?Math.pow(l,1/3):(u*l+16)/116,f=c>i?Math.pow(c,1/3):(u*c+16)/116,g=h>i?Math.pow(h,1/3):(u*h+16)/116;return[116*f-16,500*(s-f),200*(f-g)]}(u[0],u[1],u[2]);return[c[0],c[1],c[2]].concat(i.length>0&&i[0]<1?[i[0]]:[])};function E(t){var r=Math.sign(t);return((t=Math.abs(t))<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4))*r}var A=function(t,r,n){t=E(t/255),r=E(r/255),n=E(n/255);var e=M.MtxRGB2XYZ,o=M.MtxAdaptMa,a=M.MtxAdaptMaI,i=M.Xn,u=M.Yn,l=M.Zn,c=M.As,h=M.Bs,s=M.Cs,f=t*e.m00+r*e.m10+n*e.m20,g=t*e.m01+r*e.m11+n*e.m21,p=t*e.m02+r*e.m12+n*e.m22,m=i*o.m00+u*o.m10+l*o.m20,v=i*o.m01+u*o.m11+l*o.m21,b=i*o.m02+u*o.m12+l*o.m22,d=f*o.m00+g*o.m10+p*o.m20,y=f*o.m01+g*o.m11+p*o.m21,w=f*o.m02+g*o.m12+p*o.m22;return y*=v/h,w*=b/s,[f=(d*=m/c)*a.m00+y*a.m10+w*a.m20,g=d*a.m01+y*a.m11+w*a.m21,p=d*a.m02+y*a.m12+w*a.m22]},R=Math.sqrt,F=Math.atan2,N=Math.round,X=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];var n=l(t,"lab"),e=n[0],o=n[1],a=n[2],i=R(o*o+a*a),u=(F(a,o)*v+360)%360;return 0===N(1e4*i)&&(u=Number.NaN),[e,i,u]};function Z(t,r){var n=t.length;Array.isArray(t[0])||(t=[t]),Array.isArray(r[0])||(r=r.map((function(t){return[t]})));var e=r[0].length,o=r[0].map((function(t,n){return r.map((function(t){return t[n]}))})),a=t.map((function(t){return o.map((function(r){return Array.isArray(t)?t.reduce((function(t,n,e){return t+n*(r[e]||0)}),0):r.reduce((function(r,n){return r+n*t}),0)}))}));return 1===n&&(a=a[0]),1===e?a.map((function(t){return t[0]})):a}var Y=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];var n,e,o=l(t,"rgb"),a=o[0],i=o[1],u=o[2],c=o.slice(3),h=A(a,i,u);return(n=[[.210454268309314,.7936177747023054,-.0040720430116193],[1.9779985324311684,-2.42859224204858,.450593709617411],[.0259040424655478,.7827717124575296,-.8086757549230774]],e=Z([[.819022437996703,.3619062600528904,-.1288737815209879],[.0329836539323885,.9292868615863434,.0361446663506424],[.0481771893596242,.2642395317527308,.6335478284694309]],h),Z(n,e.map((function(t){return Math.cbrt(t)})))).concat(c.length>0&&c[0]<1?[c[0]]:[])};var $=Math.round,B=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];var n=l(t,"rgba"),e=c(t)||"rgb";if("hsl"===e.substr(0,3))return function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];var n=l(t,"hsla"),e=c(t)||"lsa";return n[0]=g(n[0]||0)+"deg",n[1]=g(100*n[1])+"%",n[2]=g(100*n[2])+"%","hsla"===e||n.length>3&&n[3]<1?(n[3]="/ "+(n.length>3?n[3]:1),e="hsla"):n.length=3,e.substr(0,3)+"("+n.join(" ")+")"}(w(n),e);if("lab"===e.substr(0,3)){var o=j();x("d50");var a=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];var n=l(t,"lab"),e=c(t)||"lab";return n[0]=g(n[0])+"%",n[1]=g(n[1]),n[2]=g(n[2]),"laba"===e||n.length>3&&n[3]<1?n[3]="/ "+(n.length>3?n[3]:1):n.length=3,"lab("+n.join(" ")+")"}(_(n),e);return x(o),a}if("lch"===e.substr(0,3)){var i=j();x("d50");var u=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];var n=l(t,"lch"),e=c(t)||"lab";return n[0]=g(n[0])+"%",n[1]=g(n[1]),n[2]=isNaN(n[2])?"none":g(n[2])+"deg","lcha"===e||n.length>3&&n[3]<1?n[3]="/ "+(n.length>3?n[3]:1):n.length=3,"lch("+n.join(" ")+")"}(function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];var n=l(t,"rgb"),e=n[0],o=n[1],a=n[2],i=n.slice(3),u=_(e,o,a),c=u[0],h=u[1],s=u[2],f=X(c,h,s);return[f[0],f[1],f[2]].concat(i.length>0&&i[0]<1?[i[0]]:[])}(n),e);return x(i),u}return"oklab"===e.substr(0,5)?function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];var n=l(t,"lab");return n[0]=g(100*n[0])+"%",n[1]=p(n[1]),n[2]=p(n[2]),n.length>3&&n[3]<1?n[3]="/ "+(n.length>3?n[3]:1):n.length=3,"oklab("+n.join(" ")+")"}(Y(n)):"oklch"===e.substr(0,5)?function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];var n=l(t,"lch");return n[0]=g(100*n[0])+"%",n[1]=p(n[1]),n[2]=isNaN(n[2])?"none":g(n[2])+"deg",n.length>3&&n[3]<1?n[3]="/ "+(n.length>3?n[3]:1):n.length=3,"oklch("+n.join(" ")+")"}(function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];var n=l(t,"rgb"),e=n[0],o=n[1],a=n[2],i=n.slice(3),u=Y(e,o,a),c=u[0],h=u[1],s=u[2],f=X(c,h,s);return[f[0],f[1],f[2]].concat(i.length>0&&i[0]<1?[i[0]]:[])}(n)):(n[0]=$(n[0]),n[1]=$(n[1]),n[2]=$(n[2]),("rgba"===e||n.length>3&&n[3]<1)&&(n[3]="/ "+(n.length>3?n[3]:1),e="rgba"),e.substr(0,3)+"("+n.slice(0,"rgb"===e?3:4).join(" ")+")")},C=function(){for(var t,r=[],n=arguments.length;n--;)r[n]=arguments[n];var e,o,a,i=(r=l(r,"hsl"))[0],u=r[1],c=r[2];if(0===u)e=o=a=255*c;else{var h=[0,0,0],s=[0,0,0],f=c<.5?c*(1+u):c+u-c*u,g=2*c-f,p=i/360;h[0]=p+1/3,h[1]=p,h[2]=p-1/3;for(var m=0;m<3;m++)h[m]<0&&(h[m]+=1),h[m]>1&&(h[m]-=1),6*h[m]<1?s[m]=g+6*(f-g)*h[m]:2*h[m]<1?s[m]=f:3*h[m]<2?s[m]=g+(f-g)*(2/3-h[m])*6:s[m]=g;e=(t=[255*s[0],255*s[1],255*s[2]])[0],o=t[1],a=t[2]}return r.length>3?[e,o,a,r[3]]:[e,o,a,1]},O=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];var n=(t=l(t,"lab"))[0],e=t[1],o=t[2],a=L(n,e,o),i=a[0],u=a[1],c=a[2],h=K(i,u,c);return[h[0],h[1],h[2],t.length>3?t[3]:1]},L=function(t,r,n){var e=M.kE,o=M.kK,a=M.kKE,i=M.Xn,u=M.Yn,l=M.Zn,c=(t+16)/116,h=.002*r+c,s=c-.005*n,f=h*h*h,g=s*s*s;return[(f>e?f:(116*h-16)/o)*i,(t>a?Math.pow((t+16)/116,3):t/o)*u,(g>e?g:(116*s-16)/o)*l]},W=function(t){var r=Math.sign(t);return((t=Math.abs(t))<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055)*r},K=function(t,r,n){var e=M.MtxAdaptMa,o=M.MtxAdaptMaI,a=M.MtxXYZ2RGB,i=M.RefWhiteRGB,u=M.Xn,l=M.Yn,c=M.Zn,h=u*e.m00+l*e.m10+c*e.m20,s=u*e.m01+l*e.m11+c*e.m21,f=u*e.m02+l*e.m12+c*e.m22,g=i.X*e.m00+i.Y*e.m10+i.Z*e.m20,p=i.X*e.m01+i.Y*e.m11+i.Z*e.m21,m=i.X*e.m02+i.Y*e.m12+i.Z*e.m22,v=(t*e.m00+r*e.m10+n*e.m20)*(g/h),b=(t*e.m01+r*e.m11+n*e.m21)*(p/s),d=(t*e.m02+r*e.m12+n*e.m22)*(m/f),y=v*o.m00+b*o.m10+d*o.m20,w=v*o.m01+b*o.m11+d*o.m21,k=v*o.m02+b*o.m12+d*o.m22;return[255*W(y*a.m00+w*a.m10+k*a.m20),255*W(y*a.m01+w*a.m11+k*a.m21),255*W(y*a.m02+w*a.m12+k*a.m22)]},G=Math.sin,I=Math.cos,P=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];var n=l(t,"lch"),e=n[0],o=n[1],a=n[2];return isNaN(a)&&(a=0),[e,I(a*=m)*o,G(a)*o]},S=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];var n,e,o=(t=l(t,"lab"))[0],a=t[1],i=t[2],u=t.slice(3),c=(n=[[1.2268798758459243,-.5578149944602171,.2813910456659647],[-.0405757452148008,1.112286803280317,-.0717110580655164],[-.0763729366746601,-.4214933324022432,1.5869240198367816]],e=Z([[1,.3963377773761749,.2158037573099136],[1,-.1055613458156586,-.0638541728258133],[1,-.0894841775298119,-1.2914855480194092]],[o,a,i]),Z(n,e.map((function(t){return Math.pow(t,3)})))),h=c[0],s=c[1],f=c[2],g=K(h,s,f);return[g[0],g[1],g[2]].concat(u.length>0&&u[0]<1?[u[0]]:[])};var q=/((?:-?\d+)|(?:-?\d+(?:\.\d+)?)%|none)/.source,T=/((?:-?(?:\d+(?:\.\d*)?|\.\d+)%?)|none)/.source,D=/((?:-?(?:\d+(?:\.\d*)?|\.\d+)%)|none)/.source,U=/\s*/.source,z=/\s+/.source,H=/\s*,\s*/.source,J=/((?:-?(?:\d+(?:\.\d*)?|\.\d+)(?:deg)?)|none)/.source,Q=/\s*(?:\/\s*((?:[01]|[01]?\.\d+)|\d+(?:\.\d+)?%))?/.source,V=new RegExp("^rgba?\\("+U+[q,q,q].join(z)+Q+"\\)$"),tt=new RegExp("^rgb\\("+U+[q,q,q].join(H)+U+"\\)$"),rt=new RegExp("^rgba\\("+U+[q,q,q,T].join(H)+U+"\\)$"),nt=new RegExp("^hsla?\\("+U+[J,D,D].join(z)+Q+"\\)$"),et=new RegExp("^hsl?\\("+U+[J,D,D].join(H)+U+"\\)$"),ot=/^hsla\(\s*(-?\d+(?:\.\d+)?),\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*,\s*([01]|[01]?\.\d+)\)$/,at=new RegExp("^lab\\("+U+[T,T,T].join(z)+Q+"\\)$"),it=new RegExp("^lch\\("+U+[T,T,J].join(z)+Q+"\\)$"),ut=new RegExp("^oklab\\("+U+[T,T,T].join(z)+Q+"\\)$"),lt=new RegExp("^oklch\\("+U+[T,T,J].join(z)+Q+"\\)$"),ct=Math.round,ht=function(t){return t.map((function(t,r){return r<=2?n(ct(t),0,255):t}))},st=function(t,r,n,e){return void 0===r&&(r=0),void 0===n&&(n=100),void 0===e&&(e=!1),"string"==typeof t&&t.endsWith("%")&&(t=parseFloat(t.substring(0,t.length-1))/100,t=e?r+.5*(t+1)*(n-r):r+t*(n-r)),+t},ft=function(t,r){return"none"===t?r:t},gt=function(t){if("transparent"===(t=t.toLowerCase().trim()))return[0,0,0,0];var r;if(b.format.named)try{return b.format.named(t)}catch(t){}if((r=t.match(V))||(r=t.match(tt))){for(var n=r.slice(1,4),e=0;e<3;e++)n[e]=+st(ft(n[e],0),0,255);n=ht(n);var o=void 0!==r[4]?+st(r[4],0,1):1;return n[3]=o,n}if(r=t.match(rt)){for(var a=r.slice(1,5),i=0;i<4;i++)a[i]=+st(a[i],0,255);return a}if((r=t.match(nt))||(r=t.match(et))){var u=r.slice(1,4);u[0]=+ft(u[0].replace("deg",""),0),u[1]=.01*+st(ft(u[1],0),0,100),u[2]=.01*+st(ft(u[2],0),0,100);var c=ht(C(u)),h=void 0!==r[4]?+st(r[4],0,1):1;return c[3]=h,c}if(r=t.match(ot)){var s=r.slice(1,4);s[1]*=.01,s[2]*=.01;for(var f=C(s),g=0;g<3;g++)f[g]=ct(f[g]);return f[3]=+r[4],f}if(r=t.match(at)){var p=r.slice(1,4);p[0]=st(ft(p[0],0),0,100),p[1]=st(ft(p[1],0),-125,125,!0),p[2]=st(ft(p[2],0),-125,125,!0);var m=j();x("d50");var v=ht(O(p));x(m);var d=void 0!==r[4]?+st(r[4],0,1):1;return v[3]=d,v}if(r=t.match(it)){var y=r.slice(1,4);y[0]=st(y[0],0,100),y[1]=st(ft(y[1],0),0,150,!1),y[2]=+ft(y[2].replace("deg",""),0);var w=j();x("d50");var M=ht(function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];var n=(t=l(t,"lch"))[0],e=t[1],o=t[2],a=P(n,e,o),i=a[0],u=a[1],c=a[2],h=O(i,u,c);return[h[0],h[1],h[2],t.length>3?t[3]:1]}(y));x(w);var k=void 0!==r[4]?+st(r[4],0,1):1;return M[3]=k,M}if(r=t.match(ut)){var _=r.slice(1,4);_[0]=st(ft(_[0],0),0,1),_[1]=st(ft(_[1],0),-.4,.4,!0),_[2]=st(ft(_[2],0),-.4,.4,!0);var E=ht(S(_)),A=void 0!==r[4]?+st(r[4],0,1):1;return E[3]=A,E}if(r=t.match(lt)){var R=r.slice(1,4);R[0]=st(ft(R[0],0),0,1),R[1]=st(ft(R[1],0),0,.4,!1),R[2]=+ft(R[2].replace("deg",""),0);var F=ht(function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];var n=(t=l(t,"lch"))[0],e=t[1],o=t[2],a=t.slice(3),i=P(n,e,o),u=i[0],c=i[1],h=i[2],s=S(u,c,h);return[s[0],s[1],s[2]].concat(a.length>0&&a[0]<1?[a[0]]:[])}(R)),N=void 0!==r[4]?+st(r[4],0,1):1;return F[3]=N,F}};gt.test=function(t){return V.test(t)||nt.test(t)||at.test(t)||it.test(t)||ut.test(t)||lt.test(t)||tt.test(t)||rt.test(t)||et.test(t)||ot.test(t)||"transparent"===t},d.prototype.css=function(t){return B(this._rgb,t)};var pt=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];return new(Function.prototype.bind.apply(d,[null].concat(t,["css"])))};y.css=pt,b.format.css=gt,b.autodetect.push({p:5,test:function(t){for(var r=[],n=arguments.length-1;n-- >0;)r[n]=arguments[n+1];if(!r.length&&"string"===u(t)&&gt.test(t))return"css"}});var mt=/^#?([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,vt=/^#?([A-Fa-f0-9]{8}|[A-Fa-f0-9]{4})$/,bt=Math.round;d.prototype.hex=function(t){return function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];var n=l(t,"rgba"),e=n[0],o=n[1],a=n[2],i=n[3],u=c(t)||"auto";void 0===i&&(i=1),"auto"===u&&(u=i<1?"rgba":"rgb");var h="000000"+((e=bt(e))<<16|(o=bt(o))<<8|(a=bt(a))).toString(16);h=h.substr(h.length-6);var s="0"+bt(255*i).toString(16);switch(s=s.substr(s.length-2),u.toLowerCase()){case"rgba":return"#"+h+s;case"argb":return"#"+s+h;default:return"#"+h}}(this._rgb,t)};var dt=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];return new(Function.prototype.bind.apply(d,[null].concat(t,["hex"])))};y.hex=dt,b.format.hex=function(t){if(t.match(mt)){4!==t.length&&7!==t.length||(t=t.substr(1)),3===t.length&&(t=(t=t.split(""))[0]+t[0]+t[1]+t[1]+t[2]+t[2]);var r=parseInt(t,16);return[r>>16,r>>8&255,255&r,1]}if(t.match(vt)){5!==t.length&&9!==t.length||(t=t.substr(1)),4===t.length&&(t=(t=t.split(""))[0]+t[0]+t[1]+t[1]+t[2]+t[2]+t[3]+t[3]);var n=parseInt(t,16);return[n>>24&255,n>>16&255,n>>8&255,Math.round((255&n)/255*100)/100]}throw new Error("unknown hex color: "+t)},b.autodetect.push({p:4,test:function(t){for(var r=[],n=arguments.length-1;n-- >0;)r[n]=arguments[n+1];if(!r.length&&"string"===u(t)&&[3,4,5,6,7,8,9].indexOf(t.length)>=0)return"hex"}}),d.prototype.hsl=function(){return w(this._rgb)};var yt=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];return new(Function.prototype.bind.apply(d,[null].concat(t,["hsl"])))};y.hsl=yt,b.format.hsl=C,b.autodetect.push({p:2,test:function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];if("array"===u(t=l(t,"hsl"))&&3===t.length)return"hsl"}}),d.prototype.lab=function(){return _(this._rgb)};var wt=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];return new(Function.prototype.bind.apply(d,[null].concat(t,["lab"])))};Object.assign(y,{lab:wt,getLabWhitePoint:j,setLabWhitePoint:x}),b.format.lab=O,b.autodetect.push({p:2,test:function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];if("array"===u(t=l(t,"lab"))&&3===t.length)return"lab"}}),d.prototype.oklab=function(){return Y(this._rgb)};var Mt=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];return new(Function.prototype.bind.apply(d,[null].concat(t,["oklab"])))};Object.assign(y,{oklab:Mt}),b.format.oklab=S,b.autodetect.push({p:2,test:function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];if("array"===u(t=l(t,"oklab"))&&3===t.length)return"oklab"}});var kt=Math.round;d.prototype.rgb=function(t){return void 0===t&&(t=!0),!1===t?this._rgb.slice(0,3):this._rgb.slice(0,3).map(kt)},d.prototype.rgba=function(t){return void 0===t&&(t=!0),this._rgb.slice(0,4).map((function(r,n){return n<3?!1===t?r:kt(r):r}))};var xt=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];return new(Function.prototype.bind.apply(d,[null].concat(t,["rgb"])))};Object.assign(y,{rgb:xt}),b.format.rgb=function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];var n=l(t,"rgba");return void 0===n[3]&&(n[3]=1),n},b.autodetect.push({p:3,test:function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];if("array"===u(t=l(t,"rgba"))&&(3===t.length||4===t.length&&"number"==u(t[3])&&t[3]>=0&&t[3]<=1))return"rgb"}}),d.prototype.alpha=function(t,r){return void 0===r&&(r=!1),void 0!==t&&"number"===u(t)?r?(this._rgb[3]=t,this):new d([this._rgb[0],this._rgb[1],this._rgb[2],t],"rgb"):this._rgb[3]},d.prototype.darken=function(t){void 0===t&&(t=1);var r=this.lab();return r[0]-=M.Kn*t,new d(r,"lab").alpha(this.alpha(),!0)},d.prototype.brighten=function(t){return void 0===t&&(t=1),this.darken(-t)},d.prototype.darker=d.prototype.darken,d.prototype.brighter=d.prototype.brighten,d.prototype.get=function(t){var r=t.split("."),n=r[0],e=r[1],o=this[n]();if(e){var a=n.indexOf(e)-("ok"===n.substr(0,2)?2:0);if(a>-1)return o[a];throw new Error("unknown channel "+e+" in mode "+n)}return o};var jt={};function _t(t,r,n){void 0===n&&(n=.5);for(var e=[],o=arguments.length-3;o-- >0;)e[o]=arguments[o+3];var a=e[0]||"lrgb";if(jt[a]||e.length||(a=Object.keys(jt)[0]),!jt[a])throw new Error("interpolation mode "+a+" is not defined");return"object"!==u(t)&&(t=new d(t)),"object"!==u(r)&&(r=new d(r)),jt[a](t,r,n).alpha(t.alpha()+n*(r.alpha()-t.alpha()))}d.prototype.mix=d.prototype.interpolate=function(t,r){void 0===r&&(r=.5);for(var n=[],e=arguments.length-2;e-- >0;)n[e]=arguments[e+2];return _t.apply(void 0,[this,t,r].concat(n))},d.prototype.set=function(t,r,n){void 0===n&&(n=!1);var e=t.split("."),o=e[0],a=e[1],i=this[o]();if(a){var l=o.indexOf(a)-("ok"===o.substr(0,2)?2:0);if(l>-1){if("string"==u(r))switch(r.charAt(0)){case"+":case"-":i[l]+=+r;break;case"*":i[l]*=+r.substr(1);break;case"/":i[l]/=+r.substr(1);break;default:i[l]=+r}else{if("number"!==u(r))throw new Error("unsupported value for Color.set");i[l]=r}var c=new d(i,o);return n?(this._rgb=c._rgb,this):c}throw new Error("unknown channel "+a+" in mode "+o)}return i},d.prototype.tint=function(t){void 0===t&&(t=.5);for(var r=[],n=arguments.length-1;n-- >0;)r[n]=arguments[n+1];return _t.apply(void 0,[this,"white",t].concat(r))},d.prototype.shade=function(t){void 0===t&&(t=.5);for(var r=[],n=arguments.length-1;n-- >0;)r[n]=arguments[n+1];return _t.apply(void 0,[this,"black",t].concat(r))};var Et=Math.sqrt,At=Math.pow;jt.lrgb=function(t,r,n){var e=t._rgb,o=e[0],a=e[1],i=e[2],u=r._rgb,l=u[0],c=u[1],h=u[2];return new d(Et(At(o,2)*(1-n)+At(l,2)*n),Et(At(a,2)*(1-n)+At(c,2)*n),Et(At(i,2)*(1-n)+At(h,2)*n),"rgb")};return jt.oklab=function(t,r,n){var e=t.oklab(),o=r.oklab();return new d(e[0]+n*(o[0]-e[0]),e[1]+n*(o[1]-e[1]),e[2]+n*(o[2]-e[2]),"oklab")},Object.assign(y,{Color:d,valid:function(){for(var t=[],r=arguments.length;r--;)t[r]=arguments[r];try{return new(Function.prototype.bind.apply(d,[null].concat(t))),!0}catch(t){return!1}},css:pt,hex:dt,hsl:yt,lab:wt,oklab:Mt,rgb:xt,mix:_t,interpolate:_t}),y}));
